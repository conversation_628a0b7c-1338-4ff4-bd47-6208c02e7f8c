USER_POOL_ID=ap-south-1_Y3OJo6T4R
EMAIL=<EMAIL>
NAME="Abhishek <PERSON>"
PASSWORD=Test@1234
REGION=ap-south-1
PROFILE=tsc-pos

aws cognito-idp admin-create-user --user-pool-id $USER_POOL_ID --username $EMAIL --user-attributes '[{"Name":"email", "Value":"'"$EMAIL"'"},{"Name":"name", "Value":"'"$NAME"'"}]' --message-action SUPPRESS --profile $PROFILE --region $REGION;
aws cognito-idp admin-set-user-password --user-pool-id $USER_POOL_ID --username $EMAIL --password $PASSWORD --permanent --profile $PROFILE --region $REGION;