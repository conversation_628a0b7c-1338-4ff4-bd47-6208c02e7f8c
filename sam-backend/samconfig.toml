version = 0.1
#################################
#               DEV             #
#################################
[dev]
[dev.global.parameters]
stage = 'dev'
stack_name = "pos-dev"

OpenSearchUsername = 'admin'
OpenSearchPassword = 'Admin@1234'

[dev.build.parameters]
parallel = true

[dev.validate.parameters]
lint = true

[dev.deploy.parameters]
capabilities = "CAPABILITY_IAM CAPABILITY_NAMED_IAM"
confirm_changeset = true
resolve_s3 = true
resolve_image_repos = true
s3_prefix = "pos-dev"
region = "ap-south-1"
profile = "tsc"
parameter_overrides = "StackName=\"\""

[dev.package.parameters]
resolve_s3 = true

[dev.sync.parameters]
watch = true

[dev.local_start_api.parameters]
warm_containers = "EAGER"

[dev.local_start_lambda.parameters]
warm_containers = "EAGER"


#################################
#              STAGE            #
#################################
[stage]
[stage.global.parameters]
stack_name = "pos-stage"

[stage.build.parameters]
parallel = true

[stage.validate.parameters]
lint = true

[stage.deploy.parameters]
capabilities = "CAPABILITY_IAM CAPABILITY_NAMED_IAM"
confirm_changeset = true
resolve_s3 = true
resolve_image_repos = true
s3_prefix = "pos-stage"
region = "ap-south-1"
profile = "tsc-pos"
parameter_overrides = "StackName=\"\""

[stage.package.parameters]
resolve_s3 = true

[stage.sync.parameters]
watch = true

[stage.local_start_api.parameters]
warm_containers = "EAGER"

[stage.local_start_lambda.parameters]
warm_containers = "EAGER"



#################################
#              PROD             #
#################################
[prod]
[prod.global.parameters]
stack_name = "pos-prod"

[prod.build.parameters]
parallel = true

[prod.validate.parameters]
lint = true

[prod.deploy.parameters]
capabilities = "CAPABILITY_IAM CAPABILITY_NAMED_IAM CAPABILITY_AUTO_EXPAND"
confirm_changeset = true
resolve_s3 = true
resolve_image_repos = true
s3_prefix = "pos-prod"
region = "ap-south-1"
profile = "tsc-pos"
parameter_overrides = "StackName=\"\""

[prod.package.parameters]
resolve_s3 = true

[prod.sync.parameters]
watch = true

[prod.local_start_api.parameters]
warm_containers = "EAGER"

[prod.local_start_lambda.parameters]
warm_containers = "EAGER"