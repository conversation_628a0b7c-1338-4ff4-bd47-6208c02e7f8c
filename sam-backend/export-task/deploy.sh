#!/bin/bash

# Build Docker image for export processor deployment
echo "Building Docker image..."
docker build -t export-processor . --platform linux/amd64

# Login to ECR
echo "Logging into ECR..."
aws ecr get-login-password --region ap-south-1 --profile tsc-pos | docker login --username AWS --password-stdin 307941960934.dkr.ecr.ap-south-1.amazonaws.com/export-processor

# Tag image
echo "Tagging image..."
docker tag export-processor:latest 307941960934.dkr.ecr.ap-south-1.amazonaws.com/export-processor:latest

# Push image to ECR
echo "Pushing image to ECR..."
docker push 307941960934.dkr.ecr.ap-south-1.amazonaws.com/export-processor:latest

echo "Export processor deployment complete!"