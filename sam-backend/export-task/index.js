// index.js - Main entry point for the export task
import moment from 'moment';
import { processExportOfPostOrder } from './processExportOfPostOrder.js';

// Main application entry point
async function main() {
  console.log('Starting Export Task processor...');

  try {
    // Determine execution mode
    const executionMode = process.env.EXECUTION_MODE || 'SCHEDULED';
    console.log(`Execution Mode: ${executionMode}`);

    if (executionMode === 'SCHEDULED') {
      // This is the daily cron run at 5 PM
      console.log('Running scheduled daily processing at 5 PM...');

      // Default configuration for scheduled run
      const defaultExportConfig = {
        exportType: 'REPLACEMENT_ORDERS',
        email: '<EMAIL>', // Default email for scheduled exports
        isScheduled: true, // Flag to indicate this is a scheduled run
        filters: {
          // Add default filters for scheduled run, e.g., last 24 hours
          startDate: moment().subtract(1, 'day').format('YYYY-MM-DD'),
          endDate: moment().format('YYYY-MM-DD'),
        },
        timestamp: new Date().toISOString(),
      };

      // Process the scheduled export
      await processExportOfPostOrder(defaultExportConfig);
    } else if (executionMode === 'ON_DEMAND') {
      // This is a triggered run with specific export configuration
      console.log('Running on-demand export processing...');

      const exportConfigStr = process.env.EXPORT_CONFIG;

      if (!exportConfigStr) {
        throw new Error('EXPORT_CONFIG is required for ON_DEMAND mode');
      }

      const exportConfig = JSON.parse(exportConfigStr);
      console.log(
        'Export Configuration:',
        JSON.stringify(exportConfig, null, 2),
      );

      // Process the on-demand export
      await processExportOfPostOrder(exportConfig);
    } else {
      throw new Error(`Unknown execution mode: ${executionMode}`);
    }

    console.log('Export processor completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Export processor failed:', error);
    process.exit(1);
  }
}

// Handle process termination gracefully
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, gracefully shutting down...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, gracefully shutting down...');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the application
main();
