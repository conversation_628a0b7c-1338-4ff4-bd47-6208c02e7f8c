AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  tsc-pos-api

  Sam Template for The Sleep Company POS application API

# Globals:

Parameters:
  stage:
    Type: String
    Default: stage
  CognitoUserPoolName:
    Type: String
    Default: cognito-userpool
  IdentityUserPoolName:
    Type: String
    Default: identity-userpool
  WebUsersClientName:
    Type: String
    Default: cognito-web-client
  OTPBaseURL:
    Type: String
  ProductTableName:
    Type: String
    Default: product-table
  InventoryTrackingTableName:
    Type: String
    Default: inventory-tracking-table
  IOTableName:
    Type: String
    Default: io-table
  CollectionTableName:
    Type: String
    Default: collection-table
  GlobalConfigurationTableName:
    Type: String
    Default: global-configuration-table
  LocalConfigurationTableName:
    Type: String
    Default: local-configuration-table
  StoreTableName:
    Type: String
    Default: store-table
  BulkCouponGenerationTableName:
    Type: String
    Default: bulk-coupon-generation-table
  CouponTableName:
    Type: String
    Default: coupon-table
  ReplacementOrderTableName:
    Type: String
    Default: replacement-table
  RefundDetailTableName:
    Type: String
    Default: refund-table
  CampaignCouponTableName:
    Type: String
    Default: campaign-coupon-table
  SpinTheWheelCouponConfigTableName:
    Type: String
    Default: spin-the-wheel-coupon-config-table
  STNTableName:
    Type: String
    Default: stn-table
  GRNTableName:
    Type: String
    Default: grn-table
  EmployeeTableName:
    Type: String
    Default: employee-table
  QuotationTableName:
    Type: String
    Default: quotation-table
  CartTableName:
    Type: String
    Default: cart-table
  PaymentTableName:
    Type: String
    Default: payment-table
  PincodeTableName:
    Type: String
    Default: pincode-table
  OrderTableName:
    Type: String
    Default: order-table
  OrderItemTableName:
    Type: String
    Default: order-item-table
  OrdersLogTableName:
    Type: String
    Default: orders-log-table
  InteriorArchitectureTableName:
    Type: String
    Default: interior-architecture-table
  WeLicenseCode:
    Type: String
    Default: ~716803a0
  WeAuthToken:
    Type: String
    Default: 2cf44251-9193-44f5-9889-7ab6d31ef519
  HSNCodeTableName:
    Type: String
    Default: hsn-code-table
  PriceMasterTableName:
    Type: String
    Default: price-master-table
  CMSTableName:
    Type: String
    Default: cms-table
  GRNTableName:
    Type: String
    Default: grn-table
  InventoryCredentialsTableName:
    Type: String
    Default: inventory-credentials-table
  StorePOTableName:
    Type: String
    Default: store-po-table
  CustomerAddressTableName:
    Type: String
    Default: customer-address-table
  MattressRecommendationTableName:
    Type: String
    Default: mattress-recommendation-table
  PublicBucketName:
    Type: String
    Default: public-bucket
  RefundBucketName:
    Type: String
    Default: refund-bucket
  OpenSearchUsername:
    Type: String
    Default: admin
  OpenSearchPassword:
    Type: String
    Default: Admin@1234
  ShopifyAccessToken:
    Type: String
  ShopifyAdminBaseUrl:
    Type: String
  WishlistAccessToken:
    Type: String
  WishlistAPIUrl:
    Type: String
  RazorPayAppKey:
    Type: String
  RazorPayAppUsername:
    Type: String
  RazorPayAppUrl:
    Type: String
  RazorPayAPIKey:
    Type: String
  RazorPayAPISecret:
    Type: String
  SuccessCallbackUrl:
    Type: String
  OpensearchInstanceType:
    Type: String
  OpensearchInstanceVolume:
    Type: String
  OpensearchInstanceCount:
    Type: String
  OpensearchZoneAwarenessEnabled:
    Type: String
  PayUClientId:
    Type: String
  PayUClientSecret:
    Type: String
    NoEcho: true
  PayUKey:
    Type: String
  PayUSalt:
    Type: String
    NoEcho: true
  PayUMerchantId:
    Type: String
  PayUTokenEndpoint:
    Type: String
  PayUUpdationLink:
    Type: String
  PayUVerifyPayment:
    Type: String
  ApiGatewayUrl:
    Type: String
  ReplacementBaseURL:
    Type: String
  ReplacementAPIKey:
    Type: String
  SnapmintAppUrl:
    Type: String
  SnapmintAppRequestUrl:
    Type: String
  SnapmintSuccessEdgeUrl:
    Type: String
    Default: thankyou-edge
  SnapmintFailureUrl:
    Type: String
  PineLabsMerchantId:
    Type: String
  PineLabsAppUrl:
    Type: String
  PineLabsSecurityToken:
    Type: String
  PineLabsStoreId:
    Type: String
  LeadsquaredAccessKey:
    Type: String
  LeadsquaredSecretKey:
    Type: String
  BEBaseApi  :
    Type: String
  AuthToken:
    Type: String
  MediaBaseApi:
    Type: String
  MswipeRequestUrl:
    Type: String
  MswipeVerificationBaseUrl:
    Type: String
  MswipeRequestClientCode:
    Type: String
  MswipeVerificationClientCode:
    Type: String
  MswipeUserID:
    Type: String
  MswipePassword:
    Type: String
  MswipeSalt:
    Type: String
    NoEcho: true
  FreshDeskApiUrl:
    Type: String
    Description: URL for FreshDeskApiKey
  FreshDeskApiKey:
    Type: String
  FreshDeskPassword:
    Type: String
  FreshDeskGroupId:
    Type: String
  EasyEcomEmail:
    Type: String
  EasyEcomPassword:
    Type: String
  EasyEcomBaseUrl:
    Type: String
  EasyEcomPrimaryLocationKey:
    Type: String
  OTPBaseURL:
     Type: String
  NotificationsTableName:
    Type: String
    Default: notifications-table
  GetNotificationSqsQueueUrl:
    Type: String
    Default: notifications-queue
  FirebaseTokensTableName:
    Type: String
    Default: firebase-token-table
  PriceTableName:
    Type: String
    Default: price-table
  NotificationLogsTableName:
    Type: String
    Default: notification-logs-table
  FestiveOffersCouponTableName:
    Type: String
    Default: festive-offers-coupon-table
  PriceTableName:
    Type: String
    Default: price-table
  GetOrderByAWBNumberGSI:
    Type: String
    Default: ByAwbNumberIndex
  ReplacementEasyEcomStoreId:
    Type: String
    Default: WH-FG
  SlackWebhookUrl:
    Type: String
  CustomDiscountVerificationTableName:
    Type: String
    Default: custom-discount-verification-table
  StoreOrdersApiKeysTableName:
    Type: String
    Default: store-orders-api-keys-table
  CustomerSpecificCouponTableName:
    Type: String
    Default: customer-specific-coupon-table
  EmployeeMasterTableName:
    Type: String
    Default: employee-master-table
  CancelOrderTableName:
    Type: String
    Default: cancel-table
  PaymentDetailsURL:
    Type: String
  RequestModuleAPIUrl:
    Type: String
  RequestModuleAPIKey:
    Type: String
  SubnetIds:
    Type: String
  VpcId:
    Type: String

Resources:
  webSiteBucket:
    Type: AWS::S3::Bucket
    Properties:
      CorsConfiguration:
        CorsRules:
          - AllowedMethods:
              - GET
            AllowedOrigins:
              - "*"
            MaxAge: 3600
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      WebsiteConfiguration:
        ErrorDocument: index.html
        IndexDocument: index.html
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  webSiteBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref webSiteBucket
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Action:
              - "s3:GetObject"
            Effect: Allow
            Resource: !Join ["", ["arn:aws:s3:::", !Ref webSiteBucket, "/*"]]
            Principal: "*"
  
  OffersBucket:
    Type: AWS::S3::Bucket
    Properties:
      CorsConfiguration:
        CorsRules:
          - AllowedMethods:
              - GET
            AllowedOrigins:
              - "*"
            MaxAge: 3600
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      WebsiteConfiguration:
        ErrorDocument: index.html
        IndexDocument: index.html
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  OffersBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref OffersBucket
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Action:
              - "s3:GetObject"
            Effect: Allow
            Resource: !Join ["", ["arn:aws:s3:::", !Ref OffersBucket, "/*"]]
            Principal: "*"

  ThankYouBucket:
    Type: AWS::S3::Bucket
    Properties:
      CorsConfiguration:
        CorsRules:
          - AllowedMethods:
              - GET
            AllowedOrigins:
              - "*"
            MaxAge: 3600
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      WebsiteConfiguration:
        ErrorDocument: index.html
        IndexDocument: index.html
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  ThankYouBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref ThankYouBucket
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Action:
              - "s3:GetObject"
            Effect: Allow
            Resource: !Join ["", ["arn:aws:s3:::", !Ref ThankYouBucket, "/*"]]
            Principal: "*"
    
  RefundBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref RefundBucketName]]
      CorsConfiguration:
        CorsRules:
          - AllowedMethods:
              - GET
            AllowedOrigins:
              - "*"
            MaxAge: 3600
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      WebsiteConfiguration:
        ErrorDocument: index.html
        IndexDocument: index.html
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  RefundBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref RefundBucket
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Action:
              - "s3:GetObject"
            Effect: Allow
            Resource: !Join ["", ["arn:aws:s3:::", !Ref RefundBucket, "/*"]]
            Principal: "*"
    
  PublicBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref PublicBucketName]]
      CorsConfiguration:
        CorsRules:
          - AllowedMethods:
              - GET
              - PUT
            AllowedOrigins:
              - "*"
            MaxAge: 3600
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - HEAD
              - PUT
              - POST
              - DELETE
            AllowedOrigins:
              - "*"
            ExposedHeaders:
              - x-amz-server-side-encryption
              - x-amz-request-id
              - x-amz-id-2
              - ETag
            Id: S3CORSRuleId1
            MaxAge: 3000
    UpdateReplacePolicy: Retain
    DeletionPolicy: Delete
    
  CognitoUserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref CognitoUserPoolName]]
      AdminCreateUserConfig:
        AllowAdminCreateUserOnly: true
      AutoVerifiedAttributes:
        - email
      EnabledMfas:
        - SOFTWARE_TOKEN_MFA
      MfaConfiguration: OPTIONAL
      Policies:
        PasswordPolicy:
          MinimumLength: 6
          RequireLowercase: false
          RequireNumbers: true
          RequireSymbols: false
          RequireUppercase: false
          TemporaryPasswordValidityDays: 30
      Schema:
        - AttributeDataType: String
          Mutable: true
          Name: name
          Required: true
        - AttributeDataType: String
          Mutable: false
          Name: email
          Required: true
        - AttributeDataType: String
          Mutable: true
          Name: stores
          Required: false
        - AttributeDataType: String
          Mutable: true
          Name: roles
          Required: false
      UsernameAttributes:
        - email

  WebUsersClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      ClientName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref WebUsersClientName]]
      AccessTokenValidity: 1
      TokenValidityUnits:
        AccessToken: hours
        IdToken: hours
        RefreshToken: days
      AllowedOAuthFlows:
        - implicit
      AllowedOAuthFlowsUserPoolClient: true
      AllowedOAuthScopes:
        - phone
        - email
        - openid
        - profile
        - aws.cognito.signin.user.admin
      CallbackURLs:
        - http://localhost:3000
        - http://localhost:3000/logout
      ClientName: WebUsers
      DefaultRedirectURI: http://localhost:3000
      EnableTokenRevocation: true
      ExplicitAuthFlows:
        - ALLOW_REFRESH_TOKEN_AUTH
        - ALLOW_USER_SRP_AUTH
        - ALLOW_ADMIN_USER_PASSWORD_AUTH
        - ALLOW_USER_PASSWORD_AUTH
      GenerateSecret: false
      IdTokenValidity: 1
      LogoutURLs:
        - http://localhost:3000/logout
      PreventUserExistenceErrors: ENABLED
      RefreshTokenValidity: 30
      SupportedIdentityProviders:
        - COGNITO
      UserPoolId: !Ref CognitoUserPool

  IdentityPool:
    Type: AWS::Cognito::IdentityPool
    Properties:
      IdentityPoolName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref IdentityUserPoolName]]
      AllowUnauthenticatedIdentities: true
      CognitoIdentityProviders:
        - ClientId: !GetAtt WebUsersClient.ClientId
          ProviderName: !GetAtt CognitoUserPool.ProviderName

  UnauthenticatedUserRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Join ["-", [{ Ref: "AWS::StackName" }, "identity-unauthenticated-role"]]
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Federated: cognito-identity.amazonaws.com
            Action:
              - sts:AssumeRoleWithWebIdentity
            Condition:
              StringEquals:
                cognito-identity.amazonaws.com:aud: !Ref IdentityPool
              ForAnyValue:StringLike:
                cognito-identity.amazonaws.com:amr: unauthenticated
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonS3FullAccess

  AuthenticatedUserRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Join ["-", [{ Ref: "AWS::StackName" }, "identity-authenticated-role"]]
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Federated: cognito-identity.amazonaws.com
            Action:
              - sts:AssumeRoleWithWebIdentity
            Condition:
              StringEquals:
                cognito-identity.amazonaws.com:aud: !Ref IdentityPool
              ForAnyValue:StringLike:
                cognito-identity.amazonaws.com:amr: authenticated
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonS3FullAccess

  IdentityPoolRoleAttachment:
    Type: AWS::Cognito::IdentityPoolRoleAttachment
    Properties:
      IdentityPoolId: !Ref IdentityPool
      Roles:
        authenticated: !GetAtt AuthenticatedUserRole.Arn
        unauthenticated: !GetAtt UnauthenticatedUserRole.Arn

  StoreTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref StoreTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  ProductTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "pk"
          AttributeType: "S"
        - AttributeName: "sk"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "pk"
          KeyType: "HASH"
        - AttributeName: "sk"
          KeyType: "RANGE"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref ProductTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  CollectionTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref CollectionTableName]]
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  GlobalConfigurationTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "key"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "key"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref GlobalConfigurationTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete



      
  BulkCouponGenerationTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref BulkCouponGenerationTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  LocalConfigurationTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "storeId"
          AttributeType: "S"
        - AttributeName: "key"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "storeId"
          KeyType: "HASH"
        - AttributeName: "key"
          KeyType: "RANGE"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref LocalConfigurationTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  CouponTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "pk"
          AttributeType: "S"
        - AttributeName: "sk"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "pk"
          KeyType: "HASH"
        - AttributeName: "sk"
          KeyType: "RANGE"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref CouponTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  ReplacementOrderTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "shopifyOrderId"
          AttributeType: "S"
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "shopifyOrderId"
          KeyType: "HASH"
        - AttributeName: "id"
          KeyType: "RANGE"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref ReplacementOrderTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE

  RefundDetailTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "shopifyOrderId"
          AttributeType: "S"
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "shopifyOrderId"
          KeyType: "HASH"
        - AttributeName: "id"
          KeyType: "RANGE"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref RefundDetailTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE

  CampaignCouponTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "code"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "code"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref CampaignCouponTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  STNTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref STNTableName]]
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
  
  FestiveOffersCouponTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "code"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "code"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref FestiveOffersCouponTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
    
  EmployeeTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref EmployeeTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  QuotationTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref QuotationTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
    
  OrderTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
        - AttributeName: "awbNumber"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      GlobalSecondaryIndexes:
        - IndexName: !Ref GetOrderByAWBNumberGSI
          KeySchema:
            - AttributeName: "awbNumber"
              KeyType: "HASH"
          Projection: 
            NonKeyAttributes: 
              - eeRefNo
              - ecomInvoiceId
              - ecomOrderId
              - ecomSubOrderId
            ProjectionType: INCLUDE
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref OrderTableName]]
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  PriceTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref PriceTableName]]
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: "pk"
          AttributeType: "S"
        - AttributeName: "sk"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "pk"
          KeyType: "HASH"
        - AttributeName: "sk"
          KeyType: "RANGE"
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: false
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  
  OrderItemTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref OrderItemTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  InteriorArchitectureTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref InteriorArchitectureTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  GRNTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "stnId"
          AttributeType: "S"
        - AttributeName: "grnId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "stnId"
          KeyType: "HASH"  
        - AttributeName: "grnId"
          KeyType: "RANGE" 
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref GRNTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  StoreOrdersApiKeysTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref StoreOrdersApiKeysTableName]]
      AttributeDefinitions:
        - AttributeName: apiKey
          AttributeType: S
      KeySchema:
        - AttributeName: apiKey
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
  
  CustomerSpecificCouponTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "code"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "code"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref CustomerSpecificCouponTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
  
  EmployeeMasterTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "phone"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "phone"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref EmployeeMasterTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  HSNCodeTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref HSNCodeTableName]]
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  SpinTheWheelCouponConfigTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "prefix"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "prefix"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref SpinTheWheelCouponConfigTableName]]
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
  
  CancelOrderTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "pk"
          AttributeType: "S"
        - AttributeName: "sk"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "pk"
          KeyType: "HASH"
        - AttributeName: "sk"
          KeyType: "RANGE"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref CancelOrderTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE


    
  CartTable:
    Type: AWS::DynamoDB::Table
    Properties: 
      AttributeDefinitions: 
        - AttributeName: "customerId"
          AttributeType: "S"
      KeySchema: 
        - AttributeName: "customerId"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref CartTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
  
  CustomDiscountVerificationTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "quotationId"
          AttributeType: "S"
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "quotationId"
          KeyType: "HASH"  
        - AttributeName: "id"
          KeyType: "RANGE" 
      BillingMode: "PAY_PER_REQUEST"
      TableName:
        !Join ["-", [{ Ref: "AWS::StackName" }, !Ref CustomDiscountVerificationTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
  

  PaymentTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "orderId"
          AttributeType: "S"
        - AttributeName: "transactionId"
          AttributeType: "S"
        - AttributeName: 'createdAt'
          AttributeType: 'S'
      KeySchema:
        - AttributeName: "orderId"
          KeyType: "HASH"
        - AttributeName: "transactionId"
          KeyType: "RANGE"
      GlobalSecondaryIndexes: 
        - IndexName: 'transactionId-createdAt-index'
          KeySchema:
            - AttributeName: 'transactionId'
              KeyType: 'HASH'
            - AttributeName: 'createdAt'
              KeyType: 'RANGE'
          Projection:
            ProjectionType: 'ALL'
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref PaymentTableName ]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  PincodeTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref PincodeTableName]]
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  PriceMasterTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref PriceMasterTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  CMSTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref CMSTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  InventoryCredentialsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref InventoryCredentialsTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  InventoryTrackingTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "storeId"
          AttributeType: "S"
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "storeId"
          KeyType: "HASH"
        - AttributeName: "id"
          KeyType: "RANGE"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref InventoryTrackingTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
  
  StorePOTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "stnId"
          AttributeType: "S"
        - AttributeName: "poId"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "stnId"
          KeyType: "HASH"
        - AttributeName: "poId"
          KeyType: "RANGE"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref StorePOTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

   

  IOTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "storeId"
          AttributeType: "S"
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "storeId"
          KeyType: "HASH"
        - AttributeName: "id"
          KeyType: "RANGE"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref IOTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  NotificationsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "id"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "id"
          KeyType: "HASH"
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref NotificationsTableName]]
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
  
  FirebaseTokensTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "storeId"
          AttributeType: "S"  # String type
        - AttributeName: "id"
          AttributeType: "S"  # String type
      KeySchema:
        - AttributeName: "storeId"
          KeyType: "HASH"  # Partition key
        - AttributeName: "id"
          KeyType: "RANGE"  # Sort key
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref FirebaseTokensTableName]]
      DeletionProtectionEnabled: false
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  NotificationLogsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      AttributeDefinitions:
        - AttributeName: "storeId"
          AttributeType: "S"  # String type
        - AttributeName: "notificationId"
          AttributeType: "S"  # String type
        - AttributeName: "createdAt"  # Added for GSI
          AttributeType: "S"  # String type for ISO date
      KeySchema:
        - AttributeName: "storeId"
          KeyType: "HASH"  # Partition key
        - AttributeName: "notificationId"
          KeyType: "RANGE"  # Sort key
      GlobalSecondaryIndexes: 
        - IndexName: 'StoreIdCreatedAtIndex'
          KeySchema:
            - AttributeName: 'storeId'
              KeyType: 'HASH'
            - AttributeName: 'createdAt'
              KeyType: 'RANGE'
          Projection:
            ProjectionType: 'ALL'
      BillingMode: "PAY_PER_REQUEST"
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref NotificationLogsTableName]]
      DeletionProtectionEnabled: false
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  CustomerAddressTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref CustomerAddressTableName]]
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: "pk"
          AttributeType: "S"
        - AttributeName: "sk"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "pk"
          KeyType: "HASH"
        - AttributeName: "sk"
          KeyType: "RANGE"
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: false
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  MattressRecommendationTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref MattressRecommendationTableName]]
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: "pk"
          AttributeType: "S"
        - AttributeName: "sk"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "pk"
          KeyType: "HASH"
        - AttributeName: "sk"
          KeyType: "RANGE"
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: false
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  OrdersLogTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref OrdersLogTableName]]
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: "pk"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "pk"
          KeyType: "HASH"
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: false
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete
  
  PriceTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Join ["-", [{ Ref: "AWS::StackName" }, !Ref PriceTableName]]
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: "pk"
          AttributeType: "S"
        - AttributeName: "sk"
          AttributeType: "S"
      KeySchema:
        - AttributeName: "pk"
          KeyType: "HASH"
        - AttributeName: "sk"
          KeyType: "RANGE"
      StreamSpecification:
        StreamViewType: NEW_IMAGE
      DeletionProtectionEnabled: false
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete


  ECRRepo:
    Type: AWS::ECR::Repository
    Properties:
      RepositoryName: !Join ["-", [{ Ref: "AWS::StackName" }, "task"]]
      RepositoryPolicyText:
        Version: "2012-10-17"
        Statement:
          - Sid: AllowPushPull
            Effect: Allow
            Principal: "*"
            Action: "ecr:*"
 
  OTPBaseURLParam:
     Type: AWS::SSM::Parameter
     Properties:
       DataType: text
       Description: OTP base URL
       Name: !Join ["", ["/", { Ref: AWS::StackName }, "/otp/url"]]
       Type: String
       Value: !Ref OTPBaseURL 

  UserPoolParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Name of the User pool ID
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/cognito/userpool_id"]]
      Type: String
      Value: !Ref CognitoUserPool

  WebClientParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Name of the user pool web client ID
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/cognito/client_id"]]
      Type: String
      Value: !Ref WebUsersClient
  
  EasyEcomEmailParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: EasyEcom email
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/easyecom/email"]]
      Type: String
      Value: !Ref EasyEcomEmail

  EasyEcomPasswordParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: EasyEcom password
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/easyecom/password"]]
      Type: String
      Value: !Ref EasyEcomPassword

  EasyEcomBaseUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: EasyEcom base URL
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/easyecom/base-url"]]
      Type: String
      Value: !Ref EasyEcomBaseUrl

  EasyEcomPrimaryLocationKeyParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: EasyEcom Primary Location Key
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/easyecom/locationKey"]]
      Type: String
      Value: !Ref EasyEcomPrimaryLocationKey
  
  RequestModuleAPIUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Request Module api url
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/request-module/api-url"]]
      Type: String
      Value: !Ref RequestModuleAPIUrl 
  
  RequestModuleAPIKeyParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Request Module api url
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/request-module/api-key"]]
      Type: String
      Value: !Ref RequestModuleAPIKey
  
  SlackWebhookUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Slack webhook param
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/slack/webhook"]]
      Type: String
      Value: !Ref SlackWebhookUrl  
  
  NotificationQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${AWS::StackName}-notification-queue
      VisibilityTimeout: 900 

  # NotificationQueueUrl:
  #   Description: "URL of the Notification Queue"
  #   Value: !Ref NotificationQueue
  #   Export:
  #     Name: !Sub ${AWS::StackName}-notification-queue

  # StoreTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the store table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/store"]]
  #     Type: String
  #     Value: !Ref StoreTable

  # ProductTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the product table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/product"]]
  #     Type: String
  #     Value: !Ref ProductTable

  # GlobalConfigurationParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the global configuration table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/global-configuration"]]
  #     Type: String
  #     Value: !Ref GlobalConfigurationTable

  # LocalConfigurationParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the local configuration table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/local-configuration"]]
  #     Type: String
  #     Value: !Ref LocalConfigurationTable

  # CouponTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the shopify coupon table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/coupon"]]
  #     Type: String
  #     Value: !Ref CouponTable

  # CampaignCouponTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the campaign coupon table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/campaign-coupon"]]
  #     Type: String
  #     Value: !Ref CampaignCouponTable

  # EmployeeTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the employee table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/employee"]]
  #     Type: String
  #     Value: !Ref EmployeeTable

  # QuotationTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the quotation table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/quotation"]]
  #     Type: String
  #     Value: !Ref QuotationTable
  
  # OrderTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the order table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/order"]]
  #     Type: String
  #     Value: !Ref OrderTable

  # OrderItemTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the order item table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/order-item"]]
  #     Type: String
  #     Value: !Ref OrderItemTable

  # InteriorArchitectureTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the interior architecture table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/interior-architecture"]]
  #     Type: String
  #     Value: !Ref InteriorArchitectureTable

  # HSNCodeTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the hsn code table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/hsn-code"]]
  #     Type: String
  #     Value: !Ref HSNCodeTable

  # CartTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the cart table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/cart"]]
  #     Type: String
  #     Value: !Ref CartTable

  # PaymentTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the payment table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/payment"]]
  #     Type: String
  #     Value: !Ref PaymentTable

  # PincodeTableParam:
  #   Type: AWS::SSM::Parameter
  #   Properties:
  #     DataType: text
  #     Description: Name of the pincode table
  #     Name: !Join ["", ["/", { Ref: AWS::StackName }, "/table/pincode"]]
  #     Type: String
  #     Value: !Ref PincodeTable

  OpenSearchDomainEndpointParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Name of the opensearch domain endpoint
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/opensearch/endpoint"]]
      Type: String
      Value: !GetAtt OpensearchDomain.DomainEndpoint

  OpenSearchDomainUsernameParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Name of the opensearch domain username
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/opensearch/username"]]
      Type: String
      Value: !Ref OpenSearchUsername

  OpenSearchDomainPasswordParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Name of the opensearch domain password
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/opensearch/password"]]
      Type: String
      Value: !Ref OpenSearchPassword

  ShopifyAccessTokenParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Shopify access token
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/shopify/access-token"]]
      Type: String
      Value: !Ref ShopifyAccessToken

  ShopifyAdminURLParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Shopify admin base url
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/shopify/admin-base-url"]]
      Type: String
      Value: !Ref ShopifyAdminBaseUrl

  AuthTokenParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: All stores access token
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/all-store/access-token"]]
      Type: String
      Value: !Ref AuthToken
  
  BEBaseApiParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Backend base url
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/backend/base-url"]]
      Type: String
      Value: !Ref BEBaseApi


  WishlistAccessTokenParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Wishlist access token
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/wishlist/access-token"]]
      Type: String
      Value: !Ref WishlistAccessToken

  WishListAPIUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Wishlist api base url
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/wishlist/api-url"]]
      Type: String
      Value: !Ref WishlistAPIUrl

  RazorPayAppKeyParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Razor pay APP Key
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/razorpay-pos/app-key"]]
      Type: String
      Value: !Ref RazorPayAppKey

  RazorPayAppUsernameParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Razor pay APP Key
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/razorpay-pos/app-username"]]
      Type: String
      Value: !Ref RazorPayAppUsername

  RazorPayAppURLParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Razor pay APP Key
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/razorpay-pos/app-url"]]
      Type: String
      Value: !Ref RazorPayAppUrl

  RazorPayAPIKeyParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Name of the opensearch domain password
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/direct-razorpay/api-key"]]
      Type: String
      Value: !Ref RazorPayAPIKey

  RazorPayAPISecretParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Name of the opensearch domain password
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/direct-razorpay/api-secret"]]
      Type: String
      Value: !Ref RazorPayAPISecret
 
  PayUClientIdParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Client ID for PayU POS
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/payu-pos/client-id"]]
      Type: String
      Value: !Ref PayUClientId

  PayUClientSecretParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Client Secret for PayU POS
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/payu-pos/client-secret"]]
      Type: String
      Value: !Ref PayUClientSecret
  
  PayUKeyParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Key for PayU POS
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/payu-pos/key"]]
      Type: String
      Value: !Ref PayUKey

  PayUSaltParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Salt for PayU POS
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/payu-pos/salt"]]
      Type: String
      Value: !Ref PayUSalt

  PayUTokenEndpointParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Endpoint for PayU Token Generation
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/payu-pos/token-endpoint"]]
      Type: String
      Value: !Ref PayUTokenEndpoint

  PayUUpdationLinkParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Endpoint for PayU Updation Link
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/payu-pos/updation-link"]]
      Type: String
      Value: !Ref PayUUpdationLink

  PayUVerifyPaymentParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Endpoint for PayU Verify Payment
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/payu-pos/verify-payment"]]
      Type: String
      Value: !Ref PayUVerifyPayment  

  PayUMerchantIdParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Merchant ID for PayU POS
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/payu-pos/merchant-id"]]
      Type: String
      Value: !Ref PayUMerchantId 

  FreshDeskApiUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: URL for FreshDesk API
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/freshdesk/api-url"]]
      Type: String
      Value: !Ref FreshDeskApiUrl

  FreshDeskApiKeyParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: API Key for FreshDesk
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/freshdesk/api-key"]]
      Type: String
      Value: !Ref FreshDeskApiKey

  PaymentDetailsURLParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Merchant ID for PayU POS
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/payment-details/url"]]
      Type: String
      Value: !Ref PaymentDetailsURL 

  FreshDeskPasswordParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Password for FreshDesk
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/freshdesk/password"]]
      Type: String
      Value: !Ref FreshDeskPassword

  FreshDeskGroupIdParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Password for FreshDesk
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/freshdesk/group-id"]]
      Type: String
      Value: !Ref FreshDeskGroupId
      
  RazorPayCallbackUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Name of the Razorpay Callback URL
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/direct-razorpay/callback-url"]]
      Type: String
      Value: !Ref SuccessCallbackUrl

  SnapmintAppURLParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Snapmint APP URL
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/snapmint/app-url"]]
      Type: String
      Value: !Ref SnapmintAppUrl

  SnapmintAppRequestUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Snapmint APP REQUEST URL
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/snapmint/request-app-url"]]
      Type: String
      Value: !Ref SnapmintAppRequestUrl

  SnapmintSuccessEdgeUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Snapmint Success edge URL
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/snapmint/success-edge-url"]]
      Type: String
      Value: !Sub "https://${ServerlessHttpApi}.execute-api.${AWS::Region}.amazonaws.com/${SnapmintSuccessEdgeUrl}"

  SnapmintFailureUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Snapmint failure url
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/snapmint/failure-url"]]
      Type: String
      Value: !Ref SnapmintFailureUrl
      
  PineLabsMerchantIdParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Merchant ID for Pine Labs
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/pine-labs/merchant-id"]]
      Type: String
      Value: !Ref PineLabsMerchantId

  PineLabsAppURLParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Pinelabs APP Url
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/pine-labs/app-url"]]
      Type: String
      Value: !Ref PineLabsAppUrl

  PineLabsSecurityTokenParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Security Token for Pine Labs
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/pine-labs/security-token"]]
      Type: String
      Value: !Ref PineLabsSecurityToken

  ApiGatewayUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Api Gateway Url
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/api-gateway/baseurl"]]
      Type: String
      Value: !Ref ApiGatewayUrl    

  PineLabsStoreIdParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Store ID for Pine Labs
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/pine-labs/store-id"]]
      Type: String
      Value: !Ref PineLabsStoreId

  MswipeRequestUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Base URL for Mswipe API
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/mswipe/request-url"]]
      Type: String
      Value: !Ref MswipeRequestUrl

  MswipeVerificationUrlParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Verification Base URL for Mswipe API
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/mswipe/verification-url"]]
      Type: String
      Value: !Ref MswipeVerificationBaseUrl

  MswipeRequestClientCodeParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Client Code for Mswipe API
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/mswipe/request-client-code"]]
      Type: String
      Value: !Ref MswipeRequestClientCode

  MswipeVerificationClientCodeParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Verification Client Code for Mswipe API
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/mswipe/verification-client-code"]]
      Type: String
      Value: !Ref MswipeVerificationClientCode

  MswipeUserIDParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: User ID for Mswipe API
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/mswipe/user-id"]]
      Type: String
      Value: !Ref MswipeUserID

  MswipePasswordParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Password for Mswipe API
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/mswipe/password"]]
      Type: String
      Value: !Ref MswipePassword

  MswipeSaltParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Salt for Mswipe API
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/mswipe/salt"]]
      Type: String
      Value: !Ref MswipeSalt
  ReplacementBaseURLParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: Base URL for Replacement
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/replacement/url"]]
      Type: String
      Value: !Ref ReplacementBaseURL

  ReplacementAPIKeyParam:
    Type: AWS::SSM::Parameter
    Properties:
      DataType: text
      Description: API Key for Replacement
      Name: !Join ["", ["/", { Ref: AWS::StackName }, "/replacement/apiKey"]]
      Type: String
      Value: !Ref ReplacementAPIKey

  AwsSdkLambdaLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Join ["-", [{ Ref: AWS::StackName }, "aws-sdk"]]
      Description: Dependencies for lambda
      ContentUri: layers/aws
      CompatibleRuntimes:
        - nodejs16.x
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  ElasticSearchLambdaLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Join ["-", [{ Ref: AWS::StackName }, "elastic-search"]]
      Description: Dependencies for lambda
      ContentUri: layers/elastic-search
      CompatibleRuntimes:
        - nodejs16.x
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  LodashLambdaLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Join ["-", [{ Ref: AWS::StackName }, "lodash"]]
      Description: Dependencies for lambda
      ContentUri: layers/lodash
      CompatibleRuntimes:
        - nodejs16.x
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  NodeFetchLambdaLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Join ["-", [{ Ref: AWS::StackName }, "node-fetch"]]
      Description: Dependencies for lambda
      ContentUri: layers/node-fetch
      CompatibleRuntimes:
        - nodejs16.x
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  FirebaseLambdaLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Join ["-", [{ Ref: AWS::StackName }, "firebase-admin"]]
      Description: Dependencies for lambda
      ContentUri: layers/firebase-admin
      CompatibleRuntimes:
        - nodejs16.x
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  SlugifyLambdaLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Join ["-", [{ Ref: AWS::StackName }, "slugify"]]
      Description: Dependencies for lambda
      ContentUri: layers/slugify
      CompatibleRuntimes:
        - nodejs16.x
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  MomentLambdaLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Join ["-", [{ Ref: AWS::StackName }, "moment"]]
      Description: Dependencies for lambda
      ContentUri: layers/moment
      CompatibleRuntimes:
        - nodejs16.x
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  MiscellaneousLambdaLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Join ["-", [{ Ref: AWS::StackName }, "miscellaneous"]]
      Description: Dependencies for lambda
      ContentUri: layers/miscellaneous
      CompatibleRuntimes:
        - nodejs16.x
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  BToALambdaLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Join ["-", [{ Ref: AWS::StackName }, "btoa"]]
      Description: Dependencies for lambda
      ContentUri: layers/btoa
      CompatibleRuntimes:
        - nodejs16.x
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  RazorPayLambdaLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Join ["-", [{ Ref: AWS::StackName }, "razorpay"]]
      Description: Dependencies for lambda
      ContentUri: layers/razorpay
      CompatibleRuntimes:
        - nodejs16.x
        - nodejs18.x
        - nodejs20.x
      LicenseInfo: "MIT"
      RetentionPolicy: Retain
    UpdateReplacePolicy: Delete
    DeletionPolicy: Delete

  StreamHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/dynamodb-streams
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "stream-handler"]]
      Environment:
        Variables:
          OPEN_SEARCH_DOMAIN: !GetAtt OpensearchDomain.DomainEndpoint
          OPEN_SEARCH_USERNAME: !Ref OpenSearchUsername
          OPEN_SEARCH_PASSWORD: !Ref OpenSearchPassword
          STACK_NAME: !Sub ${AWS::StackName}
      Tracing: Disabled
      Timeout: 120
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref LodashLambdaLayer
        - !Ref NodeFetchLambdaLayer
        - !Ref SlugifyLambdaLayer
        - !Ref ElasticSearchLambdaLayer
      Events:
        StoreStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt StoreTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        ProductStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt ProductTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        EmployeeTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt EmployeeTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        QuotationTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt QuotationTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        OrderTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt OrderTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        OrderItemTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt OrderItemTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        CartTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt CartTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        CampaignCouponTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt CampaignCouponTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        FestiveOffersCouponTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt FestiveOffersCouponTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        PaymentTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt PaymentTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        InteriorArchitectureTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt InteriorArchitectureTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        CMSTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt CMSTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        InventoryTrackingTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt InventoryTrackingTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        STNTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt STNTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        PriceMasterTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt PriceMasterTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        NotificationsTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt NotificationsTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        IOTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt IOTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        GRNTableStream:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt GRNTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        ReplacementOrderTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt ReplacementOrderTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        CustomDiscountVerificationTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt CustomDiscountVerificationTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        OrdersLogTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt OrdersLogTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        CancelOrderTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt CancelOrderTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        RefundDetailTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt RefundDetailTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        MattressRecommendationTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt MattressRecommendationTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        CancelOrderTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt CancelOrderTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
        RefundDetailTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt RefundDetailTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - lambda:InvokeFunction
              Resource: !GetAtt SlackNotificationFunction.Arn


  SQSShipmentHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/sqs-shipment-handler
      Handler: index.handler
      Runtime: nodejs20.x
      Timeout: 120
      Layers:
        - !Ref NodeFetchLambdaLayer
      Events:
        OrderShipmentQueueEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt OrderShipmentQueue.Arn
      Environment:
        Variables:
          EASY_ECOM_BASE_URL: !Ref EasyEcomBaseUrl
          EASY_ECOM_EMAIL: !Ref EasyEcomEmail
          EASY_ECOM_PASSWORD: !Ref EasyEcomPassword

  IOHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/io-handler
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "io-handler"]]
      Timeout: 120
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref MomentLambdaLayer
        - !Ref NodeFetchLambdaLayer
      Environment:
        Variables:
          STACK_NAME: !Sub ${AWS::StackName}
          IO_TABLE: !Ref IOTable
          INVENTORY_TRACKING_TABLE: !Ref InventoryTrackingTable
          PRICE_MASTER_TABLE: !Ref PriceMasterTable
          ORDER_TABLE: !Ref OrderTable
          STN_TABLE: !Ref STNTable
          EASY_ECOM_EMAIL: !Ref EasyEcomEmail
          EASY_ECOM_PASSWORD: !Ref EasyEcomPassword
          EASY_ECOM_BASE_URL: !Ref EasyEcomBaseUrl
          INVENTORY_CREDENTIAL_TABLE: !Ref InventoryCredentialsTable
          STORE_TABLE: !Ref StoreTable
          ORDER_SHIPMENT_QUEUE_URL: !Ref OrderShipmentQueue
      Events:
        STNTableStream:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt STNTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 10
        GRNTableStream:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt GRNTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 10
        OrderTableStream:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt OrderTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 10
      Policies:
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:PutItem
                - dynamodb:GetItem
              Resource: !GetAtt InventoryTrackingTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:PutItem
                - dynamodb:UpdateItem
              Resource: !GetAtt IOTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
              Resource: !GetAtt OrderTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:BatchGetItem
              Resource: !GetAtt PriceMasterTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
              Resource: !GetAtt InventoryCredentialsTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
              Resource: !GetAtt STNTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
              Resource: !GetAtt StoreTable.Arn
            - Effect: Allow
              Action:
                - sqs:SendMessage
              Resource: !GetAtt OrderShipmentQueue.Arn
            - Effect: Allow
              Action:
                - lambda:InvokeFunction
              Resource: !GetAtt SlackNotificationFunction.Arn

  PaymentDetailsHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/payment-details
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "payment-details-handler"]]
      Timeout: 120
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref NodeFetchLambdaLayer
      Environment:
        Variables:
          PAYMENT_DETAILS_URL: !Ref PaymentDetailsURL
      Events:
        OrderTableStreams:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt OrderTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 100

  SlackNotificationFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/slack-notification
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "slack-notification"]]
      Timeout: 10
      Environment:
        Variables:
          SLACK_WEBHOOK_URL: !Ref SlackWebhookUrl
      Layers:
        - !Ref NodeFetchLambdaLayer    
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - "logs:CreateLogGroup"
                - "logs:CreateLogStream"
                - "logs:PutLogEvents"
              Resource: "*"


  ReturnPickupHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/return-pickup
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "return-pickup-handler"]]
      Timeout: 120
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref NodeFetchLambdaLayer
      Environment:
        Variables:
          INVENTORY_CREDENTIALS_TABLE: !Ref InventoryCredentialsTable
          ORDER_BY_AWB_NUMBER_INDEX: !Ref GetOrderByAWBNumberGSI
          EASY_ECOM_EMAIL: !Ref EasyEcomEmail
          EASY_ECOM_PASSWORD: !Ref EasyEcomPassword
          EASY_ECOM_BASE_URL: !Ref EasyEcomBaseUrl
          ORDER_TABLE_NAME: !Ref OrderTable
          EASY_ECOM_STORE_ID: !Ref ReplacementEasyEcomStoreId
          REGION: !Sub ${AWS::Region}
      Events:
        ApiEvent:
          Type: HttpApi
          Properties:
            Method: "POST"
            Path: "/return/pickup-confirmed"
      Policies:
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:*
              Resource: 
                - !GetAtt OrderTable.Arn
                - !Sub "${OrderTable.Arn}/index/${GetOrderByAWBNumberGSI}"
            - Effect: Allow
              Action:
                - dynamodb:GetItem
              Resource: !GetAtt InventoryCredentialsTable.Arn

  LeadsquaredHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/leadsquared
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "leadsquared-handler"]]
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref NodeFetchLambdaLayer
      Environment:
        Variables:
          ACCESS_KEY: !Ref LeadsquaredAccessKey
          SECRET_KEY: !Ref LeadsquaredSecretKey
          BE_BASE_API: !Ref BEBaseApi
          AUTH_TOKEN: !Ref  AuthToken
          MEDIA_BASE_API: !Ref MediaBaseApi
          REGION: !Sub ${AWS::Region}
          ORDER_TABLE: !Ref OrderTable
          QUOTATION_TABLE: !Ref QuotationTable
          INTERIOR_ARCHITECTURE_TABLE: !Ref InteriorArchitectureTable
          GLOBAL_CONFIGURATION_TABLE: !Ref GlobalConfigurationTable
          WE_LICENCE_CODE: !Ref WeLicenseCode
          WE_AUTH_TOKEN: !Ref WeAuthToken
      Timeout: 120
      Events:
        QuotationTableStream:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt QuotationTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 10
        OrderTableStream:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt OrderTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 10
        ApiEvent:
          Type: HttpApi
          Properties:
            Method: "POST"
            Path: "/leadsquare/create-lead"
      Policies:
        - Version: '2012-10-17' 
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:GetItem
              Resource: !GetAtt InteriorArchitectureTable.Arn
        - Version: '2012-10-17' 
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:GetItem
              Resource: !GetAtt GlobalConfigurationTable.Arn
      

  CMSHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/cms
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "cms-handler"]]
      Environment:
        Variables:
          ORDERS_TABLE: !Ref OrderTable
          PAYMENTS_TABLE: !Ref PaymentTable
          LOCAL_CONFIGURATION_TABLE: !Ref LocalConfigurationTable
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref MomentLambdaLayer
      Policies:
        - Version: '2012-10-17' 
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:GetItem
              Resource: !GetAtt PaymentTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:UpdateItem
              Resource: !GetAtt OrderTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:UpdateItem
              Resource: !GetAtt LocalConfigurationTable.Arn
      Timeout: 900
      Events:
        CMSTableStream:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt CMSTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 10

  NotificationHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/fetch-notification
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "fetch-notification"]]
      Environment:
        Variables:
          NOTIFICATIONS_TABLE: !Ref NotificationsTable
          FIREBASETOKENS_TABLE: !Ref FirebaseTokensTable
          REGION: !Sub ${AWS::Region}
          NOTIFICATION_QUEUE: !Ref NotificationQueue
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref MomentLambdaLayer
        - !Ref FirebaseLambdaLayer
      Policies:
        - Version: '2012-10-17' 
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:GetItem
                - dynamodb:Scan           
                - dynamodb:Query          
              Resource: !GetAtt NotificationsTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:UpdateItem
                - dynamodb:Scan           
                - dynamodb:Query          
              Resource: !GetAtt FirebaseTokensTable.Arn
            - Effect: Allow
              Action:
                - sqs:SendMessage  # Added permission to send message to SQS
              Resource: !GetAtt NotificationQueue.Arn  # Referencing the SQS queue ARN

      Timeout: 900
  
  NotificationTriggerRule:
    Type: AWS::Events::Rule
    Properties:
      ScheduleExpression: "rate(15 minutes)"
      State: "ENABLED"
      Targets:
        - Arn: !GetAtt NotificationHandler.Arn
          Id: "NotificationHandlerTarget"
  
  NotificationHandlerPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !GetAtt NotificationHandler.Arn
      Action: "lambda:InvokeFunction"
      Principal: "events.amazonaws.com"
      SourceArn: !GetAtt NotificationTriggerRule.Arn
  
  OrderStatusHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/orders
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "orderStatus-handler"]]
      Environment:
        Variables:
          ORDERS_TABLE: !Ref OrderTable
          PAYMENTS_TABLE: !Ref PaymentTable
          OPEN_SEARCH_DOMAIN: !GetAtt OpensearchDomain.DomainEndpoint
          OPEN_SEARCH_USERNAME: !Ref OpenSearchUsername
          OPEN_SEARCH_PASSWORD: !Ref OpenSearchPassword
          SHOPIFY_ADMIN_BASE_URL: !Ref ShopifyAdminBaseUrl
          SHOPIFY_ACCESS_TOKEN: !Ref ShopifyAccessToken
          ORDER_STATUS_QUEUE_URL: !Ref OrderStatusSyncQueue
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref MomentLambdaLayer
        - !Ref ElasticSearchLambdaLayer
        - !Ref NodeFetchLambdaLayer
      Policies:
        - Version: '2012-10-17' 
          Statement:
            - Effect: Allow
              Action:
                - sqs:SendMessage
              Resource: !GetAtt OrderStatusSyncQueue.Arn
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:GetItem
              Resource: !GetAtt PaymentTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:UpdateItem
              Resource: !GetAtt OrderTable.Arn
      Events:
        ApiEvent:
          Type: HttpApi
          Properties:
            Method: "POST"
            Path: "/sync-order-status"
        OrderStatusQueueEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt OrderStatusSyncQueue.Arn
      Timeout: 900

  OpensearchESLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName:
        !Join ["", ["/opensearch/", { Ref: AWS::StackName }, "-logs"]]
      RetentionInDays: 30

  OpensearchSearchSlowLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName:
        !Join [
          "",
          ["/opensearch/", { Ref: AWS::StackName }, "-slow-search-logs"],
        ]
      RetentionInDays: 7
      
  NotificationSenderHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/send-notifications
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "send-notifications"]]
      Environment:
        Variables:
          NOTIFICATIONS_TABLE: !Ref NotificationsTable
          FIREBASETOKENS_TABLE: !Ref FirebaseTokensTable
          REGION: !Sub ${AWS::Region}
          NOTIFICATION_QUEUE: !Ref NotificationQueue
          NOTIFICATIONS_LOGS_TABLE: !Ref NotificationLogsTable
          STORE_TABLE: !Ref StoreTable
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref MomentLambdaLayer
        - !Ref FirebaseLambdaLayer
      Policies:
        - Version: '2012-10-17' 
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:GetItem
              Resource: !GetAtt NotificationsTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:GetItem
                - dynamodb:Scan           
                - dynamodb:Query    
              Resource: !GetAtt FirebaseTokensTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:PutItem
                - dynamodb:GetItem
                - dynamodb:Scan           
                - dynamodb:Query    
              Resource: !GetAtt NotificationLogsTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:GetItem
                - dynamodb:Scan           
                - dynamodb:Query    
              Resource: !GetAtt StoreTable.Arn
            - Effect: Allow
              Action:
                - sqs:ReceiveMessage
                - sqs:DeleteMessage
                - sqs:GetQueueAttributes
              Resource: !GetAtt NotificationQueue.Arn
      Events:
        SQSTrigger:
          Type: SQS
          Properties:
            Queue: !GetAtt NotificationQueue.Arn
      Timeout: 900

  SendImmidiateNotificationHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/send-immidiate-notification
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "send-immidiate-notification"]]
      Environment:
        Variables:
          NOTIFICATIONS_TABLE: !Ref NotificationsTable
          FIREBASETOKENS_TABLE: !Ref FirebaseTokensTable
          REGION: !Sub ${AWS::Region}
          NOTIFICATION_QUEUE: !Ref NotificationQueue
          NOTIFICATIONS_LOGS_TABLE: !Ref NotificationLogsTable
          STORE_TABLE: !Ref StoreTable
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref MomentLambdaLayer
        - !Ref FirebaseLambdaLayer
      Policies:
        - Version: '2012-10-17' 
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:GetItem
              Resource: !GetAtt NotificationsTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:GetItem
                - dynamodb:Scan           
                - dynamodb:Query    
              Resource: !GetAtt FirebaseTokensTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:PutItem
                - dynamodb:GetItem
                - dynamodb:Scan           
                - dynamodb:Query    
              Resource: !GetAtt NotificationLogsTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
                - dynamodb:GetItem
                - dynamodb:Scan           
                - dynamodb:Query    
              Resource: !GetAtt StoreTable.Arn
            - Effect: Allow
              Action:
                - sqs:ReceiveMessage
                - sqs:DeleteMessage
                - sqs:GetQueueAttributes
                - sqs:SendMessage  # Added permission to send message to SQS
              Resource: !GetAtt NotificationQueue.Arn  
      Events:
        NotificationTableStream:
          Type: DynamoDB
          Properties:
            Stream: !GetAtt NotificationsTable.StreamArn
            StartingPosition: LATEST
            BatchSize: 10
      Timeout: 900

  OpensearchIndexSlowLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName:
        !Join [
          "",
          ["/opensearch/", { Ref: AWS::StackName }, "-slow-index-logs"],
        ]
      RetentionInDays: 7

  OpensearchAuditLogsLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName:
        !Join ["", ["/opensearch/", { Ref: AWS::StackName }, "-audit-logs"]]
      RetentionInDays: 30

  OpensearchLogPolicy:
    Type: AWS::Logs::ResourcePolicy
    Properties:
      PolicyName: !Join ["", [{ Ref: AWS::StackName }, "-policy"]]
      PolicyDocument: '{ "Version": "2012-10-17", "Statement": [ { "Sid": "open-search-logs", "Effect": "Allow", "Principal": { "Service": [ "es.amazonaws.com" ] }, "Action":["logs:PutLogEvents","logs:CreateLogStream"], "Resource": "*" } ] }'

  OpensearchKMSKey:
    Type: AWS::KMS::Key
    Properties:
      Description: KMS Key for opensearch encruption
      KeyPolicy:
        Version: "2012-10-17"
        Id: key-es-3
        Statement:
          - Effect: Allow
            Principal:
              Service: es.amazonaws.com
            Action: kms:CreateGrant
            Resource: "*"
          - Sid: Allow administration of the key
            Effect: Allow
            Principal:
              AWS: !Sub "arn:aws:iam::${AWS::AccountId}:user/Abhishek"
            Action: kms:*
            Resource: "*"
          - Sid: Enable IAM User Permissions
            Effect: Allow
            Principal:
              AWS: !Sub "arn:aws:iam::${AWS::AccountId}:root"
            Action: kms:*
            Resource: "*"

  OpensearchDomain:
    Type: AWS::OpenSearchService::Domain
    Properties:
      DomainName: !Join ["", [{ Ref: AWS::StackName }, "-es"]]
      ClusterConfig:
        DedicatedMasterEnabled: false
        InstanceCount: !Ref OpensearchInstanceCount
        InstanceType: !Ref OpensearchInstanceType
        MultiAZWithStandbyEnabled: false
        WarmEnabled: false
        ZoneAwarenessEnabled: !Ref OpensearchZoneAwarenessEnabled
      EBSOptions:
        EBSEnabled: true
        Iops: 3000
        VolumeSize: !Ref OpensearchInstanceVolume
        VolumeType: gp3
      AdvancedOptions:
        rest.action.multi.allow_explicit_index: "true"
      NodeToNodeEncryptionOptions:
        Enabled: true
      AdvancedSecurityOptions:
        Enabled: true
        InternalUserDatabaseEnabled: true
        MasterUserOptions:
          MasterUserName: !Ref OpenSearchUsername
          MasterUserPassword: !Ref OpenSearchPassword
      DomainEndpointOptions:
        EnforceHTTPS: true
      EncryptionAtRestOptions:
        Enabled: true
        KmsKeyId: !Ref OpensearchKMSKey
      LogPublishingOptions:
        ES_APPLICATION_LOGS:
          CloudWatchLogsLogGroupArn: !GetAtt OpensearchESLogGroup.Arn
          Enabled: true
        SEARCH_SLOW_LOGS:
          CloudWatchLogsLogGroupArn: !GetAtt OpensearchSearchSlowLogGroup.Arn
          Enabled: true
        INDEX_SLOW_LOGS:
          CloudWatchLogsLogGroupArn: !GetAtt OpensearchIndexSlowLogGroup.Arn
          Enabled: true
        AUDIT_LOGS:
          CloudWatchLogsLogGroupArn: !GetAtt OpensearchAuditLogsLogGroup.Arn
          Enabled: true

  DirectRazorPayCallBackHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/razor-pay
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "razorpay-callback"]]
      Environment:
        Variables:
          PAYMENTS_TABLE: !Ref PaymentTable
          ORDERS_TABLE: !Ref OrderTable
          RAZORPAY_API_KEY: !Ref RazorPayAPIKey
          RAZORPAY_SECRET: !Ref RazorPayAPISecret
          REDIRECT_URL: !Ref SuccessCallbackUrl
      Policies:
        - Version: '2012-10-17' 
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:UpdateItem
              Resource: !GetAtt PaymentTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:UpdateItem
              Resource: !GetAtt OrderTable.Arn
      Tracing: Disabled
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref MomentLambdaLayer
        - !Ref RazorPayLambdaLayer
        - !Ref BToALambdaLayer
      Events:
        ApiEvent:
          Type: HttpApi
          Properties:
            Method: "GET"
            Path: "/catch"

  MswipeCallBackHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/mswipe
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "mswipe-callback"]]
      Environment:
        Variables:
          PAYMENTS_TABLE: !Ref PaymentTable
          ORDERS_TABLE: !Ref OrderTable
      Policies:
        - Version: '2012-10-17' 
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:UpdateItem
              Resource: !GetAtt PaymentTable.Arn
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:UpdateItem
              Resource: !GetAtt OrderTable.Arn
      Tracing: Disabled
      Layers:
        - !Ref AwsSdkLambdaLayer
        - !Ref MomentLambdaLayer
      Events:
        ApiEvent:
          Type: HttpApi
          Properties:
            Method: "POST"
            Path: "/mswipe/catch"

  ThankYouEdgeHandler:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/thankyou-edge
      Handler: index.handler
      Runtime: nodejs20.x
      FunctionName: !Join ["-", [{ Ref: AWS::StackName }, "thankyou-edge-handler"]]
      Environment:
        Variables:
          REDIRECT_URL: !Ref SuccessCallbackUrl
      Tracing: Disabled
      Events:
        ApiEvent:
          Type: HttpApi
          Properties:
            Method: "POST"
            Path: "/thankyou-edge"

  PublicBucketCDNAccessControlOrigin:
    Type: AWS::CloudFront::OriginAccessControl
    Properties:
      OriginAccessControlConfig:
        Description: !Join ["-", [{ Ref: AWS::StackName }, "access-control-origin"]]
        Name: !Join ["-", [{ Ref: AWS::StackName }, "access-control-origin"]]
        OriginAccessControlOriginType: s3
        SigningBehavior: always
        SigningProtocol: sigv4

  PublicBucketCloudFront:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Comment: !Join ["-", [{ Ref: AWS::StackName }, "public-bucket-cdn"]]
        Enabled: true
        Origins:
          - Id: S3Origin
            DomainName: !GetAtt PublicBucket.RegionalDomainName
            OriginAccessControlId: !GetAtt PublicBucketCDNAccessControlOrigin.Id
            S3OriginConfig:
              OriginAccessIdentity: ""
        DefaultCacheBehavior:
          CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
          TargetOriginId: S3Origin
          ViewerProtocolPolicy: redirect-to-https
          Compress: true
          AllowedMethods:
            - GET
            - HEAD
          CachedMethods:
            - GET
            - HEAD

  PublicBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref PublicBucket
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Action:
              - "s3:GetObject"
              - "s3:PutObject"
            Effect: Allow
            Resource: !Join ["", ["arn:aws:s3:::", !Ref PublicBucket, "/*"]]
            Principal: "*"
          - Sid: AllowMediaCloudfront
            Effect: Allow
            Principal:
              Service: cloudfront.amazonaws.com
            Action: s3:GetObject
            Resource: !Join ["", ["arn:aws:s3:::", !Ref PublicBucket, "/*"]]
            Condition:
              StringEquals:
                AWS:SourceArn: !Join ["", [!Sub "arn:aws:cloudfront::${AWS::AccountId}:distribution/", !Ref PublicBucketCloudFront]]

  OrderShipmentQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${AWS::StackName}-order-shipment-queue
      VisibilityTimeout: 120
  
  OrderStatusSyncQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${AWS::StackName}-order-status-sync-queue
      VisibilityTimeout: 901

  ExportDataQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${AWS::StackName}-export-data-queue
      VisibilityTimeout: 901
      

  ExportDataProcessor:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambda/export-data/
      Handler: index.handler
      Runtime: nodejs18.x
      Timeout: 900
      MemorySize: 1024
      Layers: 
        - !Ref AwsSdkLambdaLayer
        - !Ref MomentLambdaLayer
        - !Ref MiscellaneousLambdaLayer
        - !Ref ElasticSearchLambdaLayer
        - !Ref NodeFetchLambdaLayer
      Events:
        SQSEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt ExportDataQueue.Arn
      Policies:
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - dynamodb:*
                - s3:*

              Resource: '*'

  ExportECSResources:
    Type: AWS::Serverless::Application
    Properties:
      Location: export-ecs-template.yml
      Parameters:
        SubnetIds: !Ref SubnetIds
        VpcId: vpc-0ab8cde1282f70a56

Outputs:
  StackName:
    Description: "Stack name based on the environment"
    Value: !Sub ${AWS::StackName}

  NotificationQueueUrlOutput:
    Description: "URL of the SQS Notification Queue"
    Value: !Ref NotificationQueue
    Export:
      Name: !Sub ${AWS::StackName}-notification-queue

  StoresTable:
    Description: "Name of the stores DynamoDB table"
    Value: !Ref StoreTable
  OrdersTable:
    Description: "Name of the order DynamoDB table"
    Value: !Ref OrderTable
  OrderItemsTable:
    Description: "Name of the order item DynamoDB table"
    Value: !Ref OrderItemTable
  GlobalConfigurationsTable:
    Description: "Name of the global config DynamoDB table"
    Value: !Ref GlobalConfigurationTable
  LocalConfigurationsTable:
    Description: "Name of the local config DynamoDB table"
    Value: !Ref LocalConfigurationTable
  InteriorArchitecturesTable:
    Description: "Name of the interior architecture DynamoDB table"
    Value: !Ref InteriorArchitectureTable
  HSNCodesTable:
    Description: "Name of the hsn codes DynamoDB table"
    Value: !Ref HSNCodeTable
  ProductsTable:
    Description: "Name of the products DynamoDB table"
    Value: !Ref ProductTable
  CollectionsTable:
    Description: "Name of the collections DynamoDB table"
    Value: !Ref CollectionTable
  CartsTable:
    Description: "Name of the carts DynamoDB table"
    Value: !Ref CartTable
  CampaignCouponsTable:
    Description: "Name of the campaign coupons DynamoDB table"
    Value: !Ref CampaignCouponTable
  PincodesTable:
    Description: "Name of the pin-codes DynamoDB table"
    Value: !Ref PincodeTable
  PriceMastersTable:
    Description: "Name of the price master DynamoDB table"
    Value: !Ref PriceMasterTable
  ReplacementOrdersTable:
    Description: "Name of the replacement order DynamoDB table"
    Value: !Ref ReplacementOrderTable
  RefundDetailsTable:
    Description: "Name of the refund details DynamoDB table"
    Value: !Ref RefundDetailTable
  CMSsTable:
    Description: "Name of the CMS DynamoDB table"
    Value: !Ref CMSTable
  InventoryTrackingsTable:
    Description: "Name of the inventory tracking DynamoDB table"
    Value: !Ref InventoryTrackingTable
  UserPool:
    Description: "Name of the User pool"
    Value: !Ref CognitoUserPool
  CognitoUserPoolClient:
    Description: "Name of the web client"
    Value: !Ref WebUsersClient
  FEwebSiteBucket:
    Description: "FE hosting bucket name"
    Value: !Ref webSiteBucket
  FEThankYouWebSiteBucket:
    Description: "FE Thank you bucket name"
    Value: !Ref ThankYouBucket
  OffersWebsiteBucket:
    Description: "FE Offers bucket name"
    Value: !Ref OffersBucket
  StreamHandlerLambda:
    Description: "Name of the stream handler lambda name"
    Value: !Ref StreamHandler
  LeadsquaredHandlerLambda:
    Description: "Name of the leadsquared handler lambda name"
    Value: !Ref LeadsquaredHandler
  PublicBucketsName:
    Description: "Name of the public bucket name"
    Value: !Ref PublicBucket
  NotificationsTable:
    Description: "Name of the notifications DynamoDB table"
    Value: !Ref NotificationsTable
  FirebaseTokensTable:
    Description: "Name of the firebase tokens DynamoDB table"
    Value: !Ref FirebaseTokensTable
  NotificationLogsTable:
    Description: "Name of the notification logs DynamoDB table"
    Value: !Ref NotificationLogsTable
  FestiveOffersCouponsTable:
    Description: "Name of the campaign coupons DynamoDB table"
    Value: !Ref FestiveOffersCouponTable
  # FEWebsiteURL:
  #   Description: "FE application address"
  #   Value: !Join ["", ['http://', !Ref webSiteBucket, '.s3-website.', !Ref AWS::Region, '.amazonaws.com']]