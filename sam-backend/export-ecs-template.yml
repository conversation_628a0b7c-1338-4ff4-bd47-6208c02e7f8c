AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: SAM template for Export Processor Server Setup

Parameters:
  SubnetIds:
    Type: String
  VpcId:
    Type: String

Resources:
  # ECR Repository
  ExportServerECR:
    Type: AWS::ECR::Repository
    Properties:
      RepositoryName: 'export-processor'

  # ECS Cluster
  ExportServerCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub ${AWS::StackName}-export-cluster

  # Security Group for ECS Tasks
  ECSTaskSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for ECS export tasks
      VpcId: !Ref VpcId
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
        - IpProtocol: udp
          FromPort: 53
          ToPort: 53
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: 587
          ToPort: 587
          CidrIp: 0.0.0.0/0

  # Task Execution Role
  TaskExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${AWS::StackName}-task-execution-role
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy

  # Task Role
  TaskRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${AWS::StackName}-task-role
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: ExportProcessorPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 'logs:*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'dynamodb:*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 's3:*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'ses:*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'ssm:*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'ecs:RunTask'
                  - 'ecs:DescribeTasks'
                  - 'ecs:StopTask'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'iam:PassRole'
                Resource: '*'

  # Single Task Definition (handles both cron and triggered execution)
  ExportProcessorTask:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub ${AWS::StackName}-processor
      RequiresCompatibilities:
        - FARGATE
      NetworkMode: awsvpc
      Cpu: '1024'
      Memory: '3072'
      ExecutionRoleArn: !GetAtt TaskExecutionRole.Arn
      TaskRoleArn: !GetAtt TaskRole.Arn
      ContainerDefinitions:
        - Name: export-processor
          Image: !Sub ${AWS::AccountId}.dkr.ecr.${AWS::Region}.amazonaws.com/export-processor:latest
          Essential: true
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref LogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: processor
          Environment:
            - Name: AWS_REGION
              Value: !Ref AWS::Region
            - Name: S3_BUCKET
              Value: 'pos-prod-public-bucket'
            - Name: OPEN_SEARCH_DOMAIN
              Value: 'search-pos-prod-es-kej2yfeachy4owakc4x6vgv7iy.ap-south-1.es.amazonaws.com'
            - Name: REQUEST_MODULE_API_URL
              Value: 'https://apis.thesleepcompany.in/order-management/request-return-replacement'
            - Name: ROHIT_API_URL
              Value: 'https://gybqro5fdj.execute-api.ap-south-1.amazonaws.com/prod/orderdetail'
            - Name: FROM_EMAIL
              Value: '<EMAIL>'
            - Name: SMTP_USER
              Value: '<EMAIL>'

  # CloudWatch Log Group
  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${AWS::StackName}-processor
      RetentionInDays: 30

  # EventBridge Rule for Daily 5 PM execution
  ProcessorScheduleRule:
    Type: AWS::Events::Rule
    Properties:
      Name: !Sub ${AWS::StackName}-processor-schedule
      Description: Run Export processor daily at 5 PM
      ScheduleExpression: 'cron(0 17 * * ? *)'
      State: ENABLED
      Targets:
        - Id: ExportProcessorTask
          Arn: !GetAtt ExportServerCluster.Arn
          RoleArn: !GetAtt EventBridgeRole.Arn
          EcsParameters:
            TaskDefinitionArn: !Ref ExportProcessorTask
            TaskCount: 1
            LaunchType: FARGATE
            NetworkConfiguration:
              AwsVpcConfiguration:
                AssignPublicIp: ENABLED
                Subnets: !Split [",", !Ref SubnetIds]
              
          Input: |
            {
              "executionMode": "SCHEDULED"
            }

  # EventBridge Role
  EventBridgeRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${AWS::StackName}-eventbridge-role
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: events.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: ECSInvocationPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action: 'ecs:RunTask'
                Resource: !Ref ExportProcessorTask
              - Effect: Allow
                Action: 'iam:PassRole'
                Resource:
                  - !GetAtt TaskRole.Arn
                  - !GetAtt TaskExecutionRole.Arn
