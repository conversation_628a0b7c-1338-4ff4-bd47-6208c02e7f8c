sam build && sam deploy --profile tsc-pos --config-env stage --parameter-overrides 'ParameterKey=OpenSearchUsername,ParameterValue=admin' 'ParameterKey=OpenSearchPassword,ParameterValue=Admin@1234' 'ParameterKey=ShopifyAccessToken,ParameterValue=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' 'ParameterKey=ShopifyAdminBaseUrl,ParameterValue=https://tsc-dev.myshopify.com/admin/api/2024-01' 'ParameterKey=WishlistAccessToken,ParameterValue=Jl3739KUDTqQutdueiueaLjDuobervO1Tt66Raqb' 'ParameterKey=WishlistAPIUrl,ParameterValue=https://n503a0isif.execute-api.ap-south-1.amazonaws.com/develop/wishlist' 'ParameterKey=RazorPayAppKey,ParameterValue=6bd3deb6-06ef-47c8-91ef-1415c2677bde' 'ParameterKey=RazorPayAppUsername,ParameterValue=2405202300' 'ParameterKey=RazorPayAppUrl,ParameterValue=https://demo.ezetap.com/api/3.0/p2padapter' 'ParameterKey=RazorPayAPIKey,ParameterValue=rzp_test_8iwlOxQrqn9dAC' 'ParameterKey=RazorPayAPISecret,ParameterValue=aVkPB5ok21odZauSYmSE9UBj' 'ParameterKey=OpensearchInstanceType,ParameterValue=t3.medium.search' 'ParameterKey=OpensearchInstanceVolume,ParameterValue=30' 'ParameterKey=OpensearchInstanceCount,ParameterValue=1' 'ParameterKey=OpensearchZoneAwarenessEnabled,ParameterValue=false' 'ParameterKey=SuccessCallbackUrl,ParameterValue=https://success-stage.thesleepcompany.in' 'ParameterKey=SnapmintAppUrl,ParameterValue=https://sandboxapi.snapmint.com'  'ParameterKey=SnapmintAppRequestUrl,ParameterValue=https://staging-super-apis.snapmint.com' 'ParameterKey=SnapmintFailureUrl,ParameterValue=https://thesleepcompany.in/' 'ParameterKey=LeadsquaredAccessKey,ParameterValue=u%24r3f8f6b578c93f2d348094733191527e8' 'ParameterKey=LeadsquaredSecretKey,ParameterValue=524000eef0227ce5479ef4383d98e07ed283923d' 'ParameterKey=PineLabsAppUrl,ParameterValue=https://www.plutuscloudserviceuat.in:8201/API/CloudBasedIntegration/V1' 'ParameterKey=PineLabsSecurityToken,ParameterValue=a4c9741b-2889-47b8-be2f-ba42081a246e' 'ParameterKey=PineLabsMerchantId,ParameterValue=29610' 'ParameterKey=PineLabsStoreId,ParameterValue=1221258' 'ParameterKey=PayUTokenEndpoint,ParameterValue=https://accounts.payu.in/oauth/token' 'ParameterKey=PayUUpdationLink,ParameterValue=https://oneapi.payu.in/payment-links' 'ParameterKey=PayUVerifyPayment,ParameterValue=https://info.payu.in/merchant/postservice?form=2' 'ParameterKey=PayUMerchantId,ParameterValue=160565' 'ParameterKey=PayUClientId,ParameterValue=9c82c52a0278489e8220fae36ea3435591d1418ecc9d1252b0b1051afe1d7057' 'ParameterKey=PayUClientSecret,ParameterValue=978871752e39fa9518650de4224a2d9ad164ee9e520191c8f8411f38ce0e68c3' 'ParameterKey=PayUKey,ParameterValue=z6VP03' 'ParameterKey=PayUSalt,ParameterValue=EWRcq4f4' 'ParameterKey=BEBaseApi,ParameterValue=https://pos-stage-api.thesleepcompany.in' 'ParameterKey=MediaBaseApi,ParameterValue=https://d3q9eswgjwnupj.cloudfront.net' 'ParameterKey=MswipeRequestUrl,ParameterValue=https://ap.Mswipeota.com/JF' 'ParameterKey=MswipeVerificationBaseUrl,ParameterValue=https://www.mswipetech.com/verificationapi/api/VerificationApi/MswipeCardSaleVerificationApi' 'ParameterKey=MswipeRequestClientCode,ParameterValue=9401166288' 'ParameterKey=MswipeVerificationClientCode,ParameterValue=COMFORTGRIDTECHNOLOGIESPVTLTD' 'ParameterKey=MswipeUserID,ParameterValue=9401166288@SOL' 'ParameterKey=MswipePassword,ParameterValue=9401166288~Ds@240701' 'ParameterKey=MswipeSalt,ParameterValue=p055UX9MDSNO9Db7KeJ8APHE8qmrGz1H' 'ParameterKey=EasyEcomEmail,ParameterValue=<EMAIL>'  'ParameterKey=EasyEcomPrimaryLocationKey,ParameterValue=ee16685663929' 'ParameterKey=EasyEcomPassword,ParameterValue=Nayan@123' 'ParameterKey=EasyEcomBaseUrl,ParameterValue=https://api.easyecom.io' 'ParameterKey=FreshDeskApiUrl,ParameterValue=https://thesleepcompanycare.freshdesk.com' 'ParameterKey=FreshDeskApiKey,ParameterValue=********************' 'ParameterKey=FreshDeskPassword,ParameterValue=X' 'ParameterKey=FreshDeskGroupId,ParameterValue=84000291010' 'ParameterKey=SlackWebhookUrl,ParameterValue=*********************************************************************************' 'ParameterKey=ReplacementBaseURL,ParameterValue=https://15vz409ax0.execute-api.ap-south-1.amazonaws.com/dev' 'ParameterKey=ReplacementAPIKey,ParameterValue=rmK6iPQK2G0xc1UTHUHhiDpZ7RYADu3yOD5hUAf0' 'ParameterKey=ApiGatewayUrl,ParameterValue=https://pos-stage-event.thesleepcompany.in' 'ParameterKey=AuthToken,ParameterValue=57631294-erwl-7437-wtxz-0a5c7500e0df' 'ParameterKey=PaymentDetailsURL,ParameterValue=https://apis-stage.thesleepcompany.in' 'ParameterKey=OTPBaseURL,ParameterValue=https://d3cu187wy0.execute-api.ap-south-1.amazonaws.com/dev' 'ParameterKey=RequestModuleAPIUrl,ParameterValue=https://apis-stage.thesleepcompany.in/order-management/request-return-replacement' 'ParameterKey=RequestModuleAPIKey,ParameterValue=REQUESTVN9b3C3c7kTVLp94LNhKmsMH5' 
