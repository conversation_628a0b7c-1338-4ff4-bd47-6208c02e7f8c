import { unmarshall } from '@aws-sdk/util-dynamodb';
import fetch from 'node-fetch';
export const handler = async (event) => {
  console.log('event', JSON.stringify(event));

  try {
    for (const record of event.Records) {
      const {
        dynamodb: { NewImage = {}, OldImage = {} },
        eventSourceARN,
        eventName,
      } = record;

      const newItem = unmarshall(NewImage);
      const oldItem = unmarshall(OldImage);

      const {
        status: newStatus,
        shopifyOrderId: orderId,
        shopifyOrderName: orderNameId,
        id: invoiceNo,
      } = newItem;

      const { status: oldStatus } = oldItem;

      if (
        eventName === 'MODIFY' &&
        newStatus === 'SENT_TO_SHOPIFY' &&
        oldStatus !== 'SENT_TO_SHOPIFY'
      ) {
        const {
          transactions: { transactionDetails = [] },
        } = newItem;
        for (const transaction of transactionDetails) {
          const {
            transactionId,
            mode,
            amountPaid,
            transactionAmount,
            paymentMethod,
            splitPayment,
            status,
            phone,
            posId,
          } = transaction;
          const transactionPayload = {
            order_id: orderId,
            order_name_id: orderNameId,
            payment_id: transactionId,
            invoice_no: invoiceNo,
            channel: 'POS',
            payment_mode: mode,
            order_status: newStatus,
            payment_status: status,
            split_payment: splitPayment,
            payment_method: paymentMethod,
            phone,
            posId: posId || '',
            collection_amount:
              amountPaid?.toString() || transactionAmount?.toString(),
            collection_discount: calculateDiscount(
              transactionAmount,
              amountPaid || transactionAmount,
            )?.toString(),
          };
          console.log('transactionPayload', transactionPayload);
          await sendWebhook(transactionPayload);
        }
      }
    }

    return { statusCode: 200, body: 'Webhook triggered successfully.' };
  } catch (error) {
    console.error('Error processing event:', error);
    return { statusCode: 500, body: 'Failed to process the event.' };
  }
};

const calculateDiscount = (totalAmount, transactionAmount) => {
  if (!totalAmount || !transactionAmount) return 0;
  return parseFloat(totalAmount) - parseFloat(transactionAmount);
};

const sendWebhook = async (payload) => {
  const PAYMENT_DETAILS_URL = process.env.PAYMENT_DETAILS_URL;
  console.log('PAYMENT_DETAILS_URL', PAYMENT_DETAILS_URL);
  const url = `${PAYMENT_DETAILS_URL}/order-management/order/webhook/payment-details`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(JSON.stringify(payload)),
      },
      body: JSON.stringify(payload),
    });

    const data = await response.text();
    console.log('Webhook response:', data);
    if (!response.ok) {
      throw new Error(`Webhook failed with status: ${response.status}`);
    }
    return data;
  } catch (error) {
    console.error('Error sending webhook:', error);
    throw error;
  }
};
