const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const {
  DynamoDBDocumentClient,
  UpdateCommand,
  GetCommand,
} = require('@aws-sdk/lib-dynamodb');
const moment = require('moment/moment');

const client = new DynamoDBClient({
  region: process.env.REGION,
});
const docClient = DynamoDBDocumentClient.from(client);

const updateTransactionStatus = async ({
  orderId,
  transactionId,
  rrNumber,
  paymentID,
  amountPaid,
}) => {
  console.log('Updating Transaction status for -> ', orderId, transactionId);
  const itemUpdateCommand = new UpdateCommand({
    TableName: process.env.PAYMENTS_TABLE,
    Key: {
      orderId,
      transactionId,
    },
    UpdateExpression:
      'SET #status = :status, updatedAt = :updatedAt, paymentID = :paymentID, rrNumber = :rrNumber, amountPaid = :amountPaid',
    ExpressionAttributeNames: {
      '#status': 'status',
    },
    ExpressionAttributeValues: {
      ':status': 'COMPLETED',
      ':updatedAt': moment().toISOString(),
      ':paymentID': `${paymentID}`,
      ':rrNumber': `${rrNumber}`,
      ':amountPaid': Number(amountPaid),
    },
    ConditionExpression:
      'attribute_exists(orderId) AND attribute_exists(transactionId)',
    ReturnValues: 'ALL_NEW',
  });

  console.log('Update payment query before execution', itemUpdateCommand);
  const { Attributes } = await docClient.send(itemUpdateCommand);
  if (!Attributes || !Object.keys(Attributes).length) {
    console.error('ERROR: Failed to update transaction status');
    return {
      status: false,
      msg: 'Failed to update transaction status',
    };
  }

  console.log('Transaction entry updated', Attributes);
  return {
    status: true,
    msg: 'Transaction record updated!',
  };
};

module.exports.handler = async (event) => {
  console.log('Incoming event', JSON.stringify(event));

  const { body = '' } = event;
  if (!body) {
    console.error('ERROR: Body not available', body);
    return {
      status: false,
      msg: 'Update not completed due to empty body.',
    };
  }

  let parsedBody = null;

  try {
    parsedBody = JSON.parse(body);
  } catch (error) {
    console.error('ERROR: Body cannot be parsed');
    return {
      status: false,
      msg: 'Update not completed due to dody cannot be parsed.',
    };
  }

  const {
    TranAmount,
    TRAN_STATUS,
    InvNo,
    EX_NOTES1: orderId,
    EX_NOTES2: transactionId,
    RRN,
  } = parsedBody;

  if (!orderId || !transactionId) {
    console.error('ERROR: One of the query parameter is not present', {
      orderId,
      transactionId,
    });
    return {
      status: false,
      msg: 'Update not completed due to one of the missing query string param.',
    };
  }

  if (TRAN_STATUS !== 'approved') {
    console.error('ERROR: TRAN_STATUS is not approved', {
      TRAN_STATUS,
    });
    return {
      status: false,
      msg: 'Update not completed due to one of the TRAN_STATUS not in approved state.',
    };
  }

  if (TRAN_STATUS === 'approved') {
    await updateTransactionStatus({
      orderId,
      transactionId,
      amountPaid: TranAmount,
      paymentID: InvNo,
      rrNumber: RRN,
    });
  }
};
