import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';

const IO_TABLE = process.env.IO_TABLE;

//tag: INWARD, OUTWARD
//sourceType: GRN, STN, ORDER

export const updateIO = async (
  docClient,
  {
    storeId,
    id,
    quantity,
    tag,
    sourceId,
    sourceType,
    finalQuantity,
    inventoryFrom,
    inventoryTo,
  },
) => {
  console.log('updateIO', {
    docClient,
    storeId,
    id,
    quantity,
    tag,
    sourceId,
    sourceType,
    finalQuantity,
    inventoryFrom,
    inventoryTo,
  });
  return await docClient.send(
    new UpdateCommand({
      TableName: IO_TABLE,
      Key: {
        storeId,
        id: `${id}#${sourceId}`,
      },
      UpdateExpression:
        'SET updatedAt = :updatedAt, quantity = :quantity, sku = :sku, tag = :tag, sourceType = :sourceType, sourceId = :sourceId, finalQuantity = :finalQuantity, inventoryFrom = :inventoryFrom, inventoryTo = :inventoryTo',
      ExpressionAttributeValues: {
        ':quantity': quantity,
        ':sku': id,
        ':tag': tag,
        ':sourceType': sourceType,
        ':sourceId': sourceId,
        ':updatedAt': moment().toISOString(),
        ':finalQuantity': finalQuantity,
        ':inventoryFrom': inventoryFrom,
        ':inventoryTo': inventoryTo,
      },
    }),
  );
};
