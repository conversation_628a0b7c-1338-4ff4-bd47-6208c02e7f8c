import { getInventory, updateInventory } from './inventory.js';
import { updateIO } from './io.js';
import { priceMasterData } from './price-master.js';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';

const client = new DynamoDBClient({
  region: process.env.REGION,
});
const docClient = DynamoDBDocumentClient.from(client);

export const stn = async ({ newItem, oldItem, event }) => {
  console.log('stn', newItem, event);
  const { progressStatus: oldProgressStatus } = oldItem;

  const {
    status,
    requestMode,
    products,
    id: sourceId,
    storeId: _storeId,
    transferMode,
    progressStatus: newProgressStatus,
    requestedStoreId = null,
    sourceWarehouseMappingId,
  } = newItem;
  if (
    ((event === 'INSERT' || event === 'MODIFY') &&
      status === 'CREATED' &&
      requestMode === 'REVERSE' &&
      transferMode == 'WAREHOUSE') ||
    (event === 'MODIFY' &&
      status === 'CREATED' &&
      requestMode === 'FORWARD' &&
      transferMode == 'STORE' &&
      newProgressStatus === 'ACCEPTED' &&
      oldProgressStatus === 'PENDING')
  ) {
    console.log('products', products);
    try {
      let inventoryFrom, inventoryTo;
      if (requestMode === 'REVERSE' && transferMode === 'WAREHOUSE') {
        inventoryFrom = _storeId;
        inventoryTo = sourceWarehouseMappingId;
      }
      if (requestMode === 'FORWARD' && transferMode === 'STORE') {
        inventoryFrom = requestedStoreId;
        inventoryTo = _storeId;
      }
      await Promise.allSettled(
        products.map(async (product) => {
          const storeId =
            transferMode === 'WAREHOUSE' ? _storeId : requestedStoreId;
          const { sku: id, quantity: productQuantity } = product;

          const [priceMaster] = await Promise.all([
            await priceMasterData(docClient, id),
          ]);

          const {
            parentSku,
            multiplier,
            title,
            shopifyTitle = null,
          } = priceMaster;
          const [inventory] = await Promise.all([
            await getInventory(docClient, { storeId, id: parentSku }),
          ]);
          console.log('priceMaster, inventory', priceMaster, inventory);

          const { quantity = 0 } = inventory || {};

          console.log({ parentSku, multiplier }, { quantity });

          if (parentSku && multiplier) {
            const finalQuantity =
              (quantity || 0) - productQuantity * multiplier;
            console.log('finalQuantity', finalQuantity);
            try {
              await updateIO(docClient, {
                storeId,
                id: parentSku,
                quantity: productQuantity * multiplier,
                finalQuantity,
                sourceId,
                sourceType: 'STN',
                tag: 'OUTWARD',
                inventoryFrom,
                inventoryTo,
              });

              await updateInventory(docClient, {
                storeId,
                id: parentSku,
                quantity: finalQuantity,
                title: shopifyTitle || title,
              });
            } catch (err) {
              console.error('ERROR :>> product', id, err);
            }
          }
        }),
      );
    } catch (err) {
      console.error('ERROR :>>', err);
    }
  }
};
