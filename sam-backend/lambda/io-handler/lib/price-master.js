import { GetCommand, BatchGetCommand } from '@aws-sdk/lib-dynamodb';

const PRICE_MASTER_TABLE = process.env.PRICE_MASTER_TABLE;

export const priceMasterData = async (docClient, id) => {
  console.log('PRICE_MASTER_TABLE', PRICE_MASTER_TABLE);

  console.log('priceMasterData', id);

  const result = await docClient.send(
    new GetCommand({
      TableName: PRICE_MASTER_TABLE,
      Key: {
        id,
      },
    }),
  );

  console.log('result', result);
  return result.Item;
};

export const listpriceMasterByIds = async (docClient, ids) => {
  console.log('PRICE_MASTER_TABLE', PRICE_MASTER_TABLE);

  console.log('priceMasterData', ids);

  const batchGetItemsCommand = new BatchGetCommand({
    RequestItems: {
      [PRICE_MASTER_TABLE]: {
        Keys: ids.map((id) => ({
          id,
        })),
      },
    },
  });

  const result = await docClient.send(batchGetItemsCommand);

  const res = result.Responses?.[PRICE_MASTER_TABLE] ?? [];

  console.log('result', res);
  return res;
};
