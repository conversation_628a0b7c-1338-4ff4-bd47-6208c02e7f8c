import { getInventory, updateInventory, getStn } from './inventory.js';
import { updateIO } from './io.js';
import { priceMasterData } from './price-master.js';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';

const client = new DynamoDBClient({
  region: process.env.REGION,
});
const docClient = DynamoDBDocumentClient.from(client);

export const grn = async ({ item, event }) => {
  const { products, grnId: sourceId, stnId } = item;
  if (event === 'INSERT') {
    try {
      const {
        storeId,
        transferMode,
        sourceWarehouseMappingId,
        requestedStoreId,
        requestMode,
      } = await getStn(docClient, stnId);

      let inventoryFrom, inventoryTo;
      if (requestMode === 'FORWARD') {
        if (transferMode === 'WAREHOUSE') {
          inventoryFrom = sourceWarehouseMappingId;
          inventoryTo = storeId;
        } else {
          inventoryFrom = requestedStoreId;
          inventoryTo = storeId;
        }
      }

      await Promise.allSettled(
        products.map(async (product) => {
          const { sku: id, receivedQuantity: productQuantity } = product;

          const [priceMaster] = await Promise.all([
            await priceMasterData(docClient, id),
          ]);

          const {
            parentSku,
            multiplier,
            title,
            shopifyTitle = null,
          } = priceMaster;

          const [destinationInventory] = await Promise.all([
            await getInventory(docClient, { storeId, id: parentSku }),
          ]);
          console.log(
            'priceMaster, destinationInventory',
            priceMaster,
            destinationInventory,
          );
          if (parentSku && multiplier) {
            const { quantity = 0 } = destinationInventory || {};
            const finalQuantity = quantity + productQuantity * multiplier;
            try {
              await updateIO(docClient, {
                storeId,
                id: parentSku,
                quantity: productQuantity * multiplier,
                finalQuantity,
                sourceId,
                sourceType: 'GRN',
                tag: 'INWARD',
                inventoryFrom,
                inventoryTo,
              });

              await updateInventory(docClient, {
                storeId,
                id: parentSku,
                quantity: finalQuantity,
                title: shopifyTitle || title,
              });
            } catch (err) {
              console.error('ERROR :>> product', id, err);
            }
          }
        }),
      );
    } catch (err) {
      console.error('ERROR :>>', err);
    }
  }
};
