import { GetCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';

const INVENTORY_TRACKING_TABLE = process.env.INVENTORY_TRACKING_TABLE;
const STN_TABLE = process.env.STN_TABLE;
const INVENTORY_CREDENTIAL_TABLE = process.env.INVENTORY_CREDENTIAL_TABLE;

export const getInventory = async (docClient, { storeId, id }) => {
  console.log('getInventory', docClient, { storeId, id });
  return (
    await docClient.send(
      new GetCommand({
        TableName: INVENTORY_TRACKING_TABLE,
        Key: {
          storeId,
          id,
        },
      }),
    )
  ).Item;
};

export const getStn = async (docClient, id) => {
  console.log('getStn', docClient, { id });
  return (
    await docClient.send(
      new GetCommand({
        TableName: STN_TABLE,
        Key: {
          id,
        },
      }),
    )
  ).Item;
};
export const getInventoryCredential = async (docClient, id) => {
  console.log('getInventoryCredential', INVENTORY_CREDENTIAL_TABLE, { id });
  return (
    await docClient.send(
      new GetCommand({
        TableName: INVENTORY_CREDENTIAL_TABLE,
        Key: {
          id,
        },
      }),
    )
  ).Item;
};

export const updateInventory = async (
  docClient,
  { storeId, id, quantity, title },
) => {
  return await docClient.send(
    new UpdateCommand({
      TableName: INVENTORY_TRACKING_TABLE,
      Key: {
        storeId,
        id,
      },
      UpdateExpression:
        'SET updatedAt = :updatedAt, quantity = :quantity, title = :title',
      ExpressionAttributeValues: {
        ':title': title,
        ':quantity': quantity,
        ':updatedAt': moment().toISOString(),
      },
    }),
  );
};
