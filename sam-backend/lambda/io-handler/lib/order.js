import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { listpriceMasterByIds, priceMasterData } from './price-master.js';
import {
  getInventory,
  updateInventory,
  getInventoryCredential,
} from './inventory.js';
import { createOrder } from './create-order-ee.js';
import { updateIO } from './io.js';
import moment from 'moment';
import { getStore } from './store.js';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda';
const lambdaClient = new LambdaClient({ region: process.env.REGION });

const sendSlackNotification = async (message) => {
  try {
    const payload = {
      message,
      channel: `${process.env.STACK_NAME}-io-handler`,
    };

    const command = new InvokeCommand({
      FunctionName: `${process.env.STACK_NAME}-slack-notification`,
      Payload: Buffer.from(JSON.stringify(payload)),
    });

    const response = await lambdaClient.send(command);
    console.log('Slack Notification Response:', response);
  } catch (err) {
    console.error('Failed to send Slack notification:', err);
  }
};

const sqsClient = new SQSClient({ region: process.env.REGION });
const client = new DynamoDBClient({
  region: process.env.REGION,
});
const docClient = DynamoDBDocumentClient.from(client);

export const order = async ({ newItem, oldItem, event }) => {
  const {
    status: newStatus,
    shopifyOrderId,
    shopifyOrderName,
    deliveryStatus,
    orderProducts,
    storeId,
    id: sourceId,
    customer: { phone },
    orderType,
    isCreatedOnEE = false,
  } = newItem;
  console.log('orderID:', sourceId);
  if (!isCreatedOnEE) return;
  if (orderType === 'REPLACEMENT' || orderType === 'RETURN') return;

  const { status: oldStatus } = oldItem || {};
  if (
    event === 'MODIFY' &&
    oldStatus !== 'SENT_TO_SHOPIFY' &&
    newStatus === 'SENT_TO_SHOPIFY' &&
    deliveryStatus === 'CASH_AND_CARRY'
  ) {
    try {
      console.log(
        '1.getting store and proceeding with IO',
        storeId,
        sourceId,
        shopifyOrderName,
      );
      const { locationKey, state: ecomState } = await getInventoryCredential(
        docClient,
        storeId,
      );

      console.log('2.Inv creds', locationKey, ecomState);

      const store = await getStore(docClient, { storeId });
      const {
        address: shippingAddress,
        billingInfo: { address: billingAddress },
      } = store;
      console.log('gotstore', store);
      let eePayload = {
        orderDate: moment().toISOString(),
        orderNumber: shopifyOrderName + '_' + sourceId,
        orderType: 'retailorder',
        remarks1: 'Cash and Carry',
        customer: [
          {
            billing: {
              addressLine1: billingAddress.line1,
              addressLine2: billingAddress?.line2,
              postalCode: billingAddress.pinCode,
              city: billingAddress.city,
              state: ecomState,
              country: billingAddress.country,
            },
            shipping: {
              addressLine1: shippingAddress.line1,
              addressLine2: shippingAddress?.line2,
              postalCode: shippingAddress.pinCode,
              city: shippingAddress.city,
              state: ecomState,
              country: shippingAddress.country,
            },
          },
        ],
      };
      const ids = orderProducts.map((product) => product.sku);
      const parentSkusData = await listpriceMasterByIds(docClient, ids);
      console.log('parentSkusData', parentSkusData);

      const processOrderProducts = async (
        orderProducts,
        parentSkusData,
        shopifyOrderName,
        storeId,
      ) => {
        const parentskuWithExistingQuantity = [];

        for (const item of orderProducts) {
          const parentSkuData = parentSkusData.find(
            (data) => data.id === item.sku,
          );
          console.log('parentSkuData', parentSkuData);

          if (parentSkuData) {
            const sku = parentSkuData.parentSku;
            const existingItem = parentskuWithExistingQuantity.find(
              (accItem) => accItem.sku === sku,
            );

            if (existingItem) {
              existingItem.quantity +=
                Number(parentSkuData.multiplier) * Number(item.quantity);
              existingItem.finalItemPrice += Number(item.finalItemPrice);
            } else {
              parentskuWithExistingQuantity.push({
                ...item,
                sku,
                quantity:
                  Number(parentSkuData.multiplier) * Number(item.quantity),
                finalItemPrice: Number(item.finalItemPrice),
              });
            }
          } else {
            await sendSlackNotification(
              `${process.env.STACK_NAME} - Order ${shopifyOrderName} processing Error. StoreID: ${storeId} - SKU:${item.sku} : No priceMaster data found.`,
            );
            console.error('Error: No priceMaster data found for', item.sku);
          }
        }

        return parentskuWithExistingQuantity;
      };

      const parentskuWithExistingQuantity = await processOrderProducts(
        orderProducts,
        parentSkusData,
        shopifyOrderName,
        storeId,
      );

      let eeItems = [];

      await Promise.allSettled(
        parentskuWithExistingQuantity.map(async (product) => {
          const {
            sku: id,
            quantity: productQuantity,
            title,
            variantTitle,
            price,
            finalItemPrice,
          } = product;
          console.log(
            'Product data',
            id,
            productQuantity,
            title,
            variantTitle,
            price,
            finalItemPrice,
          );

          const [inventory] = await Promise.all([
            await getInventory(docClient, { storeId, id }),
          ]);
          console.log('priceMaster, inventory', inventory);

          const { quantity = 0 } = inventory || {};
          eeItems = [
            ...eeItems,
            {
              OrderItemId: shopifyOrderName + '_' + id,
              Price: Number((finalItemPrice / productQuantity).toFixed(2)),
              productName: title + ' ' + variantTitle,
              Quantity: productQuantity,
              Sku: id + '_EASY',
            },
          ];

          const finalQuantity = quantity - productQuantity;
          try {
            await updateIO(docClient, {
              storeId,
              id,
              quantity: productQuantity,
              finalQuantity,
              sourceId,
              inventoryFrom: storeId,
              inventoryTo: phone,
              sourceType: 'ORDER',
              tag: 'OUTWARD',
            });

            await updateInventory(docClient, {
              storeId,
              id,
              quantity: finalQuantity,
              title: title + ' ' + variantTitle,
            });
          } catch (err) {
            console.error('ERROR :>> product', id, err);
          }
        }),
      );
      eePayload = { ...eePayload, items: eeItems };
      const orderData = await createOrder(eePayload, locationKey);
      console.log('🚩🚩', orderData, eeItems);
      const sendMessageParams = {
        QueueUrl: process.env.ORDER_SHIPMENT_QUEUE_URL,
        MessageBody: JSON.stringify({
          sourceId: shopifyOrderName + '_' + sourceId,
          locationKey,
        }),
        DelaySeconds: 10,
      };
      const command = new SendMessageCommand(sendMessageParams);
      await sqsClient.send(command);
      await sendSlackNotification(
        `${process.env.STACK_NAME} - Order ${shopifyOrderName} processed successfully. StoreID: ${storeId}`,
      );

      console.log('🚩🚩🚩SQS message sent for markShipped🚩🚩');
    } catch (err) {
      await sendSlackNotification(
        `${process.env.STACK_NAME} - Order ${shopifyOrderName} processing Error. StoreID: ${storeId} :${err.message}`,
      );
      console.error('ERROR :>>', err);
    }
  }
};
