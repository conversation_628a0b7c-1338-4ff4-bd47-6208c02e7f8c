import fetch from 'node-fetch';

const BASE_URL = process.env.EASY_ECOM_BASE_URL;
const EMAIL = process.env.EASY_ECOM_EMAIL;
const PASSWORD = process.env.EASY_ECOM_PASSWORD;

// Function to fetch token
async function fetchToken(locationKey) {
  const response = await fetch(`${BASE_URL}/access/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: EMAIL,
      password: PASSWORD,
      location_key: locationKey,
    }),
  });

  const data = await response.json();
  if (response.ok) {
    return data.data.token.jwt_token;
  } else {
    throw new Error(data.message || 'Failed to fetch token');
  }
}

export async function createOrder(orderData, locationKey) {
  console.log('Creating order', orderData, locationKey);
  const token = await fetchToken(locationKey);

  const response = await fetch(`${BASE_URL}/webhook/v2/createOrder`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`, // Attach token in headers
    },
    body: JSON.stringify(orderData),
  });

  const responseData = await response.json();
  const { data, message } = responseData;
  console.log('🔥success', data);
  if (data.Status === 200) {
    return data;
  } else {
    throw new Error(message || 'Failed to create order');
  }
}
export async function markShipped(orderNumber, locationKey) {
  const token = await fetchToken(locationKey);

  const response = await fetch(
    `${BASE_URL}/orders/ship?orderNumber=${orderNumber}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`, // Attach token in headers
      },
    },
  );

  const data = await response.json();
  console.log('🔥success', data);

  if (response.ok) {
    return data;
  } else {
    throw new Error(data.message || 'Failed to mark order as shipped');
  }
}
