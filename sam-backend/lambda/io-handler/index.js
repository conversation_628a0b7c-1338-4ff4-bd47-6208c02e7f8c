import { unmarshall } from '@aws-sdk/util-dynamodb';
import { order } from './lib/order.js';
import { stn } from './lib/stn.js';
import { grn } from './lib/grn.js';

const ORDER_TABLE = `${process.env.STACK_NAME}-order-table`;
const STN_TABLE = `${process.env.STACK_NAME}-stn-table`;
const GRN_TABLE = `${process.env.STACK_NAME}-grn-table`;

export const handler = async (event) => {
  try {
    console.log('event,JSON.stringify(event) :>> ', JSON.stringify(event));
    if (event.Records && Array.isArray(event.Records)) {
      await Promise.all(
        event.Records?.map(async (record) => {
          const {
            dynamodb: { NewImage = {}, OldImage = {} },
            eventSourceARN,
            eventName,
          } = record;

          const newItem = unmarshall(NewImage);
          const oldItem = unmarshall(OldImage);

          console.log('newItem', newItem);

          const tableStartIndex = eventSourceARN.indexOf(':table/') + 7;
          const tableLastIndex = eventSourceARN.lastIndexOf('/stream');
          const tableIndex = eventSourceARN
            .substring(tableStartIndex, tableLastIndex)
            .toLowerCase();

          if (tableIndex) {
            switch (tableIndex) {
              case ORDER_TABLE:
                console.log(ORDER_TABLE);
                await order({ newItem, oldItem, event: eventName });
                break;
              case STN_TABLE:
                console.log(STN_TABLE);
                await stn({ newItem, oldItem, event: eventName });
                break;
              case GRN_TABLE:
                console.log(GRN_TABLE);
                await grn({ item: newItem, event: eventName });
                break;
            }
          }
        }),
      );
    }
  } catch (e) {
    console.log('error', e);
    return;
  } finally {
    return;
  }
};
