const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient } = require('@aws-sdk/lib-dynamodb');
const moment = require('moment');
const { unmarshall } = require('@aws-sdk/util-dynamodb');

const sqsClient = new SQSClient({ region: process.env.REGION });
const client = new DynamoDBClient({ region: process.env.REGION });
const docClient = DynamoDBDocumentClient.from(client);
const NOTIFICATIONS_TABLE = process.env.NOTIFICATIONS_TABLE;
const FIREBASE_TABLE = process.env.FIREBASETOKENS_TABLE;
const STORES_TABLE = process.env.STORE_TABLE;
const NOTIFICATIONS_LOGS_TABLE = process.env.NOTIFICATIONS_LOGS_TABLE;

exports.handler = async (event) => {
  console.log('event', event);
  // Handle SQS messages
  if (event.Records) {
    for (const record of event.Records) {
      const {
        dynamodb: { NewImage = {} },
        eventName,
      } = record;

      const data = unmarshall(NewImage);
      console.log('data', data);
      if (data?.sendImmediately && eventName === 'INSERT') {
        const sendMessageParams = {
          QueueUrl: process.env.NOTIFICATION_QUEUE,
          MessageBody: JSON.stringify({ id: data?.id }),
          DelaySeconds: 0,
        };
        const command = new SendMessageCommand(sendMessageParams);
        const responseSQS = await sqsClient.send(command);
        console.log('responseSQS', responseSQS);
      }
    }

    return {
      statusCode: 400,
      body: 'Missing event records',
    };
  }
};
