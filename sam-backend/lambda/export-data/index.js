import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  GetCommand,
  QueryCommand,
} from '@aws-sdk/lib-dynamodb';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { Client } from '@opensearch-project/opensearch';
import moment from 'moment';
import nodemailer from 'nodemailer';
import { json2csv } from 'json-2-csv';

// Initialize AWS clients
const dynamoClient = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(dynamoClient);
const s3Client = new S3Client({});

// Initialize OpenSearch client
const opensearchClient = new Client({
  node: `https://${process.env.OPEN_SEARCH_DOMAIN || 'search-pos-prod-es-kej2yfeachy4owakc4x6vgv7iy.ap-south-1.es.amazonaws.com'} `,
  auth: {
    username: process.env.OPEN_SEARCH_USERNAME || 'admin',
    password: process.env.OPEN_SEARCH_PASSWORD || 'Admin@1234',
  },
});

// Initialize nodemailer
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.SMTP_USER || '<EMAIL>',
    pass: process.env.SMTP_PASS || 'iodk nwfq hmir eese',
  },
});

// Constants
const MAX_ATTACHMENT_SIZE_MB = 25;
const STAGE = process.env.STAGE || 'prod';
const PAYMENT_TABLE = `pos-${STAGE}-payment-table`;
const ORDER_TABLE = `pos-${STAGE}-order-table`;
const REFUND_DETAIL_TABLE = `pos-${STAGE}-refund-table`;
const PRODUCT_TABLE = `pos-${STAGE}-product-table`;
const REQUEST_MODULE_API_URL =
  process.env.REQUEST_MODULE_API_URL ||
  'https://apis.thesleepcompany.in/order-management/request-return-replacement';
const REQUEST_MODULE_API_KEY =
  process.env.REQUEST_MODULE_API_KEY || 'REQUESTVN9b3C3c7kTVLp94LNhKmsMH5';
const ROHIT_API_URL =
  process.env.ROHIT_API_URL ||
  'https://gybqro5fdj.execute-api.ap-south-1.amazonaws.com/prod/orderdetail';
const ROHIT_API_KEY =
  process.env.ROHIT_API_KEY || 'PpuHE5OWtR3xYmZTZWRBwaa3ogMvAumX8kWc4Alm';
const S3_BUCKET = process.env.S3_BUCKET || 'pos-prod-public-bucket ';

// Product cache to store already fetched products
const productCache = new Map();

// Interfaces
const PaymentStatus = {
  COMPLETED: 'COMPLETED',
  CREATED: 'CREATED',
};

// Utility functions

// Updated date formatting to DD-MM-YYYY HH:MM:SS format
const formatDate = (dateString) => {
  if (!dateString) return '';
  try {
    return moment(dateString).format('DD-MM-YYYY HH:mm:ss');
  } catch (error) {
    return dateString;
  }
};

// Helper function to calculate days between two dates
const calculateDaysBetween = (startDate, endDate) => {
  console.log(`[DEBUG] calculateDaysBetween - Input:`, { startDate, endDate });

  if (!startDate || !endDate) {
    console.log(`[DEBUG] calculateDaysBetween - Missing date(s), returning 0`);
    return 0;
  }

  try {
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      console.log(`[DEBUG] calculateDaysBetween - Invalid date(s):`, {
        start,
        end,
      });
      return 0;
    }

    const diffTime = Math.abs(end.getTime() - start.getTime());
    const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    console.log(`[DEBUG] calculateDaysBetween - Result: ${days} days`);
    return days;
  } catch (error) {
    console.error(`[DEBUG] calculateDaysBetween - Error:`, error);
    return 0;
  }
};

// Update the getHumanReadableDateRange function to accept a base date:

const getHumanReadableDateRange = (startDays, endDays, baseDate = null) => {
  const base = baseDate ? moment(baseDate) : moment();
  const startDate = base.clone().add(startDays, 'days').format('DD MMM YYYY');
  const endDate = base.clone().add(endDays, 'days').format('DD MMM YYYY');
  return `${startDate} to ${endDate}`;
};
// Helper function to get pickup details from return/replacement orders
const getPickupDetails = (orderData, requestId) => {
  console.log(`[DEBUG] getPickupDetails - Looking for requestId: ${requestId}`);
  console.log(
    `[DEBUG] getPickupDetails - orderData keys:`,
    orderData,
    requestId,
  );

  // Check return_orders first
  if (orderData.return_orders && orderData.return_orders.length > 0) {
    console.log(
      `[DEBUG] getPickupDetails - Found ${orderData.return_orders.length} return orders`,
    );
    const returnOrder = orderData.return_orders.find(
      (order) => order.request_id === requestId,
    );
    if (returnOrder) {
      console.log(`[DEBUG] getPickupDetails - Matched return order:`, {
        awb_number: returnOrder.awb_number,
        initial_request_date: returnOrder.initial_request_date,
        refund_date_and_time: returnOrder.refund_date_and_time,
      });
      return {
        awbNumber: returnOrder.awb_number || '',
        currentStatus: returnOrder.status_udpate || '',
        pickupInitiatedDate: returnOrder.outforpickup_request_date || '',
        pickupDate: returnOrder.pickedup_request_date || '',
        refundDate: returnOrder.refund_date_and_time || '',
        bookingDate: returnOrder.initial_request_date || '',
        replacementCompletedDate:
          returnOrder.replacement_completed_date_and_time || '',
      };
    }
  }

  // Check replacement_orders
  if (orderData.replacement_orders && orderData.replacement_orders.length > 0) {
    console.log(
      `[DEBUG] getPickupDetails - Found ${orderData.replacement_orders.length} replacement orders`,
    );
    const replacementOrder = orderData.replacement_orders.find(
      (order) => order.request_id === requestId,
    );
    if (replacementOrder) {
      console.log(`[DEBUG] getPickupDetails - Matched replacement order:`, {
        awb_number: replacementOrder.awb_number,
        initial_request_date: replacementOrder.initial_request_date,
        replacement_completed_date_and_time:
          replacementOrder.replacement_completed_date_and_time,
      });
      return {
        awbNumber: replacementOrder.awb_number || '',
        currentStatus: replacementOrder.status_udpate || '',
        pickupInitiatedDate: replacementOrder.outforpickup_request_date || '',
        pickupDate: replacementOrder.pickedup_request_date || '',
        refundDate: replacementOrder.refund_date_and_time || '',
        bookingDate: replacementOrder.initial_request_date || '',
        replacementCompletedDate:
          replacementOrder.replacement_completed_date_and_time || '',
      };
    }
  }

  console.log(
    `[DEBUG] getPickupDetails - No matching order found for requestId: ${requestId}`,
  );
  return {
    awbNumber: '',
    currentStatus: '',
    pickupInitiatedDate: '',
    pickupDate: '',
    refundDate: '',
    bookingDate: '',
    replacementCompletedDate: '',
  };
};

const getPosRequestDetail = (orderData, requestId) => {
  console.log('orderData', orderData, requestId);
  if (orderData.pos_request_detail && orderData.pos_request_detail.length > 0) {
    console.log(
      '[DEBUG] getPosRequestDetail - Found pos_request_detail',
      orderData.pos_request_detail,
    );
    return orderData.pos_request_detail.find(
      (detail) => detail.request_id === requestId,
    );
  }
  return null;
};

// Helper function to check if pickup was regenerated
const checkRegeneratedPickup = (orderData, replacementOrder) => {
  let isRegenerated = false;
  let regenerateDate = '';
  let regenerateAwb = '';

  // Check if status indicates regeneration
  if (replacementOrder.status === 'RETURN_REINITIALIZED') {
    isRegenerated = true;
    regenerateDate = replacementOrder.updatedAt || '';
  }

  // Check for cancel_awb_history in return_orders or replacement_orders
  const checkAwbHistory = (orders) => {
    if (orders && orders.length > 0) {
      for (const order of orders) {
        if (order.cancel_awb_history && order.cancel_awb_history.length > 0) {
          isRegenerated = true;
          const latestCancel =
            order.cancel_awb_history[order.cancel_awb_history.length - 1];
          regenerateDate = latestCancel.date_and_time || '';
          regenerateAwb = latestCancel.cancelled_awb || '';
          break;
        }
      }
    }
  };

  if (orderData.return_orders) {
    checkAwbHistory(orderData.return_orders);
  }

  if (!isRegenerated && orderData.replacement_orders) {
    checkAwbHistory(orderData.replacement_orders);
  }

  return {
    isRegenerated,
    regenerateDate: formatDate(regenerateDate),
    regenerateAwb,
  };
};

// Helper function to get delivery date from order data
const getDeliveryDate = (orderData) => {
  if (orderData?.easyecom?.delivered_date) {
    return orderData.easyecom.delivered_date;
  }

  // Fallback to clickpost_history if available
  if (orderData?.easyecom?.clickpost_history) {
    const deliveredHistory = orderData.easyecom.clickpost_history.find(
      (h) => h.clickpost_status_description === 'Delivered',
    );
    if (deliveredHistory) {
      return deliveredHistory.creation_date_and_time;
    }
  }

  return null;
};

// Function to get product details from DynamoDB
const getProductFromDynamoDB = async (productId, variantId) => {
  const cacheKey = `${productId}#${variantId}`;

  // Check if already cached
  if (productCache.has(cacheKey)) {
    return productCache.get(cacheKey);
  }

  try {
    const getCommand = new GetCommand({
      TableName: PRODUCT_TABLE,
      Key: {
        pk: 'VARIANT',
        sk: cacheKey,
      },
    });

    const { Item } = await docClient.send(getCommand);

    // Cache the result
    if (Item) {
      productCache.set(cacheKey, Item);
    }

    return Item;
  } catch (error) {
    console.error('Error getting product from DynamoDB:', error);
    return null;
  }
};

// Function to get order details from Rohit's API
const getOrderDetailsFromRohitAPI = async (
  orderId,
  orderType = 'New Order',
) => {
  try {
    const url = `${ROHIT_API_URL}?order_id=${orderId}&order_type=${encodeURIComponent(orderType)}`;
    console.log('url', url, ROHIT_API_KEY);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': ROHIT_API_KEY,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get order details: ${response.statusText}`);
    }

    const result = await response.json();
    return result.orderDetails?.[0] || null;
  } catch (error) {
    console.error('Error getting order details from Rohit API:', error);
    return null;
  }
};

// Function to determine order status based on order data
const determineOrderStatus = (orderData) => {
  if (!orderData?.productsData?.[0]?.easyecom) {
    return 'Placed';
  }

  const easyecomData = orderData.productsData[0].easyecom;

  // Check if delivered
  if (easyecomData.delivered_date) {
    return 'Delivered';
  }

  // Check if out for delivery
  if (easyecomData.outfordelivery || easyecomData.outfordelivery_date) {
    return 'Out for Delivery';
  }

  // Check if manifested (processing)
  if (easyecomData.manifest_date) {
    return 'Processing';
  }

  return 'Placed';
};

// Function to get cancelled orders from OpenSearch
const getAllCancelledOrdersFromOS = async (filters = {}) => {
  const searchArray = [];

  // Add date range filter for cancelled_at
  if (filters.startDate || filters.endDate) {
    const dateRange = {
      range: {
        cancelled_at: {},
      },
    };

    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      startDate.setHours(0, 0, 0, 0);
      dateRange.range.cancelled_at.gte = startDate.toISOString();
    }

    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      endDate.setHours(23, 59, 59, 999);
      dateRange.range.cancelled_at.lte = endDate.toISOString();
    }

    searchArray.push(dateRange);
  }

  const query =
    searchArray.length > 0
      ? {
          bool: {
            must: [...searchArray],
          },
        }
      : {
          match_all: {},
        };

  console.log('Cancellation OpenSearch Query:', JSON.stringify(query, null, 2));

  const response = await opensearchClient.search({
    index: 'pos-prod-cancel-table',
    body: {
      from: 0,
      size: 10000,
      query: query,
    },
  });

  return response.body.hits.hits.map((hit) => hit._source);
};

// Function to get cancellation request details
const getCancellationRequestDetails = async (orderId) => {
  try {
    const response = await fetch(
      `${REQUEST_MODULE_API_URL}/order/${orderId}?requestType=CANCELLATION`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': REQUEST_MODULE_API_KEY,
        },
      },
    );

    if (!response.ok) {
      throw new Error(
        `Failed to get cancellation request details: ${response.statusText}`,
      );
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error getting cancellation request details:', error);
    return null;
  }
};

// Service functions
const getAllReplacementOrdersFromOS = async (filters = {}) => {
  const searchArray = [
    {
      terms: {
        'status.keyword': [
          'ORDER_CREATED',
          'SENT_TO_EE',
          'RETURN_CANCELLED',
          'RETURN_REINITIALIZED',
        ],
      },
    },
  ];

  // Add order type filter (REPLACEMENT/RETURN)
  if (filters.requestType) {
    if (filters.requestType === 'REPLACEMENT') {
      searchArray.push({
        term: {
          'orderType.keyword': 'REPLACEMENT',
        },
      });
    } else if (filters.requestType === 'RETURN') {
      searchArray.push({
        term: {
          'orderType.keyword': 'RETURN',
        },
      });
    }
  }

  // Add replacement type filter for PART replacements
  if (filters.replacementType === 'PART') {
    searchArray.push({
      term: {
        'replacementType.keyword': 'PART',
      },
    });
  }

  // Add date range filter
  if (filters.startDate || filters.endDate) {
    const dateRange = {
      range: {
        createdAt: {},
      },
    };

    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      startDate.setHours(0, 0, 0, 0);
      dateRange.range.createdAt.gte = startDate.toISOString();
    }

    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      endDate.setHours(23, 59, 59, 999);
      dateRange.range.createdAt.lte = endDate.toISOString();
    }

    searchArray.push(dateRange);
  }

  const query = {
    bool: {
      must: [...searchArray],
    },
  };

  console.log('OpenSearch Query:', JSON.stringify(query, null, 2));

  const response = await opensearchClient.search({
    index: 'pos-prod-replacement-table',
    body: {
      from: 0,
      size: 10000,
      query: query,
    },
  });

  return response.body.hits.hits.map((hit) => hit._source);
};

const getRequestDetails = async (replacementOrder) => {
  try {
    if (!replacementOrder.requestId) return null;

    const response = await fetch(
      `${REQUEST_MODULE_API_URL}/${replacementOrder.requestId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': REQUEST_MODULE_API_KEY,
        },
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to get request details: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error getting request details:', error);
    return null;
  }
};

const getRefundDetails = async (replacementOrder) => {
  try {
    const refundDetailCommand = new GetCommand({
      TableName: REFUND_DETAIL_TABLE,
      Key: {
        shopifyOrderId: replacementOrder.shopifyOrderId,
        id: replacementOrder.id,
      },
    });

    const { Item: data } = await docClient.send(refundDetailCommand);
    return data;
  } catch (error) {
    console.error('Error getting refund details:', error);
    return null;
  }
};

const getAllPaymentsByOrderId = async (orderId) => {
  try {
    const orderQuery = new GetCommand({
      TableName: ORDER_TABLE,
      Key: {
        id: orderId,
      },
    });

    const { Item } = await docClient.send(orderQuery);
    if (!Item || !Object.keys(Item).length) {
      throw new Error(`Order not found with this ID ${orderId}!`);
    }

    const { finalDiscountedAmount: totalOrderAmount } = Item;

    const queryCommand = new QueryCommand({
      TableName: PAYMENT_TABLE,
      KeyConditionExpression: 'orderId = :orderId',
      ExpressionAttributeValues: {
        ':orderId': orderId,
      },
    });

    const result = await docClient.send(queryCommand);

    if (result && result.Items && result.Items.length) {
      let Items = result.Items.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );

      const totalAmount = Items.reduce((acc, curr) => {
        if (curr.status === PaymentStatus.COMPLETED) {
          acc = acc + curr.transactionAmount;
        }
        return acc;
      }, 0);

      const requestedAmount = Items.reduce((acc, curr) => {
        if (curr.status === PaymentStatus.CREATED) {
          acc = acc + curr.transactionAmount;
        }
        return acc;
      }, 0);

      return {
        transactionDetails: Items,
        totalPaidAmount: totalAmount,
        requestedAmount: requestedAmount,
        remainingAmount: totalOrderAmount - totalAmount,
      };
    } else {
      return {
        transactionDetails: [],
        totalPaidAmount: 0,
        requestedAmount: 0,
        remainingAmount: totalOrderAmount,
      };
    }
  } catch (error) {
    console.error('Error getting payment details:', error);
    return null;
  }
};

const calculateAdditionalPayment = async (replacementOrder) => {
  try {
    const queryCommand = new QueryCommand({
      TableName: PAYMENT_TABLE,
      KeyConditionExpression: 'orderId = :orderId',
      FilterExpression: '#status = :status',
      ExpressionAttributeNames: {
        '#status': 'status',
      },
      ExpressionAttributeValues: {
        ':orderId': replacementOrder.id,
        ':status': PaymentStatus.COMPLETED,
      },
    });

    const result = await docClient.send(queryCommand);

    if (result && result.Items && result.Items.length) {
      const item = result.Items[0];
      return {
        amountPaid: item.amountPaid || 0,
        transactionId: item.transactionId || '',
        transactionAmount: item.transactionAmount || 0,
        status: item.status || '',
      };
    }

    return {
      amountPaid: 0,
      transactionId: '',
      transactionAmount: 0,
      status: '',
    };
  } catch (error) {
    console.error('Error calculating additional payment:', error);
    return {
      amountPaid: 0,
      transactionId: '',
      transactionAmount: 0,
      status: '',
    };
  }
};

const determineRefundOrPayment = (replacementOrder) => {
  const finalAmount = replacementOrder.finalDiscountedAmount || 0;

  if (replacementOrder.refundedPrice > 0) {
    return 'Refund';
  }
  if (finalAmount > 0) {
    return 'Additional Payment';
  }
  return 'No Change';
};

const getRefundMode = (refundData, replacementOrder) => {
  if (refundData?.refundMode) {
    return refundData.refundMode;
  }

  if (refundData?.isBackToSource) {
    return 'Back to Source';
  }

  const originalPaymentMode = replacementOrder.originalPaymentMode?.[0];
  if (originalPaymentMode) {
    return originalPaymentMode;
  }

  return '';
};

// Updated createCSVRow to include all missing fields and date calculations
const createCSVRow = async (
  replacementOrder,
  requestData,
  refundData,
  paymentData,
) => {
  const oldProduct = replacementOrder.replacedProduct || {};
  const newProduct = replacementOrder.orderProducts?.[0] || {};
  let posRequestDetail = null;
  let productData = null;
  let deliveryDate = null;
  let orderDetailsFromRohit = null;
  let pickupDetails = {
    awbNumber: '',
    currentStatus: '',
    pickupInitiatedDate: '',
    pickupDate: '',
    refundDate: '',
    bookingDate: '',
    replacementCompletedDate: '',
  };
  // Get old product details from DynamoDB
  let oldProductDetails = null;
  if (oldProduct.productId && oldProduct.variantId) {
    oldProductDetails = await getProductFromDynamoDB(
      oldProduct.productId,
      oldProduct.variantId,
    );
  }

  // Get new product details from DynamoDB
  let newProductDetails = null;
  if (newProduct.productId && newProduct.variantId) {
    newProductDetails = await getProductFromDynamoDB(
      newProduct.productId,
      newProduct.variantId,
    );
  }

  // Get replacement order status from Rohit's API
  let replacementOrderStatus = replacementOrder.status || '';
  let orderDetails = null;
  if (
    replacementOrder.eeRefNo
    // replacementOrder.orderType === 'REPLACEMENT'
  ) {
    orderDetails = await getOrderDetailsFromRohitAPI(replacementOrder.eeRefNo);
    if (orderDetails) {
      replacementOrderStatus = determineOrderStatus(orderDetails);
    }
  }

  // Get pickup details and dates from Rohit's API response for original order
  if (replacementOrder.shopifyOrderId) {
    console.log(
      `Fetching order details from Rohit's API for orderId: ${replacementOrder.shopifyOrderId}`,
    );
    orderDetailsFromRohit = await getOrderDetailsFromRohitAPI(
      replacementOrder.shopifyOrderId,
    );

    if (orderDetailsFromRohit?.productsData?.length) {
      console.log(
        `[DEBUG] All available request IDs in API response:`,
        orderDetailsFromRohit.productsData.map((product, index) => ({
          productIndex: index,
          requestIds:
            product.pos_request_detail?.map((d) => d.request_id) || [],
          hasReturnOrders: !!product.return_orders?.length,
          hasReplacementOrders: !!product.replacement_orders?.length,
        })),
      );
      console.log(
        `[DEBUG] Looking for requestId: ${replacementOrder.requestId}`,
      );

      console.log(
        `[DEBUG] createCSVRow - Processing ${orderDetailsFromRohit.productsData.length} products for requestId: ${replacementOrder.requestId}`,
      );

      productData = orderDetailsFromRohit.productsData.find(
        (product) =>
          product.pos_request_detail &&
          product.pos_request_detail.some(
            (detail) => detail.request_id === replacementOrder.requestId,
          ),
      );

      if (productData) {
        console.log(
          `[DEBUG] createCSVRow - Found matching product data for requestId: ${replacementOrder.requestId}`,
        );
        posRequestDetail = getPosRequestDetail(
          productData,
          replacementOrder.requestId,
        );
        pickupDetails = getPickupDetails(
          productData,
          replacementOrder.requestId,
        );
        deliveryDate = getDeliveryDate(productData);

        console.log(`[DEBUG] createCSVRow - Extracted data:`, {
          posRequestDetail: !!posRequestDetail,
          pickupDetails,
          deliveryDate,
        });
      } else {
        console.log(
          `[DEBUG] createCSVRow - No matching product found for requestId: ${replacementOrder.requestId}`,
        );
      }
    }
  }

  // Check for regenerated pickup
  // Check for regenerated pickup using the correct productData
  const regeneratedPickup = productData
    ? checkRegeneratedPickup(productData, replacementOrder)
    : { isRegenerated: false, regenerateDate: '', regenerateAwb: '' };

  // Calculate various date differences
  // Get the correct dates for calculations with debugging
  // Get the correct dates for calculations with debugging
  const requestDate =
    posRequestDetail?.pos_request_date || requestData?.createdAt;
  const bookingDate = pickupDetails.bookingDate; // From getPickupDetails
  const deliveryDate2 = deliveryDate; // Use the deliveryDate we already extracted from productData

  console.log(
    `[DEBUG] createCSVRow - Date extraction for order ${replacementOrder.shopifyOrderId}:`,
    {
      requestDate,
      bookingDate,
      deliveryDate2,
      pickupDate: pickupDetails.pickupDate,
      refundDate: pickupDetails.refundDate,
      replacementCompletedDate: pickupDetails.replacementCompletedDate,
    },
  );

  // Corrected date calculations:
  const daysFromRequestToReturn = calculateDaysBetween(
    requestDate,
    bookingDate,
  ); // pos_request_date to initial_request_date
  const daysFromRequestToRefund = calculateDaysBetween(
    requestDate,
    refundData.status === 'COMPLETED' ? refundData.updatedAt : '',
  ); // pos_request_date to refund_date_and_time
  const deliveryToRequestDays = calculateDaysBetween(
    deliveryDate2,
    requestDate,
  ); // delivered_date to pos_request_date
  const deliveryToBookingDays = calculateDaysBetween(
    deliveryDate2,
    bookingDate,
  ); // delivered_date to initial_request_date
  const requestToBookingDays = calculateDaysBetween(requestDate, bookingDate); // pos_request_date to initial_request_date (same as daysFromRequestToReturn)
  const requestToNewOrderDeliveryDays = calculateDaysBetween(
    requestDate,
    pickupDetails.replacementCompletedDate,
  ); // pos_request_date to replacement_completed_date_and_time
  const requestToRefundDays = calculateDaysBetween(
    requestDate,
    refundData.status === 'COMPLETED' ? refundData.updatedAt : '',
  ); // pos_request_date to refund_date_and_time (same as daysFromRequestToRefund)
  const requestToPickupDays = calculateDaysBetween(
    requestDate,
    pickupDetails.pickupDate,
  ); // pos_request_date to pickedup_request_date

  // Extract approver name from customCode
  const approverName =
    replacementOrder.customCode?.approver ||
    replacementOrder.approverName ||
    '';

  const refundOrAdditionalPayment = determineRefundOrPayment(replacementOrder);
  const {
    amountPaid,
    transactionAmount,
    transactionId,
    status: paymentStatus,
  } = await calculateAdditionalPayment(replacementOrder);

  return {
    // Order details
    orderId: replacementOrder.shopifyOrderId || '',
    shopifyOrderId: replacementOrder.shopifyOrderId || '',
    orderType: replacementOrder.orderType || '',
    replacementType:
      replacementOrder.orderType === 'REPLACEMENT'
        ? replacementOrder.replacementType
        : '',

    // Enhanced old product details
    oldProductName: oldProductDetails?.productTitle || oldProduct.title || '',
    oldProductSize: oldProductDetails?.option1 || oldProduct.variantTitle || '',
    oldProductDimension: oldProductDetails?.option3 || '',
    oldProductCategory: oldProduct.productType || '',
    oldSkuId: oldProduct.sku || '',
    oldProductPriceAfterDiscount: oldProduct.finalItemPrice || 0,

    // Request details
    requestExists: requestData ? 'Yes' : 'No',
    requestType:
      requestData?.finalRequestType || requestData?.requestType || '',
    requestTicketId: requestData?.freshdeskId || '',
    dateOfRequest: formatDate(requestData?.createdAt) || '',
    // status: replacementOrder?.status || '',
    requestStatus: requestData?.requestStatus || 'NONE',
    finalRequestType: requestData?.finalRequestType || '',
    replacementPriority: replacementOrder.pickupPriority || '',
    rejectedReason: requestData?.rejectedReason || '',

    // Replacement booking details
    dateOfReplacementBooking: formatDate(replacementOrder.createdAt) || '',
    acceptedPrimaryReason:
      replacementOrder.primaryReason || replacementOrder.reason || '',
    acceptedSubReason: replacementOrder.secondaryReason || '',
    acceptedDepartment: replacementOrder.department || '',
    agentName: replacementOrder.agentName || '',
    reasonAdditionalComments: requestData?.comment || '',

    // Enhanced replacement order and product details
    replacementOrderId: replacementOrder.eeRefNo || replacementOrder.id || '',
    replacementOrderIdStatus: replacementOrderStatus,
    replacementProductName:
      newProductDetails?.productTitle || newProduct.title || '',
    replacementProductSkuId: newProduct.sku || '',
    replacementProductSize:
      newProductDetails?.option1 || newProduct.variantTitle || '',
    replacementProductDimension: newProductDetails?.option3 || '',
    replacementProductPrice: newProduct.finalItemPrice || 0,
    customDiscountApplied: replacementOrder.customDiscountAmount || 0,

    // Payment and shipping details
    approverName: approverName,
    refundOrAdditionalPayment: refundOrAdditionalPayment,
    refundAmount: replacementOrder?.refundedPrice || 0,
    shippingChargesApplied: replacementOrder.isShippingCharged ? 'Yes' : 'No',
    shippingChargesAmount: replacementOrder.shippingCost || 0,
    additionalPaymentAmount: amountPaid || 0,
    paymentTransactionId: transactionId || '',
    paymentTransactionStatus: paymentStatus || '',

    // Ticket and EDD details
    bookingTicketId: replacementOrder.RRTicketId?.toString() || '',
    pickupEdd: pickupDetails.bookingDate
      ? getHumanReadableDateRange(5, 7, pickupDetails.bookingDate)
      : '',
    newOrderEdd:
      replacementOrder.orderType === 'REPLACEMENT' && orderDetails?.EDD
        ? `${orderDetails.EDD.start} to ${orderDetails.EDD.end}`
        : '',
    // Added pickup details from Rohit's API
    pickupAwbNumber: pickupDetails.awbNumber,
    pickupAwbCurrentStatus: pickupDetails.currentStatus,
    pickupInitiatedDate: formatDate(pickupDetails.pickupInitiatedDate),

    // Added regenerated pickup details
    regeneratedPickupFlag: regeneratedPickup.isRegenerated ? 'YES' : 'NO',
    regenerateDate: regeneratedPickup.regenerateDate,
    regenerateAwb: regeneratedPickup.regenerateAwb,

    // Refund details
    refundTrackerId: refundData?.id || '',
    refundMode: getRefundMode(refundData, replacementOrder),
    refundStatus: refundData?.status || '',
    refundTransactionId: refundData?.transactionId || '',
    refundInitiatedDate: formatDate(refundData?.createdAt) || '',

    // Enhanced days calculations with all required fields
    daysFromRequestToReturn: daysFromRequestToReturn,
    daysFromRequestToRefund: daysFromRequestToRefund,
    deliveryDateToRequestDate: deliveryToRequestDays,
    deliveryDateToBookingDate: deliveryToBookingDays,
    requestDateToBookingDate: requestToBookingDays,
    requestDateToNewOrderDeliveryDate: requestToNewOrderDeliveryDays,
    requestDateToRefundDate: requestToRefundDays,
    requestDateToPickupDate: requestToPickupDays,
  };
};

const uploadToS3 = async (csvContent, fileName) => {
  const params = {
    Bucket: S3_BUCKET,
    Key: `exports-post-order/${fileName}`,
    Body: csvContent,
    ContentType: 'text/csv',
    ContentDisposition: `attachment; filename="${fileName}"`,
  };

  const command = new PutObjectCommand(params);
  await s3Client.send(command);

  return `https://${S3_BUCKET}.s3.amazonaws.com/exports-post-order/${fileName}`;
};

const sendEmailWithS3Link = async (email, s3Url, fileName, recordCount) => {
  const mailOptions = {
    from: process.env.FROM_EMAIL || '<EMAIL>',
    to: email,
    subject: 'Replacement Orders Export - Ready for Download',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Your Export is Ready!</h2>
        <p>Dear User,</p>
        <p>Your replacement orders export has been successfully generated and is ready for download.</p>
        
        <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #555;">Export Details:</h3>
          <ul style="list-style: none; padding: 0;">
            <li style="margin: 10px 0;"><strong>File Name:</strong> ${fileName}</li>
            <li style="margin: 10px 0;"><strong>Records Count:</strong> ${recordCount}</li>
            <li style="margin: 10px 0;"><strong>Generated On:</strong> ${new Date().toLocaleString()}</li>
          </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${s3Url}" 
             style="background-color: #007bff; color: white; padding: 12px 30px; 
                    text-decoration: none; border-radius: 5px; display: inline-block;">
            Download CSV File
          </a>
        </div>
        
        <p style="color: #666; font-size: 14px;">
          <strong>Note:</strong> This download link will be available for 7 days. 
          Please download the file within this period.
        </p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="color: #999; font-size: 12px;">
          This is an automated email. Please do not reply to this message.
        </p>
      </div>
    `,
  };

  await transporter.sendMail(mailOptions);
};

// Updated createCancellationCSVRow to include refund details from refund table
const createCancellationCSVRow = async (cancellationOrder, requestData) => {
  // Get refund details for cancellation using the same logic as replacement/return
  let refundData = null;
  try {
    const refundDetailCommand = new GetCommand({
      TableName: REFUND_DETAIL_TABLE,
      Key: {
        shopifyOrderId: cancellationOrder.order_id, // Using order_id as shopifyOrderId
        id: cancellationOrder.pk, // Using pk from cancel table as id in refund table
      },
    });

    const { Item: data } = await docClient.send(refundDetailCommand);
    refundData = data;
  } catch (error) {
    console.error('Error getting refund details for cancellation:', error);
    refundData = null;
  }

  // Get proper request date and other details from requestData if available
  const requestDate = requestData?.createdAt || cancellationOrder.cancelled_at;
  const cancelledDate = cancellationOrder.cancelled_at;

  // Calculate days from request to cancellation
  const daysFromRequestToCancel = calculateDaysBetween(
    requestDate,
    cancelledDate,
  );

  // Calculate days from request to refund if refund data exists
  const daysFromRequestToRefund = refundData?.createdAt
    ? calculateDaysBetween(requestDate)
    : refundData.status === 'COMPLETED' ? refundData.updatedAt : ''

  return {
    // Order details
    orderId: cancellationOrder.order_id || '',
    shopifyOrderId: cancellationOrder.order_id || '',
    orderType: 'CANCELLATION',
    replacementType: '',

    // Old Product details - Not needed for cancellation
    oldProductName: '',
    oldProductSize: '',
    oldProductDimension: '',
    oldProductCategory: '',
    oldSkuId: '',
    oldProductPriceAfterDiscount: cancellationOrder.priceAfterDiscount || 0,

    // Enhanced request details using requestData
    requestExists: requestData ? 'Yes' : 'No',
    requestType: 'CANCELLATION',
    requestTicketId:
      requestData?.freshdeskId || cancellationOrder.freshdeskId || '',
    dateOfRequest: formatDate(requestDate) || '',
    // status: 'CANCELLED',
    requestStatus: requestData?.requestStatus || 'ACCEPTED',
    finalRequestType: 'CANCELLATION',
    replacementPriority: '',
    rejectedReason: requestData?.rejectedReason || '',

    // Cancellation details
    dateOfReplacementBooking: formatDate(cancellationOrder.cancelled_at) || '',
    acceptedPrimaryReason:
      requestData?.primaryReason ||
      cancellationOrder.reasonForCancellation ||
      '',
    acceptedSubReason: requestData?.secondaryReason || '',
    acceptedDepartment: requestData?.department || '',
    agentName: requestData?.agentName || '',
    reasonAdditionalComments: requestData?.comment || '',

    // No replacement for cancellation
    replacementOrderId: '',
    replacementOrderIdStatus: '',
    replacementProductName: '',
    replacementProductSkuId: '',
    replacementProductSize: '',
    replacementProductDimension: '',
    replacementProductPrice: 0,
    customDiscountApplied: 0,

    // Payment details
    approverName: '',
    refundOrAdditionalPayment: 'Refund',
    refundAmount: refundData?.refundedPrice || 0,
    shippingChargesApplied: 'No',
    shippingChargesAmount: 0,
    additionalPaymentAmount: 0,
    paymentTransactionId: '',
    paymentTransactionStatus: '',

    // Ticket details
    bookingTicketId: cancellationOrder.refundTicketId?.toString() || '',
    pickupEdd: '',
    newOrderEdd: '',

    // No pickup details for cancellation
    pickupAwbNumber: '',
    pickupAwbCurrentStatus: '',
    pickupInitiatedDate: '',

    // No regenerated pickup for cancellation
    regeneratedPickupFlag: '',
    regenerateDate: '',
    regenerateAwb: '',

    // Enhanced refund details using refund table data
    refundTrackerId: refundData?.id || '',
    refundMode:
      refundData?.refundMode ||
      (refundData?.isBackToSource ? 'Back to Source' : '') ||
      (cancellationOrder.isBackToSource ? 'Back to Source' : 'Bank Transfer'),
    refundStatus: refundData?.status || '',
    refundTransactionId: refundData?.transactionId || '',
    refundInitiatedDate: formatDate(refundData?.createdAt) || '',

    // Enhanced days calculations for cancellation
    daysFromRequestToReturn: '',
    daysFromRequestToRefund: daysFromRequestToRefund,
    deliveryDateToRequestDate: 0,
    deliveryDateToBookingDate: 0,
    requestDateToBookingDate: 0,
    requestDateToNewOrderDeliveryDate: 0,
    requestDateToRefundDate: daysFromRequestToRefund,
    requestDateToPickupDate: 0,
  };
};

export const handler = async (event) => {
  console.log('Received event:', JSON.stringify(event, null, 2));

  try {
    for (const record of event.Records) {
      const body = JSON.parse(record.body);
      console.log('Processing message:', body);

      const { exportType, email, filters = {} } = body;

      console.log('Starting replacement orders export process...');

      const csvData = [];

      // Determine what to process based on filters.requestType
      const shouldProcessReplacements =
        !filters.requestType ||
        filters.requestType === 'REPLACEMENT' ||
        filters.requestType === 'RETURN' ||
        filters.requestType === 'NONE';

      const shouldProcessCancellations =
        !filters.requestType ||
        filters.requestType === 'CANCELLATION' ||
        filters.requestType === 'NONE';

      // Process replacement/return orders only if needed
      if (shouldProcessReplacements) {
        console.log('Processing replacement/return orders...');

        const replacementOrders = await getAllReplacementOrdersFromOS(filters);
        console.log(
          'Total replacement orders found:',
          replacementOrders.length,
        );

        // Filter for orders with expanded status criteria including regenerated orders
        const filteredOrders = replacementOrders.filter(
          (order) =>
            order.status === 'ORDER_CREATED' ||
            order.status === 'SENT_TO_EE' ||
            order.status === 'RETURN_CANCELLED' ||
            order.status === 'RETURN_REINITIALIZED',
        );
        console.log(
          'Filtered replacement orders count:',
          filteredOrders.length,
        );

        // Process replacement/return orders
        for (const replacementOrder of filteredOrders) {
          try {
            console.log('Processing replacement order:', replacementOrder.id);

            // Get request details
            const requestData = await getRequestDetails(replacementOrder);

            // Get refund details
            const refundData = await getRefundDetails(replacementOrder);

            // Get payment details
            const paymentData = await getAllPaymentsByOrderId(
              replacementOrder.id,
            );

            // Create CSV row
            const csvRow = await createCSVRow(
              replacementOrder,
              requestData,
              refundData,
              paymentData,
            );
            csvData.push(csvRow);
          } catch (error) {
            console.error(
              'Error processing replacement order:',
              replacementOrder.id,
              error,
            );
            continue; // Continue with next order if one fails
          }
        }
      }

      // Process cancellation orders only if needed
      if (shouldProcessCancellations) {
        console.log('Processing cancellation orders...');

        const cancellationOrders = await getAllCancelledOrdersFromOS(filters);
        console.log(
          'Total cancellation orders found:',
          cancellationOrders.length,
        );

        // Process cancellation orders
        for (const cancellationOrder of cancellationOrders) {
          try {
            console.log(
              'Processing cancellation order:',
              cancellationOrder.order_id,
            );

            // Get cancellation request details
            const requestData = await getCancellationRequestDetails(
              cancellationOrder.order_id,
            );

            // Create CSV row for cancellation
            const csvRow = await createCancellationCSVRow(
              cancellationOrder,
              requestData,
            );
            csvData.push(csvRow);
          } catch (error) {
            console.error(
              'Error processing cancellation order:',
              cancellationOrder.order_id,
              error,
            );
            continue; // Continue with next order if one fails
          }
        }
      }

      console.log('Total CSV rows created:', csvData.length);

      if (csvData.length === 0) {
        await transporter.sendMail({
          from: process.env.FROM_EMAIL || '<EMAIL>',
          to: email,
          subject: 'Replacement Orders Export - No Data Found',
          text: 'No replacement orders found matching the specified criteria.',
        });
        continue;
      }

      // Convert to CSV
      const csv = await json2csv(csvData);
      const fileName = `replacement_orders_${new Date().toISOString().split('T')[0]}_${Date.now()}.csv`;

      // Check if CSV is too large
      const csvSizeInMB = Buffer.byteLength(csv, 'utf8') / (1024 * 1024);

      if (csvSizeInMB > MAX_ATTACHMENT_SIZE_MB) {
        // Upload to S3 and send link
        const s3Url = await uploadToS3(csv, fileName);
        await sendEmailWithS3Link(email, s3Url, fileName, csvData.length);
      } else {
        // Send as email attachment (if small enough)
        await transporter.sendMail({
          from: process.env.FROM_EMAIL || '<EMAIL>',
          to: email,
          subject: 'Replacement Orders Export',
          text: `Please find attached the replacement orders export containing ${csvData.length} records.`,
          attachments: [
            {
              filename: fileName,
              content: csv,
              contentType: 'text/csv',
            },
          ],
        });
      }

      console.log('Successfully processed replacement orders export');
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Successfully processed all records',
      }),
    };
  } catch (error) {
    console.error('Error processing messages:', error);
    throw error; // This will cause the message to be retried
  }
};
