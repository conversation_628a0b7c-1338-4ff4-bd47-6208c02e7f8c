import { unmarshall } from '@aws-sdk/util-dynamodb';
import fetch from 'node-fetch';
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';

const client = new DynamoDBClient({
  region: process.env.REGION,
});
const docClient = DynamoDBDocumentClient.from(client);

const ORDER_TABLE = process.env.ORDER_TABLE;
const QUOTATION_TABLE = process.env.QUOTATION_TABLE;
const INTERIOR_ARCHITECTURE_TABLE = process.env.INTERIOR_ARCHITECTURE_TABLE;
const BE_BASE_API = process.env.BE_BASE_API;
const AUTH_TOKEN = process.env.AUTH_TOKEN;
const GLOBAL_CONFIGURATION_TABLE = process.env.GLOBAL_CONFIGURATION_TABLE;

const WE_LICENCE_CODE = process.env.WE_LICENCE_CODE;
const WE_AUTH_TOKEN = process.env.WE_AUTH_TOKEN;

const setWebEngageData = async (userId, attributes) => {
  console.log('setWebEngageData', { userId, attributes });
  try {
    const response = await fetch(
      `https://api.webengage.com/v1/accounts/${WE_LICENCE_CODE}/users`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${WE_AUTH_TOKEN}`,
        },
        body: JSON.stringify({
          userId: `+91${userId}`,
          attributes: attributes,
        }),
      },
    );

    const data = await response.json();
    console.log('WebEngage Response:', data);
  } catch (err) {
    console.error('Failed to update pincode in WebEngage:', err);
  }
};

export const getInteriorArchitecture = async (docClient, id) => {
  console.log('getInteriorArchitecture', docClient, { id });
  return (
    await docClient.send(
      new GetCommand({
        TableName: INTERIOR_ARCHITECTURE_TABLE,
        Key: {
          id,
        },
      }),
    )
  ).Item;
};

export const getGlobalConfiguration = async (docClient, id) => {
  console.log('getGlobalConfiguration', { id });
  return (
    await docClient.send(
      new GetCommand({
        TableName: GLOBAL_CONFIGURATION_TABLE,
        Key: {
          key: id,
        },
      }),
    )
  ).Item;
};

const getStoreDetails = async (storeId) => {
  const url = `${BE_BASE_API}/stores/all?isActive=true`;
  const options = {
    method: 'GET',
    headers: {
      Authorization: `${AUTH_TOKEN}`,
      'Content-Type': 'application/json',
    },
  };
  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data = await response.json();
    return data.find((store) => store.id === storeId) || null;
  } catch (error) {
    console.error('Error fetching store details:', error.message);
    throw new Error(`Error fetching store details: ${error.message}`);
  }
};

export const LeadSquareAPICall = async (requestBody) => {
  const response = await fetch(
    'https://apis.thesleepcompany.in/lead/datafeed',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    },
  );
  return response;
};

export const handler = async (event) => {
  try {
    console.log('Event: ', JSON.stringify(event, null, 2));

    if (event.Records && Array.isArray(event.Records)) {
      await Promise.all(
        event.Records.map(async (record) => {
          const {
            dynamodb: { NewImage = {}, OldImage = {} },
            eventSourceARN,
            eventName,
          } = record;

          const newItem = unmarshall(NewImage);

          const { status: newStatus, orderType } = newItem;
          const { status: oldStatus } = unmarshall(OldImage);
          const globalConfiguration = await getGlobalConfiguration(
            docClient,
            'HO_STORES',
          );
          const stores = globalConfiguration?.value?.split(',') || [];
          const isHOStore = stores.includes(newItem.storeId);

          if (orderType === 'REPLACEMENT' || orderType === 'RETURN') return;

          const tableStartIndex = eventSourceARN.indexOf(':table/') + 7;
          const tableLastIndex = eventSourceARN.lastIndexOf('/stream');
          const tableIndex = eventSourceARN
            .substring(tableStartIndex, tableLastIndex)
            .toLowerCase();

          const orderId = newItem.id || '';
          const quotationId = newItem.id || '';
          const phone = newItem.customer?.phone || '';
          const storeId = newItem.storeId || '';
          const finalDiscountedAmount = newItem.finalDiscountedAmount || '';
          const firstName = newItem.customer?.firstName || '';
          const lastName = newItem.customer?.lastName || '';
          const email = newItem.customer?.email || '';
          const city = newItem.shippingAddress?.city || '';
          // const state = newItem.shippingAddress?.state || '';
          const products = newItem.quotationProducts || newItem.orderProducts;
          const storeDetails = await getStoreDetails(storeId);
          const storeName = storeDetails?.storeName || '';
          const storeAddress = storeDetails?.address?.line1 || '';
          const storePincode = storeDetails?.address?.pinCode || '';
          const storePhone = storeDetails?.number || '';
          const storeMapLink = storeDetails?.mapLink || '';
          const productCategory =
            products?.map((item) => item.productType).join('; ') || '';

          const formattedDateTime = new Date()
            .toISOString()
            .replace('T', ' ')
            .split('.')[0];
          const productTitles = (
            products?.map((item) => item.title).join('; ') || ''
          )
            .replace(/"/g, '')
            .substring(0, 179);

          let requestBody = {
            user_email: email,
            phone: phone,
            product_type: productCategory,
            product_name: productTitles,
            mx_lead_city: city,
            lead_status: 'Store',
            store_id: storeId,
            activity_date_time: formattedDateTime,
            mx_custom_1: orderId,
            mx_custom_2: JSON.stringify(finalDiscountedAmount),
            mx_custom_3: productCategory,
            mx_custom_6: productTitles,
            mx_custom_7: JSON.stringify(finalDiscountedAmount),
            mx_custom_9: productTitles,
            mx_custom_26: storeId,
            is_web_engage_event: true,
            web_engage_data: {
              product_category: productCategory,
              product_subcategory: productTitles,
              customer_city: city,
            },
          };
          const userName = [firstName, lastName].filter(Boolean).join(' ');
          if (userName) {
            requestBody['user_name'] = userName;
          }

          if (tableIndex === QUOTATION_TABLE && eventName === 'INSERT') {
            const quotationPdfResponse = await fetch(
              `${process.env.BE_BASE_API}/graphql`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  query:
                    'query quotationPdf($quotationId: String!, $type: String!) {\n  quotationPdf(quotationId: $quotationId, type: $type) {\n    message\n    status\n    success\n  }\n}\n',
                  variables: {
                    quotationId: orderId,
                    type: 'DOWNLOAD',
                  },
                }),
              },
            );

            const quotationPdfRes = await quotationPdfResponse.json();
            const quotationPdfUrl = `${process.env.MEDIA_BASE_API}/${quotationPdfRes?.data?.quotationPdf?.message}`;
            requestBody.web_engage_data['quotation_amount'] =
              finalDiscountedAmount;
            requestBody['source_page'] = quotationPdfUrl;
            requestBody['source'] =
              `${isHOStore ? 'HO ' : ''}Quotation Created`;
            requestBody['activity_source'] =
              `${isHOStore ? 'HO ' : ''}Quotation Created`;
            requestBody.web_engage_data['quotation_url'] = quotationPdfUrl;
            if (storeName) {
              requestBody.web_engage_data['store_name'] = storeName;
            }
            if (storeAddress) {
              requestBody.web_engage_data['store_address'] = storeAddress;
            }
            if (storeMapLink) {
              requestBody.web_engage_data['store_map_link'] = storeMapLink;
            }
            if (storePincode) {
              requestBody.web_engage_data['Pincode'] = storePincode;
            }
            if (storePhone) {
              requestBody.web_engage_data['Store Phone'] = storePhone;
            }
            if (quotationId) {
              requestBody.web_engage_data['quotation_id'] = quotationId;
            }
            console.log('storeDetails', storeDetails);
            await setWebEngageData(phone, { Pincode: storePincode || '' });
          } else if (
            tableIndex === ORDER_TABLE &&
            eventName === 'MODIFY' &&
            oldStatus !== 'SENT_TO_SHOPIFY' &&
            newStatus === 'SENT_TO_SHOPIFY'
          ) {
            const { interiorArchitecture } = newItem;
            const { id = '' } = interiorArchitecture;

            console.log('interiorArchitecture', id, oldStatus, newStatus);
            const orderPdfResponse = await fetch(
              `${process.env.BE_BASE_API}/graphql`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  query:
                    'query orderPdf($orderId: String!, $type: String!) {\n  orderPdf(orderId: $orderId, type: $type) {\n    message\n    status\n    success\n  }\n}\n',
                  variables: {
                    orderId: orderId,
                    type: 'DOWNLOAD',
                  },
                }),
              },
            );
            const orderPdfRes = await orderPdfResponse.json();
            console.log('orderPdfResponse', orderPdfRes);
            const orderPdfUrl = `${process.env.MEDIA_BASE_API}/${orderPdfRes?.data?.orderPdf?.message}`;

            requestBody['mx_Custom_10'] = orderPdfUrl;
            requestBody['source'] =
              `${isHOStore ? 'HO ' : ''}POS Order Confirmation`;
            requestBody.web_engage_data['order_id'] = orderId;
            requestBody['activity_source'] =
              `${isHOStore ? 'HO ' : ''}POS Order Confirmation`;
            requestBody.web_engage_data['order_value'] = finalDiscountedAmount;
            if (id) {
              const data = await getInteriorArchitecture(docClient, id);

              if (data) {
                const { gst = '', phone = '', email = '' } = data;

                const { name } = interiorArchitecture;
                (requestBody['mx_custom_15'] = name),
                  (requestBody['mx_custom_16'] = id),
                  (requestBody['mx_Partner_unique_ID'] = id),
                  (requestBody['mx_custom_17'] = gst),
                  (requestBody['mx_custom_18'] = phone),
                  (requestBody['mx_custom_19'] = email);
              }
            }
          } else {
            return;
          }
          console.log('requestBody api calling', JSON.stringify(requestBody));
          const response = await fetch(
            'https://apis.thesleepcompany.in/lead/datafeed',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(requestBody),
            },
          );

          const responseBody = await response.text();
          console.log('Status Code:', response.status);
          console.log('Response Body:', responseBody);
        }),
      );
    } else if (event.body) {
      const { newItem, leadDay } = JSON.parse(event.body);
      const quotationId = newItem.id || '';
      const phone = newItem.customer?.phone || '';
      const finalDiscountedAmount = newItem.finalDiscountedAmount || '';
      const products = newItem.quotationProducts || newItem.orderProducts;
      const storeId = newItem.storeId || '';
      const storeDetails = await getStoreDetails(storeId);
      const storeName = storeDetails?.storeName || '';
      const productCategory =
        products?.map((item) => item.productType).join('; ') || '';
      const storeAddress = storeDetails?.address?.line1 || '';
      const storePincode = storeDetails?.address?.pinCode || '';
      const storeMapLink = storeDetails?.mapLink || '';
      const productTitles =
        products?.map((item) => item.title).join('; ') || '';

      const quotationPdfUrl = `${process.env.MEDIA_BASE_API}/QUOTATION/${newItem.customerId}/${newItem.id}.pdf`;
      console.log('quotationPdfResponse pdf', quotationPdfUrl);

      let requestBody = {
        phone: phone,
        source: leadDay,
        product_type: productCategory,
        product_name: productTitles,
        lead_status: 'Store',
        store_id: storeId,
        activity_source: leadDay,
        mx_Custom_2: finalDiscountedAmount,
        mx_Custom_3: productCategory,
        mx_Custom_6: productTitles,
        mx_Custom_7: finalDiscountedAmount,
        mx_Custom_9: productTitles,
        mx_Custom_26: storeId,
        is_web_engage_event: true,
        web_engage_data: {
          product_category: productCategory,
          product_subcategory: productTitles,
          quotation_amount: finalDiscountedAmount,
          quotation_id: quotationId,
          store_name: storeName,
          store_address: storeAddress,
          store_map_link: storeMapLink,
        },
      };
      requestBody['source_page'] = quotationPdfUrl;
      requestBody.web_engage_data['quotation_url'] = quotationPdfUrl;
      if (storeName) {
        requestBody.web_engage_data['store_name'] = storeName;
      }
      if (storeAddress) {
        requestBody.web_engage_data['store_address'] = storeAddress;
      }
      if (storeMapLink) {
        requestBody.web_engage_data['store_map_link'] = storeMapLink;
      }
      if (storePincode) {
        requestBody.web_engage_data['Pincode'] = storePincode;
      }

      console.log('Sending request body:', requestBody);
      const response = await LeadSquareAPICall(requestBody);

      console.log('Response Status:', response.status);
      console.log('Response Body:', await response.text());
    }
  } catch (error) {
    console.error('Error:', error);
  }
};
