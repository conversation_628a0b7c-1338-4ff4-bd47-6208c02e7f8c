import fetch from 'node-fetch';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  QueryCommand,
  GetCommand,
} from '@aws-sdk/lib-dynamodb';

const {
  ORDER_TABLE_NAME,
  ORDER_BY_AWB_NUMBER_INDEX,
  REGION,
  EASY_ECOM_EMAIL,
  EASY_ECOM_PASSWORD,
  EASY_ECOM_BASE_URL,
  EASY_ECOM_STORE_ID,
  INVENTORY_CREDENTIALS_TABLE,
} = process.env;

const client = new DynamoDBClient({
  region: REGION,
});
const docClient = DynamoDBDocumentClient.from(client);

const getInventoryCredentials = async () => {
  console.log('🚀🚀🚀🚀  getInventoryCredentials');

  const command = new GetCommand({
    TableName: INVENTORY_CREDENTIALS_TABLE,
    Key: { id: EASY_ECOM_STORE_ID },
  });

  return (await docClient.send(command)).Item;
};

const getOrderByAWBNumber = async (awbNumber) => {
  console.log('🚀🚀🚀🚀  getOrderByAWBNumber', awbNumber);

  const command = new QueryCommand({
    TableName: ORDER_TABLE_NAME,
    IndexName: ORDER_BY_AWB_NUMBER_INDEX,
    KeyConditionExpression: 'awbNumber = :awbNumber',
    ExpressionAttributeValues: {
      ':awbNumber': awbNumber,
    },
  });

  const { Items } = await docClient.send(command);

  console.log('aa gya awb item🚀🚀🚀🚀  getOrderByAWBNumber', {
    Items,
  });
  return Items;
};

const getEasyecomAccessToken = async (locationKey) => {
  console.log('🚀🚀🚀🚀  getEasyecomAccessToken', {
    locationKey,
  });

  const body = {
    email: EASY_ECOM_EMAIL,
    password: EASY_ECOM_PASSWORD,
    location_key: locationKey,
  };

  const response = await fetch(`${EASY_ECOM_BASE_URL}/access/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  });

  const { data } = await response.json();
  const {
    token: { jwt_token },
  } = data;

  console.log('🚀🚀🚀🚀 getEasyecomAccessToken', {
    jwt_token,
  });

  return jwt_token;
};

const unHoldOrder = async (invoiceId, token) => {
  console.log('🚀🚀🚀🚀  unHoldOrder', {
    invoiceId,
  });
  const response = await fetch(`${EASY_ECOM_BASE_URL}/orders/unholdOrders`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({
      invoice_id: invoiceId,
    }),
  });

  const result = await response.json();

  console.log('🚀🚀🚀🚀 unHoldOrder', {
    result,
  });

  return result;
};

export const handler = async (event) => {
  console.log('Incoming event', JSON.stringify(event));

  const body =
    typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
  const { awb_number: awbNumber } = body;
  console.log('awbNumber in event', awbNumber);
  if (!awbNumber) {
    return {
      statusCode: 400,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        statusCode: 400,
        message: 'No awbNumber Provided',
      }),
    };
  }

  try {
    const order = await getOrderByAWBNumber(awbNumber);

    if (!order || !order.length) {
      throw new Error('Order data not found');
    }

    const { locationKey } = (await getInventoryCredentials()) || {};
    if (!locationKey) {
      throw new Error('Location key not found');
    }

    const token = await getEasyecomAccessToken(locationKey);
    if (!token) {
      throw new Error('Token not found');
    }

    console.log('invoiceId is', order, order[0].ecomInvoiceId);
    const result = await unHoldOrder(order[0].ecomInvoiceId, token);

    console.log('Result from unHoldOrder:', result);

    const responseMessage =
      result?.message || 'Operation successful but no message found.';
    const responseCode = result?.code || 400;

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        statusCode: responseCode,
        message: responseMessage,
      }),
    };
  } catch (error) {
    console.error('ERROR❌❌❌❌:', awbNumber, error);

    const errorMessage = error?.message || 'Unknown error occurred.';
    return {
      statusCode: 400,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: errorMessage,
        status: false,
      }),
    };
  }
};
