import { unmarshall } from '@aws-sdk/util-dynamodb';
import _ from 'lodash';
import fetch from 'node-fetch';
import slugify from 'slugify';
import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda';

const lambdaClient = new LambdaClient({ region: process.env.REGION });

const getSlug = (str) => {
  return slugify(str, {
    replacement: '-',
    remove: /[^\w\s-]/g,
    lower: true,
  }).replace(/(^-+)|(-+$)/g, '');
};

const sendSlackNotification = async (message) => {
  try {
    const payload = {
      message,
      channel: `${process.env.STACK_NAME}-stream-handler`,
    };

    const command = new InvokeCommand({
      FunctionName: `${process.env.STACK_NAME}-slack-notification`,
      Payload: Buffer.from(JSON.stringify(payload)),
    });

    const response = await lambdaClient.send(command);
    console.log('Slack Notification Response:', response);
  } catch (err) {
    console.error('Failed to send Slack notification:', err);
  }
};

export const handler = async (event) => {
  try {
    console.log('event, JSON.stringify(event) :>> ', JSON.stringify(event));

    if (event.Records && Array.isArray(event.Records)) {
      await Promise.all(
        event.Records?.map(async (record) => {
          try {
            const {
              dynamodb: { NewImage = {}, Keys = {} },
              eventSourceARN,
            } = record;

            const item = unmarshall(NewImage);
            const keys = unmarshall(Keys);

            const tableStartIndex = eventSourceARN.indexOf(':table/') + 7;
            const tableLastIndex = eventSourceARN.lastIndexOf('/stream');
            const tableIndex = eventSourceARN
              .substring(tableStartIndex, tableLastIndex)
              .toLowerCase();

            const domain = process.env.OPEN_SEARCH_DOMAIN;
            let method = 'PUT';

            if (
              record.eventName === 'REMOVE' ||
              !Object.keys(item).length ||
              (tableIndex && item?.isDeleted)
            ) {
              method = 'DELETE';
              delete item?.isDeleted;
            }

            console.log('🔥 method :>> ', method);

            console.log('🔥 item :>> ', item);
            console.log(
              '🩸🩸🩸 ',
              `https://${domain}/${tableIndex}/_doc/${encodeURI(
                _.values(keys).map(getSlug).join('|'),
              )}`,
            );

            const response = await fetch(
              `https://${domain}/${tableIndex}/_doc/${encodeURI(
                _.values(keys).map(getSlug).join('|'),
              )}`,
              {
                method,
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Basic ${Buffer.from(
                    `${process.env.OPEN_SEARCH_USERNAME}:${process.env.OPEN_SEARCH_PASSWORD}`,
                  ).toString('base64')}`,
                },
                body: JSON.stringify(item),
              },
            );

            const responseBody = await response.text();
            console.log('response🔥🩸', response);
            console.log(`Status Code: ${response.status}`);
            console.log(`Response Body: ${responseBody}`);

            if (![200, 201].includes(response.status)) {
              await sendSlackNotification(
                `${process.env.STACK_NAME} ------ ${JSON.stringify(item)} ------- OpenSearch indexing error. Status: ${response.status}, Table: ${tableIndex}, DocID: ${_.values(
                  keys,
                )
                  .map(getSlug)
                  .join('|')}, Response: ${responseBody}`,
              );
            }
          } catch (recordError) {
            console.error('Error processing record:', recordError);
            await sendSlackNotification(
              `${process.env.STACK_NAME}------- ${JSON.stringify(item)} --------  Error processing OpenSearch record: ${recordError.message}`,
            );
          }
        }),
      );
    }
  } catch (e) {
    console.error('Critical error in OpenSearch indexing lambda:', e);
    await sendSlackNotification(
      `${process.env.STACK_NAME} -------${JSON.stringify(item)}  ------- Critical error in OpenSearch indexing lambda: ${e.message}`,
    );
  } finally {
    return;
  }
};
