import { unmarshall } from '@aws-sdk/util-dynamodb';
import { Client } from '@opensearch-project/opensearch';

const client = new Client({
    node: `https://${process.env.OPEN_SEARCH_DOMAIN}`,
    auth: {
        username: process.env.OPEN_SEARCH_USERNAME,
        password: process.env.OPEN_SEARCH_PASSWORD
    }
});

export const handler = async (event) => {
    console.log("event,JSON.stringify(event) :>> ", JSON.stringify(event));
    if (event.Records && Array.isArray(event.Records)) {

        const { Records = [] } = event;
        await Promise.all(Records.map(async record => {
            try {
                const {
                    dynamodb: { NewImage },
                } = record;

                const parsedDoc = unmarshall(NewImage);
                const tableName = process.env.PRODUCT_TABLE;

                if (record.eventName === 'INSERT') {
                    await client.index({
                        index: tableName,
                        id: parsedDoc.pk,
                        body: { ...parsedDoc },
                    });
                } else if (record.eventName === 'MODIFY') {
                    const { isActive = true } = NewImage;
                    if (isActive) {
                        await client.update({
                            index: tableName,
                            id: parsedDoc.pk,
                            body: { ...parsedDoc },
                        });
                    } else {
                        await client.delete({
                            index: tableName,
                            id: parsedDoc.pk
                        })
                    }
                }

            } catch (e) {
                console.log('ERROR in updating opensearch', JSON.stringify(e));
            }
        }))
    }
};
