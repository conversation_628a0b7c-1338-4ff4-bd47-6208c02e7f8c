import fetch from 'node-fetch';

const BASE_URL = process.env.EASY_ECOM_BASE_URL;
const EMAIL = process.env.EASY_ECOM_EMAIL;
const PASSWORD = process.env.EASY_ECOM_PASSWORD;

// Function to fetch token
async function fetchToken(locationKey) {
  const response = await fetch(`${BASE_URL}/access/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: EMAIL,
      password: PASSWORD,
      location_key: locationKey,
    }),
  });

  const data = await response.json();
  if (response.ok) {
    return data.data.token.jwt_token;
  } else {
    throw new Error(data.message || 'Failed to fetch token');
  }
}

export async function markShipped(orderNumber, locationKey) {
  const token = await fetchToken(locationKey);

  const response = await fetch(
    `${BASE_URL}/orders/ship?orderNumber=${orderNumber}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    },
  );

  const data = await response.json();
  console.log('🔥success', data);

  if (response.ok) {
    return data;
  } else {
    throw new Error(data.message || 'Failed to mark order as shipped');
  }
}

export const handler = async (event) => {
  try {
    console.log('SQS event received:', JSON.stringify(event));

    for (const record of event.Records) {
      const { sourceId, locationKey } = JSON.parse(record.body);

      try {
        console.log(`Marking order ${sourceId} as shipped`);
        const data = await markShipped(sourceId, locationKey);
        console.log('Order successfully marked as shipped', data);
      } catch (error) {
        console.error(`Error marking order ${sourceId} as shipped`, error);
      }
    }
  } catch (error) {
    console.error('Error processing SQS event', error);
  }
};
