import fetch from 'node-fetch';

export const handler = async (event) => {
  try {
    const { message, channel = 'io-handler' } = event; // Channel default is 'io-handler'
    const webhookUrl = process.env.SLACK_WEBHOOK_URL;

    if (!webhookUrl) {
      throw new Error('SLACK_WEBHOOK_URL is not defined.');
    }

    const payload = {
      text: message,
      channel,
    };

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorDetails = await response.text();
      throw new Error(`Slack API error: ${errorDetails}`);
    }

    return {
      statusCode: 200,
      body: JSON.stringify({ success: true }),
    };
  } catch (err) {
    console.error('Error sending Slack notification:', err);
    return {
      statusCode: 500,
      body: JSON.stringify({ success: false, error: err.message }),
    };
  }
};
