import fetch from 'node-fetch';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { Client } from '@opensearch-project/opensearch';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
const sqsClient = new SQSClient({ region: process.env.REGION });

const client = new DynamoDBClient({ region: process.env.REGION });
const docClient = DynamoDBDocumentClient.from(client);

const searchClient = new Client({
  node: `https://${process.env.OPEN_SEARCH_DOMAIN}`,
  auth: {
    username: process.env.OPEN_SEARCH_USERNAME,
    password: process.env.OPEN_SEARCH_PASSWORD,
  },
});

const SHOPIFY_ADMIN_BASE_URL = process.env.SHOPIFY_ADMIN_BASE_URL;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ACCESS_TOKEN;

async function query({
  index,
  limit: size = 100,
  page = 1,
  filter,
  sort,
  nextToken: nt,
}) {
  let searchAfter;
  if (nt) {
    searchAfter = JSON.parse(Buffer.from(nt, 'base64').toString('ascii'));
  }

  console.log('searchAfter :>> ', searchAfter);

  // Building search request
  const searchParams = {
    index,
    size,
    from: (page - 1) * size,
    body: {
      version: false,
      track_total_hits: true,
      search_after: searchAfter,
      query: filter,
      sort: [...sort],
    },
  };

  // Executing the OpenSearch request
  const { body } = await searchClient.search(searchParams);

  const { hits } = body;
  const { hits: results = [], total } = hits;
  const lastResult = results[results.length - 1];
  const nextToken =
    lastResult && lastResult.sort
      ? Buffer.from(JSON.stringify(lastResult.sort), 'ascii').toString('base64')
      : null;

  return {
    page,
    pageSize: size,
    totalPages: Math.ceil(total.value / size),
    total: total.value,
    items: results.map(({ _source }) => _source),
    nextToken: nextToken,
  };
}

// Helper function for recursively fetching all matching documents
async function queryAll({ index, filter, nextToken: nT }) {
  console.log('index :>> ', index);
  const sortItems = [{ 'id.keyword': { order: 'desc' } }];

  const { items, nextToken } = await query({
    index,
    filter,
    sort: sortItems,
    nextToken: nT,
    limit: 9999,
  });

  if (nextToken) {
    const nextItems = await queryAll({
      index,
      filter,
      nextToken,
    });
    return [...items, ...nextItems];
  }

  return items;
}

async function fetchDataForOrder(shopifyOrderId) {
  const filter = {
    bool: {
      must: [
        {
          term: {
            'shopifyOrderId.keyword': shopifyOrderId,
          },
        },
      ],
    },
  };

  const data = await queryAll({
    index: process.env.ORDERS_TABLE,
    filter: filter,
    nextToken: null,
  });

  console.log('Data for order ID:', data);
  return data;
}

const updateTransactionStatus = async (transactions, orderStatus) => {
  console.log('payment orderStatus', orderStatus);
  if (!transactions || !transactions.length) return [];

  const transactionUpdates = transactions.map(({ transactionId, orderId }) => ({
    TableName: process.env.PAYMENTS_TABLE,
    Key: { orderId, transactionId },
    UpdateExpression:
      'SET #orderStatus = :orderStatus, #updatedAt = :updatedAt',
    ExpressionAttributeNames: {
      '#orderStatus': 'orderStatus',
      '#updatedAt': 'updatedAt',
    },
    ExpressionAttributeValues: {
      ':orderStatus': orderStatus,
      ':updatedAt': new Date().toISOString(),
    },
    ConditionExpression:
      'attribute_exists(orderId) AND attribute_exists(transactionId)',
    ReturnValues: 'ALL_NEW',
  }));

  try {
    const updatedTransactions = await Promise.all(
      transactionUpdates.map((params) =>
        docClient.send(new UpdateCommand(params)),
      ),
    );
    return updatedTransactions.map((res) => res.Attributes);
  } catch (error) {
    console.error('Error updating payment table:', error);
    return [];
  }
};

const updateOrderInDB = async (POSId, updatedTransactions, statusInfo) => {
  console.log('statusInfo', statusInfo);
  let updateExpression =
    'SET transactions = :transactions, #status = :status, shopifyOrderStatus = :shopifyOrderStatus, updatedAt = :updatedAt';
  let expressionAttributeValues = {
    ':transactions': {
      ...statusInfo.transactions,
      transactionDetails: updatedTransactions,
    },
    ':status': statusInfo.status,
    ':shopifyOrderStatus': statusInfo.shopifyOrderStatus,
    ':updatedAt': new Date().toISOString(),
  };

  if (statusInfo.shopifyOrderId) {
    updateExpression += ', shopifyOrderId = :shopifyOrderId';
    expressionAttributeValues[':shopifyOrderId'] = statusInfo.shopifyOrderId;
  }

  if (statusInfo.shopifyPaymentStatus) {
    updateExpression += ', shopifyPaymentStatus = :shopifyPaymentStatus';
    expressionAttributeValues[':shopifyPaymentStatus'] =
      statusInfo.shopifyPaymentStatus;
  }

  if (statusInfo.shopifyShipmentStatus) {
    updateExpression += ', shopifyShipmentStatus = :shopifyShipmentStatus';
    expressionAttributeValues[':shopifyShipmentStatus'] =
      statusInfo.shopifyShipmentStatus;
  }

  const updateParams = {
    TableName: process.env.ORDERS_TABLE,
    Key: { id: POSId },
    UpdateExpression: updateExpression,
    ExpressionAttributeNames: {
      '#status': 'status',
    },
    ExpressionAttributeValues: expressionAttributeValues,
  };
  console.log('cancel params', updateParams);
  try {
    await docClient.send(new UpdateCommand(updateParams));
  } catch (error) {
    console.error('Error updating Order in DB:', error);
  }
};

const fetchShopifyOrder = async (shopifyOrderName) => {
  try {
    const graphqlQuery = `
      query GetOrderByName($query: String!) {
        orders(query: $query, first: 1) {
          edges {
            node {
              id
                  name
                  cancelledAt
                  displayFinancialStatus
                  displayFulfillmentStatus
                  metafield(namespace: "shopify_meta", key: "shipment_status") {
                    value
                  }
      
            }
          }
        }
      }
    `;

    const SHOPIFY_GRAPHQL_URL = `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`;
    console.log('TIMINING out');

    console.log('TIMINING continue');

    const response = await fetch(SHOPIFY_GRAPHQL_URL, {
      method: 'POST',
      headers: {
        'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: graphqlQuery,
        variables: {
          query: `name:${shopifyOrderName}`,
        },
      }),
    });

    const { data, errors } = await response.json();
    console.log('data, errors', data, errors);

    if (errors) {
      console.error('GraphQL errors:', errors);
      return null;
    }

    const shopifyOrder = data?.orders?.edges?.[0]?.node;

    if (!shopifyOrder) {
      return null;
    }

    return {
      id: shopifyOrder.id.split('/').pop(),
      cancelled_at: shopifyOrder.cancelledAt,
      financial_status: shopifyOrder.displayFinancialStatus?.toLowerCase(),
      fulfillment_status: shopifyOrder.displayFulfillmentStatus?.toLowerCase(),
      shipment_status: shopifyOrder.metafield?.value || null,
    };
  } catch (error) {
    console.error('Error fetching Shopify Order with GraphQL:', error);
    return null;
  }
};

const updateOrderStatus = async (orderData, shopifyOrderData) => {
  const {
    id: POSId,
    transactions,
    shopifyOrderName,
  } = shopifyOrderData?.[0] || {};
  if (!POSId) {
    console.error('POSId not found in order data');
    return;
  }

  const shopifyOrder = await fetchShopifyOrder(shopifyOrderName);
  console.log('shpofiy API data', shopifyOrderName, shopifyOrder);
  const {
    id: newOrderId,
    cancelled_at,
    financial_status,
    fulfillment_status,
    shipment_status,
  } = shopifyOrder;

  if (!shopifyOrder) {
    const updatedTransactions = await updateTransactionStatus(
      transactions?.transactionDetails,
      'DELETED',
    );
    if (!updatedTransactions.length) {
      console.error(`Failed to update transactions for order: ${POSId}`);
      return;
    }

    await updateOrderInDB(POSId, updatedTransactions, {
      transactions,
      status: 'DELETED_FROM_SHOPIFY',
      shopifyOrderStatus: 'Deleted',
      shopifyOrderId: newOrderId,
    });
    return;
  }
  if (shopifyOrder && cancelled_at) {
    const updatedTransactions = await updateTransactionStatus(
      transactions?.transactionDetails,
      'CANCELLED',
    );
    if (!updatedTransactions.length) {
      console.error(`Failed to update transactions for order: ${POSId}`);
      return;
    }
    await updateOrderInDB(POSId, updatedTransactions, {
      transactions,
      status: 'CANCELLED',
      shopifyOrderStatus: 'Cancelled',
      shopifyPaymentStatus: financial_status,
      shopifyShipmentStatus:
        shipment_status || fulfillment_status || 'undelivered',
      shopifyOrderId: newOrderId,
    });
    return;
  }

  if (shopifyOrder && !cancelled_at) {
    const updatedTransactions = await updateTransactionStatus(
      transactions?.transactionDetails,
      'SENT_TO_SHOPIFY',
    );
    if (!updatedTransactions.length) {
      console.error(`Failed to update transactions for order: ${POSId}`);
      return;
    }

    await updateOrderInDB(POSId, updatedTransactions, {
      transactions,
      status: 'SENT_TO_SHOPIFY',
      shopifyOrderStatus: 'Confirmed',
      shopifyPaymentStatus: financial_status,
      shopifyShipmentStatus:
        shipment_status || fulfillment_status || 'undelivered',
      shopifyOrderId: newOrderId,
    });
  }
};

export const handler = async (event) => {
  try {
    console.log('Incoming event', JSON.stringify(event));

    const isSqsEvent = event.Records && event.Records.length > 0;
    console.log('isSqsEvent', isSqsEvent, process.env.ORDER_STATUS_QUEUE_URL);
    if (!isSqsEvent) {
      const sendMessageParams = {
        QueueUrl: process.env.ORDER_STATUS_QUEUE_URL,
        MessageBody: JSON.stringify(event),
        DelaySeconds: 180,
      };
      const command = new SendMessageCommand(sendMessageParams);
      await sqsClient.send(command);
      console.log('Event pushed in queue');
      return;
    }
    console.log('Continuing Execution isSqsEvent', isSqsEvent);

    for (const record of event.Records) {
      const parsedEvent = JSON.parse(record.body);
      console.log('parsedEvent', parsedEvent);

      const { body } = parsedEvent;
      if (!body) return sendResponse(400, 'ERROR: Body not available');

      const parsedBody = JSON.parse(body);
      const { id: shopifyId } = parsedBody;

      const shopifyOrderData = await fetchDataForOrder(shopifyId);
      if (!shopifyOrderData.length) {
        return sendResponse(
          200,
          'ERROR: shopifyOrder not available for Id',
          shopifyId,
        );
      }

      await updateOrderStatus(parsedBody, shopifyOrderData);
      return sendResponse(200, 'Order status updated successfully');
    }
  } catch (error) {
    console.error('ERROR: Failed to handle event', error);
    return sendResponse(200, 'Internal Server Error');
  }
};

const sendResponse = (statusCode, message = '') => {
  return {
    statusCode,
    body: JSON.stringify({ success: true, message }),
  };
};
