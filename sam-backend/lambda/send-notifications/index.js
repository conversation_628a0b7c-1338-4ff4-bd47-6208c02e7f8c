const AWS = require('aws-sdk');
const admin = require('firebase-admin');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const {
  DynamoDBDocumentClient,
  UpdateCommand,
  GetCommand,
  <PERSON>an<PERSON>ommand,
  PutCommand,
} = require('@aws-sdk/lib-dynamodb');
const moment = require('moment');

const client = new DynamoDBClient({ region: process.env.REGION });
const docClient = DynamoDBDocumentClient.from(client);
const NOTIFICATIONS_TABLE = process.env.NOTIFICATIONS_TABLE;
const FIREBASE_TABLE = process.env.FIREBASETOKENS_TABLE;
const STORES_TABLE = process.env.STORE_TABLE;
const NOTIFICATIONS_LOGS_TABLE = process.env.NOTIFICATIONS_LOGS_TABLE;

// admin.initializeApp({
//   credential: admin.credential.cert({
//     type: 'service_account',
//     project_id: 'tsc-pos',
//     private_key_id: '0d8c25104c17935ffcbbbc7b6c862dae7409ee89',
//     private_key:
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
//     client_email: '<EMAIL>',
//     client_id: '118373773852552869343',
//     auth_uri: 'https://accounts.google.com/o/oauth2/auth',
//     token_uri: 'https://oauth2.googleapis.com/token',
//     auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
//     client_x509_cert_url:
//       'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-ceney%40tsc-pos.iam.gserviceaccount.com',
//     universe_domain: 'googleapis.com',
//   }),
// });
admin.initializeApp({
  credential: admin.credential.cert({
    type: 'service_account',
    project_id: 'pos-notifications-3c340',
    private_key_id: '12b88f76f799caf74a9a2dc71b261460fd9f37ef',
    private_key:
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    client_email:
      '<EMAIL>',
    client_id: '103630992583393903320',
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url:
      'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-ctph7%40pos-notifications-3c340.iam.gserviceaccount.com',
    universe_domain: 'googleapis.com',
  }),
});

exports.handler = async (event) => {
  console.log('event', event);
  // Handle SQS messages
  if (event.Records) {
    for (const record of event.Records) {
      const body = record;
      console.log('body', body);
      const parsedBody = JSON.parse(body.body);
      console.log('parsedBody', parsedBody);

      const data = await getNotification(parsedBody.id);
      console.log('here data', data);

      let tokens = [];
      let storeIds = [];
      if (data?.allocationType === 'ACROSS') {
        const command = new ScanCommand({
          TableName: FIREBASE_TABLE,
          ProjectionExpression: '#token, storeId',
          ExpressionAttributeNames: {
            '#token': 'token',
          },
        });
        console.log('command', command);
        const { Items } = await docClient.send(command);
        console.log('Items', Items);

        tokens = Items ? Items.map((item) => item.token) : [];
        const response = await scanTable();
        storeIds = response.map((item) => item.id);
      } else {
        const command = new ScanCommand({
          TableName: FIREBASE_TABLE,
          ProjectionExpression: '#token, storeId',
          ExpressionAttributeNames: {
            '#token': 'token',
          },
        });
        console.log('command', command);
        const { Items } = await docClient.send(command);
        storeIds = data.stores;

        if (Items) {
          tokens = Items.filter((item) =>
            data.stores.includes(item.storeId),
          ).map((item) => item.token);
        }
      }

      // Send notifications
      await sendMulticastNotification(
        tokens,
        data?.header,
        JSON.stringify(data),
      );
      console.log('storeIds', storeIds);
      // Update notification status to "sent"
      await updateNotificationStatus(parsedBody.id, storeIds);

      // Delete the SQS message
      const sqs = new AWS.SQS();
      const deleteParams = {
        QueueUrl: process.env.NOTIFICATION_QUEUE,
        ReceiptHandle: body.receiptHandle,
      };
      await sqs.deleteMessage(deleteParams).promise();
      console.log(`Deleted message from queue with ID: ${parsedBody.id}`);

      await createNotificationLog(storeIds, data);
    }

    return {
      statusCode: 200,
      body: 'Messages processed successfully',
    };
  }

  return {
    statusCode: 400,
    body: 'Missing event records',
  };
};

const getNotification = async (id) => {
  const command = new GetCommand({
    TableName: NOTIFICATIONS_TABLE,
    Key: { id },
  });

  const { Item } = await docClient.send(command);
  return Item || [];
};

const scanTable = async (nextToken = null) => {
  const params = {
    TableName: STORES_TABLE,
    ProjectionExpression: 'id, isActive',
  };

  if (nextToken) params.ExclusiveStartKey = nextToken;

  const command = new ScanCommand(params);
  const { Items, LastEvaluatedKey } = await docClient.send(command);

  const activeItems = Items.filter((item) => item.isActive === true);

  if (LastEvaluatedKey) {
    const moreData = await scanTable(LastEvaluatedKey);
    return [
      ...activeItems,
      ...moreData.filter((item) => item.isActive === true),
    ];
  }

  return activeItems;
};

const updateNotificationStatus = async (id, storeIds) => {
  try {
    const command = new UpdateCommand({
      TableName: NOTIFICATIONS_TABLE,
      Key: { id },
      UpdateExpression:
        'SET #notificationStatus = :notificationStatus , #stores = :stores',
      ExpressionAttributeNames: {
        '#notificationStatus': 'notificationStatus',
        '#stores': 'stores',
      },
      ExpressionAttributeValues: {
        ':notificationStatus': 'SENT',
        ':stores': storeIds,
      },
    });

    const response = await docClient.send(command);
    console.log('Update successful:', response);
    return response;
  } catch (error) {
    console.error('Error updating notification status:', error);
    throw error;
  }
};

async function sendMulticastNotification(tokens, title, body) {
  try {
    const message = {
      notification: {
        title: title,
        body: body,
      },
      tokens: tokens,
    };

    const response = await admin.messaging().sendEachForMulticast(message);
    console.log('Successfully sent messages:', response);

    if (response.failureCount > 0) {
      const failedTokens = [];
      response.responses.forEach((resp, idx) => {
        if (!resp.success) {
          failedTokens.push(tokens[idx]);
        }
      });
      console.log('List of tokens that caused failures: ', failedTokens);
    }
  } catch (error) {
    console.error('Error sending message:', error);
  }
}

const createNotificationLog = async (storeIds, notificationData) => {
  console.log('notificationData', notificationData?.id);
  console.log('creating log', storeIds);

  for (const item of storeIds) {
    const logItem = {
      storeId: item,
      notificationId: notificationData?.id,
      read: false,
      clickCounts: 0,
      header: notificationData?.header,
      description: notificationData?.description,
      notificationtype: notificationData?.notificationtype,
      triggerTime: notificationData?.triggerTime,
      link: notificationData?.link,
      highAlert: notificationData?.highAlert,
      pinned: notificationData?.pinned,
      pinnedContent: notificationData?.pinnedContent,
      pinnedStartTime: notificationData?.pinnedStartTime,
      pinnedEndTime: notificationData?.pinnedEndTime,
      createdAt: moment().toISOString(),
      updatedAt: moment().toISOString(),
    };

    try {
      const command = new PutCommand({
        TableName: NOTIFICATIONS_LOGS_TABLE,
        Item: logItem,
      });

      const res = await docClient.send(command);
    } catch (error) {
      console.error(`Error creating log for storeId ${item}:`, error);
    }
  }
};
