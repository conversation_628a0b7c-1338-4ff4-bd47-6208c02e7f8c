import { unmarshall } from '@aws-sdk/util-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  UpdateCommand,
  GetCommand,
} from '@aws-sdk/lib-dynamodb';
import moment from 'moment';

const client = new DynamoDBClient({
  region: process.env.REGION,
});
const docClient = DynamoDBDocumentClient.from(client);

export const handler = async (event) => {
  try {
    console.log('event,JSON.stringify(event) :>> ', JSON.stringify(event));
    if (event.Records && Array.isArray(event.Records)) {
      await Promise.all(
        event.Records?.map(async (record) => {
          const {
            dynamodb: { NewImage = {} },
          } = record;

          const item = unmarshall(NewImage);
          const {
            transactionsCovered,
            status,
            handoverId,
            handoverDate,
            id,
            storeId,
          } = item;

          console.log('status :>> ', status);

          if (status !== 'COLLECTED') return;

          console.log('handoverId :>> ', handoverId, id);

          if (transactionsCovered && transactionsCovered.length) {
            for (const { orderId, transactionId } of transactionsCovered) {
              //Update Payment table
              await docClient.send(
                new UpdateCommand({
                  TableName: process.env.PAYMENTS_TABLE,
                  Key: {
                    orderId,
                    transactionId,
                  },
                  UpdateExpression:
                    'SET updatedAt = :updatedAt, handoverId = :handoverId, handoverDate = :handoverDate, cmsId = :cmsId, handoverStatus = :handoverStatus',
                  ExpressionAttributeValues: {
                    ':handoverStatus': status,
                    ':handoverId': handoverId,
                    ':handoverDate': handoverDate,
                    ':cmsId': id,
                    ':updatedAt': moment().toISOString(),
                  },
                }),
              );

              //Update Orders Table
              let {
                Item: { transactions },
              } = await docClient.send(
                new GetCommand({
                  TableName: process.env.ORDERS_TABLE,
                  Key: {
                    id: orderId,
                  },
                  ProjectionExpression: 'transactions',
                }),
              );

              if (
                transactions &&
                transactions.transactionDetails &&
                transactions.transactionDetails.length
              ) {
                const transactionDetails = transactions.transactionDetails.map(
                  (t) => {
                    if (t.transactionId === transactionId) {
                      return {
                        ...t,
                        cmsId: id,
                        handoverId,
                        handoverDate,
                        handoverStatus: status,
                        updatedAt: moment().toISOString(),
                      };
                    }
                    return t;
                  },
                );

                await docClient.send(
                  new UpdateCommand({
                    TableName: process.env.ORDERS_TABLE,
                    Key: {
                      id: orderId,
                    },
                    UpdateExpression:
                      'SET transactions = :transactions, updatedAt = :updatedAt',
                    ExpressionAttributeValues: {
                      ':transactions': {
                        ...transactions,
                        transactionDetails,
                      },
                      ':updatedAt': moment().toISOString(),
                    },
                  }),
                );
              }
            }
          }

          const { Item } = await docClient.send(
            new GetCommand({
              TableName: process.env.LOCAL_CONFIGURATION_TABLE,
              Key: { storeId, key: 'CMS_LAST_HANDOVER_DATE' },
            }),
          );

          await docClient.send(
            new UpdateCommand({
              TableName: process.env.LOCAL_CONFIGURATION_TABLE,
              Key: {
                storeId,
                key: 'CMS_LAST_HANDOVER_DATE',
              },
              UpdateExpression:
                'SET #value = :value, updatedAt = :updatedAt, createdAt = :createdAt',
              ExpressionAttributeNames: {
                '#value': 'value',
              },
              ExpressionAttributeValues: {
                ':value': Number(Item?.value || 0) + 1,
                ':createdAt': moment().toISOString(),
                ':updatedAt': moment().toISOString(),
              },
            }),
          );
        }),
      );
    }
  } catch (e) {
    console.log('error', e);
    return;
  } finally {
    return;
  }
};
