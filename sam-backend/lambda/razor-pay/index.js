// import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
// import { DynamoDBDocumentClient, UpdateCommand } from '@aws-sdk/lib-dynamodb';
// import moment from 'moment';
// import { validatePaymentVerification } from 'razorpay/dist/utils/razorpay-utils';
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const {
  DynamoDBDocumentClient,
  UpdateCommand,
  GetCommand,
} = require('@aws-sdk/lib-dynamodb');
const moment = require('moment/moment');
const {
  validatePaymentVerification,
} = require('razorpay/dist/utils/razorpay-utils');
const btoa = require('btoa');

const client = new DynamoDBClient({
  region: process.env.REGION,
});
const docClient = DynamoDBDocumentClient.from(client);

module.exports.handler = async (event) => {
  console.log('Incoming event', JSON.stringify(event));

  const { queryStringParameters = {} } = event;
  if (!queryStringParameters || !Object.keys(queryStringParameters).length) {
    console.error(
      'ERROR: Query Parameters not available',
      queryStringParameters,
    );
    return {
      status: false,
      msg: 'Update not completed due to empty query strings.',
    };
  }

  const {
    razorpay_payment_id: paymentID = false,
    razorpay_payment_link_id: linkID = false,
    razorpay_payment_link_reference_id: referenceID = false,
    razorpay_payment_link_status: linkStatus = false,
    razorpay_signature: linkSignature = false,
    order_id: orderId = false,
    transaction_id: transactionId = false,
  } = queryStringParameters;

  if (!paymentID || !linkID || !linkStatus || !linkSignature) {
    console.error(
      'ERROR: One of the query parameter is not present',
      queryStringParameters,
    );
    return {
      status: false,
      msg: 'Update not completed due to one of the missing query string param.',
    };
  }
  const result = await verifyRazorPayPaymentSignature(
    paymentID,
    linkID,
    linkStatus,
    linkSignature,
  );
  console.log('Result', result);

  if (result.status === false) {
    console.log(result.msg);
    return result;
  }

  const updateResult = await updateTransactionStatus(
    orderId,
    transactionId,
    'COMPLETED',
    paymentID,
  );
  if (updateResult.status === false) {
    console.log(updateResult.msg);
    return updateResult;
  }

  const getCommand = new GetCommand({
    TableName: process.env.ORDERS_TABLE,
    Key: { id: orderId },
    ProjectionExpression: 'isFirstTimeVisit',
  });

  const { Item: orderData } = await docClient.send(getCommand);

  if (orderData) {
    const { isFirstTimeVisit } = orderData;

    const encodedQuery = btoa(
      JSON.stringify({ id: orderId, isFirstTimeVisit }),
    );

    console.log('encodedQuery', encodedQuery);

    return {
      statusCode: 301,
      headers: {
        Location: `${process.env.REDIRECT_URL}?q=${encodedQuery}`,
      },
    };
  } else {
    console.error('ERROR: Order data not found');
    return {
      statusCode: 301,
      status: false,
      msg: 'Order data not found',
    };
  }
};

const verifyRazorPayPaymentSignature = async (
  paymentID,
  linkID,
  linkStatus,
  linkSignature,
) => {
  console.log(
    'Verifying razorpay payment signature with -> ',
    paymentID,
    linkID,
    linkStatus,
    linkSignature,
  );
  const verificationResult = validatePaymentVerification(
    {
      payment_link_id: linkID,
      payment_id: paymentID,
      payment_link_reference_id: '',
      payment_link_status: linkStatus,
    },
    linkSignature,
    process.env.RAZORPAY_SECRET,
  );

  if (!verificationResult) {
    console.log('Payment verification failed', verificationResult);
    return {
      status: false,
      msg: 'Failed to verify RazorPay payment signature',
    };
  }

  console.log('Payment verified', verificationResult);
  return {
    status: true,
    msg: 'Payment verified',
  };
};

const updateTransactionStatus = async (
  orderId,
  transactionId,
  status,
  paymentID,
) => {
  console.log('Updating Transaction status for -> ', orderId, transactionId);
  const itemUpdateCommand = new UpdateCommand({
    TableName: process.env.PAYMENTS_TABLE,
    Key: {
      orderId,
      transactionId,
    },
    UpdateExpression:
      'SET #status = :status, #updatedAt = :updatedAt, paymentID = :paymentID',
    ExpressionAttributeNames: {
      '#status': 'status',
      '#updatedAt': 'updatedAt',
    },
    ExpressionAttributeValues: {
      ':status': status,
      ':updatedAt': moment().toISOString(),
      ':paymentID': paymentID,
    },
    ConditionExpression:
      'attribute_exists(orderId) AND attribute_exists(transactionId)',
    ReturnValues: 'ALL_NEW',
  });

  console.log('Update payment query before execution', itemUpdateCommand);
  const { Attributes } = await docClient.send(itemUpdateCommand);
  if (!Attributes || !Object.keys(Attributes).length) {
    console.error('ERROR: Failed to update transaction status');
    return {
      status: false,
      msg: 'Failed to update transaction status',
    };
  }

  console.log('Transaction entry updated', Attributes);
  return {
    status: true,
    msg: 'Transaction record updated!',
  };
};
