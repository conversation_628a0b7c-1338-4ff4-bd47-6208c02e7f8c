const AWS = require('aws-sdk');

const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');
const moment = require('moment/moment');
const dynamoDb = new AWS.DynamoDB.DocumentClient();

const sqsClient = new SQSClient({ region: process.env.REGION });
const NOTIFICATIONS_TABLE = process.env.NOTIFICATIONS_TABLE;

exports.handler = async (event) => {
  try {
    const currentISTTime = moment().utcOffset('+05:30'); // Current time in IST
    const futureTime = currentISTTime.add(15, 'minutes').format(); // Adding 15 minutes in IST

    console.log('futureTime', futureTime);
    // Scan the Notifications Table with specific conditions
    const params = {
      TableName: NOTIFICATIONS_TABLE,
      FilterExpression:
        'isDisabled = :isDisabled AND notificationStatus = :notificationStatus AND triggerTime <= :futureTime',
      ExpressionAttributeValues: {
        ':isDisabled': false,
        ':notificationStatus': 'SCHEDULED',
        ':futureTime': futureTime,
      },
    };

    const result = await dynamoDb.scan(params).promise();
    const notificationsToProcess = result.Items;
    console.log('notifications to send ', notificationsToProcess);

    for (const notification of notificationsToProcess) {
      // Check and send the notification
      console.log('notification', notification?.triggerTime);
      const triggerTime = moment(notification?.triggerTime).toISOString();
      const currentTimeMoment = moment().toISOString();
      let delay = moment(triggerTime).diff(currentTimeMoment, 'seconds');
      console.log('delay', delay);
      const sendMessageParams = {
        QueueUrl: process.env.NOTIFICATION_QUEUE,
        MessageBody: JSON.stringify({ id: notification?.id }),
        DelaySeconds: delay < 0 ? 0 : delay,
      };
      const command = new SendMessageCommand(sendMessageParams);
      const responseSQS = await sqsClient.send(command);
      console.log('responseSQS', responseSQS);
    }

    return { statusCode: 200, body: 'Notifications processed' };
  } catch (error) {
    console.error(error);
    return { statusCode: 500, body: 'An error occurred' };
  }
};
