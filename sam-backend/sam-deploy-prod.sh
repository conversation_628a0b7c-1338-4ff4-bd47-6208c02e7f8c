sam build && sam deploy --profile tsc-pos --config-env prod --parameter-overrides 'ParameterKey=OpenSearchUsername,ParameterValue=admin' 'ParameterKey=OpenSearchPassword,ParameterValue=Admin@1234' 'ParameterKey=ShopifyAccessToken,ParameterValue=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' 'ParameterKey=ShopifyAdminBaseUrl,ParameterValue=https://thesleepcompanystore.myshopify.com/admin/api/2024-01' 'ParameterKey=WishlistAccessToken,ParameterValue=KPwkS7MFb71KXrezuxUYJaf4EnFGvgkK9kgWaf7q' 'ParameterKey=WishlistAPIUrl,ParameterValue=https://api.thesleepcompany.in/wishlist' 'ParameterKey=RazorPayAppKey,ParameterValue=cfbf0178-9a06-428d-b7ce-5db521914152' 'ParameterKey=RazorPayAppUsername,ParameterValue=9930447498' 'ParameterKey=RazorPayAppUrl,ParameterValue=https://www.ezetap.com/api/3.0/p2padapter' 'ParameterKey=RazorPayAPIKey,ParameterValue=***********************' 'ParameterKey=RazorPayAPISecret,ParameterValue=GDv4bZufMykSaOKTk3U2yd4D'  'ParameterKey=OpensearchInstanceType,ParameterValue=m6g.xlarge.search' 'ParameterKey=OpensearchInstanceVolume,ParameterValue=40' 'ParameterKey=OpensearchInstanceCount,ParameterValue=2' 'ParameterKey=OpensearchZoneAwarenessEnabled,ParameterValue=true' 'ParameterKey=SuccessCallbackUrl,ParameterValue=https://success.thesleepcompany.in'  'ParameterKey=SnapmintAppUrl,ParameterValue=https://api.snapmint.com'  'ParameterKey=SnapmintAppRequestUrl,ParameterValue=https://super-apis.snapmint.com' 'ParameterKey=SnapmintFailureUrl,ParameterValue=https://thesleepcompany.in/' 'ParameterKey=LeadsquaredAccessKey,ParameterValue=u%24r3f8f6b578c93f2d348094733191527e8' 'ParameterKey=LeadsquaredSecretKey,ParameterValue=524000eef0227ce5479ef4383d98e07ed283923d' 'ParameterKey=PineLabsAppUrl,ParameterValue=https://www.plutuscloudservice.in:8201/API/CloudBasedIntegration/V1' 'ParameterKey=PineLabsSecurityToken,ParameterValue=02d388ae-bad1-41a7-b4f7-5432f30f8712' 'ParameterKey=PineLabsMerchantId,ParameterValue=650141' 'ParameterKey=PineLabsStoreId,ParameterValue=1103760' 'ParameterKey=PayUTokenEndpoint,ParameterValue=https://accounts.payu.in/oauth/token' 'ParameterKey=PayUUpdationLink,ParameterValue=https://oneapi.payu.in/payment-links' 'ParameterKey=PayUVerifyPayment,ParameterValue=https://info.payu.in/merchant/postservice?form=2' 'ParameterKey=PayUMerchantId,ParameterValue=160565' 'ParameterKey=PayUClientId,ParameterValue=9c82c52a0278489e8220fae36ea3435591d1418ecc9d1252b0b1051afe1d7057' 'ParameterKey=PayUClientSecret,ParameterValue=978871752e39fa9518650de4224a2d9ad164ee9e520191c8f8411f38ce0e68c3' 'ParameterKey=PayUKey,ParameterValue=z6VP03' 'ParameterKey=PayUSalt,ParameterValue=EWRcq4f4' 'ParameterKey=BEBaseApi,ParameterValue=https://pos-api.thesleepcompany.in' 'ParameterKey=MediaBaseApi,ParameterValue=https://pos-media.thesleepcompany.in' 'ParameterKey=MswipeRequestUrl,ParameterValue=https://ap.Mswipeota.com/JF' 'ParameterKey=MswipeVerificationBaseUrl,ParameterValue=https://www.mswipetech.com/verificationapi/api/VerificationApi/MswipeCardSaleVerificationApi' 'ParameterKey=MswipeRequestClientCode,ParameterValue=9401166288' 'ParameterKey=MswipeVerificationClientCode,ParameterValue=COMFORTGRIDTECHNOLOGIESPVTLTD' 'ParameterKey=MswipeUserID,ParameterValue=9401166288@SOL' 'ParameterKey=MswipePassword,ParameterValue=9401166288~Ds@240701' 'ParameterKey=MswipeSalt,ParameterValue=p055UX9MDSNO9Db7KeJ8APHE8qmrGz1H' 'ParameterKey=FreshDeskApiUrl,ParameterValue=https://thesleepcompanycare.freshdesk.com' 'ParameterKey=FreshDeskApiKey,ParameterValue=********************' 'ParameterKey=FreshDeskPassword,ParameterValue=X' 'ParameterKey=FreshDeskGroupId,ParameterValue=84000291010' 'ParameterKey=EasyEcomEmail,ParameterValue=<EMAIL>' 'ParameterKey=EasyEcomPassword,ParameterValue=Sleep@2024' 'ParameterKey=EasyEcomBaseUrl,ParameterValue=https://api.easyecom.io' 'ParameterKey=SlackWebhookUrl,ParameterValue=*********************************************************************************' 'ParameterKey=ApiGatewayUrl,ParameterValue=https://pos-event.thesleepcompany.in' 'ParameterKey=ReplacementBaseURL,ParameterValue=https://gybqro5fdj.execute-api.ap-south-1.amazonaws.com/prod' 'ParameterKey=ReplacementAPIKey,ParameterValue=PpuHE5OWtR3xYmZTZWRBwaa3ogMvAumX8kWc4Alm' 'ParameterKey=AuthToken,ParameterValue=57631294-erwl-7437-wtxz-0a5c7500e0df' 'ParameterKey=PaymentDetailsURL,ParameterValue=https://apis.thesleepcompany.in' 'ParameterKey=EasyEcomPrimaryLocationKey,ParameterValue=ne10776308481' 'ParameterKey=RequestModuleAPIUrl,ParameterValue=https://apis.thesleepcompany.in/order-management/request-return-replacement' 'ParameterKey=RequestModuleAPIKey,ParameterValue=REQUESTVN9b3C3c7kTVLp94LNhKmsMH5' 'ParameterKey=OTPBaseURL,ParameterValue=https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod' 'ParameterKey=SubnetIds,ParameterValue=subnet-0e7f7c675c81e6e14,subnet-0a526df13f96880a9,subnet-026f0537da9c95f36' 'ParameterKey=VpcId,ParameterValue=vpc-0ab8cde1282f70a56'
