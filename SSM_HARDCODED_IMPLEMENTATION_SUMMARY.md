# SSM Hardcoded Parameters Implementation

## Overview
To avoid SSM rate limiting issues, I've implemented a hardcoded map of all SSM parameters based on your SAM deployment command.

## What I've Added

### 1. Updated SSM Client (`pos-app/src/common/ssm-client/ssm-client.ts`)

Added three new methods:

#### `getHardcodedSSMParametersMap(stackName: string = 'pos-prod'): Record<string, string>`
Returns a complete map of all SSM parameters with their hardcoded values.

#### `getHardcodedSSMParameter(parameterName: string, stackName: string = 'pos-prod'): string`
Gets a specific parameter value from the hardcoded map.

#### `getHardcodedCognitoUserPoolId(stackName: string = 'pos-prod'): string`
Gets the cognito user pool ID specifically (the one you requested).

### 2. Complete Parameter List
Based on your SAM template and deployment command, I've included all 71 SSM parameters:

- **Cognito**: userpool_id, client_id
- **OpenSearch**: username, password, endpoint
- **Shopify**: access-token, admin-base-url
- **Wishlist**: access-token, api-url
- **RazorPay POS**: app-key, app-username, app-url
- **Direct RazorPay**: api-key, api-secret, callback-url
- **PayU POS**: All PayU parameters (token-endpoint, updation-link, etc.)
- **Backend & API Gateway**: base-url, baseurl
- **Mswipe**: All Mswipe parameters
- **FreshDesk**: api-url, api-key, password, group-id
- **EasyEcom**: email, password, base-url, locationKey
- **Slack**: webhook
- **Replacement**: url, apiKey
- **Pine Labs**: All Pine Labs parameters
- **Snapmint**: All Snapmint parameters
- **And many more...**

## Usage Examples

### Get the specific map you requested:
```typescript
const ssmClient = new AppSsmClient(configService);

// This returns: {"/pos-prod/cognito/userpool_id": "ap-south-1_Y3OJo6T4R"}
const cognitoMap = {
  [`/pos-prod/cognito/userpool_id`]: ssmClient.getHardcodedCognitoUserPoolId('pos-prod')
};
```

### Get any specific parameter:
```typescript
const userPoolId = ssmClient.getHardcodedSSMParameter('/pos-prod/cognito/userpool_id', 'pos-prod');
const razorpayKey = ssmClient.getHardcodedSSMParameter('/pos-prod/direct-razorpay/api-key', 'pos-prod');
```

### Get all parameters at once:
```typescript
const allParams = ssmClient.getHardcodedSSMParametersMap('pos-prod');
console.log(allParams['/pos-prod/cognito/userpool_id']); // "ap-south-1_Y3OJo6T4R"
```

## Migration from SSM API calls

### Before (with SSM API calls):
```typescript
const {
  Parameter: { Value: userPoolId },
} = await this.ssmClient.getSSMParamByKey(
  new GetParameterCommand({
    Name: `/${this.stackName}/cognito/userpool_id`,
    WithDecryption: true,
  }),
);
```

### After (hardcoded, no rate limiting):
```typescript
const userPoolId = this.ssmClient.getHardcodedCognitoUserPoolId(this.stackName);
```

## Benefits

1. **No Rate Limiting**: No SSM API calls = no rate limiting issues
2. **Faster Performance**: Instant parameter retrieval
3. **Offline Development**: Works without AWS credentials
4. **Predictable Values**: All values are known at compile time
5. **Easy Maintenance**: All parameters in one place

## Files Created/Modified

1. **Modified**: `pos-app/src/common/ssm-client/ssm-client.ts` - Added hardcoded methods
2. **Created**: `pos-app/src/common/ssm-client/ssm-usage-example.ts` - Usage examples
3. **Created**: `pos-app/src/config/config-hardcoded-example.ts` - Config service example

## Important Notes

- The hardcoded values are based on your SAM deployment command
- Some values like `userpool_id` and `client_id` are set by CloudFormation and may need to be updated after deployment
- You can still use the original SSM methods if needed
- The hardcoded map uses the same parameter naming convention as your SAM template

## Your Specific Request

You wanted a map where the key is `/pos-prod/cognito/userpool_id`. Here's exactly what you get:

```typescript
const map = {
  "/pos-prod/cognito/userpool_id": "ap-south-1_Y3OJo6T4R"
};
```

This is now available through:
```typescript
const cognitoUserPoolIdMap = ssmClient.getCognitoUserPoolIdMap('pos-prod');
// Returns: {"/pos-prod/cognito/userpool_id": "ap-south-1_Y3OJo6T4R"}
```
