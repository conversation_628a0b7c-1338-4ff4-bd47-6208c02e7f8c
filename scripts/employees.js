const AWS = require('aws-sdk');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const TABLE = 'pos-stage-employee-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const codes = [
  {
    id: 'TSCH0689',
    firstName: 'Pravin Vijay',
    lastName: 'Bhawsar',
    stores: ['TSC42'],
    designation: 'Store Manager',
    phone: '9821133538',
    email: '<EMAIL>',
  },
  {
    id: 'TSCH0837',
    firstName: 'Dhruv <PERSON><PERSON><PERSON><PERSON>',
    lastName: '<PERSON>uhan',
    stores: ['TSC42'],
    designation: 'CRE',
    phone: '7490866206',
    email: 'Dhruv51<PERSON><PERSON><PERSON><PERSON>@gmail.com',
    role: 'STAFF',
  },
  {
    id: 'TSCH0177',
    firstName: '<PERSON><PERSON><PERSON>',
    lastName: '<PERSON><PERSON>',
    stores: ['TSC01'],
    designation: 'Store Manager',
    phone: '8796527750',
    email: '<EMAIL>',
    role: 'STAFF',
  },
  {
    id: 'TSCH0246',
    firstName: 'Maaz Shakeel',
    lastName: 'Shaikh',
    stores: ['TSC01'],
    designation: 'Executive',
    phone: '9076419580',
    email: '<EMAIL>',
    role: 'STAFF',
  },
  {
    id: 'TSCH0339',
    firstName: 'Azim Amin',
    lastName: 'Sayyed',
    stores: ['TSC01'],
    designation: 'CRE',
    phone: '9326746322',
    email: '<EMAIL>',
    role: 'STAFF',
  },
  {
    id: 'TSCH0391',
    firstName: 'Prathmesh Dattaram',
    lastName: 'Shinde',
    stores: ['TSC01'],
    designation: 'CRE',
    phone: '8451807609',
    email: '<EMAIL>',
    role: 'STAFF',
  },
  {
    id: 'TSCH0636',
    firstName: 'Vikash Kumar',
    lastName: 'Mishra',
    stores: ['TSC01'],
    designation: 'CRE',
    phone: '8454058170',
    email: '<EMAIL>',
    role: 'STAFF',
  },
  {
    id: 'TSCH1066',
    firstName: 'Sandesh Parshuram',
    lastName: 'Patne',
    stores: ['TSC01'],
    designation: 'CRE',
    phone: '9821424114',
    email: '<EMAIL>',
    role: 'STAFF',
  },
];

const upload = async () => {
  await Promise.all(
    codes.map(async (Item) => {
      const params = {
        TableName: TABLE,
        Item: {
          ...Item,
          role: 'STAFF',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      };
      await db.put(params).promise();
    }),
  );
};
upload();
