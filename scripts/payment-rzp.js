const AWS = require('aws-sdk');

// Reference credentials from the file you provided
const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const PAYMENT_TABLE = 'pos-prod-payment-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

// Sample payment items array (this will come from your input)
const paymentItems = [
  {
    id: 'pay_PZM73a6BxOMI3o',
    amount: 35569,
    currency: 'INR',
    status: 'captured',
    order_id: 'order_PZM5I5R3YMhXzK',
    invoice_id: '',
    international: 0,
    method: 'upi',
    amount_refunded: 0,
    amount_transferred: 0,
    refund_status: '',
    captured: 1,
    description: '#PZM4lqA7Xnf5BF',
    card_id: '',
    card: '',
    bank: '',
    wallet: '',
    vpa: 'preetasing<PERSON>@axisbank',
    email: '<EMAIL>',
    contact: '+************',
    notes: {
      orderId: 'ITSC072425-1217',
      transactionId: '15449f2b-68d5-4229-92d4-97a41ae2af3b',
    },
    fee: 8.26,
    tax: 1.26,
    error_code: '',
    error_description: '',
    created_at: '20/12/2024 13:25:44',
    card_type: '',
    card_network: '',
    Auth_code: 'bank_account',
    Payments_ARN: '',
    Payments_RRN: ************,
    flow: 'collect',
  },
  {
    id: 'pay_PZNO9MJ9KdNt4X',
    amount: 33499,
    currency: 'INR',
    status: 'authorized',
    order_id: 'order_PZNLpfD8npVZmC',
    invoice_id: '',
    international: 0,
    method: 'card',
    amount_refunded: 0,
    amount_transferred: 0,
    refund_status: '',
    captured: 0,
    description: '#PZNKzDWTMp5Uqf',
    card_id: 'card_PZNO9VlmG2HEs8',
    card: {
      entity: 'card',
      id: 'card_PZNO9VlmG2HEs8',
      name: '',
      last4: '6007',
      network: 'MasterCard',
      type: 'credit',
      issuer: 'ICIC',
      international: false,
      emi: true,
    },
    bank: '',
    wallet: '',
    vpa: '',
    email: '<EMAIL>',
    contact: ************,
    notes: {
      orderId: 'ITSC072425-1218',
      transactionId: '60ee5444-ba09-4617-bc3d-eb56b225ad65',
    },
    fee: '',
    tax: '',
    error_code: '',
    error_description: '',
    created_at: '20/12/2024 14:40:38',
    card_type: 'credit',
    card_network: 'MasterCard',
    Auth_code: 246969,
    Payments_ARN: '',
    Payments_RRN: ************,
    flow: '',
  },
  {
    id: 'pay_PZLH8RU2TAxUNJ',
    amount: 45940,
    currency: 'INR',
    status: 'captured',
    order_id: 'order_PZLF5Bgx5MALgb',
    invoice_id: '',
    international: 0,
    method: 'upi',
    amount_refunded: 0,
    amount_transferred: 0,
    refund_status: '',
    captured: 1,
    description: '#PZLEnZlCcqPqbC',
    card_id: '',
    card: '',
    bank: '',
    wallet: '',
    vpa: '**********@ptaxis',
    email: '<EMAIL>',
    contact: '+91**********',
    notes: {
      orderId: 'ITSC622425-0321',
      transactionId: 'aa632f8a-188e-4228-a602-a151a5135093',
    },
    fee: 8.26,
    tax: 1.26,
    error_code: '',
    error_description: '',
    created_at: '20/12/2024 12:36:35',
    card_type: '',
    card_network: '',
    Auth_code: 'bank_account',
    Payments_ARN: 'PTM23508de257ca4c99bf11d6ba7f18d5eb',
    Payments_RRN: ************,
    flow: 'intent',
  },
  {
    id: 'pay_PZPHTYMg9ZryRj',
    amount: 23430,
    currency: 'INR',
    status: 'captured',
    order_id: 'order_PZPHC3vCaAfYBv',
    invoice_id: '',
    international: 0,
    method: 'upi',
    amount_refunded: 0,
    amount_transferred: 0,
    refund_status: '',
    captured: 1,
    description: '#PZPH4IWu0UwYwb',
    card_id: '',
    card: '',
    bank: '',
    wallet: '',
    vpa: 'abhinavlohia12-1@okicici',
    email: '<EMAIL>',
    contact: '+************',
    notes: {
      orderId: 'ITSC492425-0904',
      transactionId: 'c48c6db9-6650-4780-b45a-6d112a21da00',
    },
    fee: 3.54,
    tax: 0.54,
    error_code: '',
    error_description: '',
    created_at: '20/12/2024 16:31:41',
    card_type: '',
    card_network: '',
    Auth_code: 'bank_account',
    Payments_ARN: 'ICI9408f9f6874e48ae97b0b7d8f11a65d6',
    Payments_RRN: ************,
    flow: 'intent',
  },
];

const fetchTransactionAmount = async (orderId, transactionId) => {
  const params = {
    TableName: PAYMENT_TABLE,
    Key: {
      orderId,
      transactionId,
    },
  };

  try {
    const result = await db.get(params).promise();
    if (!result.Item || !result.Item.transactionAmount) {
      throw new Error(
        `TransactionAmount not found for orderId: ${orderId}, transactionId: ${transactionId}`,
      );
    }
    return result.Item.transactionAmount;
  } catch (error) {
    console.error(
      `Error fetching transactionAmount for orderId: ${orderId}, transactionId: ${transactionId}`,
      error,
    );
    throw error;
  }
};

const updatePaymentRecord = async (payment) => {
  if (!payment.id || !payment.notes) {
    throw new Error('Invalid payment data: Missing id or notes');
  }

  const { orderId, transactionId } = payment.notes;

  // Fetch transactionAmount from the table
  const transactionAmount = await fetchTransactionAmount(
    orderId,
    transactionId,
  );

  const now = new Date().toISOString(); // Current date in ISO string format

  const params = {
    TableName: PAYMENT_TABLE,
    Key: {
      orderId,
      transactionId,
    },
    UpdateExpression:
      'SET #status = :status, #transactionCompletedDate = :transactionCompletedDate, #updatedAt = :updatedAt, #paymentID = :paymentID, #amountPaid = :amountPaid',
    ExpressionAttributeValues: {
      ':status': 'COMPLETED',
      ':transactionCompletedDate': now,
      ':updatedAt': now,
      ':paymentID': payment.id,
      ':amountPaid': Number(transactionAmount), // Use transactionAmount from table
    },
    ExpressionAttributeNames: {
      '#status': 'status',
      '#transactionCompletedDate': 'transactionCompletedDate',
      '#updatedAt': 'updatedAt',
      '#paymentID': 'paymentID',
      '#amountPaid': 'amountPaid',
    },
    ConditionExpression:
      'attribute_exists(orderId) AND attribute_exists(transactionId)', // Ensure record exists
  };

  try {
    await db.update(params).promise();
    console.log(
      `Payment record for order ${orderId} and transaction ${transactionId} updated successfully.`,
    );
  } catch (error) {
    console.error(
      `Error updating payment record for order ${orderId} and transaction ${transactionId}: `,
      error,
    );
  }
};

const handler = async () => {
  for (const payment of paymentItems) {
    try {
      await updatePaymentRecord(payment);
    } catch (error) {
      console.error(`Error processing payment: `, error);
    }
  }
};

handler();
