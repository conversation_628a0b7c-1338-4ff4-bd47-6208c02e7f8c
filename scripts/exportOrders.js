const AWS = require('aws-sdk');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const TABLE = 'pos-prod-order-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const upload = async () => {
  const params = {
    TableName: TABLE,
    ProjectionExpression: 'id, createdAt, orderProducts, updatedAt',
  };
  const data = (await db.scan(params).promise()).Items.filter((item) =>
    item.orderProducts.some((o) => !o.id),
  );

  console.log(JSON.stringify(data, null, 2));
};
upload();
