const AWS = require('aws-sdk');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const STORE_TABLE = 'pos-prod-order-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

// Helper function to get timestamp for 12 PM IST
const get12PMISTTimestamp = () => {
  const now = new Date();
  now.setHours(6, 30, 0, 0); // Convert to 12 PM IST (UTC + 5:30)
  return now.toISOString();
};

const scanOrdersBefore12PMIST = async (nextToken = null) => {
  const params = {
    TableName: STORE_TABLE,
    ProjectionExpression: 'id, orderType',
    FilterExpression: '#createdAt < :timestamp',
    ExpressionAttributeNames: {
      '#createdAt': 'createdAt', // Adjust field name based on your schema
    },
    ExpressionAttributeValues: {
      ':timestamp': get12PMISTTimestamp(),
    },
  };

  if (nextToken) params.ExclusiveStartKey = nextToken;

  const { Items, LastEvaluatedKey } = await db.scan(params).promise();

  if (LastEvaluatedKey) {
    const moreData = await scanOrdersBefore12PMIST(LastEvaluatedKey);
    return [...Items, ...moreData];
  }

  return Items;
};

const handler = async () => {
  const orders = await scanOrdersBefore12PMIST();
  const filteredOrders = orders.filter(({ orderType }) => orderType !== 'POS');

  console.log('filteredOrders', filteredOrders[0], filteredOrders?.length);
  // for (const { id } of orders) {
  //   console.log('Updating order ID:>> ', id);

  // await db
  //   .update({
  //     TableName: STORE_TABLE,
  //     Key: { id },
  //     UpdateExpression: 'SET #orderType = :orderType',
  //     ExpressionAttributeNames: {
  //       '#orderType': 'orderType',
  //     },
  //     ExpressionAttributeValues: {
  //       ':orderType': 'POS',
  //     },
  //     ConditionExpression: 'attribute_exists(id)',
  //   })
  //   .promise()
  //   .then((res) => {
  //     console.log('Order ID updated:>> ', id);
  //   })
  //   .catch((error) => {
  //     console.log('Error Order ID:>> ', error);
  //   });
  //  }
};

handler();
