const AWS = require('aws-sdk');
const axios = require('axios');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const TABLE = 'pos-prod-pincode-table';
const SHEET_ID = '1C3WgqoetBHV4I1O7fXIfYwFQEvMQ4kuC0-4UhSzl5U8';
const SHEET_NAME = 'Sheet4';
const API_KEY = 'AIzaSyDeFSOTDCsgAjdXMKvzEnCTqZbp4MMbTt0';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const fetchDataFromGoogleSheets = async () => {
  const apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${SHEET_ID}/values/${SHEET_NAME}?key=${API_KEY}`;

  try {
    const response = await axios.get(apiUrl);
    const data = response.data;

    if (Array.isArray(data.values)) {
      const { values } = data;

      const headerRow = values.find((row) => {
        return row.some((cell) =>
          ['pincodes', 'Average SLA', 'Applicability'].includes(cell),
        );
      });

      if (!headerRow) {
        console.log('No header row found in the spreadsheet.');
        return [];
      }

      const headerIndex = values.indexOf(headerRow);

      const headerMap = new Map();
      headerRow.forEach((headerField, index) => {
        let fieldName = null;
        switch (headerField) {
          case 'pincodes':
            fieldName = 'pincode';
            break;
          case 'Average SLA':
            fieldName = 'SLA';
            break;
          case 'Applicability':
            fieldName = 'applicability';
            break;
        }

        if (fieldName) {
          headerMap.set(fieldName, index);
        }
      });

      const stores = values.slice(headerIndex + 1).map((row) => {
        const storeMap = {};
        headerMap.forEach((index, fieldName) => {
          storeMap[fieldName] = row[index] || '';
        });
        return storeMap;
      });
      return stores;
    }
  } catch (error) {
    console.error('Error fetching or processing spreadsheet data:', error);
  }
  return [];
};

const handler = async () => {
  const pincodeData = await fetchDataFromGoogleSheets();
  await Promise.all(
    pincodeData.map(async (item, index) => {
      const { pincode, SLA, applicability } = item;
      await db
        .update({
          TableName: TABLE,
          Key: { id: pincode },
          UpdateExpression: 'set SLA = :sla, applicability = :applicability',
          ExpressionAttributeValues: {
            ':sla': SLA || '0',
            ':applicability': applicability || 'Non Serviceable',
          },
          ReturnValues: 'UPDATED_NEW',
        })
        .promise()
        .then((res) => {
          'Updated', index;
        });
    }),
  );
};
handler();
