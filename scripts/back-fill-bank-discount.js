const AWS = require('aws-sdk');
const axios = require('axios');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const PAYMENT_TABLE = 'pos-prod-payment-table';
const ORDER_TABLE = 'pos-prod-order-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const getISTTimestamp = () => {
  const istDate = new Date('2024-08-01T00:00:00Z');
  istDate.setHours(istDate.getHours() + 5, istDate.getMinutes() + 30);
  return istDate.toISOString();
};

const scanPayments = async (nextToken = null) => {
  const params = {
    TableName: PAYMENT_TABLE,
    ProjectionExpression:
      'orderId, transactionId, paymentID, createdAt, #status',
    FilterExpression:
      '#status = :completed AND #mode = :razorpay AND #createdAt > :timestamp',
    ExpressionAttributeNames: {
      '#status': 'status',
      '#mode': 'mode',
      '#createdAt': 'createdAt',
    },
    ExpressionAttributeValues: {
      ':completed': 'COMPLETED',
      ':razorpay': 'RAZORPAY',
      ':timestamp': getISTTimestamp(),
    },
  };

  if (nextToken) {
    params.ExclusiveStartKey = nextToken;
  }

  const { Items, LastEvaluatedKey } = await db.scan(params).promise();

  if (LastEvaluatedKey) {
    const moreData = await scanPayments(LastEvaluatedKey);
    return [...Items, ...moreData];
  }

  return Items;
};

const updateOrderTable = async (orderId, transactionId, amountPaid) => {
  try {
    const { Item } = await db
      .get({
        TableName: ORDER_TABLE,
        Key: { id: orderId },
        ProjectionExpression: 'transactions',
      })
      .promise();

    const transactions = Item?.transactions || {};
    const transactionDetails = transactions.transactionDetails || [];

    const index = transactionDetails?.findIndex(
      (transaction) => transaction.transactionId === transactionId,
    );

    if (index !== -1) {
      transactionDetails[index].amountPaid = amountPaid;
    } else {
      console.log(
        `Transaction with transactionId: ${transactionId} not found for orderId: ${orderId}`,
      );
      return;
    }
    await db
      .update({
        TableName: ORDER_TABLE,
        Key: { id: orderId },
        UpdateExpression:
          'SET transactions.transactionDetails = :transactionDetails',
        ExpressionAttributeValues: {
          ':transactionDetails': transactionDetails,
        },
        ConditionExpression: 'attribute_exists(id)',
      })
      .promise();

    console.log(
      `Order table updated for orderId: ${orderId}, transactionId: ${transactionId}`,
    );
  } catch (error) {
    console.error(
      `Error updating order table for orderId: ${orderId}, transactionId: ${transactionId}`,
      error,
    );
  }
};

const handler = async () => {
  let payments = await scanPayments();
  console.log('Total payments fetched:', payments.length);
  //payments = payments?.slice(1, 3);
  //console.log('Total payments to update:', payments.length);
  for (const [index, payment] of payments.entries()) {
    const { orderId, transactionId, paymentID } = payment;

    try {
      const username = '***********************';
      const password = 'GDv4bZufMykSaOKTk3U2yd4D';
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      const url = `https://api.razorpay.com/v1/payments/${paymentID}/?expand[]=offers`;

      const response = await axios.get(url, {
        headers: {
          Authorization: `Basic ${auth}`,
        },
      });

      const data = response.data;
      const paidAmount = data?.amount ? Number(data?.amount) / 100 : 0;
      console.log(
        `${index} Razorpay API response: transactionId: ${transactionId}, orderId: ${orderId}`,
        paidAmount,
      );
      if (paidAmount > 0) {
        await db
          .update({
            TableName: PAYMENT_TABLE,
            Key: { orderId, transactionId },
            UpdateExpression: 'SET #amountPaid = :amountPaid',
            ExpressionAttributeNames: {
              '#amountPaid': 'amountPaid',
            },
            ExpressionAttributeValues: {
              ':amountPaid': paidAmount,
            },
            ConditionExpression:
              'attribute_exists(orderId) AND attribute_exists(transactionId)',
          })
          .promise();

        console.log(
          `${index} Payment table updated for transactionId: ${transactionId}`,
        );

        await updateOrderTable(orderId, transactionId, paidAmount);
      } else {
        console.error(
          `${index} Error processing paid is 0 -> transactionId: ${transactionId}, orderId: ${orderId}`,
          error,
        );
      }
    } catch (error) {
      console.error(
        `${index} Error processing transactionId: ${transactionId}, orderId: ${orderId}`,
        error,
      );
    }
  }
};

handler();
