const axios = require('axios');
const converter = require('json-2-csv');

// Function to fetch discount codes recursively
async function fetchDiscountCodes(cursor = null) {
  const query = cursor
    ? `
        {
            priceRules(first: 250, after: "${cursor}") {
                edges {
                    cursor
                    node {
                        title
                        discountCodesCount
                        usageCount
                        status
                    }
                }
            }
        }
    `
    : `
    {
        priceRules(first: 250) {
            edges {
                cursor
                node {
                    title
                    discountCodesCount
                    usageCount
                    status
                }
            }
        }
    }
`;

  try {
    const res = await fetch(
      'https://foxtale-consumer.myshopify.com/admin/api/2024-01/graphql.json',
      {
        method: 'POST',
        headers: {
          'X-Shopify-Access-Token': '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',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      },
    );

    const response = await res.json();

    console.log('response :>> ', response);

    const { data } = response;
    const priceRules = data.priceRules.edges;

    let discountCodes = [];

    for (const rule of priceRules) {
      discountCodes = [...discountCodes, rule.node];
    }

    return {
      cursor: data.priceRules.edges.length
        ? data.priceRules.edges[data.priceRules.edges.length - 1].cursor
        : null,
      discountCodes,
    };
  } catch (error) {
    console.error('Error fetching discount codes:', error);
    throw error;
  }
}

// Main function to fetch and update discount codes
async function fetchAndStoreDiscountCodes(sheetId) {
  try {
    let cursor = null;
    let allDiscountCodes = [];

    do {
      const { cursor: nextCursor, discountCodes } =
        await fetchDiscountCodes(cursor);
      allDiscountCodes = [...allDiscountCodes, ...discountCodes];
      console.log('nextCursor :>> ', nextCursor);
      cursor = nextCursor;
    } while (cursor);

    const csvData = await converter.json2csv(allDiscountCodes, {});

    console.log(csvData);
  } catch (error) {
    console.error('Error fetching and storing discount codes:', error);
  }
}

// Replace 'YOUR_GOOGLE_SHEET_ID' with your Google Sheet ID
fetchAndStoreDiscountCodes('YOUR_GOOGLE_SHEET_ID');
