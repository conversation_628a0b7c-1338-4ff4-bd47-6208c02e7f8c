const AWS = require('aws-sdk');

const accessKeyId = '********************'; // Replace with actual access key
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W'; // Replace with actual secret key
const region = 'ap-south-1';
const CUSTOMER_SPECIFIC_COUPON_TABLE =
  'pos-stage-customer-specific-coupon-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const couponDataToUpdate = [
  {
    Phone: 8939570507,
    Coupon: 'AGL72488',
    Store: 'AnnaNagar Store',
  },
  {
    Phone: 7303296020,
    Coupon: 'AGL97H2K',
    Store: 'Indirapuram Store',
  },
  {
    Phone: 9425106687,
    Coupon: 'AGLJW748',
    Store: 'Phoenix Citadel Mall Store',
  },
  {
    Phone: 9703855387,
    Coupon: 'AGLNW6JV',
    Store: 'Tolichowki Store',
  },
  {
    Phone: 9910137652,
    Coupon: 'AGLH95F8',
    Store: 'Indirapuram Store',
  },
  {
    Phone: 8580783789,
    Coupon: 'AGLSSZJN',
    Store: 'Indirapuram Store',
  },
  {
    Phone: 9922733767,
    Coupon: 'AGLQFCJT',
    Store: 'Hinjewadi Store',
  },
  {
    Phone: 9711511050,
    Coupon: 'AGL589RQ',
    Store: 'Dwarka Sec 12 Store',
  },
  {
    Phone: 7381680577,
    Coupon: 'AGLNVJ63',
    Store: 'Bhubaneswar Store',
  },
  {
    Phone: 7972149878,
    Coupon: 'AGLH7SSC',
    Store: 'Tribeca High Street Store',
  },
  {
    Phone: 6268003162,
    Coupon: 'AGLMD5RJ',
    Store: 'Phoenix Citadel Mall Store',
  },
];

const putCouponData = async (coupon) => {
  const params = {
    TableName: CUSTOMER_SPECIFIC_COUPON_TABLE,
    Item: {
      phone: coupon.Phone.toString(),
      code: coupon.Coupon,
      store: coupon.Store,
    },
    ConditionExpression: 'attribute_not_exists(code)', // Ensure the item doesn't already exist
  };

  try {
    await db.put(params).promise();
    console.log(
      `Coupon added for phone: ${coupon.Phone}, couponCode: ${coupon.Coupon}`,
    );
  } catch (error) {
    if (error.code === 'ConditionalCheckFailedException') {
      console.warn(
        `Coupon for phone: ${coupon.Phone} already exists, skipping...`,
      );
    } else {
      console.error(
        `Error adding coupon for phone: ${coupon.Phone}`,
        error.message,
      );
    }
  }
};

const handler = async () => {
  try {
    console.log('Adding coupons to the table...');
    await Promise.all(
      couponDataToUpdate.map((coupon) => putCouponData(coupon)),
    );
    console.log('All coupons added successfully!');
  } catch (error) {
    console.error('Error adding coupons:', error.message);
  }
};

handler();
