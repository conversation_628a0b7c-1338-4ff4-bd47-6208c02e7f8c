const AWS = require('aws-sdk');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const ORDER_ITEM_TABLE = 'pos-stage-order-item-table';
const ORDER_TABLE = 'pos-stage-order-table';
const HSN_TABLE = 'pos-stage-hsn-code-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const scanOrderItemTable = async (nextToken = null) => {
  const params = {
    TableName: ORDER_ITEM_TABLE,
    FilterExpression: '#hsn = :hsn',
    ExpressionAttributeValues: {
      ':hsn': '',
    },
    ExpressionAttributeNames: {
      '#hsn': 'hsn',
    },
  };

  if (nextToken) params.ExclusiveStartKey = nextToken;

  const { Items, LastEvaluatedKey } = await db.scan(params).promise();

  if (LastEvaluatedKey) {
    const moreData = await scanOrderItemTable(LastEvaluatedKey);
    return [...Items, ...moreData];
  }

  return Items;
};

const getItem = async (id, TABLE) => {
  const params = {
    TableName: TABLE,
    Key: {
      id,
    },
  };
  const { Item } = await db.get(params).promise();
  return Item;
};

const handler = async () => {
  const orderItems = await scanOrderItemTable();
  for (const orderItem of orderItems) {
    const { sku, orderId, id } = orderItem;
    console.log('id orderId:>> ', id, orderId);

    const [hsn, order] = await Promise.all([
      getItem(sku, HSN_TABLE),
      getItem(orderId, ORDER_TABLE),
    ]);

    if (hsn) {
      await db
        .put({
          TableName: ORDER_ITEM_TABLE,
          Item: { ...orderItem, hsn: hsn.hsnCode },
        })
        .promise();

      const orderProducts = order.orderProducts.map((p) => {
        if (p.id === id) {
          return {
            ...p,
            hsn: hsn.hsnCode,
          };
        }
        return p;
      });

      await db
        .put({
          TableName: ORDER_TABLE,
          Item: { ...order, orderProducts },
        })
        .promise();
    }
  }
};

handler();
