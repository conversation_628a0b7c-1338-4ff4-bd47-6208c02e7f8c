const AWS = require('aws-sdk');
const axios = require('axios');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const TABLE = 'pos-prod-price-master-table';
const SHEET_ID = '1BFCSJxm_xmqsQ0qj3DblYPsqc86uu3h_4YuVf3V8pyE';
const SHEET_NAME = 'ProductAttribute312024112502031';
const API_KEY = 'AIzaSyDeFSOTDCsgAjdXMKvzEnCTqZbp4MMbTt0';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const fetchDataFromGoogleSheets = async () => {
  const apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${SHEET_ID}/values/${SHEET_NAME}?key=${API_KEY}`;

  try {
    const response = await axios.get(apiUrl);
    const data = response.data;

    if (Array.isArray(data.values)) {
      const { values } = data;

      const headerRow = values.find((row) => {
        return row.some((cell) =>
          [
            'CPId',
            'Status',
            'ModelNum',
            'Category',
            'SKU',
            'ProductName',
            'Description',
            'UPC',
            'Color',
            'Size',
            'MaterialType',
            'ReplacemtWindow',
            'ReturnOnly',
            'ReturnandReplacement',
          ].includes(cell),
        );
      });

      if (!headerRow) {
        console.log('No header row found in the spreadsheet.');
        return [];
      }

      const headerIndex = values.indexOf(headerRow);

      const headerMap = new Map();
      headerRow.forEach((headerField, index) => {
        let fieldName = null;
        switch (headerField) {
          case 'CPId':
            fieldName = 'CPId';
            break;
          case 'Status':
            fieldName = 'status';
            break;
          case 'ModelNum':
            fieldName = 'modelNumber';
            break;
          case 'Category':
            fieldName = 'category';
            break;
          case 'SKU':
            fieldName = 'sku';
            break;
          case 'ProductName':
            fieldName = 'productName';
            break;
          case 'Description':
            fieldName = 'description';
            break;
          case 'UPC':
            fieldName = 'upc';
            break;
          case 'Color':
            fieldName = 'color';
            break;
          case 'Size':
            fieldName = 'size';
            break;
          case 'MaterialType':
            fieldName = 'materialType';
            break;
          case 'ReplacemtWindow':
            fieldName = 'replacementWindow';
            break;
          case 'ReturnOnly':
            fieldName = 'returnOnly';
            break;
          case 'ReturnandReplacement':
            fieldName = 'returnAndReplacement';
            break;
          default:
            console.warn(`Unrecognized header field: ${headerField}`);
            break;
        }

        if (fieldName) {
          headerMap.set(fieldName, index);
        }
      });

      const stores = values.slice(headerIndex + 1).map((row) => {
        const storeMap = {};
        headerMap.forEach((index, fieldName) => {
          storeMap[fieldName] = row[index] || '';
        });
        return storeMap;
      });
      return stores;
    }
  } catch (error) {
    console.error('Error fetching or processing spreadsheet data:', error);
  }
  return [];
};

const handler = async () => {
  const sheetData = await fetchDataFromGoogleSheets();

  console.log('sheetData is', sheetData);
  await Promise.all(
    sheetData.map(async (item, index) => {
      const { sku, returnOnly, replacementWindow, returnAndReplacement } = item;
      console.log(
        'index is',
        index,
        sku?.replace('_EASY', ''),
        returnOnly,
        replacementWindow,
        returnAndReplacement,
      );
      await db
        .update({
          TableName: TABLE,
          Key: { id: sku?.replace('_EASY', '') },
          UpdateExpression:
            'SET #replacementShippingCost = :replacementShippingCost ,  #returnShippingCost = :returnShippingCost ,  #replacementWindow = :replacementWindow ,  #updatedAt = :updatedAt',
          ExpressionAttributeValues: {
            ':replacementShippingCost': returnAndReplacement,
            ':returnShippingCost': returnOnly,
            ':replacementWindow': replacementWindow,
            ':updatedAt': new Date().toISOString(),
          },
          ExpressionAttributeNames: {
            '#replacementShippingCost': 'replacementShippingCost',
            '#returnShippingCost': 'returnShippingCost',
            '#replacementWindow': 'replacementWindow',
            '#updatedAt': 'updatedAt',
          },
          ConditionExpression: 'attribute_exists(id)',
        })
        .promise()
        .then(() => {
          console.log('Item updated successfully');
        })
        .catch((error) => {
          console.error('Error updating item:', error);
        });
    }),
  );
};
handler();
