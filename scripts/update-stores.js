const AWS = require('aws-sdk');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const STORE_TABLE = 'pos-prod-store-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const scanOrderTable = async (nextToken = null) => {
  const params = {
    TableName: STORE_TABLE,
  };

  if (nextToken) params.ExclusiveStartKey = nextToken;

  const { Items, LastEvaluatedKey } = await db.scan(params).promise();

  if (LastEvaluatedKey) {
    const moreData = await scanOrderTable(LastEvaluatedKey);
    return [...Items, ...moreData];
  }

  return Items;
};

const handler = async () => {
  const orders = await scanOrderTable();
  for (const order of orders) {
    const { id } = order;
    console.log('id storeID:>> ', id);

    await db
      .update({
        TableName: STORE_TABLE,
        Key: { id },
        UpdateExpression:
          'SET #sourceWarehouseMappingId = :sourceWarehouseMappingId',
        ExpressionAttributeValues: {
          ':sourceWarehouseMappingId': 'WH-FG',
        },
        ExpressionAttributeNames: {
          '#sourceWarehouseMappingId': 'sourceWarehouseMappingId',
        },
        ConditionExpression: 'attribute_exists(id)',
      })
      .promise();
    console.log('id storeID updated:>> ', id);
  }
};

handler();
