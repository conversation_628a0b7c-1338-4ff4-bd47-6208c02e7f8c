const fs = require('fs');
const path = require('path');
const Papa = require('papaparse');
const moment = require('moment');

// Configuration
const CONFIG = {
  easyEcom: {
    baseUrl: 'https://api.easyecom.io',
    email: '<EMAIL>',
    password: 'Sleep@2024',
    locationKey: 'ne10776308481',
  },
  shopify: {
    adminBaseUrl:
      'https://thesleepcompanystore.myshopify.com/admin/api/2024-01',
    accessToken: '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',
  },
  serverlessOms: {
    baseUrl: 'https://gybqro5fdj.execute-api.ap-south-1.amazonaws.com/prod',
    apiKey: 'PpuHE5OWtR3xYmZTZWRBwaa3ogMvAumX8kWc4Alm',
  },
  inputCsvPath: path.resolve(__dirname, 'orders-cancel-ee.csv'),
  outputDir: path.resolve(__dirname, 'results_3'),
  logLevel: 'info', // 'debug', 'info', 'warn', 'error'
};

// Array to collect all logs
const allLogs = [];

// Setup logger
const logger = {
  debug: (message) => {
    if (['debug'].includes(CONFIG.logLevel)) {
      console.log(`[DEBUG] ${message}`);
      allLogs.push({
        level: 'DEBUG',
        message,
        timestamp: new Date().toISOString(),
      });
    }
  },
  info: (message) => {
    if (['debug', 'info'].includes(CONFIG.logLevel)) {
      console.log(`[INFO] ${message}`);
      allLogs.push({
        level: 'INFO',
        message,
        timestamp: new Date().toISOString(),
      });
    }
  },
  warn: (message) => {
    if (['debug', 'info', 'warn'].includes(CONFIG.logLevel)) {
      console.log(`[WARN] ${message}`);
      allLogs.push({
        level: 'WARN',
        message,
        timestamp: new Date().toISOString(),
      });
    }
  },
  error: (message) => {
    if (['debug', 'info', 'warn', 'error'].includes(CONFIG.logLevel)) {
      console.error(`[ERROR] ${message}`);
      allLogs.push({
        level: 'ERROR',
        message,
        timestamp: new Date().toISOString(),
      });
    }
  },
};

// Ensure output directory exists
if (!fs.existsSync(CONFIG.outputDir)) {
  fs.mkdirSync(CONFIG.outputDir, { recursive: true });
}

// Result files
const ordersStateFile = path.join(CONFIG.outputDir, 'orders-final-state.json');
const successFile = path.join(CONFIG.outputDir, 'cancelled-orders.json');
const onHoldFile = path.join(CONFIG.outputDir, 'on-hold-orders.json');
const failedFile = path.join(CONFIG.outputDir, 'failed-orders.json');
const logsFile = path.join(CONFIG.outputDir, 'allLogs.json');

// Results tracking
const results = {
  cancelled: [],
  onHold: [],
  failed: [],
};

// New object to track the state of each order
const ordersState = {};

// Helper Functions
async function getEasyEcomAccessToken() {
  try {
    logger.info('Getting EasyEcom access token');

    const body = {
      email: CONFIG.easyEcom.email,
      password: CONFIG.easyEcom.password,
      location_key: CONFIG.easyEcom.locationKey,
    };

    const response = await fetch(`${CONFIG.easyEcom.baseUrl}/access/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!data.data || !data.data.token || !data.data.token.jwt_token) {
      throw new Error('Failed to get access token from EasyEcom');
    }

    const token = data.data.token.jwt_token;
    logger.debug('Successfully retrieved EasyEcom access token');

    return token;
  } catch (error) {
    logger.error(`Error while getting access token: ${error.message}`);
    throw error;
  }
}

async function getOrderDetailsFromEasyEcom(referenceCode) {
  try {
    logger.info(`Getting order details for ${referenceCode} from EasyEcom`);

    const token = await getEasyEcomAccessToken();

    const response = await fetch(
      `${CONFIG.easyEcom.baseUrl}/orders/V2/getOrderDetails?reference_code=${referenceCode}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      },
    );

    const data = await response.json();

    if (data.code !== 200 || !data.data || !data.data.length) {
      throw new Error(
        `Failed to get order details from EasyEcom API for reference code ${referenceCode}`,
      );
    }

    logger.debug(`Order details retrieved successfully for ${referenceCode}`);
    return data.data[0]; // Return the first order in the array
  } catch (error) {
    logger.error(`Failed to get order details: ${error.message}`);
    throw error;
  }
}

async function getOrderFromServerlessOms(orderId) {
  try {
    logger.info(`Getting order details from Serverless OMS for ${orderId}`);

    const finalURL = `${CONFIG.serverlessOms.baseUrl}/order/orderInfo?orderId=${orderId}`;

    const response = await fetch(finalURL, {
      method: 'GET',
      headers: {
        'x-api-key': CONFIG.serverlessOms.apiKey,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(
        `Failed to get order from Serverless OMS: ${response.statusText}`,
      );
    }

    const responseData = await response.json();

    if (!responseData || !responseData.data) {
      throw new Error('Invalid response from Serverless OMS');
    }

    const { data } = responseData;

    if (Array.isArray(data) && data.length > 0) {
      const [shopifyOrderData] = data;
      logger.debug(
        `Successfully retrieved order data from Serverless OMS for ${orderId}`,
      );
      return shopifyOrderData;
    }

    throw new Error(`No order data found in Serverless OMS for ${orderId}`);
  } catch (error) {
    logger.error(`Failed to get order from Serverless OMS: ${error.message}`);
    throw error;
  }
}

async function cancelOrderInEasyEcom(referenceCode) {
  try {
    logger.info(`Attempting to cancel order ${referenceCode} in EasyEcom`);

    const token = await getEasyEcomAccessToken();

    const response = await fetch(
      `${CONFIG.easyEcom.baseUrl}/orders/cancelOrder`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          reference_code: referenceCode,
        }),
      },
    );

    const result = await response.json();

    if (result.code !== 200) {
      throw new Error(
        `EasyEcom cancellation returned non-success code: ${result.code}`,
      );
    }

    logger.info(`Successfully cancelled order ${referenceCode} in EasyEcom`);
    return result;
  } catch (error) {
    logger.error(
      `Failed to cancel order ${referenceCode} in EasyEcom: ${error.message}`,
    );
    throw error;
  }
}

async function holdOrderOnEasyEcom(referenceCode) {
  try {
    logger.info(`Attempting to hold order ${referenceCode} in EasyEcom`);

    // First, get the order details to retrieve the invoice_id
    const orderDetails = await getOrderDetailsFromEasyEcom(referenceCode);
    const invoiceId = orderDetails.invoice_id;

    if (!invoiceId) {
      throw new Error(`Order ${referenceCode} does not have an invoice id`);
    }

    const token = await getEasyEcomAccessToken();

    const response = await fetch(
      `${CONFIG.easyEcom.baseUrl}/orders/holdOrders`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          invoice_id: invoiceId,
          hold_reason: 'Other',
        }),
      },
    );

    const result = await response.json();

    if (result.code !== 200) {
      throw new Error(
        `EasyEcom hold order returned non-success code: ${result.code}`,
      );
    }

    logger.info(`Successfully put order ${referenceCode} on hold in EasyEcom`);
    return result;
  } catch (error) {
    logger.error(
      `Failed to put order ${referenceCode} on hold in EasyEcom: ${error.message}`,
    );
    throw error;
  }
}

async function cancelOrderInShopify(shopifyOrderId) {
  try {
    logger.info(`Attempting to cancel order ${shopifyOrderId} in Shopify`);

    // Prepare GraphQL query
    const graphqlQuery = `
      mutation orderCancel(
        $orderId: ID!, 
        $reason: OrderCancelReason!, 
        $refund: Boolean!, 
        $restock: Boolean!
      ) {
        orderCancel(
          orderId: $orderId, 
          reason: $reason, 
          refund: $refund, 
          restock: $restock
        ) {
          job {
            id
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    // Convert to Shopify GraphQL ID format
    const formattedOrderId = `gid://shopify/Order/${shopifyOrderId}`;

    // Execute GraphQL query
    const response = await fetch(
      `${CONFIG.shopify.adminBaseUrl}/graphql.json`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': CONFIG.shopify.accessToken,
        },
        body: JSON.stringify({
          query: graphqlQuery,
          variables: {
            orderId: formattedOrderId,
            reason: 'OTHER',
            refund: true,
            restock: true,
          },
        }),
      },
    );

    const result = await response.json();

    // Check for GraphQL errors
    if (result.errors) {
      throw new Error(result.errors[0].message);
    }

    // Check for user errors in the response
    if (result.data?.orderCancel?.userErrors?.length > 0) {
      throw new Error(result.data.orderCancel.userErrors[0].message);
    }

    logger.info(`Successfully cancelled order ${shopifyOrderId} in Shopify`);
    return result.data?.orderCancel?.job;
  } catch (error) {
    logger.error(
      `Failed to cancel order ${shopifyOrderId} in Shopify: ${error.message}`,
    );
    throw error;
  }
}

async function checkOrderStatusInEasyEcom(referenceCode) {
  try {
    const orderDetails = await getOrderDetailsFromEasyEcom(referenceCode);

    // Check if order is already cancelled
    if (orderDetails.order_status === 'Cancelled') {
      return {
        isEligible: false,
        reason: `Order ${referenceCode} is already cancelled`,
        isAlreadyCancelled: true,
      };
    }

    // Check if order has a manifest date
    if (orderDetails.manifest_date) {
      return {
        isEligible: false,
        reason: `Order ${referenceCode} cannot be cancelled as it has already been manifested for shipping`,
        isManifested: true,
      };
    }

    // Check if order has status that prevents cancellation
    const nonCancellableStatuses = ['Delivered', 'Out For Delivery', 'Shipped'];

    if (nonCancellableStatuses.includes(orderDetails.order_status)) {
      return {
        isEligible: false,
        reason: `Order ${referenceCode} cannot be cancelled as it is in ${orderDetails.order_status} status`,
        currentStatus: orderDetails.order_status,
      };
    }

    // If we get here, the order is eligible for cancellation
    return {
      isEligible: true,
      orderDetails,
      currentStatus: orderDetails.order_status,
    };
  } catch (error) {
    logger.error(`Failed to check order status: ${error.message}`);
    throw error;
  }
}

async function processOrder(referenceCode) {
  try {
    logger.info(`Processing order ${referenceCode}`);

    // Initialize the state for this order
    ordersState[referenceCode] = {
      referenceCode,
      // shopifyOrderId: null,
      timestamp: moment().toISOString(),
      cancelledOnShopify: false,
      cancelledOnEasyEcom: false,
      heldOnEasyEcom: false,
      failedEveryStep: false,
      statusDetails: {
        easyEcomStatus: null,
        isManifested: false,
        isAlreadyCancelled: false,
      },
      errors: []
    };

    // First check if the order can be cancelled
    try {
      const statusCheck = await checkOrderStatusInEasyEcom(referenceCode);
      
      // Update the order state with status information
      ordersState[referenceCode].statusDetails.easyEcomStatus = statusCheck.currentStatus || 'Unknown';
      
      if (statusCheck.isManifested) {
        ordersState[referenceCode].statusDetails.isManifested = true;
      }
      
      if (statusCheck.isAlreadyCancelled) {
        ordersState[referenceCode].statusDetails.isAlreadyCancelled = true;
        ordersState[referenceCode].cancelledOnEasyEcom = true; // Already cancelled counts as success
      }

      if (!statusCheck.isEligible) {
        logger.warn(statusCheck.reason);

        // Even if it's already cancelled, we still record that as a "failure" in terms of our current cancellation attempt
        results.failed.push({
          referenceCode,
          timestamp: moment().toISOString(),
          reason: statusCheck.reason,
        });
        
        ordersState[referenceCode].errors.push(statusCheck.reason);
        ordersState[referenceCode].failedEveryStep = !statusCheck.isAlreadyCancelled;

        return false;
      }
    } catch (checkError) {
      logger.error(`Error checking order status: ${checkError.message}`);
      
      // Record the error
      ordersState[referenceCode].errors.push(`Status check error: ${checkError.message}`);

      // If we can't check status, we'll still try to cancel
      logger.info(
        'Proceeding with cancellation attempt despite status check failure',
      );
    }

    // // Get Shopify order ID from ServerlessOMS
    // let shopifyOrderId = null;
    // try {
    //   const omsOrderData = await getOrderFromServerlessOms(referenceCode);
    //   shopifyOrderId = omsOrderData.id;
    //   ordersState[referenceCode].shopifyOrderId = shopifyOrderId;
      
    //   logger.info(
    //     `Retrieved Shopify order ID ${shopifyOrderId} for reference code ${referenceCode}`,
    //   );
    // } catch (omsError) {
    //   logger.warn(
    //     `Failed to get Shopify order ID from OMS: ${omsError.message}. Will attempt cancellation in EasyEcom only.`,
    //   );
      
    //   ordersState[referenceCode].errors.push(`OMS error: ${omsError.message}`);
    // }

    // // Track if all cancellation attempts fail
    let atLeastOneSuccess = false;

    // // First attempt to cancel in Shopify (if we have a Shopify order ID)
    // if (shopifyOrderId) {
    //   try {
    //     await cancelOrderInShopify(shopifyOrderId);
    //     logger.info(`Order ${shopifyOrderId} cancelled in Shopify`);
        
    //     // Update order state for Shopify cancellation
    //     ordersState[referenceCode].cancelledOnShopify = true;
    //     atLeastOneSuccess = true;
        
    //     results.cancelled.push({
    //       referenceCode,
    //       shopifyOrderId,
    //       timestamp: moment().toISOString(),
    //       onShopify: true,
    //     });
    //   } catch (shopifyError) {
    //     logger.warn(
    //       `Could not cancel order ${shopifyOrderId} in Shopify: ${shopifyError.message}`,
    //     );
        
    //     ordersState[referenceCode].errors.push(`Shopify cancellation error: ${shopifyError.message}`);

    //     results.failed.push({
    //       referenceCode,
    //       shopifyOrderId,
    //       timestamp: moment().toISOString(),
    //       onShopify: true,
    //       reason: shopifyError.message,
    //     });

    //     // Continue with EasyEcom cancellation even if Shopify fails
    //   }
    // }

    // Then attempt to cancel in EasyEcom
    try {
      const easyEcomResult = await cancelOrderInEasyEcom(referenceCode);

      // Update order state for EasyEcom cancellation
      ordersState[referenceCode].cancelledOnEasyEcom = true;
      atLeastOneSuccess = true;


      // If we got here, cancellation was successful
      results.cancelled.push({
        referenceCode,
        // shopifyOrderId,
        timestamp: moment().toISOString(),
        easyEcomResult,
      });

      logger.info(`Order ${referenceCode} successfully cancelled in EasyEcom`);
      return true;
    } catch (easyEcomCancelError) {
      logger.warn(
        `Could not cancel order ${referenceCode} in EasyEcom: ${easyEcomCancelError.message}`,
      );
      
      ordersState[referenceCode].errors.push(`EasyEcom cancellation error: ${easyEcomCancelError.message}`);

      // If cancellation failed, try to put the order on hold
      try {
        const holdResult = await holdOrderOnEasyEcom(referenceCode);
        
        // Update order state for EasyEcom hold
        ordersState[referenceCode].heldOnEasyEcom = true;
        atLeastOneSuccess = true; // Holding counts as a partial success


        results.onHold.push({
          referenceCode,
          // shopifyOrderId,
          timestamp: moment().toISOString(),
          holdResult,
          cancelError: easyEcomCancelError.message,
        });

        logger.info(`Order ${referenceCode} put on hold in EasyEcom`);
        return false;
      } catch (holdError) {
        // Both cancellation and hold failed
        ordersState[referenceCode].errors.push(`EasyEcom hold error: ${holdError.message}`);
        
        results.failed.push({
          referenceCode,
          // shopifyOrderId,
          timestamp: moment().toISOString(),
          cancelError: easyEcomCancelError.message,
          holdError: holdError.message,
        });

        logger.error(
          `Order ${referenceCode} could not be cancelled or put on hold: ${holdError.message}`,
        );
        return false;
      }
    } finally {
      // Update the failedEveryStep flag
      ordersState[referenceCode].failedEveryStep = !atLeastOneSuccess && ordersState[referenceCode].errors.length > 0;
    }
  } catch (error) {
    // Initialize the order state if it doesn't exist
    if (!ordersState[referenceCode]) {
      ordersState[referenceCode] = {
        referenceCode,
        // shopifyOrderId: null,
        timestamp: moment().toISOString(),
        cancelledOnShopify: false,
        cancelledOnEasyEcom: false,
        heldOnEasyEcom: false,
        failedEveryStep: true,
        statusDetails: {
          easyEcomStatus: null,
          isManifested: false,
          isAlreadyCancelled: false,
        },
        errors: [`General processing error: ${error.message}`]
      };
    } else {
      ordersState[referenceCode].errors.push(`General processing error: ${error.message}`);
      ordersState[referenceCode].failedEveryStep = true;
    }
    
    results.failed.push({
      referenceCode,
      timestamp: moment().toISOString(),
      error: error.message,
    });

    logger.error(`Failed to process order ${referenceCode}: ${error.message}`);
    return false;
  }
}

// Main function
async function main() {
  logger.info('Starting order cancellation script');

  try {
    // Check if input CSV exists
    if (!fs.existsSync(CONFIG.inputCsvPath)) {
      throw new Error(`Input CSV file not found: ${CONFIG.inputCsvPath}`);
    }

    // Read CSV file
    const csvContent = fs.readFileSync(CONFIG.inputCsvPath, 'utf8');
    logger.debug(`CSV content loaded: ${csvContent.substring(0, 100)}...`);

    // Parse CSV
    const parseResult = Papa.parse(csvContent, {
      header: true,
      skipEmptyLines: true,
      trimHeaders: true, // Trim whitespace from headers
    });

    const { data, errors } = parseResult;

    if (errors.length > 0) {
      logger.error(`CSV parsing errors: ${JSON.stringify(errors)}`);
    }

    if (!data || data.length === 0) {
      throw new Error('No data found in CSV or CSV format is incorrect');
    }

    // Get the header name for the ID column (could be 'id' or 'id ' or other variations)
    const headers = Object.keys(data[0]);
    logger.debug(`CSV headers: ${headers.join(', ')}`);

    const idColumn = headers.find(
      (header) => header.trim().toLowerCase() === 'id',
    );

    if (!idColumn) {
      throw new Error("CSV must have a column named 'id'");
    }

    logger.info(`Found ${data.length} orders to process`);

    // Process each order
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const referenceCode = row[idColumn].trim();

      if (!referenceCode) {
        logger.warn(`Empty reference code at row ${i + 1}, skipping`);
        continue;
      }

      logger.info(`Processing order ${i + 1}/${data.length}: ${referenceCode}`);

      try {
        await processOrder(referenceCode);
      } catch (error) {
        logger.error(
          `Error processing order ${referenceCode}: ${error.message}`,
        );
        
        // Ensure order state is recorded even if there's a processing error
        if (!ordersState[referenceCode]) {
          ordersState[referenceCode] = {
            referenceCode,
            // shopifyOrderId: null,
            timestamp: moment().toISOString(),
            cancelledOnShopify: false,
            cancelledOnEasyEcom: false,
            heldOnEasyEcom: false,
            failedEveryStep: true,
            statusDetails: {
              easyEcomStatus: null,
              isManifested: false,
              isAlreadyCancelled: false,
            },
            errors: [`Fatal processing error: ${error.message}`]
          };
        }
        
        results.failed.push({
          referenceCode,
          timestamp: moment().toISOString(),
          error: error.message,
        });
      }

      // Small delay between requests to avoid rate limiting
      if (i < data.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    // Write results to files
    fs.writeFileSync(successFile, JSON.stringify(results.cancelled, null, 2));
    fs.writeFileSync(onHoldFile, JSON.stringify(results.onHold, null, 2));
    fs.writeFileSync(failedFile, JSON.stringify(results.failed, null, 2));
    fs.writeFileSync(ordersStateFile, JSON.stringify(ordersState, null, 2));

    // Write all logs to file
    fs.writeFileSync(logsFile, JSON.stringify(allLogs, null, 2));

    logger.info('Order cancellation script completed');
    logger.info(
      `Results: ${results.cancelled.length} cancelled, ${results.onHold.length} on hold, ${results.failed.length} failed`,
    );
    logger.info(`Final order states written to ${ordersStateFile}`);
    logger.info(`Details written to ${CONFIG.outputDir} directory`);
    logger.info(`All logs written to ${logsFile}`);
  } catch (error) {
    logger.error(`Script execution failed: ${error.message}`);

    // Even if the script fails, try to write the logs and order states that were collected
    try {
      fs.writeFileSync(logsFile, JSON.stringify(allLogs, null, 2));
      if (Object.keys(ordersState).length > 0) {
        fs.writeFileSync(ordersStateFile, JSON.stringify(ordersState, null, 2));
        console.log(`Order states written to ${ordersStateFile} despite script failure`);
      }
      console.log(`Logs written to ${logsFile} despite script failure`);
    } catch (writeError) {
      console.error(`Failed to write logs or order states: ${writeError.message}`);
    }

    process.exit(1);
  }
}

// Run the script
main();