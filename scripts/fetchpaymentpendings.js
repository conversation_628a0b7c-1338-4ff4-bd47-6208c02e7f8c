const AWS = require('aws-sdk');

// Replace with environment variables
const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
const region = 'ap-south-1';
const ORDER_TABLE = 'pos-prod-replacement-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const scanOrders = async (nextToken = null) => {
  const params = {
    TableName: ORDER_TABLE,
    ProjectionExpression: 'id, orderType, finalDiscountedAmount',
    FilterExpression:
      '#orderType = :orderType AND #finalDiscountedAmount > :finalDiscountedAmount',
    ExpressionAttributeNames: {
      '#orderType': 'orderType',
      '#finalDiscountedAmount': 'finalDiscountedAmount',
    },
    ExpressionAttributeValues: {
      ':orderType': 'REPLACEMENT',
      ':finalDiscountedAmount': 0, // Set to 0 for comparison (finalDiscountedAmount > 0)
    },
    Limit: 10,
  };

  if (nextToken) {
    params.ExclusiveStartKey = nextToken;
  }

  try {
    const result = await db.scan(params).promise();
    return {
      Items: result.Items || [],
      LastEvaluatedKey: result.LastEvaluatedKey || null,
    };
  } catch (error) {
    console.error('Error scanning orders:', error);
    throw error;
  }
};

const handler = async () => {
  try {
    let allOrders = [];
    let nextToken = null;

    do {
      const { Items, LastEvaluatedKey } = await scanOrders(nextToken);
      console.log('get itemsss', Items.length, LastEvaluatedKey);

      allOrders = allOrders.concat(Items);
      nextToken = LastEvaluatedKey;
    } while (nextToken);

    console.log('Filtered Orders:', allOrders);
    console.log('Total Orders:', allOrders.length, allOrders[0]);
  } catch (error) {
    console.error('Error processing orders:', error);
  }
};
handler();

// const handler = async () => {
//   try {
//     // Provide the correct starting key (replace with a valid key from your table)
//     const startingKey = { id: 'ITSC812425-0684' }; // Example key structure
//     const { Items, LastEvaluatedKey } = await scanOrders(startingKey);

//     console.log('Current Page Orders:', Items);
//     console.log('Next Token:', LastEvaluatedKey);

//     return { Items, LastEvaluatedKey };
//   } catch (error) {
//     console.error('Error processing orders:', error);
//   }
// };
