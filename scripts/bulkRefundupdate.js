const AWS = require('aws-sdk');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const STORE_TABLE = 'pos-prod-refund-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const scanOrderTable = async (nextToken = null) => {
  const params = {
    TableName: STORE_TABLE,
  };

  if (nextToken) params.ExclusiveStartKey = nextToken;

  const { Items, LastEvaluatedKey } = await db.scan(params).promise();

  if (LastEvaluatedKey) {
    const moreData = await scanOrderTable(LastEvaluatedKey);
    return [...Items, ...moreData];
  }

  return Items;
};

const handler = async () => {
  const orders = await scanOrderTable();
  for (const order of orders) {
    const {
      id,
      shopifyOrderId,
      // Bank details
      accountHolderName,
      accountNo,
      bankAccountName,
      ifscCode,
      // UPI details
      upiId,
      upiName,
    } = order;

    console.log('Processing refund order:>> ', id);

    // Check if either bank details or UPI details exist
    const hasBankDetails = !!(
      accountHolderName &&
      accountNo &&
      bankAccountName &&
      ifscCode
    );
    const hasUpiDetails = !!(upiId && upiName);
    let isFormSubmitted = false;
    // Mark form as submitted only if either bank or UPI details exist
    isFormSubmitted = hasBankDetails || hasUpiDetails;

    await db
      .update({
        TableName: STORE_TABLE,
        Key: { shopifyOrderId, id },
        UpdateExpression: 'SET #isFormSubmitted = :isFormSubmitted',
        ExpressionAttributeValues: {
          ':isFormSubmitted': isFormSubmitted,
        },
        ExpressionAttributeNames: {
          '#isFormSubmitted': 'isFormSubmitted',
        },
        ConditionExpression:
          'attribute_exists(shopifyOrderId) and attribute_exists(id)',
      })
      .promise();

    console.log(`Order ${id} updated, isFormSubmitted: ${isFormSubmitted}`);
  }
};

handler();
