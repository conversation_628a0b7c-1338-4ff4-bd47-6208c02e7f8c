const AWS = require('aws-sdk');
const moment = require('moment');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const TABLE = 'pos-stage-store-table';
const EMPLOYEE_TABLE = 'pos-stage-employee-table';
const LOCAL_TABLE = 'pos-stage-local-configuration-table';
const USERPOOL_ID = 'ap-south-1_KCZExa5Cd';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});
const cognito = new AWS.CognitoIdentityServiceProvider({
  accessKeyId,
  secretAccessKey,
  region,
});

const stores = [
  {
    ID: 'TSC90',
    City: 'College Road',
    Country: 'India',
    Latitude: 20.00593002,
    'Address line1':
      'Shop No. 3, 4 and 5, Yashogandha Society, Patil Colony, Canada Corner, College Road, Nashik - 422005.',
    'Address line2': '',
    'Address line3': '',
    Longitude: 73.76577757,
    PinCode: 422005,
    State: 'Maharashtra',
    StateCode: 'MH',
    ASM: 'Sagar',
    'Billing Address:City': 'Nashik',
    'Billing Address:Country': 'India',
    'Billing Address:Address line1':
      'Shop No. 3, 4 and 5, Yashogandha Society, Patil Colony, Canada Corner, College Road, Nashik - 422005.',
    'Billing Address:Address line2': '',
    'Billing Address:Address line3': '',
    'Billing Address:PinCode': 422005,
    'Billing Address:State': 'Maharashtra',
    'Billing Address:StateCode': 'MH',
    'Store name': 'The Sleep Company Experience Store-College Road',
    Description: 'The Sleep Company Experience Store-College Road',
    'Store email': '<EMAIL>',
    'GstDetails: CompanyName': 'COMFORT GRID TECHNOLOGIES PRIVATE LIMITED',
    'GstDetails: GstNumber': '27AAICC4845L1Z8',
    InvoiceStartCode: 'ITSC90242500001',
    'Store is Active?': 'Yes',
    Name: 'College Road',
    Phone: 9820386536,
    PosIds: '',
    'SAP Location Code': 'CollegeRd_Nashik',
    'Warehouse code': 'WH-CRS',
    'Acct code': 41110102,
    'Card code': 'CCO213',
    'Zonal Mgr': 'Viishal L',
  },
];

const scanTable = async (nextToken = null) => {
  if (nextToken) params.ExclusiveStartKey = nextToken;

  const { Items, LastEvaluatedKey } = await db
    .scan({
      TableName: TABLE,
    })
    .promise();

  if (LastEvaluatedKey) {
    const moreData = await scanTable(LastEvaluatedKey);
    return [...Items, ...moreData];
  }

  return Items;
};

const getFinancialYear = () => {
  const currentMonth = moment().month() + 1;
  const currentYear = moment().year();

  let financialYearStart, financialYearEnd;
  if (currentMonth < 4) {
    financialYearStart = currentYear - 1;
    financialYearEnd = currentYear;
  } else {
    financialYearStart = currentYear;
    financialYearEnd = currentYear + 1;
  }

  // Format the financial year as "YY-YY"
  const financialYear =
    financialYearStart.toString().substr(-2) +
    financialYearEnd.toString().substr(-2);

  return financialYear;
};

const generateCode = async (storeId, key, prefix) => {
  console.log('GenerateCode', 'generateCode', {
    input: { storeId, key, prefix },
  });
  try {
    const localConfig = await db
      .get({
        TableName: LOCAL_TABLE,
        Key: { storeId, key },
      })
      .promise()
      .then((res) => res.Item);

    const value = Number(localConfig?.value || 0) + 1;

    await db
      .update({
        TableName: LOCAL_TABLE,
        Key: { storeId, key },
        UpdateExpression: 'SET #value = :value, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
          '#value': 'value',
        },
        ExpressionAttributeValues: {
          ':value': value,
          ':updatedAt': moment().toISOString(),
        },
      })
      .promise();

    // const randomNumber = Math.floor(Math.random() * (value * 10));
    const financialYear = getFinancialYear();

    const code = `${prefix}${financialYear}-${String(value).padStart(4, '0')}`;
    // + String(randomNumber).padStart(4, '0')
    return code;
  } catch (e) {
    console.error('generateCode', 'generateCode', { error: e });
    throw e;
  }
};

const createStoreUser = async (createStoreInput) => {
  try {
    const code = await generateCode('GLOBAL', 'EMPLOYEE_COUNT', 'TSCEMP-');

    const storeUserInput = {
      email: createStoreInput.email,
      role: 'STORE_LOGIN',
      id: code,
    };

    const input = {
      UserPoolId: USERPOOL_ID,
      Username: storeUserInput.email,
      UserAttributes: [
        {
          Name: 'email_verified',
          Value: 'true',
        },
        {
          Name: 'email',
          Value: storeUserInput.email,
        },
        {
          Name: 'custom:roles',
          Value: storeUserInput.role,
        },
        {
          Name: 'profile',
          Value: storeUserInput.id,
        },
      ],
      TemporaryPassword: 'Test@1234',
      MessageAction: 'SUPPRESS',
      DesiredDeliveryMediums: [],
    };

    const cognitoResponse = await cognito.adminCreateUser(input).promise();

    const {
      User: { Attributes: res },
    } = cognitoResponse;

    const cognitoStoreId = res.find((e) => e.Name === 'sub').Value;

    console.log('cognitoStoreId', cognitoStoreId);

    const employeeInput = {
      id: code,
      cognitoId: cognitoStoreId,
      firstName: createStoreInput.name,
      lastName: 'STORE',
      email: createStoreInput.email,
      phone: createStoreInput.phone,
      role: 'STORE_LOGIN',
      stores: [createStoreInput.id],
      designation: 'STORE_LOGIN',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await db
      .put({
        TableName: EMPLOYEE_TABLE,
        Item: employeeInput,
      })
      .promise();
  } catch (error) {
    console.log(error);
  }
};

const stateNo = [
  {
    stateCode: 'AN',
    stateNo: 35,
  },
  {
    stateCode: 'AP',
    stateNo: 37,
  },
  {
    stateCode: 'AR',
    stateNo: 12,
  },
  {
    stateCode: 'AS',
    stateNo: 18,
  },
  {
    stateCode: 'BR',
    stateNo: 10,
  },
  {
    stateCode: 'CH',
    stateNo: 4,
  },
  {
    stateCode: 'CGL',
    stateNo: 22,
  },
  {
    stateCode: 'DN',
    stateNo: 26,
  },
  {
    stateCode: 'DD',
    stateNo: 25,
  },
  {
    stateCode: 'DL',
    stateNo: 7,
  },
  {
    stateCode: 'GA',
    stateNo: 30,
  },
  {
    stateCode: 'GJ',
    stateNo: 24,
  },
  {
    stateCode: 'HR',
    stateNo: 6,
  },
  {
    stateCode: 'HP',
    stateNo: 2,
  },
  {
    stateCode: 'JK',
    stateNo: 1,
  },
  {
    stateCode: 'JHL',
    stateNo: 20,
  },
  {
    stateCode: 'KA',
    stateNo: 29,
  },
  {
    stateCode: 'KL',
    stateNo: 32,
  },
  {
    stateCode: 'LD',
    stateNo: 31,
  },
  {
    stateCode: 'MP',
    stateNo: 23,
  },
  {
    stateCode: 'MH',
    stateNo: 27,
  },
  {
    stateCode: 'MN',
    stateNo: 14,
  },
  {
    stateCode: 'ML',
    stateNo: 17,
  },
  {
    stateCode: 'MZ',
    stateNo: 15,
  },
  {
    stateCode: 'NL',
    stateNo: 13,
  },
  {
    stateCode: 'OR',
    stateNo: 21,
  },
  {
    stateCode: 'PY',
    stateNo: 34,
  },
  {
    stateCode: 'PB',
    stateNo: 3,
  },
  {
    stateCode: 'RJ',
    stateNo: 8,
  },
  {
    stateCode: 'SK',
    stateNo: 11,
  },
  {
    stateCode: 'TN',
    stateNo: 33,
  },
  {
    stateCode: 'TR',
    stateNo: 16,
  },
  {
    stateCode: 'UP',
    stateNo: 9,
  },
  {
    stateCode: 'UAL',
    stateNo: 5,
  },
  {
    stateCode: 'W',
    stateNo: 19,
  },
];

const upload = async () => {
  const stores = await scanTable();

  for (const item of stores) {
    // const Item = {
    //   id: item.ID,
    //   accountCode: `${item['Acct code']}`,
    //   asm: item.ASM,
    //   cardCode: item['Card code'],
    //   description: item.Description,
    //   email: item['Store email'],
    //   invoiceStartCode: `I${item.ID}`,
    //   name: item.Name,
    //   phone: `${item.Phone}`,
    //   posIds: [`${item.PosIds}`],
    //   quotationStartCode: `Q${item.ID}`,
    //   sapLocationCode: item['SAP Location Code'],
    //   sourceWarehouseMappingId: item['Warehouse code'],
    //   zonalHead: item['Zonal Mgr'],
    //   gstDetails: {
    //     companyName: item['GstDetails: CompanyName'],
    //     gstNumber: item['GstDetails: GstNumber'],
    //   },
    //   address: {
    //     city: item.City,
    //     country: item.Country,
    //     latitude: `${item.Latitude}`,
    //     line1: item['Address line1'],
    //     line2: `${item['Address line2']}, ${item['Address line3']}`,
    //     longitude: `${item.Longitude}`,
    //     pinCode: `${item.PinCode}`,
    //     state: item.State,
    //     stateCode: item.StateCode,
    //   },
    //   billingInfo: {
    //     name: item['Store name'],
    //     address: {
    //       city: item['Billing Address:City'],
    //       country: item['Billing Address:Country'],
    //       line1: item['Billing Address:Address line1'],
    //       line2: `${item['Billing Address:Address line2']}, ${item['Billing Address:Address line3']}`,
    //       pinCode: `${item['Billing Address:PinCode']}`,
    //       state: item['Billing Address:State'],
    //       stateCode: item['Billing Address:StateCode'],
    //     },
    //   },
    //   isActive: true,
    //   createdAt: new Date().toISOString(),
    //   updatedAt: new Date().toISOString(),
    // };
    const Item = {
      ...item,
      billingInfo: {
        ...item.billingInfo,
        address: {
          ...item.billingInfo.address,
          stateNo: stateNo.find(
            ({ stateCode }) => stateCode === item.billingInfo.address.stateCode,
          ).stateNo,
        },
      },
    };

    // await createStoreUser(Item);
    await db
      .put({
        TableName: TABLE,
        Item,
      })
      .promise();
  }
};
upload();
