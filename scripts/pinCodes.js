// userId: id,
// gender: gender ? gender : null,
// dob: dob ? dob : null,
// country: country ? country : null,
// state: state ? state : null,
// city: city ? city : null,
// pinCode: pinCode ? pinCode : null,
// landmark: landmark ? landmark : null,
// address: address,
// location: location ? location : null,
// area: area ? area : null,

const AWS = require('aws-sdk');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const TO_TABLE = 'pos-dev-hsn-code-table';
const FROM_TABLE = 'pos-prod-pincode-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const codes = [
  {
    stateCode: 'AN',
    state: 'Andaman and Nicobar',
    stateNo: 35,
  },
  {
    stateCode: 'AP',
    state: 'Andhra Pradesh',
    stateNo: 37,
  },
  {
    stateCode: 'AR',
    state: 'Arunachal Pradesh',
    stateNo: 12,
  },
  {
    stateCode: 'AS',
    state: 'Assam',
    stateNo: 18,
  },
  {
    stateCode: 'BR',
    state: 'Bihar',
    stateNo: 10,
  },
  {
    stateCode: 'CH',
    state: 'Chandigarh',
    stateNo: 4,
  },
  {
    stateCode: 'CGL',
    state: 'Chhattisgarh',
    stateNo: 22,
  },
  {
    stateCode: 'DN',
    state: 'Dadra and Nagar Haveli',
    stateNo: 26,
  },
  {
    stateCode: 'DD',
    state: 'Daman and Diu',
    stateNo: 25,
  },
  {
    stateCode: 'DL',
    state: 'Delhi',
    stateNo: 7,
  },
  {
    stateCode: 'GA',
    state: 'Goa',
    stateNo: 30,
  },
  {
    stateCode: 'GJ',
    state: 'Gujarat',
    stateNo: 24,
  },
  {
    stateCode: 'HR',
    state: 'Haryana',
    stateNo: 6,
  },
  {
    stateCode: 'HP',
    state: 'Himachal Pradesh',
    stateNo: 2,
  },
  {
    stateCode: 'JK',
    state: 'Jammu and Kashmir',
    stateNo: 1,
  },
  {
    stateCode: 'JHL',
    state: 'Jharkhand',
    stateNo: 20,
  },
  {
    stateCode: 'KA',
    state: 'Karnataka',
    stateNo: 29,
  },
  {
    stateCode: 'KL',
    state: 'Kerala',
    stateNo: 32,
  },
  {
    stateCode: 'LD',
    state: 'Lakshadweep',
    stateNo: 31,
  },
  {
    stateCode: 'MP',
    state: 'Madhya Pradesh',
    stateNo: 23,
  },
  {
    stateCode: 'MH',
    state: 'Maharashtra',
    stateNo: 27,
  },
  {
    stateCode: 'MN',
    state: 'Manipur',
    stateNo: 14,
  },
  {
    stateCode: 'ML',
    state: 'Meghalaya',
    stateNo: 17,
  },
  {
    stateCode: 'MZ',
    state: 'Mizoram',
    stateNo: 15,
  },
  {
    stateCode: 'NL',
    state: 'Nagaland',
    stateNo: 13,
  },
  {
    stateCode: 'OR',
    state: 'Orissa',
    stateNo: 21,
  },
  {
    stateCode: 'PY',
    state: 'Puducherry',
    stateNo: 34,
  },
  {
    stateCode: 'PB',
    state: 'Punjab',
    stateNo: 3,
  },
  {
    stateCode: 'RJ',
    state: 'Rajasthan',
    stateNo: 8,
  },
  {
    stateCode: 'SK',
    state: 'Sikkim',
    stateNo: 11,
  },
  {
    stateCode: 'TN',
    state: 'Tamil Nadu',
    stateNo: 33,
  },
  {
    stateCode: 'TR',
    state: 'Tripura',
    stateNo: 16,
  },
  {
    stateCode: 'UP',
    state: 'Uttar Pradesh',
    stateNo: 9,
  },
  {
    stateCode: 'UAL',
    state: 'Uttaranchal',
    stateNo: 5,
  },
  {
    stateCode: 'W',
    state: 'Bengal',
    stateNo: 19,
  },
  {
    stateCode: 'TL',
    state: 'Telangana',
    stateNo: 36,
  },
];

const scanTable = async (nextToken = null) => {
  const params = {
    TableName: FROM_TABLE,
    FilterExpression: '#state = :state',
    ExpressionAttributeValues: {
      ':state': 'Uttaranchal',
    },
    ExpressionAttributeNames: {
      '#state': 'state',
    },
  };

  if (nextToken) params.ExclusiveStartKey = nextToken;

  const { Items, LastEvaluatedKey } = await db.scan(params).promise();

  if (LastEvaluatedKey) {
    const moreData = await scanTable(LastEvaluatedKey);
    return [...Items, ...moreData];
  }

  return Items;
};

const getUsers = async () => {
  const users = await scanUserTable(null);

  return await Promise.all(
    users.map(async (user) => {
      const address = await getUserAddress(user.id);
      return { ...user, address };
    }),
  );
};

const handler = async () => {
  try {
    // const users = await getUsers();
    // const exportUsers = users.map((user) => {
    //   const { id, gender, dob, address } = user;
    //   if (address) {
    //     return {
    //       USER_ID: id,
    //       GENDER: gender ? gender : null,
    //       DOB: dob ? dob : null,
    //       COUNTRY: address.country ? address.country : null,
    //       STATE: address.state ? address.state : null,
    //       CITY: address.city ? address.city : null,
    //       PIN_CODE: address.pinCode ? address.pinCode : null,
    //       LANDMARK: address.landmark ? address.landmark : null,
    //       ADDRESS: address.address,
    //       LOCATION: address.location ? address.location : null,
    //       AREA: address.area ? address.area : null,
    //     };
    //   }
    // });
    // const json = await converter.json2csv(exportUsers, {});
    // console.log(json);

    const entities = await scanTable();

    for (const item of entities) {
      if (item.state === 'Bengal') item.state = 'West Bengal';
      if (item.state === 'Jammu & Kashmir') item.state = 'Jammu and Kashmir';
      if (item.state === 'Uttaranchal') item.state = 'Uttarakhand';
      if (item.state === 'Odisha') item.state = 'Orissa';
      if (item.state === 'Chattisgarh') item.state = 'Chhattisgarh';
      if (item.state === 'Andaman and Nicobar Islands')
        item.state = 'Andaman and Nicobar';

      const data = codes.find(({ state }) => state === item.state);
      const Item = {
        ...item,
        ...data,
      };

      // await createStoreUser(Item);
      await db
        .put({
          TableName: FROM_TABLE,
          Item,
        })
        .promise();
    }
  } catch (error) {
    console.log(error);
  }
};

handler();
