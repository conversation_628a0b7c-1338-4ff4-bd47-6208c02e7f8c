
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const {
  DynamoDBDocumentClient,
  GetCommand,
  QueryCommand,
} = require('@aws-sdk/lib-dynamodb');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const { Client } = require('@opensearch-project/opensearch');
const moment = require('moment');
const nodemailer = require('nodemailer');
const { json2csv } = require('json-2-csv');
const fs = require('fs');

// Direct AWS Configuration
const AWS_CONFIG = {
  region: 'ap-south-1', // Replace with your AWS region
  credentials: {
    accessKeyId: '********************', // Replace with your access key
    secretAccessKey: 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W', // Replace with your secret key
  }
};

// Initialize AWS clients with direct config
const dynamoClient = new DynamoDBClient(AWS_CONFIG);
const docClient = DynamoDBDocumentClient.from(dynamoClient);
const s3Client = new S3Client(AWS_CONFIG);

// Initialize OpenSearch client with direct values
const opensearchClient = new Client({
  node: 'https://search-pos-prod-es-kej2yfeachy4owakc4x6vgv7iy.ap-south-1.es.amazonaws.com',
  auth: {
    username: 'admin',
    password: 'Admin@1234',
  },
});

// Initialize nodemailer with direct values
const transporter = nodemailer.createTransporter({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'iodk nwfq hmir eese', // Consider using app password for Gmail
  },
});

// Direct constants
const MAX_ATTACHMENT_SIZE_MB = 25;
const REPLACEMENT_ORDER_TABLE = 'pos-prod-replacement-table';
const PAYMENT_TABLE = 'pos-prod-payment-table';
const ORDER_TABLE = 'pos-prod-order-table';
const REFUND_DETAIL_TABLE = 'pos-prod-refund-table';
const REQUEST_MODULE_API_URL = 'https://apis.thesleepcompany.in/order-management/request-return-replacement';
const REQUEST_MODULE_API_KEY = 'REQUESTVN9b3C3c7kTVLp94LNhKmsMH5';
const S3_BUCKET = 'pos-prod-public-bucket';
const FROM_EMAIL = '<EMAIL>';

// Interfaces
const PaymentStatus = {
  COMPLETED: 'COMPLETED',
  CREATED: 'CREATED',
};

// Utility functions
const getHumanReadableDateRange = (startDays, endDays) => {
  const startDate = moment().add(startDays, 'days').format('DD MMM YYYY');
  const endDate = moment().add(endDays, 'days').format('DD MMM YYYY');
  return `${startDate} to ${endDate}`;
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  try {
    return new Date(dateString).toLocaleDateString('en-GB');
  } catch (error) {
    return dateString;
  }
};

const calculateDaysFromRequest = (requestData, replacementOrder) => {
  if (!requestData?.createdAt) return 0;

  const requestDate = new Date(requestData.createdAt);
  const returnDate = new Date(replacementOrder.createdAt);

  const diffTime = Math.abs(returnDate.getTime() - requestDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// Service functions
const getAllReplacementOrdersFromOS = async (filter = {}) => {
  const searchArray = [
    {
      terms: {
        'status.keyword': ['ORDER_CREATED', 'SENT_TO_EE'],
      },
    },
  ];

  // Add order type filter (REPLACEMENT/RETURN)
  if (filter.orderType) {
    if (filter.orderType === 'REPLACEMENT') {
      searchArray.push({
        term: {
          'orderType.keyword': 'REPLACEMENT',
        },
      });
    } else if (filter.orderType === 'RETURN') {
      searchArray.push({
        term: {
          'orderType.keyword': 'RETURN',
        },
      });
    }
  }

  // Add replacement type filter for PART replacements
  if (filter.replacementType === 'PART') {
    searchArray.push({
      term: {
        'replacementType.keyword': 'PART',
      },
    });
  }

  // Add date range filter
  if (filter.startDate || filter.endDate) {
    const dateRange = {
      range: {
        createdAt: {},
      },
    };

    if (filter.startDate) {
      // Start date at beginning of day (00:00:00)
      const startDate = new Date(filter.startDate);
      startDate.setHours(0, 0, 0, 0);
      dateRange.range.createdAt.gte = startDate.toISOString();
    }

    if (filter.endDate) {
      // End date at end of day (23:59:59)
      const endDate = new Date(filter.endDate);
      endDate.setHours(23, 59, 59, 999);
      dateRange.range.createdAt.lte = endDate.toISOString();
    }

    searchArray.push(dateRange);
  }

  const query = {
    bool: {
      must: [...searchArray],
    },
  };

  console.log('OpenSearch Query:', JSON.stringify(query, null, 2));

  const response = await opensearchClient.search({
    index: 'pos-prod-replacement-table',
    body: {
      from: 0,
      size: 10000,
      query: query,
    },
  });

  return response.body.hits.hits.map((hit) => hit._source);
};

const getRequestDetails = async (replacementOrder) => {
  try {
    if (!replacementOrder.requestId) return null;

    const response = await fetch(
      `${REQUEST_MODULE_API_URL}/${replacementOrder.requestId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': REQUEST_MODULE_API_KEY,
        },
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to get request details: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('Error getting request details:', error);
    return null;
  }
};

const getRefundDetails = async (replacementOrder) => {
  try {
    const refundDetailCommand = new GetCommand({
      TableName: REFUND_DETAIL_TABLE,
      Key: {
        shopifyOrderId: replacementOrder.shopifyOrderId,
        id: replacementOrder.id,
      },
    });

    const { Item: data } = await docClient.send(refundDetailCommand);
    return data;
  } catch (error) {
    console.error('Error getting refund details:', error);
    return null;
  }
};

const getAllPaymentsByOrderId = async (orderId) => {
  try {
    const orderQuery = new GetCommand({
      TableName: ORDER_TABLE,
      Key: {
        id: orderId,
      },
    });

    const { Item } = await docClient.send(orderQuery);
    if (!Item || !Object.keys(Item).length) {
      throw new Error(`Order not found with this ID ${orderId}!`);
    }

    const { finalDiscountedAmount: totalOrderAmount } = Item;

    const queryCommand = new QueryCommand({
      TableName: PAYMENT_TABLE,
      KeyConditionExpression: 'orderId = :orderId',
      ExpressionAttributeValues: {
        ':orderId': orderId,
      },
    });

    const result = await docClient.send(queryCommand);

    if (result && result.Items && result.Items.length) {
      let Items = result.Items.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );

      const totalAmount = Items.reduce((acc, curr) => {
        if (curr.status === PaymentStatus.COMPLETED) {
          acc = acc + curr.transactionAmount;
        }
        return acc;
      }, 0);

      const requestedAmount = Items.reduce((acc, curr) => {
        if (curr.status === PaymentStatus.CREATED) {
          acc = acc + curr.transactionAmount;
        }
        return acc;
      }, 0);

      return {
        transactionDetails: Items,
        totalPaidAmount: totalAmount,
        requestedAmount: requestedAmount,
        remainingAmount: totalOrderAmount - totalAmount,
      };
    } else {
      return {
        transactionDetails: [],
        totalPaidAmount: 0,
        requestedAmount: 0,
        remainingAmount: totalOrderAmount,
      };
    }
  } catch (error) {
    console.error('Error getting payment details:', error);
    return null;
  }
};

const calculateAdditionalPayment = async (replacementOrder) => {
  try {
    const queryCommand = new QueryCommand({
      TableName: PAYMENT_TABLE,
      KeyConditionExpression: 'orderId = :orderId',
      FilterExpression: '#status = :status',
      ExpressionAttributeNames: {
        '#status': 'status',
      },
      ExpressionAttributeValues: {
        ':orderId': replacementOrder.id,
        ':status': PaymentStatus.COMPLETED,
      },
    });

    const result = await docClient.send(queryCommand);

    if (result && result.Items && result.Items.length) {
      const item = result.Items[0];
      return {
        amountPaid: item.amountPaid || 0,
        transactionId: item.paymentID ? Number(item.paymentID) : '',
        transactionAmount: item.transactionAmount || 0,
      };
    }

    return { amountPaid: 0, transactionId: '', transactionAmount: 0 };
  } catch (error) {
    console.error('Error calculating additional payment:', error);
    return { amountPaid: 0, transactionId: '', transactionAmount: 0 };
  }
};

const determineRefundOrPayment = (replacementOrder) => {
  const finalAmount = replacementOrder.finalDiscountedAmount || 0;

  if (replacementOrder.refundedPrice > 0) {
    return 'Refund';
  }
  if (finalAmount > 0) {
    return 'Additional Payment';
  }
  return 'No Change';
};

const getRefundMode = (refundData, replacementOrder) => {
  if (refundData?.refundMode) {
    return refundData.refundMode;
  }

  if (refundData?.isBackToSource) {
    return 'Back to Source';
  }

  const originalPaymentMode = replacementOrder.originalPaymentMode?.[0];
  if (originalPaymentMode) {
    return originalPaymentMode;
  }

  return '';
};

const createCSVRow = async (
  replacementOrder,
  requestData,
  refundData,
  paymentData,
) => {
  const oldProduct = replacementOrder.replacedProduct || {};
  const newProduct = replacementOrder.orderProducts?.[0] || {};

  const daysFromRequest = calculateDaysFromRequest(
    requestData,
    replacementOrder,
  );
  const refundOrAdditionalPayment = determineRefundOrPayment(replacementOrder);
  const { amountPaid, transactionAmount, transactionId } =
    await calculateAdditionalPayment(replacementOrder);

  return {
    // Order details
    orderId: replacementOrder.orderId || '',
    shopifyOrderId: replacementOrder.shopifyOrderId || '',

    // Old Product details
    oldProductName: oldProduct.title || '',
    oldProductSize: oldProduct.variantTitle || '',
    oldSkuId: oldProduct.sku || '',
    oldProductPriceAfterDiscount: oldProduct.finalItemPrice || 0,

    // Request details
    requestExists: requestData ? 'Yes' : 'No',
    requestType:
      requestData?.finalRequestType || requestData?.requestType || '',
    requestTicketId: requestData?.freshdeskId || '',
    dateOfRequest: requestData?.createdAt || '',
    status: replacementOrder?.status || '',
    requestStatus: requestData?.requestStatus || 'NONE',
    finalRequestType: requestData?.finalRequestType || '',
    replacementPriority: replacementOrder.pickupPriority || '',
    rejectedReason: requestData?.rejectedReason || '',

    // Replacement booking details
    dateOfReplacementBooking: replacementOrder.createdAt || '',
    acceptedPrimaryReason:
      replacementOrder.primaryReason || replacementOrder.reason || '',
    acceptedSubReason: replacementOrder.secondaryReason || '',
    acceptedDepartment: replacementOrder.department || '',
    agentName: replacementOrder.agentName || '',
    reasonAdditionalComments: replacementOrder.additionalComments || '',

    // Replacement order and product details
    replacementOrderId: replacementOrder.id || '',
    replacementOrderIdStatus: replacementOrder.status || '',
    replacementProductName: newProduct.title || '',
    replacementProductSkuId: newProduct.sku || '',
    replacementProductSize: newProduct.variantTitle || '',
    replacementProductPrice: newProduct.finalItemPrice || 0,
    customDiscountApplied: replacementOrder.customDiscountAmount || 0,

    // Payment and shipping details
    approverName: replacementOrder.approverName || '',
    refundOrAdditionalPayment: refundOrAdditionalPayment,
    refundAmount: replacementOrder?.refundedPrice || 0,
    shippingChargesApplied: replacementOrder.isShippingCharged ? 'Yes' : 'No',
    shippingChargesAmount: replacementOrder.shippingCost || 0,
    additionalPaymentAmount: amountPaid || 0,
    paymentTransactionId: transactionId || '',

    // Ticket and EDD details
    bookingTicketId: replacementOrder.RRTicketId?.toString() || '',
    pickupEdd: formatDate(replacementOrder.pickupEdd),
    newOrderEdd: formatDate(replacementOrder.deliveryDate),

    // Refund details
    refundTrackerId: refundData?.id || '',
    refundMode: getRefundMode(refundData, replacementOrder),
    refundStatus: refundData?.status || 'PENDING',
    refundTransactionId: refundData?.transactionId || '',
    refundInitiatedDate: refundData?.createdAt || '',

    // Days calculation
    daysFromRequestToReturn: daysFromRequest,
  };
};

const uploadToS3 = async (csvContent, fileName) => {
  const params = {
    Bucket: S3_BUCKET,
    Key: `exports-post-order/${fileName}`,
    Body: csvContent,
    ContentType: 'text/csv',
    ContentDisposition: `attachment; filename="${fileName}"`,
  };

  const command = new PutObjectCommand(params);
  await s3Client.send(command);

  return `https://${S3_BUCKET}.s3.amazonaws.com/exports-post-order/${fileName}`;
};

const saveToLocalFile = async (csvContent, fileName) => {
  const localPath = `./exports/${fileName}`;
  
  // Create exports directory if it doesn't exist
  if (!fs.existsSync('./exports')) {
    fs.mkdirSync('./exports', { recursive: true });
  }
  
  fs.writeFileSync(localPath, csvContent);
  console.log(`File saved locally at: ${localPath}`);
  return localPath;
};

const sendEmailWithS3Link = async (email, s3Url, fileName, recordCount) => {
  const mailOptions = {
    from: FROM_EMAIL,
    to: email,
    subject: 'Replacement Orders Export - Ready for Download',
    html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">Your Export is Ready!</h2>
                <p>Dear User,</p>
                <p>Your replacement orders export has been successfully generated and is ready for download.</p>
                
                <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #555;">Export Details:</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin: 10px 0;"><strong>File Name:</strong> ${fileName}</li>
                        <li style="margin: 10px 0;"><strong>Records Count:</strong> ${recordCount}</li>
                        <li style="margin: 10px 0;"><strong>Generated On:</strong> ${new Date().toLocaleString()}</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="${s3Url}" 
                       style="background-color: #007bff; color: white; padding: 12px 30px; 
                              text-decoration: none; border-radius: 5px; display: inline-block;">
                        Download CSV File
                    </a>
                </div>
                
                <p style="color: #666; font-size: 14px;">
                    <strong>Note:</strong> This download link will be available for 7 days. 
                    Please download the file within this period.
                </p>
                
                <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                <p style="color: #999; font-size: 12px;">
                    This is an automated email. Please do not reply to this message.
                </p>
            </div>
        `,
  };

  await transporter.sendMail(mailOptions);
};

// Main function for local execution
const runExport = async (email, filter = {}, saveLocal = true) => {
  try {
    console.log('Starting replacement orders export process...');

    // Get all replacement orders from OpenSearch
    const replacementOrders = await getAllReplacementOrdersFromOS(filter);
    console.log('Total replacement orders found:', replacementOrders.length);

    // Filter for orders with status ORDER_CREATED or SENT_TO_EE
    const filteredOrders = replacementOrders.filter(
      (order) =>
        order.status === 'ORDER_CREATED' || order.status === 'SENT_TO_EE',
    );
    console.log('Filtered orders count:', filteredOrders.length);

    const csvData = [];

    // Process each order
    for (const replacementOrder of filteredOrders) {
      try {
        console.log('Processing order:', replacementOrder.id);

        // Get request details
        const requestData = await getRequestDetails(replacementOrder);

        // Get refund details
        const refundData = await getRefundDetails(replacementOrder);

        // Get payment details
        const paymentData = await getAllPaymentsByOrderId(
          replacementOrder.id,
        );

        // Create CSV row
        const csvRow = await createCSVRow(
          replacementOrder,
          requestData,
          refundData,
          paymentData,
        );
        csvData.push(csvRow);
      } catch (error) {
        console.error('Error processing order:', replacementOrder.id, error);
        continue; // Continue with next order if one fails
      }
    }

    console.log('Total CSV rows created:', csvData.length);

    if (csvData.length === 0) {
      console.log('No data found matching the criteria');
      return;
    }

    // Convert to CSV
    const csv = await json2csv(csvData);
    const fileName = `replacement_orders_${new Date().toISOString().split('T')[0]}_${Date.now()}.csv`;

    if (saveLocal) {
      // Save locally
      await saveToLocalFile(csv, fileName);
    }

    // Check if CSV is too large for email
    const csvSizeInMB = Buffer.byteLength(csv, 'utf8') / (1024 * 1024);

    if (email) {
      if (csvSizeInMB > MAX_ATTACHMENT_SIZE_MB) {
        // Upload to S3 and send link
        const s3Url = await uploadToS3(csv, fileName);
        await sendEmailWithS3Link(email, s3Url, fileName, csvData.length);
        console.log('File uploaded to S3 and email sent with download link');
      } else {
        // Send as email attachment
        await transporter.sendMail({
          from: FROM_EMAIL,
          to: email,
          subject: 'Replacement Orders Export',
          text: `Please find attached the replacement orders export containing ${csvData.length} records.`,
          attachments: [
            {
              filename: fileName,
              content: csv,
              contentType: 'text/csv',
            },
          ],
        });
        console.log('Email sent with CSV attachment');
      }
    }

    console.log('Successfully processed replacement orders export');
    return {
      success: true,
      recordCount: csvData.length,
      fileName: fileName,
    };
  } catch (error) {
    console.error('Error processing export:', error);
    throw error;
  }
};

// Example usage
const main = async () => {
  const filter = {
    orderType: 'REPLACEMENT', // or 'RETURN'
    // replacementType: '',
    startDate: '2025-06-01',
    endDate: '2024-06-10'
  };
  
  const email = '<EMAIL>'; 
  
  await runExport(email, filter, true);
};

// Run the script if called directly

main()