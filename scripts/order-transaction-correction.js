const AWS = require('aws-sdk');

const accessKeyId = '********************';
const secretAccessKey = 'bH61DrHGFHCc152xpE3ORxXfAxOnav2j7xcrpH8W';
const region = 'ap-south-1';
const ORDER_TABLE = 'pos-prod-order-table';
const PAYMENT_TABLE = 'pos-prod-payment-table';

const db = new AWS.DynamoDB.DocumentClient({
  accessKeyId,
  secretAccessKey,
  region,
});

const paymentItems = [
  {
    shopifyOrderName: 20198858,
    P2PRequestID: '240612083531042E200122875',
    RazorpayPOSTxnId: '240612083556504E190103621',
  },
];

let payments = [];

const scanPaymentsTable = async (TABLE, nextToken = null) => {
  let params = {
    TableName: TABLE,
  };

  if (nextToken) params.ExclusiveStartKey = nextToken;

  const { Items, LastEvaluatedKey } = await db.scan(params).promise();

  if (LastEvaluatedKey) {
    const moreData = await scanPaymentsTable(TABLE, LastEvaluatedKey);
    return [...(Items || []), ...(moreData || [])];
  }

  return Items;
};

const scanPaymentTable = async (externalRefId) => {
  return payments.find(({ externalRefId: e }) => e == externalRefId);
};

const getItem = async (id, TABLE) => {
  const params = {
    TableName: TABLE,
    Key: {
      id,
    },
  };
  const { Item } = await db.get(params).promise();
  return Item;
};

const handler = async () => {
  payments = await scanPaymentsTable(PAYMENT_TABLE);
  const orders = await scanPaymentsTable(ORDER_TABLE);

  console.log('payments :>> ', payments, '\n\n\n\n\n');
  console.log('orders :>> ', orders, '\n\n\n\n\n');

  for (const {
    P2PRequestID,
    RazorpayPOSTxnId,
    shopifyOrderName,
  } of paymentItems) {
    console.log(
      'id orderId:>> ',
      P2PRequestID,
      RazorpayPOSTxnId,
      shopifyOrderName,
    );

    const payment = await scanPaymentTable(P2PRequestID);

    const order = await getItem(payment.orderId, ORDER_TABLE);

    if (shopifyOrderName == order.shopifyOrderName) {
      const { transactions } = order;
      const { transactionDetails } = transactions;

      const updatedTransactions = transactionDetails.map((t) =>
        payment.transactionId == t.transactionId
          ? { ...t, paymentID: RazorpayPOSTxnId }
          : t,
      );

      console.log(
        'updatedTransactions :>> ',
        JSON.stringify(updatedTransactions),
      );

      await db
        .update({
          TableName: PAYMENT_TABLE,
          Key: { transactionId: payment.transactionId, orderId: order.id },
          UpdateExpression: 'SET #paymentID = :paymentID',
          ExpressionAttributeValues: {
            ':paymentID': RazorpayPOSTxnId,
          },
          ExpressionAttributeNames: {
            '#paymentID': 'paymentID',
          },
          ConditionExpression:
            'attribute_exists(orderId) AND attribute_exists(transactionId)',
        })
        .promise();

      await db
        .update({
          TableName: ORDER_TABLE,
          Key: { id: order.id },
          UpdateExpression: 'SET #transactions = :transactions',
          ExpressionAttributeValues: {
            ':transactions': {
              ...transactions,
              transactionDetails: updatedTransactions,
            },
          },
          ExpressionAttributeNames: {
            '#transactions': 'transactions',
          },
          ConditionExpression: 'attribute_exists(id)',
        })
        .promise();
    }
  }
};

handler();
