name: Deploy to ECS Fargate

on:
  push:
    branches:
      - staging
      - prod
      - dev

jobs:
  deploy:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./pos-app

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18.x'

      - name: Set environment based on branch
        id: set-env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/staging" ]]; then
            echo "ENVIRONMENT=stage" >> $GITHUB_ENV
            echo "APP_CONTAINER_NAME=app-container" >> $GITHUB_ENV
            echo "MEDIA_BUCKET=pos-stage-media" >> $GITHUB_ENV
          elif [[ "${{ github.ref }}" == "refs/heads/prod" ]]; then
            echo "ENVIRONMENT=prod" >> $GITHUB_ENV
            echo "APP_CONTAINER_NAME=POS-PROD" >> $GITHUB_ENV
            echo "MEDIA_BUCKET=pos-media" >> $GITHUB_ENV
          elif [[ "${{ github.ref }}" == "refs/heads/dev" ]]; then
            echo "ENVIRONMENT=dev" >> $GITHUB_ENV
            echo "APP_CONTAINER_NAME=pos-app" >> $GITHUB_ENV
            echo "MEDIA_BUCKET=pos-dev-media" >> $GITHUB_ENV
          fi

      - name: Create .env.${{ env.ENVIRONMENT }} file
        run: |
          echo "REGION=${{ secrets.AWS_REGION }}" >> .env.${{ env.ENVIRONMENT }}
          echo "PORT=3000" >> .env.${{ env.ENVIRONMENT }}
          echo "STACK_NAME=pos-${{ env.ENVIRONMENT }}" >> .env.${{ env.ENVIRONMENT }}
          echo "MEDIA_BUCKET=${{ env.MEDIA_BUCKET }}" >> .env.${{ env.ENVIRONMENT }}

      - name: Show .env.${{ env.ENVIRONMENT }} content
        run: cat .env.${{ env.ENVIRONMENT }}

      - name: Log current directory
        run: pwd

      - name: Install jq
        run: sudo apt-get install -y jq

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Download task definition
        run: |
          aws ecs describe-task-definition \
            --task-definition pos-${{ env.ENVIRONMENT }}-task \
            --query taskDefinition \
            > $GITHUB_WORKSPACE/.github/workflows/task-definition.json

      - name: Remove unsupported fields from task definition
        run: |
          jq 'del(.enableFaultInjection)' \
            $GITHUB_WORKSPACE/.github/workflows/task-definition.json \
            > $GITHUB_WORKSPACE/.github/workflows/task-definition-updated.json

      - name: Verify updated task definition
        run: cat $GITHUB_WORKSPACE/.github/workflows/task-definition-updated.json

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: pos-${{ env.ENVIRONMENT }}-task
          IMAGE_TAG: latest
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG . --platform linux/amd64 -f Dockerfile.${{ env.ENVIRONMENT }}
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Create or update ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: .github/workflows/task-definition-updated.json
          container-name: ${{ env.APP_CONTAINER_NAME }}
          image: ${{ steps.build-image.outputs.image }}

      - name: Set cluster name based on environment
        id: set-cluster
        run: |
          if [[ "${{ env.ENVIRONMENT }}" == "dev" ]]; then
            echo "CLUSTER_NAME=pos-${{ env.ENVIRONMENT }}-cluster" >> $GITHUB_ENV
          else
            echo "CLUSTER_NAME=pos-${{ env.ENVIRONMENT }}" >> $GITHUB_ENV
          fi

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: pos-${{ env.ENVIRONMENT }}-alb
          cluster: ${{ env.CLUSTER_NAME }}
          wait-for-service-stability: true