{"dependencies": {"@nestjs/mapped-types": "*", "graphql-client": "^2.0.1", "graphql-request": "^7.1.2"}, "name": "tsc-pos-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"format": "prettier --write \"pos-app/src/**/*.ts\" \"pos-app/test/**/*.ts\"", "lint": "eslint \"pos-app/{src,apps,libs,test}/**/*.ts\" \"sam-backend/**/**/*.js\" --fix", "precommit": "lint-staged", "prepare": "npx husky install", "start:dev": "cd pos-app && npm run start:dev", "start:stage": "cd pos-app && npm run start:stage", "start:prod": "cd pos-app && npm run start:prod"}, "lint-staged": {"*.ts": ["npm run lint", "prettier --write", "npm run format", "git add"]}, "husky": {"hooks": {"pre-commit": "npm run precommit"}}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@typescript-eslint/eslint-plugin": "^7.4.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.10", "lint-staged": "^15.2.1", "nodemon": "^3.1.0", "prettier": "^3.0.0"}}