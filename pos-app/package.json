{"name": "tsc-pos-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "build:local": "export NODE_ENV=local && nest build", "build:dev": "export NODE_ENV=dev && nest build", "build:stage": "export NODE_ENV=stage && nest build", "build:prod": "export NODE_ENV=prod && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nodemon start", "start:temp": "nest start", "start:stage:local": "export NODE_ENV=stage && nodemon start --watch", "start:dev": "export NODE_ENV=dev && nodemon start --watch", "start:local": "export NODE_ENV=local && nodemon start --watch", "start:stage": "export NODE_ENV=stage && nodemon start --watch", "start:prod": "export NODE_ENV=prod && nodemon start --watch", "start:dist:dev": "export NODE_ENV=dev && node dist/main", "start:dist:stage": "export NODE_ENV=stage && node dist/main", "start:dist:prod": "export NODE_ENV=prod && node dist/main", "start:debug": "nodemon start --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "precommit": "lint-staged", "prepare": "npx husky install"}, "lint-staged": {"*.ts": ["npm run lint", "prettier --write", "npm run format", "git add"]}, "husky": {"hooks": {"pre-commit": "npm run precommit"}}, "dependencies": {"@apollo/server": "^4.10.0", "@aws-sdk/client-cognito-identity-provider": "^3.507.0", "@aws-sdk/client-dynamodb": "^3.507.0", "@aws-sdk/client-s3": "^3.556.0", "@aws-sdk/client-secrets-manager": "^3.507.0", "@aws-sdk/client-sqs": "^3.654.0", "@aws-sdk/client-ssm": "^3.540.0", "@aws-sdk/lib-dynamodb": "^3.507.0", "@aws-sdk/s3-request-presigner": "^3.556.0", "@elastic/elasticsearch": "7.10", "@nestjs/apollo": "^12.0.11", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.1", "@nestjs/core": "^10.0.0", "@nestjs/graphql": "^12.0.11", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.1", "append-query": "^2.1.1", "aws-sdk": "^2.1552.0", "body-parser": "^1.20.3", "chrome-aws-lambda": "^10.1.0", "class-validator": "^0.14.1", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csvtojson": "^2.0.10", "ejs": "^3.1.10", "graphql": "^16.8.1", "graphql-request": "^7.1.2", "isomorphic-fetch": "^3.0.0", "json-2-csv": "^5.5.1", "jsonwebtoken": "^9.0.2", "jwk-to-pem": "^2.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "nodemailer": "^6.9.13", "path": "^0.12.7", "puppeteer-core": "^10.1.0", "qrcode": "^1.5.4", "razorpay": "^2.9.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "to-words": "^4.0.1", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/append-query": "^2.0.3", "@types/cors": "^2.8.17", "@types/ejs": "^3.1.5", "@types/express": "^4.17.17", "@types/html-pdf": "^3.0.3", "@types/isomorphic-fetch": "^0.0.39", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "^9.0.6", "@types/jwk-to-pem": "^2.0.3", "@types/lodash": "^4.14.202", "@types/multer": "^1.4.13", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.14", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.0.10", "jest": "^29.5.0", "lint-staged": "^15.2.1", "moment-timezone": "^0.5.46", "nodemon": "^3.0.3", "prettier": "^3.0.0", "puppeteer": "^22.6.5", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}