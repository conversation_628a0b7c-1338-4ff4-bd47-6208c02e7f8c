# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type EmployeeData {
  id: ID!
  firstName: String!
  lastName: String!
  email: String
  cognitoId: String
  phone: String
  isActive: Boolean!

  """Role to a specific store"""
  role: String

  """Access to a store"""
  stores: [String!]
  createdAt: String!
  updatedAt: String!

  """Designation of an employee"""
  designation: String
}

type Employees {
  data: [EmployeeData!]

  """Count of Total data"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type Employee {
  data: EmployeeData
  message: String
  status: Float
  success: Boolean
}

type VariantData {
  id: ID!
  product_id: ID!
  type: String!
  admin_graphql_api_id: ID
  barcode: String
  product_type: String
  compare_at_price: String
  createdAt: String
  created_at: String
  fulfillment_service: String
  grams: Float
  image_id: String
  image: Image
  inventory_item_id: ID
  inventory_management: String
  inventory_policy: String
  productTitle: String
  inventory_quantity: Float
  metafields: [Metafield!]
  old_inventory_quantity: Float
  option1: String
  option2: String
  option3: String
  option4: String
  option5: String
  option6: String
  option7: String
  option8: String
  option9: String
  option10: String
  position: Float
  price: String
  requires_shipping: Boolean
  sku: String
  status: ProductStatus
  taxable: Boolean
  title: String
  updatedAt: String
  updated_at: String
  weight: Float
  weight_unit: String
  variantTitle: String
}

enum ProductStatus {
  ACTIVE
  DRAFT
}

type Variants {
  data: [VariantData!]

  """Count of Total data"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type Variant {
  data: VariantData
  message: String
  status: Float
  success: Boolean
}

type Image {
  admin_graphql_api_id: ID
  alt: String
  created_at: String
  height: Float
  id: ID
  position: Float
  product_id: ID
  src: String
  updated_at: String
  variant_ids: [ID!]
  width: Float
}

type Metafield {
  admin_graphql_api_id: ID
  created_at: String
  description: String
  id: ID
  key: String
  namespace: String
  owner_id: ID
  owner_resource: String
  type: String
  updated_at: String
  value: String
}

type Option {
  id: ID
  name: String
  position: Float
  product_id: ID
  values: [String!]
}

type ProductData {
  id: ID!
  type: String!
  admin_graphql_api_id: ID
  body_html: String
  createdAt: String
  created_at: String
  handle: String!
  price: String
  image: Image
  images: [Image!]
  metafields: [Metafield!]
  options: [Option!]
  product_type: String
  published_at: String
  published_scope: String
  status: ProductStatus
  tags: [String!]
  template_suffix: String
  title: String
  updatedAt: String
  updated_at: String
  vendor: String
  variants: Variants
  variant: Variant
  product_id: ID
  sku: String
  inventory_quantity: Float
  inventory_management: String
  productTitle: String
  variantTitle: String
  minimumEmi: String
}

type Products {
  data: [ProductData!]

  """Count of Total data"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type Product {
  data: ProductData
  message: String
  status: Float
  success: Boolean
}

type Response {
  message: String!
  status: Float
  success: Boolean
}

type ProductTypeData {
  key: String!
  doc_count: Float!
}

type ProductType {
  data: [ProductTypeData!]
  count: Float
  message: String
  status: Float
  success: Boolean
}

type PrerequisiteQuantityRange {
  greater_than_or_equal_to: String
}

type PrerequisiteSubtotalRange {
  greater_than_or_equal_to: String
}

type PrerequisiteToEntitlementPurchase {
  prerequisite_amount: Float
}

type PrerequisiteToEntitlementQuantityRatio {
  entitled_quantity: Float
  prerequisite_quantity: Float
}

type PriceRule {
  admin_graphql_api_id: String
  allocation_limit: String
  allocation_method: String
  created_at: String
  customer_segment_prerequisite_ids: [Float!]
  customer_selection: String
  ends_at: String
  entitled_collection_ids: [Float!]
  entitled_country_ids: [Float!]
  entitled_product_ids: [Float!]
  entitled_variant_ids: [Float!]
  id: Float
  once_per_customer: Boolean
  prerequisite_collection_ids: [Float!]
  prerequisite_customer_ids: [Float!]
  prerequisite_product_ids: [Float!]
  prerequisite_quantity_range: PrerequisiteQuantityRange
  prerequisite_shipping_price_range: String
  prerequisite_subtotal_range: PrerequisiteSubtotalRange
  prerequisite_to_entitlement_purchase: PrerequisiteToEntitlementPurchase
  prerequisite_to_entitlement_quantity_ratio: PrerequisiteToEntitlementQuantityRatio
  prerequisite_variant_ids: [Float!]
  starts_at: String
  target_selection: String
  target_type: String
  title: String
  updated_at: String
  usage_limit: String
  value: String
  value_type: String
}

type CouponData {
  admin_graphql_api_id: String
  code: String
  createdAt: String
  created_at: String
  id: Float
  price_rule_id: Float
  updatedAt: String
  updated_at: String
  usage_count: Float
  isValidated: Boolean
  priceRule: PriceRule
}

type Coupon {
  data: CouponData
  message: String
  status: Float
  success: Boolean
}

type PromotionalCodeDiscountData {
  isValidated: Boolean!
  discount: Float!
  message: String
}

type PromotionalCodeDiscount {
  data: PromotionalCodeDiscountData
  message: String
  status: Float
  success: Boolean
}

type Address {
  """Address Line 1"""
  line1: String!

  """Address Line 2"""
  line2: String

  """City"""
  city: String!

  """State"""
  state: String!

  """State Code"""
  stateCode: String

  """State Code"""
  stateNo: String

  """Country"""
  country: String!

  """Pin code"""
  pinCode: String!

  """Latitude"""
  latitude: String

  """Longitude"""
  longitude: String
}

type BillingInfo {
  """Name of the Entity"""
  name: String!

  """Address of the Entity"""
  address: Address!
}

type GstDetails {
  """GST Number"""
  gstNumber: String!

  """GST Owner Name"""
  companyName: String!
}

type Snapmint {
  """snapmint Merchant ID"""
  merchantId: String!

  """snapmint token"""
  accessToken: String!

  """snapmint token"""
  merchantKey: String!
}

type Pinelabs {
  """Pinelabs POS ID"""
  posId: String!

  """pine labs storeid"""
  storeId: String!
}

type StoreData {
  """Store ID"""
  id: ID!

  """Name of the store"""
  name: String!

  """Description of the store"""
  description: String

  """Billing Information with Name and Address info"""
  reviewsBrochure: String

  """store location key"""
  getStoreLocationKey: String

  """map link"""
  mapLink: String

  """Store short code"""
  storeShortCode: String

  """Store Timings"""
  storeTimings: String

  """Store Image"""
  storeImage: [String!]

  """Address of the store"""
  address: Address!

  """Contact number of the store"""
  phone: String!

  """Email address of the store"""
  email: String!

  """Billing Information with Name and Address info"""
  billingInfo: BillingInfo!

  """GST Details"""
  gstDetails: GstDetails

  """Time of store creation"""
  createdAt: String!

  """Time of store update"""
  updatedAt: String!

  """Active/Inactive status"""
  isActive: Boolean!

  """Quotation Start Code"""
  quotationStartCode: String!

  """Invoice Start Code"""
  invoiceStartCode: String!

  """SAP Location Code"""
  sapLocationCode: String!

  """Warehouse mapping ID"""
  warehouseMappingId: String!

  """EasyEcom Location ID"""
  easyEcomWarehouseLocationId: String

  """Source Warehouse mapping ID"""
  sourceWarehouseMappingId: String!

  """SAP Account Code"""
  accountCode: String!

  """SAP Card Code"""
  cardCode: String!

  """POS ID"""
  posIds: [String!]

  """MSwipe POS ID"""
  mswipePosIds: [String!]

  """Area Sales Manager"""
  asm: String!

  """DID Number"""
  didNumber: String

  """Zonal Head"""
  zonalHead: String!

  """Billing Information with Name and Address info"""
  snapmintInfo: Snapmint

  """Billing Information with Name and Address info"""
  pinelabsInfo: [Pinelabs!]
}

type Stores {
  data: [StoreData!]

  """Count of Total data"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type Store {
  data: StoreData
  message: String
  status: Float
  success: Boolean
}

type CustomRangeData {
  min: String!
  max: String!
  includesMin: Boolean
}

type CustomData {
  length: String!
  breadth: String!
  height: String!
  lengthRange: CustomRangeData
  breadthRange: CustomRangeData
}

type CartProduct {
  productId: String!
  variantId: String
  quantity: Int!
  customData: CustomData
  price: Float
  priceType: String
}

type CustomCode {
  value_type: CouponType!
  approver: String!
  value: Float!
}

enum CouponType {
  FIXED
  PERCENTAGE
}

type CartData {
  id: String!
  customerId: String!
  cartProducts: [CartProduct!]
  storeId: String!
  employeeId: String!
  campaignCode: String
  customCode: CustomCode
  promotionalCode: String
  notes: String
  source: String
  createdAt: String!
  updatedAt: String!
  pinCode: String
}

type Carts {
  data: [CartData!]

  """Count of Total data"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type Cart {
  data: CartData
  message: String
  status: Float
  success: Boolean
}

type ApproverDetails {
  """The unique identifier of the store requesting the discount approval."""
  name: String!

  """The unique identifier of the store requesting the discount approval."""
  email: String!

  """The unique identifier of the store requesting the discount approval."""
  phone: String!
}

type VerificationResponseData {
  """
  The status of the discount approval request. The options are PENDING, REJECTED, APPROVED, APPLIED, EXPIRED.
  """
  customDiscountApprovalStatus: CustomDiscountApprovalStatus!
}

enum CustomDiscountApprovalStatus {
  REQUESTED
  REJECTED
  APPROVED
  APPLIED
  EXPIRED
}

type VerificationResponse {
  data: VerificationResponseData
  message: String
  status: Float
  success: Boolean
}

type CustomDiscountVerificationData {
  """The unique identifier of the store requesting the discount approval."""
  storeId: String!

  """Quotation ID"""
  quotationId: String!

  """Quotation ID"""
  id: String!

  """The staff ID of the person requesting the discount approval."""
  employeeId: String!

  """The customer’s unique number associated with the discount request."""
  customerNumber: String!

  """The initial cart amount before applying the discount."""
  initialCartDiscountedAmount: Float!

  """The initial cart amount before applying the discount."""
  cartTotalAmount: Float!

  """The final cart amount after applying the discount."""
  finalCartDiscountedAmount: Float

  """
  The unique identifier for the order associated with the discount request.
  """
  orderId: String

  """The discount amount requested for the order."""
  customDiscountType: CouponType!

  """The discount amount requested for the order."""
  customDiscountValue: Float!

  """The discount amount requested for the order."""
  customDiscountAmount: Float!

  """The mobile number of the customer requesting the discount."""
  approverDetails: ApproverDetails!

  """The OTP to whom the discount approval request is made."""
  otp: String!

  """
  The status of the discount approval process (REQUESTED, REJECTED, APPROVED, APPLIED, EXPIRED).
  """
  customDiscountApprovalStatus: CustomDiscountApprovalStatus!

  """The timestamp when the discount approval request was made."""
  approvalRequestTimestamp: String!

  """
  The timestamp when the discount approval was either approved or rejected.
  """
  approvalApprovedOrRejectedTimestamp: String

  """Final Amount"""
  promotionalDiscountAmount: Float!

  """The promotional code applied to the order."""
  promotionalCode: String

  """Final Amount"""
  campaignDiscountAmount: Float!

  """The campaign code applied to the order."""
  campaignCode: String

  """The timestamp when the discount was applied to the order."""
  discountAppliedTimestamp: String

  """The timestamp when the discount expired."""
  discountExpiredTimestamp: String!

  """The discount amount requested for the order."""
  additionalDiscountPercentage: Float!
}

type CustomDiscountVerification {
  data: CustomDiscountVerificationData
  message: String
  status: Float
  success: Boolean
}

type CustomerMetadata {
  """First Name of the customer"""
  firstName: String

  """First Name of the customer"""
  lastName: String

  """Phone number"""
  phone: String

  """Email of the customer"""
  email: String

  """date of birth"""
  dob: String

  """anniversaryDate"""
  anniversaryDate: String

  """serviceLift"""
  serviceLift: String

  """accommodationType"""
  accommodationType: String

  """landmark"""
  landmark: String

  """latitude"""
  latitude: String

  """longitude"""
  longitude: String
}

"""Details of the employee discount applied to a quotation"""
type DiscountDetails {
  """Timestamp when the discount was applied (ISO format)"""
  appliedAt: String!

  """Unique discount code applied for the employee"""
  code: String!

  """Discount value applied to the quotation"""
  discountValue: Float!

  """Quotation ID associated with the discount"""
  quotationId: String!
}

type EmployeeMasterDetailsData {
  """First Name of the customer"""
  name: String!
  discountDetails: DiscountDetails

  """Phone number"""
  phone: String!
}

type EmployeeMasterDetails {
  """Quotation"""
  data: EmployeeMasterDetailsData
  message: String
  status: Float
  success: Boolean
}

type DeliveryChargeApprovalDetails {
  name: String!
  phone: String!
  requestedCharge: Float!
  status: DeliveryChargeApprovalStatus!
  otp: String
  expiresAt: String!
  createdAt: String!
  updatedAt: String!
}

enum DeliveryChargeApprovalStatus {
  CREATED
  APPROVED
  EXPIRED
  APPLIED
}

type CustomDiscountVerificationDetails {
  """Quotation ID"""
  id: String!

  """The discount amount requested for the order."""
  customDiscountAmount: Float!

  """The initial cart amount before applying the discount."""
  initialCartDiscountedAmount: Float!

  """The initial cart amount before applying the discount."""
  cartTotalAmount: Float!

  """The final cart amount after applying the discount."""
  finalCartDiscountedAmount: Float

  """The mobile number of the customer requesting the discount."""
  approverDetails: ApproverDetails!

  """
  The status of the discount approval process (REQUESTED, REJECTED, APPROVED, APPLIED, EXPIRED).
  """
  customDiscountApprovalStatus: CustomDiscountApprovalStatus!

  """The discount amount requested for the order."""
  additionalDiscountPercentage: Float!

  """The discount amount requested for the order."""
  customDiscountType: CouponType!

  """The discount amount requested for the order."""
  customDiscountValue: Float!

  """Final Amount"""
  promotionalDiscountAmount: Float!

  """Final Amount"""
  campaignDiscountAmount: Float!

  """The timestamp when the discount expired."""
  discountExpiredTimestamp: String!
}

type EmployeeDiscountDetails {
  """Employee's phone number"""
  phone: String!

  """Employee's phone number"""
  quotationId: String!

  """Employee's full name"""
  name: String!

  """OTP sent for discount approval"""
  otp: String!

  """Timestamp when the request was created"""
  createdAt: String!

  """Timestamp when the request was last updated"""
  updatedAt: String!

  """Status of the discount approval request"""
  status: EmployeeDiscountStatus!

  """Expiry time for the OTP"""
  expiresAt: String!
}

"""Status of the employee discount request"""
enum EmployeeDiscountStatus {
  PENDING
  APPROVED
  APPLIED
  EXPIRED
}

type QuotationProduct {
  id: String
  productId: String!
  variantId: String
  quantity: Int!
  customData: CustomData
  price: Float
  priceType: String
  mrp: String
  title: String
  variantTitle: String
  image: Image
  sku: String!
  edd: Float
  productType: String
  itemDiscount: Float
  finalItemPrice: Float!
  originalPrice: Float
  hasColorOptions: Boolean
}

type InteriorArchitectureDetails {
  """GST Number of the customer."""
  id: String!

  """Company Name of the customer."""
  name: String

  """Company Name of the customer."""
  source: String

  """Commissioned."""
  commissioned: Boolean
}

type GSTDetails {
  """GST Number of the customer."""
  gstNumber: String!

  """Company Name of the customer."""
  companyName: String!
}

type QuotationAddress {
  """Shopify address Id"""
  shopifyAddressId: String

  """Address Line 1"""
  line1: String!

  """Address Line 2"""
  line2: String

  """City"""
  city: String!

  """State"""
  state: String!

  """Country"""
  country: String!

  """Pin code"""
  pinCode: String!
}

type AdditionalPromotionalCoupon {
  """Promotional Code"""
  promotionalCode: String!

  """Promotional Discount Amount"""
  promotionalDiscountAmount: Float!
}

type QuotationData {
  """ID of the customer for the quotation."""
  id: String!

  """Order ID of the customer for the quotation."""
  orderId: String

  """ID of the customer for the quotation."""
  customerId: String!

  """List of products being quoted."""
  quotationProducts: [QuotationProduct!]!

  """ID of the store issuing the quotation."""
  storeId: String!

  """ID of the employee creating the quotation."""
  employeeId: String!

  """Campaign code associated with the quotation."""
  campaignCode: String

  """Custom code for the quotation."""
  customCode: CustomCode

  """Promotional code for the quotation."""
  promotionalCode: String

  """Notes or comments for the quotation."""
  notes: String

  """Source for the quotation."""
  source: String

  """Customer metadata for the quotation."""
  customer: CustomerMetadata

  """Shipping Address for the quotation."""
  shippingAddress: QuotationAddress

  """Billing Address for the quotation."""
  billingAddress: QuotationAddress
  gstDetails: GSTDetails

  """Interior Architecture"""
  interiorArchitecture: InteriorArchitectureDetails
  deliveryChargeApprovalDetails: DeliveryChargeApprovalDetails

  """delivery amount"""
  deliveryCharge: Float

  """Total amount"""
  totalAmount: Float!

  """Final Amount"""
  finalDiscountedAmount: Float!

  """Final Amount"""
  promotionalDiscountAmount: Float!

  """Additional Promotional Coupons"""
  additionalPromotionalCoupons: [AdditionalPromotionalCoupon!]
  customDiscountVerificationDetails: CustomDiscountVerificationDetails

  """"""
  employeeDiscountDetails: EmployeeDiscountDetails

  """Final Amount"""
  customDiscountAmount: Float!

  """Final Amount"""
  campaignDiscountAmount: Float!

  """Final Amount"""
  freeProducts: [CartProduct!]

  """Quotation Status"""
  status: QuotationStatus!

  """Created At"""
  createdAt: String!

  """Updated At"""
  updatedAt: String!

  """Expires At"""
  expiresAt: String!

  """Customer acknowledgement for the order."""
  customerAcknowledgement: String

  """Customer Identity proof for the order."""
  customerIdentityProof: String

  """Quotation Type"""
  type: String

  """Price lock booking amount"""
  bookingAmount: Float

  """Price lock booking amount status"""
  bookingAmountStatus: String

  """BNPL Product data"""
  bnplProduct: QuotationProduct

  """BNPL Super Order Id"""
  superOrderId: String
}

enum QuotationStatus {
  ACTIVE
  EXPIRED
  ORDER_CREATED
  PAYMENT_INITIATED
  CONVERTED
  PRICE_LOCKED
}

type Quotation {
  """Quotation"""
  data: QuotationData
  message: String
  status: Float
  success: Boolean
}

type Quotations {
  """Quotations"""
  data: [QuotationData!]

  """Total Count"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type ShopifyAddressData {
  id: Float
  customerId: String
  firstName: String
  lastName: String
  company: String
  line1: String
  line2: String
  city: String
  state: String
  country: String
  pinCode: String
  name: String
  province_code: String
  country_code: String
  country_name: String
  default: Boolean
  serviceLift: String
  accommodationType: String
  landmark: String
  latitude: Float
  longitude: Float
}

type EmailMarketingConsent {
  state: String
  opt_in_level: String
  consent_updated_at: String
}

type SmsMarketingConsent {
  state: String
  opt_in_level: String
  consent_updated_at: String
  consent_collected_from: String
}

type CustomerData {
  id: String
  email: String
  created_at: String
  updated_at: String
  firstName: String
  lastName: String
  dob: String
  anniversaryDate: String!
  orders_count: Float
  state: String
  total_spent: String
  last_order_id: Float
  note: String
  verified_email: Boolean
  multipass_identifier: String
  tax_exempt: Boolean
  tags: String
  last_order_name: String
  currency: String
  phone: String
  addresses: [ShopifyAddressData!]
  tax_exemptions: [String!]
  email_marketing_consent: EmailMarketingConsent
  sms_marketing_consent: SmsMarketingConsent
  admin_graphql_api_id: String
  default_address: ShopifyAddressData
}

type Customer {
  data: CustomerData
  message: String
  status: Float
  success: Boolean
}

type ShopifyAddress {
  data: ShopifyAddressData
  message: String
  status: Float
  success: Boolean
}

type PincodeData {
  """Pincode"""
  id: String!

  """State"""
  state: String

  """SLA"""
  SLA: String

  """applicability"""
  applicability: String

  """isDeliverable"""
  isDeliverable: Boolean

  """City"""
  city: String

  """State code"""
  stateCode: String

  """State code"""
  stateNo: String
}

type Pincode {
  data: PincodeData
  message: String
  status: Float
  success: Boolean
}

type WishlistData {
  productId: String
  productUrl: String
  productType: String
  productAvailability: String
  createdAt: String
  variantId: String
  product: ProductData
  variant: VariantData
}

type Wishlist {
  data: [WishlistData!]
}

type ApplicableProducts {
  productId: String!
  variantId: String
}

type StoreWiseUsageCount {
  storeId: String!
  usageCount: Int
}

type StoreWiseUsageCountLimit {
  storeId: String!
  usageLimit: Int
}

type CampaignCouponData {
  code: String!
  storeWiseUsageCount: [StoreWiseUsageCount!]
  storeUsageLimit: [StoreWiseUsageCountLimit!]
  usageResetType: CouponUsageResetType
  maxOrderValue: Float
  usageLimitForEachStore: Boolean
  createdAt: String!
  updatedAt: String!
  description: String
  campaignCouponType: CampaignCouponType!
  buyXQuantity: Int
  buyMinXQuantity: Int
  status: CouponStatus
  getYAmount: Float
  getYPercentage: Float
  getYQuantity: Int
  getYProduct: ApplicableProducts
  minOrderValue: Float
  maxDiscount: Float
  applicableProducts: [ApplicableProducts!]
  applicableProductsY: [ApplicableProducts!]
  startsAt: String!
  endsAt: String
  storeAllocationType: StoreAllocationType!
  entitledStoreIds: [String!]
  isActive: Boolean
  usageLimit: Int
  usageCount: Int
  customOrderValueErrorMsg: String
  promoApplicable: String
  promoCoupons: [String!]
  couponApplicationType: CouponApplicationType
}

enum CouponUsageResetType {
  DAILY
  WEEKLY
  MONTHLY
  DISABLED
}

enum CampaignCouponType {
  FIXED
  PERCENTAGE
  BUY_X_GET_Y
  BUY_X_GET_Y_AT_Z
  FREEBIE
}

enum CouponStatus {
  ACTIVE
  EXPIRED
  SCHEDULED
  DEACTIVATED
}

enum StoreAllocationType {
  EACH
  ACROSS
}

enum CouponApplicationType {
  CART
  PRODUCT
}

type CampaignCoupons {
  data: [CampaignCouponData!]
  count: Int
  message: String
  status: Float
  success: Boolean
}

type CampaignCoupon {
  data: CampaignCouponData
  message: String
  status: Float
  success: Boolean
}

type Property {
  name: String!
  value: String!
}

type ReplacedProduct {
  productId: String!
  variantId: String
  quantity: Int!
  price: Float
  title: String
  customData: CustomData

  """VariantTitle of the order product"""
  variantTitle: String
  sku: String!
  productType: String
  metafields: [Metafield!]

  """Image of the order product"""
  image: Image
  finalItemPrice: Float!
  itemDiscount: Float
  bankDiscount: Float
  edd: Float
  properties: [Property!]
}

type UpdatedRefundPrice {
  approver: String!
  reason: String!
}

type ReplacementOrderData {
  id: String!

  """RR order payment link status"""
  linkStatus: String

  """RR order payment link time to expire"""
  linkTimeToExpire: String

  """carrier"""
  carrier: String

  """creditNoteId"""
  creditNoteId: String
  originalPaymentMode: [String!]
  transactionIDs: [String!]
  RRTicketId: String
  isRazorpay: Boolean
  shopifyOrderId: String!
  orderId: String
  customerId: String!
  replacedProduct: ReplacedProduct!
  pickupPriority: PickupPriority
  replacementType: ReplacementType
  ticketId: String!
  orderType: OrderType!
  replacementOption: ReplacementOptions
  replaceableDiscountType: ReplaceableDiscountType

  """List of products being quoted."""
  orderProducts: [ReplacedProduct!]

  """List of products being quoted."""
  accessories: [ReplacedProduct!]

  """Order status."""
  status: OrderStatus!

  """Customer metadata for the quotation."""
  customer: OrderCustomerMetadata

  """Custom code for the quotation."""
  customCode: CustomCode

  """Custom Discount Amount"""
  customDiscountAmount: Float

  """Total amount"""
  totalAmount: Float

  """Final Amount"""
  finalDiscountedAmount: Float

  """Refunded Price"""
  refundedPrice: Float

  """Order Total"""
  orderTotal: Float

  """Bank's Discount"""
  bankDiscount: Float

  """Primary reason for the replacement"""
  primaryReason: String

  """Secondary reason or sub-reason for the replacement"""
  secondaryReason: String

  """Department related to the replacement request"""
  department: String

  """Agent name, required when department is AGENT"""
  agentName: String

  """Additional comments or notes about the replacement"""
  additionalComments: String

  """Shipping Address for the quotation."""
  shippingAddress: QuotationAddress

  """Shipping charges."""
  isShippingCharged: Boolean

  """Shipping Charges."""
  shippingCost: Float

  """Billing Address for the quotation."""
  billingAddress: QuotationAddress

  """Created At"""
  orderCreatedAt: String!

  """Created At"""
  createdAt: String!

  """Updated At"""
  updatedAt: String!

  """Is in replaceable window"""
  inReplaceableWindow: Boolean

  """Delivery Type of the order product"""
  deliveryStatus: DeliveryType

  """Max Delivery date of the order product"""
  deliveryDate: String

  """Min Delivery date of the order product"""
  minDeliveryDate: String

  """Easy ecom Reference Number"""
  eeRefNo: String

  """Easy ecom Reference Number"""
  referenceCode: String

  """Manually modified refund price reason"""
  updatedRefundPrice: UpdatedRefundPrice

  """Easy ecom locationKey"""
  locationKey: String

  """Easy ecom awbNumber"""
  awbNumber: String

  """Replaceable part product ids"""
  partProductsIds: [String!]

  """refundDetailAlreadyAdded"""
  refundDetailAlreadyAdded: String

  """request id"""
  requestId: String
}

enum PickupPriority {
  DELIVERY_BEFORE_PICKUP
  PICKUP_BEFORE_DELIVERY
  PICKUP_WITH_DELIVER
  NO_PICKUP
}

enum ReplacementType {
  FULL
  PART
}

enum OrderType {
  POS
  REPLACEMENT
  RETURN
  BNPL
}

enum ReplacementOptions {
  REFUND
  ACCESSORIES
}

enum ReplaceableDiscountType {
  WAIVE_OFF
  PART_DISCOUNT
  NO_DISCOUNT
  FORCED_DISCOUNT
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PARTIALLY_CONFIRMED
  SENT_TO_SHOPIFY
  ON_HOLD
  CANCELLED
  DELETED_FROM_SHOPIFY
  SENT_TO_EE
  ORDER_CREATED
  AWB_CANCELLED
  AWB_REINITIALIZED
}

enum DeliveryType {
  CASH_AND_CARRY
  WEBSITE_DELIVERY_DATE
  FUTURE_DISPATCH_DATE
  BOOK_NOW_PAY_LATER
}

type LastEvaluatedKeyObj {
  id: String!
  shopifyOrderId: String!
}

type ReplacementOrder {
  """Replacement order."""
  data: ReplacementOrderData
  message: String
  status: Float
  success: Boolean
}

type PaymentPendingReplacementOrders {
  """Replacement order."""
  data: [ReplacementOrderData!]
  count: Float
  lastEvaluatedKey: LastEvaluatedKeyObj
  message: String
  status: Float
  success: Boolean
}

type CancelInitiatedReturn {
  """Operation success status"""
  success: Boolean

  """Response message"""
  message: String

  """HTTP status code"""
  status: Float

  """Cancelled AWB number"""
  cancelledAwbNumber: String

  """Error source if operation fails"""
  errorSource: String
}

type RegenerateReturn {
  """Operation success status"""
  success: Boolean

  """Response message"""
  message: String

  """HTTP status code"""
  status: Float

  """New AWB number generated"""
  newAwbNumber: String

  """Previous AWB number generated"""
  previousAwbNumber: String

  """Error source if operation fails"""
  errorSource: String
}

type OrderCustomerMetadata {
  """First Name of the customer"""
  firstName: String

  """First Name of the customer"""
  lastName: String

  """Phone number"""
  phone: String!

  """Email of the customer"""
  email: String

  """serviceLift"""
  serviceLift: String

  """accommodationType"""
  accommodationType: String

  """landmark"""
  landmark: String

  """latitude"""
  latitude: String

  """longitude"""
  longitude: String
}

type RaisedIssueData {
  """Timestamp when created"""
  createdAt: String

  """description of issue"""
  description: String

  """Issue type"""
  subject: String

  """LSQ Ticket ID (Link to the ticket)"""
  ticketId: String!
}

type OrderProductData {
  id: String
  productId: String!
  variantId: String

  """Quantity of the order product"""
  quantity: Int!

  """Custom Meta data"""
  customData: CustomData

  """Price of the order product"""
  price: Float!

  """Price Type"""
  priceType: String

  """Title of the order product"""
  title: String

  """VariantTitle of the order product"""
  variantTitle: String

  """Image of the order product"""
  image: Image

  """Sku of the order product"""
  sku: String!
  productType: String
  edd: Float

  """GST Amount of the order product"""
  hsn: String

  """GST Rate of the order product"""
  gstRate: String

  """GST Amount of the order product"""
  gstAmount: Float

  """Delivery Type of the order product"""
  deliveryStatus: DeliveryType

  """Delivery date of the order product"""
  deliveryDate: String

  """Delivery range of the order product"""
  deliveryRange: String

  """Min Delivery date of the order product"""
  minDeliveryDate: String

  """EDD assumed for this order product"""
  isAssumed: Boolean
  itemDiscount: Float
  finalItemPrice: Float!
  dispatchDate: String
  fallbackEdd: Boolean

  """Final Item Price Without GST"""
  finalItemPriceWithoutGst: Float
}

type PaymentDetails {
  """Transactions"""
  transactionDetails: [PaymentData!]

  """Payment Paid by the customer"""
  totalPaidAmount: Float

  """Payment Paid by the customer"""
  remainingAmount: Float

  """Payment Requested Amount by the customer"""
  requestedAmount: Float
}

type ErrorData {
  isError: Boolean
  scope: String
  message: String
}

type OrderData {
  """ORDER ID of the customer for the order."""
  id: ID!

  """RR order payment link status"""
  linkStatus: String

  """RR order payment link time to expire"""
  linkTimeToExpire: String

  """Additional Promotional Coupons"""
  additionalPromotionalCoupons: [AdditionalPromotionalCoupon!]

  """carrier"""
  carrier: String

  """creditNoteId"""
  creditNoteId: String
  originalPaymentMode: [String!]
  transactionIDs: [String!]

  """isRazorpay mode"""
  isRazorpay: Boolean

  """RRTicketId (Link to the ticket)"""
  RRTicketId: String

  """AWBApprovedBy name"""
  AWBApprovedBy: String

  """Easy ecom Reference Number"""
  eeRefNo: String

  """"""
  employeeDiscountDetails: EmployeeDiscountDetails

  """Easy ecom awbNumber """
  awbNumber: String

  """Shipping Charges."""
  shippingCost: Float

  """Shipping charges."""
  isShippingCharged: Boolean

  """Reason for Holding the Order"""
  holdOrderReason: String
  pickupPriority: PickupPriority
  replacementType: ReplacementType
  ticketId: String

  """Quotation ID of the customer for the order."""
  quotationId: String!

  """Replaced product for the order."""
  replacedProduct: ReplacedProduct

  """ID of the customer for the order."""
  customerId: String

  """Min Delivery date of the order"""
  minDeliveryDate: String

  """Delivery date of the order"""
  deliveryDate: String
  dispatchDate: String
  eddHeaderId: String
  posFallbackEdd: Boolean

  """Unhold future dispatch date of the order"""
  unHoldFutureDispatchDate: String

  """Delivery Type of the order"""
  deliveryStatus: DeliveryType
  deliveryChargeApprovalDetails: DeliveryChargeApprovalDetails

  """delivery amount"""
  deliveryCharge: Float

  """List of products being ordered."""
  orderProducts: [OrderProductData!]

  """Payment being ordered."""
  transactions: PaymentDetails

  """ID of the store issuing the order."""
  storeId: String!

  """Order type."""
  orderType: OrderType!

  """Order status."""
  status: OrderStatus!

  """ID of the employee creating the order."""
  employeeId: String!

  """Campaign code associated with the order."""
  campaignCode: String

  """Custom code for the order."""
  customCode: CustomCode

  """Promotional code for the order."""
  promotionalCode: String

  """Notes or comments for the order."""
  notes: String

  """Source for the order."""
  source: String

  """Customer acknowledgement for the order."""
  customerAcknowledgement: String

  """Customer Identity proof for the order."""
  customerIdentityProof: String

  """Customer metadata for the order."""
  customer: OrderCustomerMetadata

  """Shipping Address for the order."""
  shippingAddress: QuotationAddress!

  """Billing Address for the order."""
  billingAddress: QuotationAddress!

  """GST Details for the order."""
  gstDetails: GstDetails

  """Interior Architecture."""
  interiorArchitecture: InteriorArchitectureDetails

  """Total amount"""
  totalAmount: Float!

  """Final Amount"""
  finalDiscountedAmount: Float!

  """Refunded Amount"""
  refundedPrice: Float

  """Final Amount"""
  promotionalDiscountAmount: Float!

  """Final Amount"""
  customDiscountAmount: Float

  """Final Amount"""
  campaignDiscountAmount: Float!

  """Free products"""
  freeProducts: [CartProduct!]

  """Shopify Order ID"""
  shopifyOrderId: String

  """Shopify Order status"""
  shopifyOrderStatus: String

  """Shopify Payment status"""
  shopifyPaymentStatus: String

  """Shopify Order name"""
  shopifyOrderName: String

  """Shopify Shipment status"""
  shopifyShipmentStatus: String

  """Shopify Shipment status"""
  shopifyDiscountCode: String

  """Shopify Shipment status"""
  shopifyDiscountType: String

  """Shopify Shipment status"""
  shopifyDiscountAmount: String

  """Shopify Shipment status"""
  shopifyCreatedAt: String

  """Shopify Shipment status"""
  series: String

  """Shopify Shipment status"""
  acctcode: String

  """Shopify Shipment status"""
  cardcode: String

  """Shopify Shipment status"""
  loggedusername: String

  """Shopify Shipment status"""
  whscode: String

  """Hold Order status"""
  holdOrder: Boolean

  """Created At"""
  createdAt: String!

  """Updated At"""
  updatedAt: String!

  """Check for first time visit"""
  isFirstTimeVisit: String

  """List of raised issues associated with the order."""
  raisedIssues: [RaisedIssueData!]

  """Max Delivery date of the order product"""
  ecomInvoiceId: String

  """Min Delivery date of the order product"""
  ecomOrderId: String

  """Min Delivery date of the order product"""
  ecomSubOrderId: String

  """refundDetailAlreadyAdded"""
  refundDetailAlreadyAdded: String

  """Super Order id"""
  superOrderId: String

  """Sub Order Ids"""
  subOrderIds: [String!]

  """Tentative Purchase Date"""
  tentativePurchaseDate: String

  """Booking amount of BNPL Order"""
  subOrderType: SubOrderType

  """Booking amount of BNPL Order"""
  productFinalAmount: Float

  """Booking amount of BNPL Order"""
  bnplProduct: OrderProductData

  """Type of order"""
  type: String

  """Error details"""
  errorDetails: ErrorData
}

enum SubOrderType {
  LOCK_PRICE
  FINAL_ORDER
}

type Order {
  """Order Data"""
  data: OrderData
  message: String
  status: Float
  success: Boolean
}

type Orders {
  """Orders Data"""
  data: [OrderData!]

  """count"""
  count: Int
  message: String
  status: Float
  success: Boolean
}

type OrderProducts {
  """Order Product Data"""
  data: [OrderProductData!]
  message: String
  status: Float
  success: Boolean
}

type RaisedIssue {
  """Raised Issue Data"""
  data: RaisedIssueData
  message: String
  status: Float
  success: Boolean
}

type CardData {
  cardHolderName: String
  last4Digits: String
  network: String
  cardType: String
  issuer: String
  emiEligible: String
  international: String
}

type PaymentData {
  transactionId: String!
  orderId: String!
  shopifyOrderId: String
  splitPayment: Boolean
  status: PaymentStatus
  transactionAmount: Float
  mode: PaymentMode
  remainingAmount: Float
  amountPaid: Float
  createdAt: String
  paymentMethod: String
  updatedAt: String
  externalRefId: String
  transactionType: TransactionType
  transactionCompletedDate: String
  refId: String
  posId: String
  phone: String
  transactionScreenshotS3Key: String
  paymentID: String
  qrImageUrl: String
  storeId: String
  orderCreatedAt: String
  orderShopifyCreatedAt: String
  orderTotalAmount: String
  orderStatus: String
  orderShopifyId: String
  shopifyOrderName: String
  cmsId: String
  handoverId: String
  handoverStatus: HandoverStatus
  handoverDate: String
  POrderID: String
  transactionNumber: String
  shortUrl: String
  upiId: String
  rrn: String
  bankName: String
  cardDetails: CardData
  isManualVerification: Boolean
}

enum PaymentStatus {
  COMPLETED
  CONFIRMED
  FAILED
  CANCELLED
  EXPIRED
  PAID
  CREATED
  PENDING
}

enum PaymentMode {
  RAZORPAY_POS
  RAZORPAY
  CASH
  CHEQUE
  BANK_TRANSFER
  M_SWIPE
  PAYU
  SNAPMINT
  PINELABS
}

enum TransactionType {
  CARD
  DIGITAL
}

enum HandoverStatus {
  PENDING
  COLLECTED
}

type Payment {
  data: PaymentData
  message: String
  status: Float
  success: Boolean
}

type Payments {
  data: [PaymentData!]

  """count"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type PaymentMetaDetails {
  data: PaymentDetails

  """count"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type ShopifyOrderData {
  """Phone number of the order"""
  phone: String

  """ID of the order"""
  id: String

  """ID of the order"""
  name: String

  """Total price of the order"""
  current_total_price: String

  """Email of the order"""
  email: String

  """Title of the order"""
  title: String

  """ID of the order"""
  order_status_url: String

  """Date of the order"""
  created_at: String
}

type ShopifyOrders {
  """Shopify Order Data"""
  data: [ShopifyOrderData!]
  message: String
  status: Float
  success: Boolean
}

type InteriorArchitectureData {
  """Unique identifier for the user"""
  id: String

  """City of the user"""
  city: String

  """Creation timestamp of the user"""
  createdAt: String

  """Date of the user"""
  date: String

  """Email of the user"""
  email: String

  """File upload of the user"""
  fileupload: String

  """GST of the user"""
  gst: String

  """Name of the user"""
  name: String

  """Last Name of the user"""
  lname: String

  """Organisation of the user"""
  organisation: String

  """PAN of the user"""
  pan: String

  """Phone number of the user"""
  phone: String

  """Source of the user"""
  source: String

  """Store of the user"""
  store: String

  """Last update timestamp of the user"""
  updatedAt: String

  """Store of the user"""
  vendorcode: String
}

type InteriorArchitecture {
  data: InteriorArchitectureData
  message: String
  status: Float
  success: Boolean
}

type InteriorArchitectures {
  data: [InteriorArchitectureData!]
  message: String
  status: Float
  success: Boolean
}

type GlobalConfigurationData {
  """Key"""
  key: String!

  """Value"""
  value: String!

  """Updated By"""
  updatedBy: String

  """CreatedAt"""
  createdAt: String

  """UpdatedAt"""
  updatedAt: String!
}

type GlobalConfiguration {
  """Global Configuration"""
  data: GlobalConfigurationData
  message: String
  status: Float
  success: Boolean
}

type GlobalConfigurations {
  """Global Configuration"""
  data: [GlobalConfigurationData!]

  """Count of Total data"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type LocalConfigurationData {
  """StoreId"""
  storeId: String!

  """Key"""
  key: String!

  """Value"""
  value: Float!

  """CreatedAt"""
  createdAt: String!

  """UpdatedAt"""
  updatedAt: String!
}

type LocalConfiguration {
  """Local Configuration"""
  data: LocalConfigurationData!
  message: String
  status: Float
  success: Boolean
}

type LocalConfigurations {
  """Local Configuration"""
  data: [LocalConfigurationData!]!

  """Count of Total data"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type S3UploaderData {
  """Presigned Url"""
  url: String!
}

type S3Uploader {
  """Presigned Url"""
  data: S3UploaderData
  message: String
  status: Float
  success: Boolean
}

type CollectionData {
  id: String!
  handle: String!
  title: String!
  updated_at: String!
  body_html: String!
  published_at: String!
  sort_order: String!
  template_suffix: String!
  published_scope: String
  admin_graphql_api_id: String!
  productIds: [String!]!
}

type Collection {
  data: CollectionData
  message: String
  status: Float
  success: Boolean
}

type SkuPriceMasterData {
  """Action"""
  action: String

  """SKU"""
  id: String!

  """Title"""
  title: String!

  """Title"""
  shopifyTitle: String

  """Parent SKU"""
  parentSku: String!

  """New Price"""
  price: Float!

  """STNOrderVisibility"""
  STNOrderVisibility: String!

  """Old Price"""
  multiplier: Float!

  """Replacement Window"""
  replacementWindow: Float

  """Return Shipping Cost"""
  returnShippingCost: Float

  """Replacement Shipping Cost"""
  replacementShippingCost: Float

  """Created At"""
  createdAt: String!

  """Updated At"""
  updatedAt: String!
}

type SkuPriceMaster {
  data: SkuPriceMasterData
  message: String
  status: Float
  success: Boolean
}

type SkuPriceMasters {
  data: [SkuPriceMasterData!]

  """count"""
  count: Int
  message: String
  status: Float
  success: Boolean
}

type STNProductData {
  """SKU of the product"""
  sku: String!

  """Title of the product"""
  title: String!

  """Quantity of the product ordered"""
  quantity: Float!

  """Price of the product"""
  price: Float
}

type STNData {
  """Order number"""
  id: String!

  """Order number"""
  sourceWarehouseMappingId: String

  """Order number"""
  attachmentS3Key: String

  """Additional remarks for the order"""
  remarks1: String

  """Additional remarks for the order"""
  remarks2: String

  """List of products included in the order"""
  products: [STNProductData!]!

  """Unique identifier for the customer"""
  status: STNStatus!

  """Unique identifier for the customer"""
  progressStatus: STNProgressStatus

  """Unique identifier for the customer"""
  storeId: String!

  """Forward or Reverse"""
  requestMode: STNRequestMode!

  """Forward or Reverse"""
  transferMode: STNTransferMode!

  """Requested store ID"""
  requestedStoreId: String

  """Creation timestamp of the order"""
  createdAt: String!

  """Last update timestamp of the order"""
  updatedAt: String!

  """Last update timestamp of the order"""
  ecomOrderId: String

  """Last update timestamp of the order"""
  ecomSubOrderId: String

  """Last update timestamp of the order"""
  ecomInvoiceId: String

  """Bulk STN Code"""
  bulkSTNCode: String
}

enum STNStatus {
  CREATED
  DRAFT
}

enum STNProgressStatus {
  PENDING
  ACCEPTED
  REJECTED
  COMPLETED
}

enum STNRequestMode {
  FORWARD
  REVERSE
}

enum STNTransferMode {
  STORE
  WAREHOUSE
}

type STN {
  """List of STN data"""
  data: STNData

  """Response message"""
  message: String

  """Response status"""
  status: Int

  """Response success flag"""
  success: Boolean
}

type STNs {
  """List of STN data"""
  data: [STNData!]

  """Response message"""
  message: String

  """Response status"""
  status: Int

  """Response count"""
  count: Int

  """Response success flag"""
  success: Boolean
}

type ExportResponse {
  message: String!
  status: Float
  success: Boolean
}

type GRNProduct {
  """SKU of the product"""
  sku: String!

  """Quantity of the product ordered"""
  receivedQuantity: Float!

  """Quantity of the product ordered"""
  assignedQuantity: Float!

  """Title of the product"""
  title: String!

  """Variant of the product"""
  price: Float
}

type GRN {
  """STN Id"""
  stnId: String!

  """PO Id"""
  poId: String!

  """GRN Id"""
  grnId: String

  """vendor Id"""
  vendorId: Int

  """ Id"""
  requestedStoreId: String

  """List of products included in the order"""
  products: [GRNProduct!]!

  """GRN queue Id"""
  queueId: String

  """Creation timestamp of the order"""
  createdAt: String!

  """Creation timestamp of the order"""
  poCreatedAt: String!

  """Partial GRN Reason"""
  partialGRNReason: String

  """Remarks"""
  remarks: String

  """attachement"""
  attachmentS3Key: String

  """Last update timestamp of the order"""
  updatedAt: String!
}

type GRNsData {
  """List of goods received notes"""
  data: [GRN!]

  """Response message"""
  message: String!

  """Response status"""
  status: Int!

  """Response success flag"""
  success: Boolean!
}

type GRNData {
  """List of goods received notes"""
  data: GRN

  """Response message"""
  message: String!

  """Response status"""
  status: Int!

  """Response success flag"""
  success: Boolean!
}

type PurchaseOrderItem {
  """Purchase Order Detail ID"""
  purchase_order_detail_id: Int!

  """CP ID"""
  cp_id: Int!

  """Product ID"""
  product_id: Int!

  """SKU of the product"""
  sku: String!

  """HSN code of the product"""
  hsn: String!

  """Model number of the product"""
  model_no: String!

  """EAN code of the product"""
  ean: String

  """Product description"""
  product_description: String!

  """Original quantity ordered"""
  original_quantity: Int!

  """Pending quantity"""
  pending_quantity: Int!

  """Item price"""
  item_price: String!
}

type PurchaseOrder {
  """Purchase Order ID"""
  po_id: Int!

  """Total value of the purchase order"""
  total_po_value: String!

  """Purchase Order number"""
  po_number: Int!

  """Purchase Order reference number"""
  po_ref_num: String!

  """Purchase Order status ID"""
  po_status_id: Int!

  """Creation date of the purchase order"""
  po_created_date: String!

  """Last update date of the purchase order"""
  po_updated_date: String!

  """Warehouse where the purchase order was created"""
  po_created_warehouse: String!

  """Warehouse ID where the purchase order was created"""
  po_created_warehouse_c_id: Int!

  """Location key of the warehouse where the purchase order was created"""
  po_created_location_key: String!

  """Vendor name"""
  vendor_name: String!

  """Vendor ID"""
  vendor_c_id: Int!

  """Vendor location key"""
  vendor_location_key: String!

  """List of items in the purchase order"""
  po_items: [PurchaseOrderItem!]!
}

type PurchaseOrdersData {
  """List of purchase orders"""
  data: [PurchaseOrder!]

  """Response message"""
  message: String!

  """Response status code"""
  status: Int!

  """Response success flag"""
  success: Boolean!
}

type InventoryCredentialsData {
  """Unique identifier for the credential"""
  id: String!

  """Location key for the inventory system"""
  locationKey: String!

  """Secret key for the inventory system"""
  secretKey: String!

  """State for the inventory system"""
  state: String!
}

type InventoryCredentials {
  data: InventoryCredentialsData!
  message: String
  status: Float
  success: Boolean
}

type InventoryTrackingData {
  """StoreID for the inventory system"""
  storeId: String!

  """Unique identifier for the credential"""
  id: String!

  """Quantity for the inventory system"""
  quantity: Float!

  """Quantity for the inventory system"""
  displayItemQuantity: Float

  """UpdatedAt for the inventory"""
  updatedAt: String!

  """title of the product"""
  title: String!
}

type InventoryTracking {
  data: [InventoryTrackingData!]!

  """count"""
  count: Float
  message: String
  status: Float
  success: Boolean
}

type FirebaseToken {
  """id"""
  id: String!

  """storeId"""
  storeId: String!

  """cognitoId"""
  cognitoId: String!

  """CreatedAt"""
  createdAt: String

  """UpdatedAt"""
  updatedAt: String!
}

type Notification {
  """id"""
  id: String!

  """createdBy"""
  createdBy: String

  """header"""
  header: String!

  """description"""
  description: String!

  """notificationtype"""
  notificationtype: NotificationType!

  """triggerTime"""
  triggerTime: String!

  """link"""
  link: String

  """HighAlert"""
  highAlert: Boolean!

  """pinned"""
  pinned: Boolean!

  """pinnedContent"""
  pinnedContent: String

  """pinnedStartTime"""
  pinnedStartTime: String

  """pinnedEndTime"""
  pinnedEndTime: String

  """allocationType"""
  allocationType: StoreAllocationType!

  """stores"""
  stores: [String!]!

  """status"""
  notificationStatus: NotificationStatus!

  """CreatedAt"""
  createdAt: String

  """UpdatedAt"""
  updatedAt: String!

  """isDisabled"""
  isDisabled: Boolean!

  """sqsMessageId"""
  sqsMessageId: String

  """sendImmediately"""
  sendImmediately: Boolean!
}

enum NotificationType {
  SYSTEM
  SALES
}

enum NotificationStatus {
  SENT
  SCHEDULED
}

type NotificationData {
  """Notification Data"""
  data: Notification
  message: String
  status: Float
  success: Boolean
}

type NotificationsData {
  """Notifications Data"""
  data: [Notification!]

  """count"""
  count: Int
  message: String
  status: Float
  success: Boolean
}

type TokenData {
  """Token Data"""
  data: FirebaseToken
  message: String
  status: Float
  success: Boolean
}

type StoreNotification {
  """notificationIdd"""
  notificationId: String!

  """header"""
  header: String!

  """description"""
  description: String!

  """notificationtype"""
  notificationtype: NotificationType!

  """triggerTime"""
  triggerTime: String!

  """link"""
  link: String

  """HighAlert"""
  highAlert: Boolean!

  """pinned"""
  pinned: Boolean!

  """pinnedContent"""
  pinnedContent: String

  """pinnedStartTime"""
  pinnedStartTime: String

  """pinnedEndTime"""
  pinnedEndTime: String

  """clickCounts"""
  clickCounts: Float

  """read"""
  read: Boolean!

  """CreatedAt"""
  createdAt: String

  """UpdatedAt"""
  updatedAt: String!
}

type StoreNotificationData {
  """StoreNotification Data"""
  data: [StoreNotification!]
  message: String
  status: Float
  success: Boolean
}

type CMSIds {
  """Order ID"""
  orderId: String!

  """Transaction ID"""
  transactionId: String!
}

type CMSData {
  """ID"""
  id: String!

  """Total Cash"""
  totalCash: Float!

  """Total Orders"""
  totalOrders: Float!

  """Status"""
  status: CMSStatus!

  """CMS Order IDs"""
  transactionsCovered: [CMSIds!]!

  """Employee ID"""
  employeeId: String

  """Image URL"""
  imageUrl: String

  """Handover ID"""
  handoverId: String

  """Confirm Cash HandOver"""
  confirmCashHandOver: Float

  """Handover Date"""
  handoverDate: String

  """Store ID"""
  storeId: String!
  createdAt: String!
  updatedAt: String!
}

enum CMSStatus {
  DRAFT
  COLLECTED
}

type CMSs {
  data: [CMSData!]
  count: Int
  message: String
  status: Float
  success: Boolean
}

type CMS {
  data: CMSData
  message: String
  status: Float
  success: Boolean
}

type CMSDashboardReportData {
  totalOrdersPending: Float
  totalAmountPending: Float
}

type CMSDashboardReport {
  data: CMSDashboardReportData
  message: String
  status: Float
  success: Boolean
}

type StorePOData {
  poId: String!
  stnId: String!
  requestedStoreId: String!

  """List of products included in the order"""
  products: [POProductData!]!

  """Creation timestamp of the order"""
  createdAt: String!

  """Last update timestamp of the order"""
  updatedAt: String!
}

type StorePO {
  """PO data"""
  data: StorePOData
  message: String
  status: Float
  success: Boolean
}

type StorePOs {
  """PO data"""
  data: [StorePOData!]
  message: String
  status: Float
  success: Boolean
}

type POProductData {
  """SKU of the product"""
  id: String!

  """Title of the product"""
  title: String!

  """Price of the product"""
  price: Float

  """Quantity of the product ordered"""
  assignedQuantity: Float!

  """Pending Inward Quantity of the product ordered"""
  pendingQuantity: Float!
}

type RefundDetailData {
  id: String!
  shopifyOrderId: String!

  """Is Back To Source"""
  isBackToSource: Boolean!

  """Account Holder Name"""
  accountHolderName: String

  """Bank Account Name"""
  bankAccountName: String

  """IFSC Code"""
  ifscCode: String

  """Account No"""
  accountNo: String

  """Cancelled Checkque Image S3 Key"""
  chequeImageKey: String

  """Cancelled Checkque Image S3 Key"""
  refundTicket: RaisedIssueData

  """Transaction ID"""
  transactionId: String

  """Refund Date"""
  refundedAt: String

  """Refund Status"""
  status: String

  """Created At"""
  createdAt: String!

  """Updated At"""
  updatedAt: String!

  """replacementOrderId"""
  replacementOrderId: String
  sku: String
  upiName: String
  upiId: String
  refundType: String
  isFormSubmitted: Boolean
  refundedPrice: Float
  priceAfterDiscount: Float
  refundAmount: Float
  requestId: String
}

type RefundDetail {
  """Refund Detail"""
  data: RefundDetailData
  message: String
  status: Float
  success: Boolean
}

type RefundDetails {
  """Refund Details Array"""
  data: [RefundDetailData!]
  message: String
  status: Float
  success: Boolean
  count: Float
}

type RefundSmsResponse {
  status: String!
  success: Boolean!
  message: String
}

type RefundExportResponse {
  status: String!
  success: Boolean!
  message: String
}

type IOData {
  """Unique identifier for the inventory operation"""
  id: String!

  """Identifier for the store involved in the operation"""
  storeId: String!

  """Final quantity after the inventory operation"""
  finalQuantity: String!

  """Initial quantity before the inventory operation"""
  quantity: String!

  """Stock Keeping Unit (SKU) associated with the product"""
  sku: String!

  """ID of the source from where the inventory is transferred"""
  sourceId: String!

  """Type of the source, e.g., warehouse, supplier"""
  sourceType: String!

  """Type of the source, e.g., warehouse, supplier"""
  inventoryFrom: String!

  """Type of the source, e.g., warehouse, supplier"""
  inventoryTo: String!

  """Tag or label associated with the operation for categorization"""
  tag: String!

  """Timestamp of when the operation was last updated"""
  updatedAt: String!
}

type GeneratedCode {
  """customerPhone"""
  phone: String!

  """code"""
  code: String!
}

type SpinTheWheelData {
  """prefix"""
  prefix: String!

  """prefix"""
  label: String!

  """title"""
  title: String!

  """currentCount"""
  currentCount: Float!

  """limit"""
  limit: Float!

  """priceruleId"""
  priceruleId: String!

  """probability"""
  probability: Float!

  """status"""
  status: String!

  """created codes"""
  createdCodes: [GeneratedCode!]

  """value_type"""
  value_type: String!
}

type SpinTheWheel {
  data: [SpinTheWheelData!]
  message: String
  status: Float
  success: Boolean
}

type ClaimedCode {
  code: String
}

type ClaimSpinTheWheel {
  data: ClaimedCode
  message: String
  status: Float
  success: Boolean
}

type StoreOrdersApiKeyData {
  """Unique API Key"""
  apiKey: String!

  """Name associated with the API key"""
  name: String!

  """Store IDs linked to the API key"""
  storeIds: [String!]!

  """Creation timestamp"""
  createdAt: String!

  """Last updated timestamp"""
  updatedAt: String!
}

type StoreOrdersApiKey {
  data: StoreOrdersApiKeyData
  message: String
  status: Float
  success: Boolean
}

type StoreOrdersApiKeys {
  data: [StoreOrdersApiKeyData!]
  message: String
  status: Float
  success: Boolean
}

type BulkGenerateCouponData {
  """S3 file path for CSV"""
  filePath: String!

  """createdAt"""
  createdAt: String!

  """updatedAt"""
  updatedAt: String!

  """status"""
  status: BulkCouponStatus!

  """error"""
  errorMessage: String
}

enum BulkCouponStatus {
  INITIATED
  COMPLETED
  FAILED
}

type BulkGenerateCoupon {
  data: BulkGenerateCouponData
  message: String
  status: Float
  success: Boolean
}

type BulkGenerateCoupons {
  data: [BulkGenerateCouponData!]
  message: String
  status: Float
  success: Boolean
}

type RecommendationResponse {
  message: String!
  data: String!
  status: Float
  success: Boolean
}

type Recommendations {
  """Unique id"""
  id: String!

  """Name of the user"""
  name: String!

  """Phone number of the user"""
  phone: String!

  """Questions and answers of the user"""
  questions: String!

  """Recommendation for the user"""
  recommendation: String!

  """Creation timestamp of the user"""
  createdAt: String!
}

type ListRecommendationResponse {
  data: [Recommendations!]
  count: Float
  message: String
  status: Float
  success: Boolean
}

type SendWhatsAppSuccess {
  message: String
  status: Float
  success: Boolean
}

type CancelOrderData {
  """Primary key after cancellation added to db"""
  pk: String

  """Secondary key after cancellation added to db"""
  sk: String

  """Reference code for the cancellation on easycom"""
  order_id: String

  """Freshdesk ticket Id"""
  freshdeskId: String

  """Reason for cancellation"""
  reasonForCancellation: String

  """Creation timestamp of the order"""
  cancelled_at: String
  refundTicketId: String
  priceAfterDiscount: String
  priceBeforeDiscount: String
  transactionIDs: [String!]
  originalPaymentMode: [String!]
  isRazorpay: Boolean
  bankDiscount: Float
  refundDetailAlreadyAdded: Boolean
  refundFormLink: String
}

type CancelOrder {
  """Status of the cancellation"""
  status: String
  data: CancelOrderData
}

type Price {
  """Date of price"""
  date: String!

  """Product ID"""
  product_id: ID!

  """Variant ID"""
  variant_id: ID!

  """Price of product"""
  price: String!

  """SKU of product"""
  sku: String

  """Created at timestamp"""
  createdAt: String!
}

type PriceData {
  """Order Product Data"""
  data: Price
  message: String
  status: Float
  success: Boolean
}

type Query {
  listEmployees(listEmployeeInput: ListEmployeeInput): Employees!
  getEmployee(id: String!): Employee!
  createCRMUsers: String!
  listProductsWithVariants(listProductInput: ListProductInput): Products!
  fetchProducts: Response!
  getProductById(id: String!): Product!
  getVariantById(product_id: String!, id: String!): Product!
  listProducts(query: String): Products!
  listAccessories(query: String): Products!
  listVariants(listVariantInput: ListVariantInput): Variants!
  listProductTypes: ProductType!
  listProductsByIds(productIds: [String!], activeProducts: Boolean = false): Products!
  getCoupon(code: String!): Coupon!
  calculatePromotionalDiscount(promotionalCodeCalculationInput: PromotionalCodeDiscountInput!, isEditingQuotation: Boolean): PromotionalCodeDiscount!
  fetchCoupons: Response!
  listStores(listStoreInput: ListStoreInput): Stores!
  listStoresByIds(storeIds: [String!]): Stores!
  getStore(id: String!): Store!
  listPayments(listPaymentInput: ListPaymentInput): Payments!
  getPaymentsByOrderID(orderId: String!): PaymentMetaDetails!
  listQuotations(listQuotationInput: ListQuotationInput): Quotations!
  exportQuotations(email: String!, listQuotationInput: ListQuotationInput): Response!
  quotationPdf(quotationId: String!, type: String!): Response!
  getEmployeeDetails(phone: String!): EmployeeMasterDetails!
  getQuotation(id: String!): Quotation!
  getCart(customerId: String!): Cart!
  listCarts(listCartInput: ListCartInput): Carts!
  getCustomer(phone: String!): Customer!
  sendBrochure(phone: String!, type: String!, storeId: String): Response!
  validatePincode(id: String!): Pincode!
  getWishlist(getWishlistInput: GetWishlistInput!): Wishlist!
  getCampaignCoupon(code: String!): CampaignCoupon!
  listCampaignCoupons(listCampaignCouponInput: ListCampaignCouponInput): CampaignCoupons!
  resetDailyUsage: Response!
  resetWeeklyUsage: Response!
  resetMonthlyUsage: Response!
  getOrder(id: String!): Order!
  getOrderByQuotationId(getOrderByQuotationIdInput: GetOrderByQuotationIdInput!): Order!
  listOrders(listOrderInput: ListOrderInput): Orders!
  exportOrders(email: String!, listOrderInput: ListOrderInput, exportTypes: [String!]!): Response!
  listOrderItemsByOrderId(orderId: String!): OrderProducts!
  orderPdf(orderId: String!, type: String!): Response!
  listShopifyOrders(customerId: String!): ShopifyOrders!
  getInteriorArchitecture(id: String!): InteriorArchitecture!
  listInteriorArchitectures(query: String!): InteriorArchitectures!
  fetchInteriorArchitecture: Response!
  listGlobalConfigurations: GlobalConfigurations!
  getGlobalConfiguration(key: String!): GlobalConfiguration!
  localConfigurations(storeId: String!): LocalConfigurations!
  localConfiguration(storeId: String!, key: String!): LocalConfiguration!
  getS3PresignedUrl(filePath: String!): S3Uploader!
  getCollection(id: String!): Collection!
  fetchCollections: Response!
  getSkuPriceMaster(id: String!): SkuPriceMaster!
  listSkuPriceMasters(listSkuPriceMasterInput: ListSkuPriceMasterInput): SkuPriceMasters!
  exportSkuPriceMasters(email: String!): Response!
  listSKUPriceMasterByIds(ids: [String!]!): SkuPriceMasters!
  getSTN(id: String!): STN!
  listSTNs(listSTNInput: ListSTNInput): STNs!
  exportSTNs(email: String!, listSTNInput: ListSTNInput): ExportResponse!
  listGRNBySTN(stnId: String!): GRNsData!
  searchPOs(stnId: String!, storeId: String!): PurchaseOrdersData!
  getInventoryCredentialById(id: String!): InventoryCredentials!
  listCMS(listCMSInput: ListCMSInput): CMSs!
  cmsDashboardReport(storeId: String): CMSDashboardReport!
  getCMS(id: String!): CMS!
  listInventoryTracking(listInventoryTrackingInput: ListInventoryTrackingInput): InventoryTracking!
  listInventoryTrackingByIds(storeId: String!, ids: [String!]!): InventoryTracking!
  exportInventoryTracking(email: String!, listInventoryTrackingInput: ListInventoryTrackingInput): ExportResponse!
  listStorePoBySTN(stnId: String!): StorePOs!
  storePo(id: Int!): StorePO!
  getNotification(id: String!): NotificationData!
  listNotifications(listNotificationInput: ListNotificationInput): NotificationsData!
  listStoreNotification(storeId: String!): StoreNotificationData!
  replacementOrderPdf(id: String!, shopifyOrderId: String!, type: String!): Response!
  getReplacementOrder(id: String!, shopifyOrderId: String!): ReplacementOrder!
  replacementOrders(listPaymentPendingOrdersInput: ListPaymentPendingOrdersInput): PaymentPendingReplacementOrders!
  exportReplacementOrders(email: String!, listReplacementInput: ExportReplacementOrdersInput): Response!
  getRefundDetail(id: String!, shopifyOrderId: String!): RefundDetail!
  getAllRefundDetails(filter: ListRefundDetailInput): RefundDetails!
  exportRefundDetails(email: String!, filter: ListRefundDetailInput): RefundExportResponse!
  sendRefundFormSms(id: String!, shopifyOrderId: String!): RefundSmsResponse!
  exportIO(email: String!, listIOInput: ListIOInput): ExportResponse!
  listSpinTheWheelCoupon(type: String!): SpinTheWheel!
  customDiscountVerification(quotationId: String!, id: String!): CustomDiscountVerification!
  exportCustomDiscountVerification(email: String!, listCustomDiscountVerificationInput: ListCustomDiscountVerificationInput!): ExportResponse!
  listStoreOrdersApiKeys: StoreOrdersApiKeys!
  listBulkGenerateCoupons: BulkGenerateCoupons!
  bulkGenerateCoupon(id: Int!): BulkGenerateCoupon!
  getCancelledOrder(id: String!): CancelOrderData!
  listRecommendations(listRecommendationsInput: ListRecommendationInput): ListRecommendationResponse!
  getVariantPriceOnDate(getVariantPriceOnDate: FindPriceInput!): PriceData!
}

input ListEmployeeInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Boolean fields to filter the records"""
  termSearchFields: EmployeeTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: EmployeeTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: EmployeeSortingFieldsInput = {createdAt: "desc"}
}

input EmployeeTermSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  isActive: Boolean
  createdAt: String
  updatedAt: String
}

input EmployeeTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  firstName: String

  """Count from which user wants to retrieve the records"""
  lastName: String

  """Count from which user wants to retrieve the records"""
  id: String

  """Count from which user wants to retrieve the records"""
  cognitoId: String

  """Count from which user wants to retrieve the records"""
  stores: String

  """Count from which user wants to retrieve the records"""
  role: String

  """Count from which user wants to retrieve the records"""
  email: String
}

input EmployeeSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input ListProductInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Boolean fields to filter the records"""
  termSearchFields: ProductTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: ProductTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: ProductSortingFieldsInput = {createdAt: "desc"}
}

input ProductTermSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  id: String

  """Count from which user wants to retrieve the records"""
  createdAt: String

  """Count from which user wants to retrieve the records"""
  updatedAt: String
}

input ProductTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  title: String

  """Count from which user wants to retrieve the records"""
  type: String

  """Count from which user wants to retrieve the records"""
  product_type: String

  """Count from which user wants to retrieve the records"""
  status: String

  """Count from which user wants to retrieve the records"""
  handle: String
}

input ProductSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input ListVariantInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Boolean fields to filter the records"""
  termSearchFields: VariantTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: VariantTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: VariantSortingFieldsInput = {createdAt: "desc"}
}

input VariantTermSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  id: String

  """Count from which user wants to retrieve the records"""
  product_id: String

  """Count from which user wants to retrieve the records"""
  createdAt: String

  """Count from which user wants to retrieve the records"""
  updatedAt: String
}

input VariantTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  title: String

  """Count from which user wants to retrieve the records"""
  type: String
}

input VariantSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input PromotionalCodeDiscountInput {
  """Coupon Code"""
  code: String!

  """Cart total"""
  cartTotal: Float!

  """Cart items"""
  cartItems: [CartProductInput!]!

  """Customer phone number"""
  phone: String
}

input CartProductInput {
  productId: String!
  variantId: String
  quantity: Int!
  customData: CustomDataInput
  price: Float
  priceType: String
  originalPrice: Float
  hasColorOptions: Boolean
}

input CustomDataInput {
  length: String
  breadth: String
  height: String
  lengthRange: CustomRangeDataInput
  breadthRange: CustomRangeDataInput
}

input CustomRangeDataInput {
  min: String!
  max: String!
  includesMin: Boolean
}

input ListStoreInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Boolean fields to filter the records"""
  termSearchFields: StoreTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: StoreTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: StoreSortingFieldsInput = {createdAt: "desc"}

  """Count till which user wants to retrieve the records"""
  storeIds: [String!]
}

input StoreTermSearchFieldsInput {
  isActive: Boolean
  createdAt: String
  updatedAt: String
}

input StoreTextSearchFieldsInput {
  name: String
  id: String
  phone: String
  email: String
  posIds: String
}

input StoreSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input ListPaymentInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Text fields to filter the records"""
  termSearchFields: PaymentTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: PaymentTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: PaymentSortingFieldsInput = {orderCreatedAt: "desc"}

  """Count from which user wants to retrieve the records"""
  fromDate: String

  """Count from which user wants to retrieve the records"""
  toDate: String
}

input PaymentTermSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  storeId: String
}

input PaymentTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  handoverId: String

  """Count from which user wants to retrieve the records"""
  orderStatus: String

  """Count from which user wants to retrieve the records"""
  handoverStatus: String
}

input PaymentSortingFieldsInput {
  """Value should be in asc or desc"""
  orderCreatedAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input ListQuotationInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Boolean fields to filter the records"""
  termSearchFields: QuotationTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: QuotationTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: QuotationSortingFieldsInput = {createdAt: "desc"}

  """Date from which user wants to retrieve the records"""
  fromDate: String

  """Date until which user wants to retrieve the records"""
  toDate: String
}

input QuotationTermSearchFieldsInput {
  createdAt: String
  updatedAt: String
  expiresAt: String
  customerId: String
}

input QuotationTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  id: String

  """Count from which user wants to retrieve the records"""
  storeId: String

  """Count from which user wants to retrieve the records"""
  employeeId: String

  """Count from which user wants to retrieve the records"""
  status: String

  """Type of quotation"""
  type: String

  """Customer Email from which user wants to retrieve the records"""
  customeremail: String

  """Customer Phone from which user wants to retrieve the records"""
  customerphone: String
}

input QuotationSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input ListCartInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Boolean fields to filter the records"""
  termSearchFields: CartTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: CartTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: CartSortingFieldsInput = {createdAt: "desc"}
}

input CartTermSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  customerId: String

  """Count from which user wants to retrieve the records"""
  createdAt: String

  """Count from which user wants to retrieve the records"""
  updatedAt: String
}

input CartTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  storeId: String

  """Count from which user wants to retrieve the records"""
  id: String

  """Count from which user wants to retrieve the records"""
  employeeId: String
}

input CartSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input GetWishlistInput {
  customerId: String!
}

input ListCampaignCouponInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Text fields to filter the records"""
  termSearchFields: CampaignCouponTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: CampaignCouponTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: CampaignCouponSortingFieldsInput = {createdAt: "desc"}
}

input CampaignCouponTermSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  startsAt: String

  """Count from which user wants to retrieve the records"""
  endsAt: String

  """Count from which user wants to retrieve the records"""
  createdAt: String

  """Count from which user wants to retrieve the records"""
  updatedAt: String
}

input CampaignCouponTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  code: String

  """Count from which user wants to retrieve the records"""
  campaignCouponType: CampaignCouponType

  """Count from which user wants to retrieve the records"""
  storeAllocationType: String

  """Count from which user wants to retrieve the records"""
  entitledStoreIds: String

  """Count from which user wants to retrieve the records"""
  status: String
}

input CampaignCouponSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input GetOrderByQuotationIdInput {
  """Value should be in asc or desc"""
  quotationId: String!

  """Value should be in asc or desc"""
  subOrderType: String
}

input ListOrderInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Filter by which user wants to retrieve the records"""
  filterBy: OrderFilterBy!

  """Search Text fields to filter the records"""
  termSearchFields: OrderTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: OrderTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: OrderSortingFieldsInput = {createdAt: "desc"}

  """Count from which user wants to retrieve the records"""
  fromDate: String

  """Count from which user wants to retrieve the records"""
  toDate: String
}

enum OrderFilterBy {
  SHOPIFY
  INVOICE
  TRANSACTION
}

input OrderTermSearchFieldsInput {
  """CustomerId from which user wants to retrieve the records"""
  customerId: String

  """Count from which user wants to retrieve the records"""
  storeId: String

  """orderType"""
  orderType: String

  """holdOrder"""
  holdOrder: Boolean

  """Type of order"""
  type: String
}

input OrderTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  id: String

  """Count from which user wants to retrieve the records"""
  shopifyOrderName: String

  """Count from which user wants to retrieve the records"""
  quotationId: String

  """Count from which user wants to retrieve the records"""
  employeeId: String

  """Count from which user wants to retrieve the records"""
  status: String

  """Customer Email from which user wants to retrieve the records"""
  customeremail: String

  """Customer Phone from which user wants to retrieve the records"""
  customerphone: String
}

input OrderSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input ListSkuPriceMasterInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Text fields to filter the records"""
  textSearchFields: SkuPriceMasterTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: SkuPriceMasterSortingFieldsInput = {createdAt: "desc"}
}

input SkuPriceMasterTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  id: String

  """Count from which user wants to retrieve the records"""
  title: String

  """Count from which user wants to retrieve the records"""
  STNOrderVisibility: String
}

input SkuPriceMasterSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input ListSTNInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search term fields to filter the records"""
  termSearchFields: STNTermSearchFieldsInput

  """Search text fields to filter the records"""
  textSearchFields: STNTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: STNSortingFieldsInput = {createdAt: "desc"}

  """Count from which user wants to retrieve the records"""
  fromDate: String

  """Count from which user wants to retrieve the records"""
  toDate: String
}

input STNTermSearchFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  storeId: String

  """Value should be in asc or desc"""
  storeIds: [String!]

  """requested store id"""
  requestedStoreId: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input STNTextSearchFieldsInput {
  """Unique identifier for the STN"""
  id: String

  """Status of the STN"""
  status: STNStatus

  """Status of the STN"""
  requestMode: STNRequestMode
}

input STNSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input ListCMSInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Text fields to filter the records"""
  termSearchFields: CMSTermSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: CMSSortingFieldsInput = {createdAt: "desc"}
}

input CMSTermSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  storeId: String
}

input CMSSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input ListInventoryTrackingInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Boolean fields to filter the records"""
  termSearchFields: InventoryTrackingTermSearchFieldsInput
}

input InventoryTrackingTermSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  storeId: String
}

input ListNotificationInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Boolean fields to filter the records"""
  termSearchFields: NotificationTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: NotificationTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: NotificationSortingFieldsInput = {createdAt: "desc"}
}

input NotificationTermSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  id: String

  """Count from which user wants to retrieve the records"""
  createdAt: String

  """Count from which user wants to retrieve the records"""
  updatedAt: String
}

input NotificationTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  storeId: String

  """Count from which user wants to retrieve the records"""
  id: String
}

input NotificationSortingFieldsInput {
  """Value should be in asc or desc"""
  createdAt: String

  """Value should be in asc or desc"""
  updatedAt: String
}

input ListPaymentPendingOrdersInput {
  lastEvaluatedKey: LastEvaluatedKey
  size: String!
  orderId: String
  phoneNumber: String
}

input LastEvaluatedKey {
  id: String!
  shopifyOrderId: String!
}

input ExportReplacementOrdersInput {
  """Request type filter"""
  requestType: String

  """Start date for filtering (YYYY-MM-DD)"""
  startDate: String

  """End date for filtering (YYYY-MM-DD)"""
  endDate: String

  """Replacement type filter"""
  replacementType: String
}

input ListRefundDetailInput {
  page: Float
  pageSize: Float
  search: String
  sortBy: String
  sortOrder: String
  id: String
  shopifyOrderId: String
  status: String
  isBackToSource: Boolean
  formSubmitted: Boolean
  transactionId: String
  fromDate: String
  toDate: String
}

input ListIOInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Text fields to filter the records"""
  textSearchFields: IOTextSearchFieldsInput

  """Search Text fields to filter the records"""
  termSearchFields: IOTermSearchFieldsInput

  """Count from which user wants to retrieve the records"""
  fromDate: String

  """Count from which user wants to retrieve the records"""
  toDate: String

  """Sort fields to sort the records"""
  sortBy: IOSortingFieldsInput = {updatedAt: "desc"}
}

input IOTextSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  id: String
}

input IOTermSearchFieldsInput {
  """Count from which user wants to retrieve the records"""
  storeId: String
}

input IOSortingFieldsInput {
  """Value should be in asc or desc"""
  updatedAt: String
}

input ListCustomDiscountVerificationInput {
  """Start date for filtering records (ISO format)"""
  fromDate: String!

  """End date for filtering records (ISO format)"""
  toDate: String!
}

input ListRecommendationInput {
  """Count from which user wants to retrieve the records"""
  from: String

  """Count till which user wants to retrieve the records"""
  size: String

  """Search Boolean fields to filter the records"""
  termSearchFields: RecommendationTermSearchFieldsInput

  """Search Text fields to filter the records"""
  textSearchFields: RecommendationTextSearchFieldsInput

  """Sort fields to sort the records"""
  sortBy: QuotationSortingFieldsInput = {createdAt: "desc"}
}

input RecommendationTermSearchFieldsInput {
  phone: String
}

input RecommendationTextSearchFieldsInput {
  phone: String
}

input FindPriceInput {
  """Product ID"""
  product_id: ID!

  """Variant ID"""
  variant_id: ID!

  """Date"""
  date: String!
}

type Mutation {
  createEmployee(createEmployeeInput: CreateEmployeeInput!): Employee!
  updateEmployee(updateEmployeeInput: UpdateEmployeeInput!): Employee!
  deleteEmployee(id: String!, cognitoId: String): Employee!
  createCoupon(createCouponInput: CreateCouponInput!): Coupon!
  createStore(createStoreInput: CreateStoreInput!): Store!
  updateStore(updateStoreInput: UpdateStoreInput!): Store!
  deleteStore(id: String!): Store!
  createPayment(createPaymentInput: CreatePaymentInput!): Payment!
  verifyPaymentTransaction(verifyPaymentTransactionInput: VerifyPaymentTransactionInput!): Payment!
  cancelPaymentTransaction(cancelPaymentTransactionInput: CancelPaymentTransactionInput!): Payment!
  resendPaymentTransaction(resendPaymentTransactionInput: CancelPaymentTransactionInput!): Payment!
  createQuotation(createQuotationInput: CreateQuotationInput!): Quotation!
  requestEmployeeDiscountOTP(quotationId: String!, employeeDiscountDetails: EmployeeDetailsInput!): Quotation!
  verificationEmployeeDiscountOTP(quotationId: String!, otp: String!): Quotation!
  updateQuotation(updateQuotationInput: UpdateQuotationInput!): Quotation!
  requestDeliveryChargeApproval(quotationId: String!, input: DeliveryChargeApprovalInput!, resend: Boolean! = false): Quotation!
  verifyDeliveryChargeApproval(quotationId: String!, otp: String!): Quotation!
  updateQuotationProducts(updateQuotationProductsInput: UpdateQuotationProductsInput!): Quotation!
  createCart(createCartInput: CreateCartInput!): Cart!
  updateCart(updateCartInput: UpdateCartInput!): Cart!
  deleteCart(customerId: String!): Cart!
  createCustomerAddress(createCustomerAddressInput: CreateCustomerAddressInput!): ShopifyAddress!
  updateCustomerAddress(updateCustomerAddressInput: UpdateCustomerAddressInput!): ShopifyAddress!
  setCustomerName(setCustomerNameInput: SetCustomerNameInput!): Customer!
  createCampaignCoupon(createCampaignCouponInput: CreateCampaignCouponInput!): CampaignCoupon!
  updateCampaignCoupon(updateCampaignCouponInput: UpdateCampaignCouponInput!): CampaignCoupon!
  deleteCampaignCoupon(code: String!): CampaignCoupon!
  createOrder(createOrderInput: CreateOrderInput!): Order!
  finalOrderConfirm(orderId: String!, storeId: String!): Order!
  syncShopifyOrderStatus(orderId: String!): Order!
  updateOrder(updateOrderInput: UpdateOrderInput!): Order!
  holdOrder(orderId: String!): Order!
  createIssueTicket(createIssueTicketInput: CreateIssueTicketInput!): RaisedIssue!
  createInteriorArchitecture(createInteriorArchitectureInput: CreateInteriorArchitectureInput!): InteriorArchitecture!
  createGlobalConfiguration(createGlobalConfigurationInput: CreateGlobalConfigurationInput!): GlobalConfiguration!
  updateGlobalConfiguration(updateGlobalConfigurationInput: CreateGlobalConfigurationInput!): GlobalConfiguration!
  deleteGlobalConfiguration(key: String!, updatedBy: String!): GlobalConfiguration!
  createLocalConfiguration(createLocalConfigurationInput: CreateLocalConfigurationInput!): LocalConfiguration!
  updateLocalConfiguration(updateLocalConfigurationInput: CreateLocalConfigurationInput!): LocalConfiguration!
  removeLocalConfiguration(storeId: String!, key: String!): LocalConfiguration!
  bulkUpdateSkuPriceMaster(bulkUpdateSkuPriceMasterInput: BulkUpdateSkuPriceMasterInput!): SkuPriceMaster!
  createSTN(createSTNInput: CreateSTNInput!): STN!
  updateSTN(updateSTNInput: UpdateSTNInput!): STN!
  updateSTNProgressStatus(id: String!, progressStatus: String!): STN!
  removeSTN(id: String!): STN!
  createGRN(createGRNInput: CreateGRNInput!): GRNData!
  createInventoryCredential(createInventoryCredentialInput: CreateInventoryCredentialInput!): InventoryCredentials!
  createCMS(createCMSInput: CreateCMSInput!): CMS!
  updateCMS(updateCMSInput: UpdateCMSInput!): CMS!
  confirmCMS(confirmCMSInput: ConfirmCMSInput!): CMS!
  deleteCMS(id: String!): CMS!
  updateInventoryTracking(updateInventoryTrackingInput: UpdateInventoryTrackingInput!): InventoryTracking!
  createStorePo(createStorePOInput: CreateStorePOInput!): STN!
  updateStorePo(updateStorePoInput: UpdateStorePOInput!): StorePO!
  removeStorePo(id: Int!): StorePO!
  createNotification(createNotificationInput: CreateNotificationsInput!): NotificationData!
  updateNotification(updateNotificationsInput: UpdateNotificationsInput!): NotificationData!
  deleteNotification(id: String!): NotificationData!
  updateFirebaseToken(updateFirebaseToken: UpdateFirebaseTokenInput!): TokenData!
  updateStoreNotificationAction(updateNotificationActionInput: UpdateNotificationActionInput!): StoreNotificationData!
  createReplacementOrder(createReplacementOrderInput: CreateReplacementOrderInput!): ReplacementOrder!
  updateReplacementOrderProduct(updateReplacementOrderProductInput: UpdateReplacementOrderProductInput!): ReplacementOrder!
  validateAmountReplacementOrder(validateAmountReplacementOrderInput: ValidateReplacementOrderInput!): ReplacementOrder!
  createFinalOrder(id: String!, shopifyOrderId: String!, AWBNumber: String, AWBApprovedBy: String, newMinDate: String, newMaxDate: String): ReplacementOrder!
  confirmReplacementOrder(id: String!, AWBNumber: String, AWBApprovedBy: String, newMinDate: String, newMaxDate: String): ReplacementOrder!
  updateRefundPrice(updateRefundPriceInput: UpdateRefundPriceInput!): ReplacementOrder!
  cancelInitiatedReturn(id: String!, shopifyOrderId: String!): CancelInitiatedReturn!
  regenerateReturn(id: String!, shopifyOrderId: String!): RegenerateReturn!
  createRefundDetail(createRefundDetailInput: CreateRefundDetailInput!): RefundDetail!
  updateRefundStatus(updateRefundStatusInput: UpdateRefundDetailInput!): RefundDetail!
  createCancellationRefund(createCancellationRefundInput: CreateCancellationRefundInput!): RefundDetail!
  claimSpinWheelReward(claimSpinWheelRewardInput: ClaimSpinWheelRewardInput!): ClaimSpinTheWheel!
  createCustomDiscountVerification(createCustomDiscountVerificationInput: CreateCustomDiscountVerificationInput!): CustomDiscountVerification!
  verifyCustomDiscountOTP(quotationId: String!, otp: String!): VerificationResponse!
  removeCustomDiscountVerification(id: Int!): CustomDiscountVerificationData!
  createStoreOrdersApiKey(input: CreateStoreOrdersApiKeyInput!): StoreOrdersApiKey!
  updateStoreOrdersApiKey(input: UpdateStoreOrdersApiKeyInput!): StoreOrdersApiKey!
  createBulkGenerateCoupon(createBulkGenerateCouponsInput: CreateBulkGenerateCouponInput!): BulkGenerateCoupon!
  updateBulkGenerateCoupon(updateBulkGenerateCouponInput: UpdateBulkGenerateCouponInput!): BulkGenerateCoupon!
  removeBulkGenerateCoupon(id: Int!): BulkGenerateCoupon!
  cancelOrder(cancelOrderInput: CancelOrderInput!): CancelOrder!
  createAiMattressOtp(createAiMattressOtpInput: CreateAiMattressOtpInput!): Response!
  validateAiMattressOtp(verifyAiMattressOtpInput: VerifyAiMattressOtpInput!): Response!
  recommendMattress(saveReportInput: SaveReportInput!): RecommendationResponse!
  sendWhatsAppMessage(sendWhatsAppMessage: SendWhatsAppMessageInput!): SendWhatsAppSuccess!
  createPrice(createPriceInput: CreatePriceInput!): Price!
}

input CreateEmployeeInput {
  """ID of the note author"""
  id: String!

  """First name of the note author"""
  firstName: String!

  """Last name of the note author"""
  lastName: String!

  """Email address associated with the note"""
  email: String

  """Phone number associated with the note"""
  phone: String

  """Role to a specific store"""
  role: Role = STAFF

  """Access to a store"""
  stores: [String!]

  """Cognito ID of an employee"""
  cognitoId: String

  """Designation of an employee"""
  designation: String
}

enum Role {
  STAFF
  STORE_LOGIN
  MANAGER
  AREA_MANAGER
  CRM
  ADMIN
  CRM_SALES
}

input UpdateEmployeeInput {
  """ID of the employee"""
  id: String!

  """First name of the note author"""
  firstName: String

  """Last name of the note author"""
  lastName: String

  """Phone number associated with the note"""
  phone: String

  """First name of the note author"""
  isActive: Boolean

  """Access to a store"""
  stores: [String!]

  """Designation of an employee"""
  designation: String
}

input CreateCouponInput {
  value_type: String!
  value: String!
  customerId: String!
  storeId: String!
}

input CreateStoreInput {
  """Name of the store"""
  name: String!

  """Description of the store"""
  description: String

  """Contact number of the store"""
  phone: String!

  """Email address of the store"""
  email: String!

  """Source Warehouse mapping ID"""
  sourceWarehouseMappingId: String!

  """Store code"""
  id: String!

  """GST Details"""
  gstDetails: GstDetailsInput

  """Billing Information with Name and Address info"""
  billingInfo: BillingInfoInput!

  """Billing Information with Name and Address info"""
  reviewsBrochure: String

  """Address info"""
  address: AddressInput!

  """Quotation Start Code"""
  quotationStartCode: String!

  """store location key"""
  getStoreLocationKey: String!

  """map link"""
  mapLink: String!

  """Store short code"""
  storeShortCode: String!

  """Store Timings"""
  storeTimings: String!

  """Store Image"""
  storeImage: [String!]!

  """Invoice Start Code"""
  invoiceStartCode: String!

  """SAP Location Code"""
  sapLocationCode: String!

  """Warehouse mapping ID"""
  warehouseMappingId: String!

  """EasyEcom Location ID"""
  easyEcomWarehouseLocationId: String

  """SAP Account Code"""
  accountCode: String!

  """SAP Card Code"""
  cardCode: String!

  """POS ID"""
  posIds: [String!]

  """DID Number"""
  didNumber: String

  """Mswipe POS ID"""
  mswipePosIds: [String!]

  """Area Sales Manager"""
  asm: String!

  """Zonal Head"""
  zonalHead: String!

  """Billing Information with Name and Address info"""
  snapmintInfo: SnapmintInput

  """Billing Information with Name and Address info"""
  pinelabsInfo: [PinelabsInput!]
}

input GstDetailsInput {
  """GST Number"""
  gstNumber: String!

  """GST Owner Name"""
  companyName: String!
}

input BillingInfoInput {
  """Name of the Entity"""
  name: String!

  """Address of the Entity"""
  address: AddressInput!
}

input AddressInput {
  """Address Line 1"""
  line1: String!

  """Address Line 2"""
  line2: String

  """City"""
  city: String!

  """State"""
  state: String!

  """State Code"""
  stateCode: String

  """State Code"""
  stateNo: String

  """Country"""
  country: String!

  """Pin code"""
  pinCode: String!

  """Latitude"""
  latitude: String

  """Longitude"""
  longitude: String
}

input SnapmintInput {
  """snapmint Merchant ID"""
  merchantId: String!

  """snapmint token"""
  accessToken: String!

  """snapmint token"""
  merchantKey: String!
}

input PinelabsInput {
  """Pinelabs POS ID"""
  posId: String!

  """pine labs storeid"""
  storeId: String!
}

input UpdateStoreInput {
  """Item pointer"""
  id: String!

  """Name of the store"""
  name: String

  """Billing Information with Name and Address info"""
  pinelabsInfo: [PinelabsInput!]

  """Description of the store"""
  description: String

  """Contact number of the store"""
  phone: String

  """Billing Information with Name and Address info"""
  reviewsBrochure: String

  """Email address of the store"""
  email: String

  """GST Details"""
  gstDetails: GstDetailsInput

  """Billing Information with Name and Address info"""
  billingInfo: BillingInfoInput

  """Address info"""
  address: AddressInput

  """Quotation Start Code"""
  quotationStartCode: String

  """Invoice Start Code"""
  invoiceStartCode: String

  """store location key"""
  getStoreLocationKey: String

  """map link"""
  mapLink: String

  """Store short code"""
  storeShortCode: String

  """Store Timings"""
  storeTimings: String

  """Store Image"""
  storeImage: [String!]

  """SAP Location Code"""
  sapLocationCode: String

  """Warehouse mapping ID"""
  warehouseMappingId: String

  """SAP Account Code"""
  accountCode: String

  """SAP Card Code"""
  cardCode: String

  """POS ID"""
  posIds: [String!]

  """MSwipe POS ID"""
  mswipePosIds: [String!]

  """Area Sales Manager"""
  asm: String

  """Source Warehouse mapping ID"""
  sourceWarehouseMappingId: String

  """DID Number"""
  didNumber: String

  """Zonal Head"""
  zonalHead: String

  """EasyEcom Location ID"""
  easyEcomWarehouseLocationId: String

  """Active/Inactive status"""
  isActive: Boolean!

  """Billing Information with Name and Address info"""
  snapmintInfo: SnapmintInput
}

input CreatePaymentInput {
  transactionAmount: Float!
  splitPayment: Boolean!
  mode: PaymentMode!
  orderId: String!
  posId: String
  transactionType: TransactionType
  phone: String
  transactionNumber: String
  transactionScreenshotS3Key: String
  storeId: String!
}

input VerifyPaymentTransactionInput {
  transactionId: String!
  orderId: String!
  manualVerification: ManualVerificationInput
  isPolling: Boolean
}

input ManualVerificationInput {
  paymentID: String!
  amountPaid: Float!
  paymentMethod: String!
}

input CancelPaymentTransactionInput {
  transactionId: String!
  orderId: String!
  storeId: String
}

input CreateQuotationInput {
  """Quotation Type"""
  type: String!

  """ID of the customer for the quotation."""
  customerId: String!

  """Customer metadata for the quotation."""
  customer: CustomerMetadataInput

  """List of products being quoted."""
  quotationProducts: [CartProductInput!]

  """ID of the store issuing the quotation."""
  storeId: String!

  """ID of the employee creating the quotation."""
  employeeId: String!

  """Campaign code associated with the quotation."""
  campaignCode: String

  """Custom code for the quotation."""
  customCode: CustomCodeInput

  """Promotional code for the quotation."""
  promotionalCode: String

  """Notes or comments for the quotation."""
  notes: String

  """Source for the quotation."""
  source: String

  """Price lock booking amount"""
  bookingAmount: Float

  """Price lock booking amount status"""
  bookingAmountStatus: String

  """BNPL Product data"""
  bnplProduct: CartProductInput

  """Total amount"""
  totalAmount: Float!

  """Final Amount"""
  finalDiscountedAmount: Float!

  """Promotional Discount Amount"""
  promotionalDiscountAmount: Float!

  """Custom Discount Amount"""
  customDiscountAmount: Float!

  """Campaign Discount Amount"""
  campaignDiscountAmount: Float!

  """Free Products"""
  freeProducts: [CartProductInput!]
}

input CustomerMetadataInput {
  """First Name of the customer"""
  firstName: String

  """First Name of the customer"""
  lastName: String

  """Phone number"""
  phone: String

  """Email of the customer"""
  email: String

  """date of birth"""
  dob: String

  """anniversaryDate"""
  anniversaryDate: String

  """serviceLift"""
  serviceLift: String

  """accommodationType"""
  accommodationType: String

  """landmark"""
  landmark: String

  """latitude"""
  latitude: String

  """longitude"""
  longitude: String
}

input CustomCodeInput {
  value_type: CouponType
  approver: String
  value: Float
}

input EmployeeDetailsInput {
  """Employee's phone number"""
  phone: String!

  """Employee's full name"""
  name: String!
}

input UpdateQuotationInput {
  """ID of the customer for the quotation."""
  id: String!

  """ID of the employee creating the quotation."""
  employeeId: String

  """Customer metadata for the quotation."""
  customer: CustomerMetadataInput

  """Final Amount"""
  finalDiscountedAmount: Float!

  """Shipping Address for the quotation."""
  shippingAddress: QuotationAddressInput

  """Billing Address for the quotation."""
  billingAddress: QuotationAddressInput
  gstDetails: GSTDetailsInput

  """Interior Architecture."""
  interiorArchitecture: InteriorArchitectureDetailsInput

  """Notes or comments for the order."""
  notes: String

  """Source for the order."""
  source: String

  """delivery amount"""
  deliveryCharge: Float

  """Customer acknowledgement for the order."""
  customerAcknowledgement: String

  """Customer Identity proof for the order."""
  customerIdentityProof: String

  """Additional Promotional Coupons"""
  additionalPromotionalCoupons: [AdditionalPromotionalCouponInput!]

  """Quotation Expire."""
  expiresAt: String

  """Quotation status"""
  status: String

  """Quotation status"""
  bookingAmountStatus: String

  """Order Id"""
  orderId: String

  """Booking Amount Discount"""
  bookingAmountPaid: Float

  """Check Shopify Inventory"""
  checkShopifyInventory: Boolean
}

input QuotationAddressInput {
  """Shopify address Id"""
  shopifyAddressId: String

  """Address Line 1"""
  line1: String!

  """Address Line 2"""
  line2: String

  """City"""
  city: String!

  """State"""
  state: String!

  """Country"""
  country: String!

  """Pin code"""
  pinCode: String!
}

input GSTDetailsInput {
  """GST Number of the customer."""
  gstNumber: String

  """Company Name of the customer."""
  companyName: String
}

input InteriorArchitectureDetailsInput {
  """GST Number of the customer."""
  id: String!

  """Company Name of the customer."""
  name: String

  """Company Name of the customer."""
  source: String

  """Commissioned."""
  commissioned: Boolean
}

input AdditionalPromotionalCouponInput {
  """Promotional Code"""
  promotionalCode: String!

  """Promotional Discount Amount"""
  promotionalDiscountAmount: Float!
}

input DeliveryChargeApprovalInput {
  name: String!
  phone: String!
  requestedCharge: Float!
}

input UpdateQuotationProductsInput {
  """ID of the quotation."""
  id: String!

  """Additional Promotional Coupons"""
  additionalPromotionalCoupons: [AdditionalPromotionalCouponInput!]

  """"""
  employeeDiscountStatus: EmployeeDiscountStatus

  """ID of the customer for the quotation."""
  customerId: String!

  """Customer metadata for the quotation."""
  customer: CustomerMetadataInput

  """List of products being quoted."""
  quotationProducts: [CartProductInput!]

  """ID of the store issuing the quotation."""
  storeId: String!

  """ID of the employee creating the quotation."""
  employeeId: String!

  """Campaign code associated with the quotation."""
  campaignCode: String

  """Custom code for the quotation."""
  customCode: CustomCodeInput

  """Promotional code for the quotation."""
  promotionalCode: String

  """Notes or comments for the quotation."""
  notes: String

  """Source for the quotation."""
  source: String

  """delivery amount"""
  deliveryCharge: Float

  """Total amount"""
  totalAmount: Float!

  """Total amount"""
  customDiscountVerificationDetails: CustomDiscountVerificationDetailsInput

  """Final Amount"""
  finalDiscountedAmount: Float!

  """Promotional Discount Amount"""
  promotionalDiscountAmount: Float!

  """Custom Discount Amount"""
  customDiscountAmount: Float!

  """Campaign Discount Amount"""
  campaignDiscountAmount: Float!

  """Free Products"""
  freeProducts: [CartProductInput!]
  type: String
  bookingAmountStatus: String
  bnplProduct: CartProductInput
  bookingAmount: Float
}

input CustomDiscountVerificationDetailsInput {
  """Quotation ID"""
  id: String!

  """The discount amount requested for the order."""
  customDiscountAmount: Float!

  """The mobile number of the customer requesting the discount."""
  approverDetails: ApproverDetailsInput!

  """
  The status of the discount approval process (REQUESTED, REJECTED, APPROVED, APPLIED, EXPIRED).
  """
  customDiscountApprovalStatus: CustomDiscountApprovalStatus!

  """The discount amount requested for the order."""
  customDiscountType: CouponType!

  """The discount amount requested for the order."""
  customDiscountValue: Float!

  """The discount amount requested for the order."""
  additionalDiscountPercentage: Float!

  """The discount amount requested for the order."""
  cartTotalAmount: Float!

  """The timestamp when the discount expired."""
  discountExpiredTimestamp: String!
}

input ApproverDetailsInput {
  """The unique identifier of the store requesting the discount approval."""
  name: String!

  """The unique identifier of the store requesting the discount approval."""
  email: String!

  """The unique identifier of the store requesting the discount approval."""
  phone: String!
}

input CreateCartInput {
  customerId: String!
  cartProducts: [CartProductInput!]!
  storeId: String!
  employeeId: String!
  campaignCode: String
  customCode: CustomCodeInput
  promotionalCode: String
  notes: String
  pinCode: String
}

input UpdateCartInput {
  customerId: String!
  cartProducts: [CartProductInput!]!
  storeId: String!
  employeeId: String!
  campaignCode: String
  customCode: CustomCodeInput
  promotionalCode: String
  notes: String
  pinCode: String
}

input CreateCustomerAddressInput {
  """Address Line 1"""
  address: CustomerAddressInput!

  """Phone number"""
  phone: String

  """First Name"""
  firstName: String

  """Last Name"""
  lastName: String

  """Customer ID"""
  customerId: String!
}

input CustomerAddressInput {
  """Address Line 1"""
  line1: String!

  """Address Line 2"""
  line2: String

  """City"""
  city: String!

  """State"""
  state: String!

  """Country"""
  country: String!

  """Pin code"""
  pinCode: String!

  """latitude"""
  serviceLift: String

  """longitude"""
  accommodationType: String

  """longitude"""
  landmark: String

  """latitude"""
  latitude: Float

  """longitude"""
  longitude: Float
}

input UpdateCustomerAddressInput {
  """Address"""
  address: CustomerAddressInput!

  """Phone number"""
  phone: String

  """First Name"""
  firstName: String

  """Last Name"""
  lastName: String

  """Customer ID"""
  customerId: String!

  """Address ID"""
  addressId: String!
}

input SetCustomerNameInput {
  """First Name"""
  firstName: String

  """Last Name"""
  lastName: String

  """Last Name"""
  email: String

  """Last Name"""
  customerId: String!

  """Last Name"""
  dob: String

  """Anniversary Date"""
  anniversaryDate: String
}

input CreateCampaignCouponInput {
  code: String!
  description: String
  campaignCouponType: CampaignCouponType!
  buyXQuantity: Int
  buyMinXQuantity: Int
  getYAmount: Float
  getYPercentage: Float
  getYQuantity: Int
  getYProduct: ApplicableProductsInput
  minOrderValue: Float
  maxOrderValue: Float
  usageLimitForEachStore: Boolean
  maxDiscount: Float
  applicableProducts: [ApplicableProductsInput!]
  applicableProductsY: [ApplicableProductsInput!]
  startsAt: String!
  endsAt: String
  storeAllocationType: StoreAllocationType!
  entitledStoreIds: [String!]
  usageLimit: Int
  storeUsageLimit: [StoreWiseUsageCountInput!]
  usageResetType: CouponUsageResetType
  customOrderValueErrorMsg: String
  promoApplicable: String
  promoCoupons: [String!]
  couponApplicationType: CouponApplicationType
}

input ApplicableProductsInput {
  productId: String!
  variantId: String
}

input StoreWiseUsageCountInput {
  storeId: String!
  usageLimit: Int
}

input UpdateCampaignCouponInput {
  code: String!
  description: String
  campaignCouponType: CampaignCouponType!
  buyXQuantity: Int
  getYAmount: Float
  getYPercentage: Float
  getYQuantity: Int
  buyMinXQuantity: Int
  minOrderValue: Float
  maxDiscount: Float
  applicableProducts: [ApplicableProductsInput!]
  applicableProductsY: [ApplicableProductsInput!]
  startsAt: String!
  endsAt: String
  storeAllocationType: StoreAllocationType
  entitledStoreIds: [String!]
  getYProduct: ApplicableProductsInput
  isActive: Boolean
  usageLimit: Int
  maxOrderValue: Float
  usageLimitForEachStore: Boolean
  storeUsageLimit: [StoreWiseUsageCountInput!]
  usageResetType: CouponUsageResetType
  customOrderValueErrorMsg: String
  promoApplicable: String
  promoCoupons: [String!]
  couponApplicationType: CouponApplicationType
}

input CreateOrderInput {
  """Quotation ID of the customer for the order."""
  quotationId: String!

  """List of products being quoted."""
  orderProducts: [OrderProductInput!]!

  """ID of the store issuing the order."""
  storeId: String!

  """ID of the employee creating the order."""
  employeeId: String!

  """Min Delivery date of the order"""
  minDeliveryDate: String

  """Unhold future dispatch date of the order"""
  unHoldFutureDispatchDate: String

  """Delivery date of the order"""
  deliveryDate: String!
  dispatchDate: String
  eddHeaderId: String
  posFallbackEdd: Boolean

  """Delivery Type of the order"""
  deliveryStatus: DeliveryType

  """Order notes"""
  notes: String

  """Tentative Purchase Date"""
  tentativePurchaseDate: String
}

input OrderProductInput {
  """ID of the order item"""
  id: String

  """ProductID of the order product"""
  productId: String!

  """VariantID of the order product"""
  variantId: String

  """Quantity of the order product"""
  quantity: Int!

  """Custom Meta data"""
  customData: CustomDataInput

  """Price of the order product"""
  price: Float!

  """Type of price"""
  priceType: String

  """Title of the order product"""
  title: String

  """VariantTitle of the order product"""
  variantTitle: String

  """Image of the order product"""
  image: ImageInput

  """Sku of the order product"""
  sku: String!
  productType: String

  """Delivery Type of the order product"""
  deliveryStatus: DeliveryType!

  """Delivery date of the order product"""
  deliveryDate: String!

  """Delivery range of the order product"""
  deliveryRange: String

  """Min Delivery date of the order product"""
  minDeliveryDate: String

  """EDD assumed for this order product"""
  isAssumed: Boolean
  dispatchDate: String
  fallbackEdd: Boolean
  hasColorOptions: Boolean
}

input ImageInput {
  admin_graphql_api_id: String
  alt: String
  created_at: String
  height: Float
  id: ID
  position: Float
  product_id: ID
  src: String
  updated_at: String
  variant_ids: [ID!]
  width: Float
}

input UpdateOrderInput {
  """Order ID"""
  id: String!

  """Hold Order"""
  holdOrder: Boolean

  """ID of the employee creating the quotation."""
  employeeId: String

  """Customer metadata for the quotation."""
  customer: CustomerMetadataInput

  """Shipping Address for the quotation."""
  shippingAddress: QuotationAddressInput

  """Billing Address for the quotation."""
  billingAddress: QuotationAddressInput
  gstDetails: GSTDetailsInput

  """Interior Architecture."""
  interiorArchitecture: InteriorArchitectureDetailsInput

  """Notes or comments for the order."""
  notes: String

  """Customer acknowledgement for the order."""
  customerAcknowledgement: String

  """Customer Identity proof for the order."""
  customerIdentityProof: String

  """List of products being quoted."""
  orderProducts: [OrderProductInput!]

  """Min Delivery date of the order"""
  minDeliveryDate: String

  """Unhold future dispatch date of the order"""
  unHoldFutureDispatchDate: String

  """Reason for Holding the Order"""
  holdOrderReason: String
  dispatchDate: String
  eddHeaderId: String
  posFallbackEdd: Boolean

  """Delivery date of the order"""
  deliveryDate: String

  """Delivery Type of the order"""
  deliveryStatus: DeliveryType

  """delivery amount"""
  deliveryCharge: Float

  """Final Amount"""
  finalDiscountedAmount: Float

  """Additional Promotional Coupons"""
  additionalPromotionalCoupons: [AdditionalPromotionalCouponInput!]

  """Tentative Purchase Date"""
  tentativePurchaseDate: String
}

input CreateIssueTicketInput {
  """Current status of the ticket"""
  status: Float!

  """Description of the issue"""
  description: String!

  """RRTicketId"""
  RRTicketId: String

  """Priority of the issue"""
  priority: Float!

  """Type of the issue"""
  subject: String!

  """Order ID related to the issue"""
  id: String!

  """Shopify Order Name related to the issue"""
  orderId: String!

  """Phone number of the customer"""
  phone: String!

  """Name of the customer"""
  name: String!

  """Email of the customer"""
  email: String!

  """Group_id of the ticket"""
  group_id: Float

  """voc1 of the ticket"""
  voc1: String

  """voc2 of the ticket"""
  voc2: String
}

input CreateInteriorArchitectureInput {
  """Unique identifier for the user"""
  id: String!

  """City of the user"""
  city: String

  """Date of the user"""
  date: String

  """Email of the user"""
  email: String

  """File upload of the user"""
  fileupload: String

  """GST of the user"""
  gst: String

  """Name of the user"""
  name: String

  """Last Name of the user"""
  lname: String

  """Organisation of the user"""
  organisation: String

  """PAN of the user"""
  pan: String

  """Phone number of the user"""
  phone: String

  """Source of the user"""
  source: String!

  """Store of the user"""
  store: String

  """Store of the user"""
  vendorcode: String
}

input CreateGlobalConfigurationInput {
  """Key"""
  key: String!

  """Value"""
  value: String!

  """Updated By"""
  updatedBy: String
}

input CreateLocalConfigurationInput {
  """StoreID"""
  storeId: String!

  """Key"""
  key: String!

  """Value"""
  value: Float!
}

input BulkUpdateSkuPriceMasterInput {
  """file path"""
  filePath: String!
}

input CreateSTNInput {
  """Additional remarks for the order"""
  remarks1: String

  """Additional remarks for the order"""
  remarks2: String

  """List of products included in the order"""
  products: [STNProductInput!]!

  """attachement"""
  attachmentS3Key: String

  """Unique identifier for the customer"""
  storeId: String!

  """Requested store ID"""
  requestedStoreId: String

  """Order number"""
  sourceWarehouseMappingId: String

  """Forward or Reverse"""
  requestMode: STNRequestMode!

  """Forward or Reverse"""
  transferMode: STNTransferMode!

  """Unique identifier for the customer"""
  status: STNStatus!

  """Bulk STN Code"""
  bulkSTNCode: String
}

input STNProductInput {
  """SKU of the product"""
  sku: String!

  """title of the product"""
  title: String!

  """Quantity of the product ordered"""
  quantity: Float!
}

input UpdateSTNInput {
  """Type of the order"""
  id: String!

  """Additional remarks for the order"""
  remarks1: String

  """attachement"""
  attachmentS3Key: String

  """List of products included in the order"""
  products: [STNProductInput!]!

  """Forward or Reverse"""
  requestMode: STNRequestMode!

  """Unique identifier for the customer"""
  status: STNStatus!

  """Bulk STN Code"""
  bulkSTNCode: String
}

input CreateGRNInput {
  """ PO Id"""
  poId: String!

  """ STN Id"""
  stnId: String!

  """Vendor Id"""
  vendorId: Int

  """Partial GRN Reason"""
  partialGRNReason: String

  """Remarks"""
  remarks: String

  """attachement"""
  attachmentS3Key: String

  """List of products included in the order"""
  products: [GRNProductInput!]!
}

input GRNProductInput {
  """SKU of the product"""
  sku: String!

  """Quantity of the product ordered"""
  receivedQuantity: Float!

  """Quantity of the product ordered"""
  assignedQuantity: Float!
}

input CreateInventoryCredentialInput {
  """Unique identifier for the credential"""
  id: String!

  """Location key for the inventory system"""
  locationKey: String!

  """Secret key for the inventory system"""
  secretKey: String!

  """Secret key for the inventory system"""
  state: String!
}

input CreateCMSInput {
  """Total Cash"""
  totalCash: Float!

  """Total Orders"""
  totalOrders: Float!

  """CMS Order IDs"""
  transactionsCovered: [CMSIdsInput!]!

  """Employee ID"""
  employeeId: String

  """Store ID"""
  storeId: String!
}

input CMSIdsInput {
  """ID"""
  orderId: String!

  """ID"""
  transactionId: String!
}

input UpdateCMSInput {
  """ID"""
  id: String!

  """Total Cash"""
  totalCash: Float

  """Total Orders"""
  totalOrders: Float

  """CMS Order IDs"""
  transactionsCovered: [CMSIdsInput!]

  """Employee ID"""
  employeeId: String

  """Image URL"""
  imageUrl: String

  """Handover ID"""
  handoverId: String

  """Confirm Cash HandOver"""
  confirmCashHandOver: Float

  """Handover Date"""
  handoverDate: String
}

input ConfirmCMSInput {
  """ID"""
  id: String!

  """Total Cash"""
  totalCash: Float!

  """Total Orders"""
  totalOrders: Float!

  """CMS Order IDs"""
  transactionsCovered: [CMSIdsInput!]!

  """Employee ID"""
  employeeId: String!

  """Image URL"""
  imageUrl: String

  """Handover ID"""
  handoverId: String!

  """Confirm Cash HandOver"""
  confirmCashHandOver: Float

  """Handover Date"""
  handoverDate: String!
}

input UpdateInventoryTrackingInput {
  """Id of store"""
  storeId: String!

  """List of products to update"""
  products: [UpdateInventoryTrackingProductInput!]!
}

input UpdateInventoryTrackingProductInput {
  """Id of product to update"""
  id: String!

  """Quantity of product to update"""
  displayItemQuantity: Float!
}

input CreateStorePOInput {
  """Example field (placeholder)"""
  stnId: String!
}

input UpdateStorePOInput {
  stnId: String!
  poId: String!

  """List of products included in the order"""
  products: [UpdateStorePOProductsInput!]!
}

input UpdateStorePOProductsInput {
  """SKU of the product"""
  id: String!

  """Quantity of the product ordered"""
  pendingQuantity: String!

  """Quantity to update"""
  assignedQuantity: Float!
}

input CreateNotificationsInput {
  """createdBy"""
  createdBy: String!

  """header"""
  header: String!

  """description"""
  description: String!

  """notificationtype"""
  notificationtype: NotificationType!

  """triggerTime"""
  triggerTime: String!

  """link"""
  link: String

  """HighAlert"""
  highAlert: Boolean!

  """pinned"""
  pinned: Boolean!

  """pinnedContent"""
  pinnedContent: String

  """pinnedStartTime"""
  pinnedStartTime: String

  """pinnedEndTime"""
  pinnedEndTime: String

  """allocationType"""
  allocationType: StoreAllocationType!

  """stores"""
  stores: [String!]!

  """status"""
  notificationStatus: NotificationStatus!

  """isDisabled"""
  isDisabled: Boolean!

  """sendImmediately"""
  sendImmediately: Boolean!
}

input UpdateNotificationsInput {
  """id"""
  id: String!

  """header"""
  header: String!

  """description"""
  description: String!

  """notificationtype"""
  notificationtype: NotificationType!

  """triggerTime"""
  triggerTime: String!

  """link"""
  link: String

  """HighAlert"""
  highAlert: Boolean!

  """pinned"""
  pinned: Boolean!

  """pinnedContent"""
  pinnedContent: String

  """pinnedStartTime"""
  pinnedStartTime: String

  """pinnedEndTime"""
  pinnedEndTime: String

  """allocationType"""
  allocationType: StoreAllocationType!

  """stores"""
  stores: [String!]!

  """isDisabled"""
  isDisabled: Boolean!

  """status"""
  notificationStatus: NotificationStatus!

  """sendImmediately"""
  sendImmediately: Boolean!
}

input UpdateFirebaseTokenInput {
  """storeId"""
  storeId: String!

  """token"""
  token: String!

  """cognitoId"""
  cognitoId: String!
}

input UpdateNotificationActionInput {
  """storeId"""
  storeId: String!

  """notificationId"""
  notificationId: String!

  """increaseCount"""
  increaseCount: Boolean

  """isRead"""
  isRead: Boolean
}

input CreateReplacementOrderInput {
  shopifyOrderId: String!
  ticketId: String!
  orderType: OrderType! = POS
  replacedProduct: ReplacedProductInput!
  replacementType: ReplacementType

  """Primary reason for the replacement"""
  primaryReason: String

  """Secondary reason or sub-reason for the replacement"""
  secondaryReason: String

  """Department related to the replacement request"""
  department: String

  """Agent name, required when department is AGENT"""
  agentName: String

  """Additional comments or notes about the replacement"""
  additionalComments: String
  requestId: String
}

input ReplacedProductInput {
  productId: String!
  variantId: String
  sku: String
  quantity: Int!
  customData: CustomDataInput
}

input UpdateReplacementOrderProductInput {
  id: String!
  shopifyOrderId: String!
  ticketId: String
  orderType: OrderType
  replacedProduct: ReplacedProductInput
  pickupPriority: PickupPriority!
  replacementType: ReplacementType
  reason: String
  orderProducts: [CartProductInput!]!
}

input ValidateReplacementOrderInput {
  id: String!
  shopifyOrderId: String!
  replacementOption: ReplacementOptions
  replacedProduct: ReplacedProductInput
  pickupPriority: PickupPriority
  replacementType: ReplacementType
  reason: String
  orderProducts: [CartProductInput!]
  replaceableDiscountType: ReplaceableDiscountType

  """Custom code for the quotation."""
  customCode: CustomCodeInput

  """Custom Discount Amount"""
  customDiscountAmount: Float

  """Total amount"""
  totalAmount: Float

  """Final Amount"""
  finalDiscountedAmount: Float

  """List of products being quoted."""
  accessories: [CartProductInput!]

  """Delivery Type of the order product"""
  deliveryStatus: DeliveryType

  """Max Delivery date of the order product"""
  deliveryDate: String

  """Min Delivery date of the order product"""
  minDeliveryDate: String

  """Is Shipping charges."""
  isShippingCharged: Boolean

  """Shipping Address."""
  shippingAddress: QuotationAddressInput

  """Billing Address."""
  billingAddress: QuotationAddressInput
  ticketId: String
  orderType: OrderType
}

input UpdateRefundPriceInput {
  id: String!
  shopifyOrderId: String!

  """Manually modified refund price reason"""
  updatedRefundPrice: UpdatedRefundInput!

  """Refunded Price"""
  refundedPrice: Float!
}

input UpdatedRefundInput {
  approver: String!
  reason: String!
}

input CreateRefundDetailInput {
  id: String!
  shopifyOrderId: String!

  """Is Back To Source"""
  isBackToSource: Boolean!

  """Account Holder Name"""
  accountHolderName: String

  """Bank Account Name"""
  bankAccountName: String

  """IFSC Code"""
  ifscCode: String

  """Account No"""
  accountNo: String

  """Cancelled Checkque Image S3 Key"""
  chequeImageKey: String
  upiName: String
  upiId: String
  isFormSubmitted: Boolean
  requestId: String
}

input UpdateRefundDetailInput {
  id: String!
  shopifyOrderId: String!

  """Transaction ID for the refund"""
  transactionId: String

  """Amount refunded """
  refundAmount: Float

  """Date when refund was processed"""
  refundDate: String

  """Status of the refund"""
  status: String

  """sku for refund"""
  sku: String

  """Is Back To Source"""
  isBackToSource: Boolean

  """Account Holder Name"""
  accountHolderName: String

  """Bank Account Name"""
  bankAccountName: String

  """IFSC Code"""
  ifscCode: String

  """Account No"""
  accountNo: String

  """Cancelled Cheque Image S3 Key"""
  chequeImageKey: String

  """UPI ID"""
  upiId: String

  """UPI Name"""
  upiName: String

  """Form submission status"""
  isFormSubmitted: Boolean

  """Order type (CANCELLATION, RETURN, REPLACEMENT)"""
  orderType: String
}

input CreateCancellationRefundInput {
  id: String!
  shopifyOrderId: String!

  """Is Back To Source"""
  isBackToSource: Boolean!

  """Account Holder Name"""
  accountHolderName: String

  """Bank Account Name"""
  bankAccountName: String

  """IFSC Code"""
  ifscCode: String

  """Account No"""
  accountNo: String

  """Cancelled Cheque Image S3 Key"""
  chequeImageKey: String

  """UPI ID"""
  upiId: String
  isFormSubmitted: Boolean
  requestId: String
  customerEmail: String
  customerPhone: String
  customerFirstName: String
  customerLastName: String
}

input ClaimSpinWheelRewardInput {
  """number"""
  customerPhone: String!

  """codePrefix"""
  codePrefix: String!

  """storeId"""
  storeId: String!

  """storeId"""
  storeState: String!

  """storeId"""
  storeCity: String!

  """storeId"""
  storePinCode: String!
}

input CreateCustomDiscountVerificationInput {
  """Quotation Id"""
  quotationId: String!

  """Mobile number of the approver"""
  approverDetails: ApproverDetailsInput!

  """The discount amount requested for the order."""
  customDiscountType: CouponType!

  """The discount amount requested for the order."""
  customDiscountValue: Float!

  """The discount amount requested for the order."""
  customDiscountAmount: Float!

  """The discount amount requested for the order."""
  additionalDiscountPercentage: Float!
}

input CreateStoreOrdersApiKeyInput {
  """Array of Store IDs"""
  storeIds: [String!]!

  """Name for the API key"""
  name: String!
}

input UpdateStoreOrdersApiKeyInput {
  """API Key ID"""
  apiKey: String!

  """Array of Store IDs"""
  storeIds: [String!]!

  """Name for the API key"""
  name: String!
}

input CreateBulkGenerateCouponInput {
  """S3 file path for CSV"""
  filePath: String!
}

input UpdateBulkGenerateCouponInput {
  """S3 file path for CSV"""
  filePath: String
  id: Int!
}

input CancelOrderInput {
  """Reference code for the cancellation on easycom"""
  referenceCode: String!

  """Freshdesk ticket Id"""
  freshdeskId: String!

  """Reason for cancellation"""
  requestReason: String!

  """Request Id for cancellation"""
  requestId: String!
}

input CreateAiMattressOtpInput {
  """Name of the user"""
  name: String!

  """Email of the user"""
  email: String!

  """Phone number of the user"""
  phone: String!
}

input VerifyAiMattressOtpInput {
  """Phone number of the user"""
  phone: String!

  """OTP of the user"""
  otp: String!
}

input SaveReportInput {
  """Name of the user"""
  name: String!

  """Phone number of the user"""
  phone: String!

  """Questions and answers of the user"""
  questions: String!

  """storeId"""
  storeId: String!

  """storeId"""
  storeState: String!

  """storeId"""
  storeCity: String!

  """storeId"""
  storePinCode: String!
}

input SendWhatsAppMessageInput {
  """Phone number"""
  phone: String!

  """Message Type"""
  type: String!

  """Data"""
  data: DataObject

  """Data"""
  scope: [String!]
}

input DataObject {
  """Order Tracking link"""
  trackURL: String

  """Order Tracking link"""
  tracklink: String

  """Order ID"""
  order_id: String

  """Product Name"""
  productName: String
}

input CreatePriceInput {
  """Product ID"""
  product_id: ID!

  """Variant ID"""
  variant_id: ID!

  """Price of product"""
  price: String!

  """SKU of product"""
  sku: String
}