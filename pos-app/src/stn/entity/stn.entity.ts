import { ObjectType, Field, Int } from '@nestjs/graphql';
import {
  STNStatus,
  STNRequestMode,
  STNTransferMode,
  STNProgressStatus,
} from 'src/common/enum/stn';

@ObjectType()
export class STNProductData {
  @Field(() => String, { description: 'SKU of the product' })
  sku: string;

  @Field(() => String, { description: 'Title of the product' })
  title: string;

  @Field(() => Number, { description: 'Quantity of the product ordered' })
  quantity: number;

  @Field(() => Number, { description: 'Price of the product', nullable: true })
  price?: number;
}

@ObjectType()
export class STNData {
  @Field(() => String, { description: 'Order number' })
  id: string;

  @Field(() => String, {
    nullable: true,
    description: 'Order number',
  })
  sourceWarehouseMappingId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Order number',
  })
  attachmentS3Key?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Additional remarks for the order',
  })
  remarks1?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Additional remarks for the order',
  })
  remarks2?: string;

  @Field(() => [STNProductData], {
    description: 'List of products included in the order',
  })
  products: STNProductData[];

  @Field(() => STNStatus, {
    description: 'Unique identifier for the customer',
  })
  status: STNStatus;

  @Field(() => STNProgressStatus, {
    description: 'Unique identifier for the customer',
    nullable: true,
  })
  progressStatus?: STNProgressStatus;

  @Field(() => String, {
    description: 'Unique identifier for the customer',
    nullable: false,
  })
  storeId: string;

  @Field(() => STNRequestMode, {
    description: 'Forward or Reverse',
    nullable: false,
  })
  requestMode: STNRequestMode;

  @Field(() => STNTransferMode, {
    description: 'Forward or Reverse',
    nullable: false,
  })
  transferMode: STNTransferMode;

  @Field(() => String, {
    description: 'Requested store ID',
    nullable: true,
  })
  requestedStoreId?: string;

  @Field(() => String, { description: 'Creation timestamp of the order' })
  createdAt: string;

  @Field(() => String, { description: 'Last update timestamp of the order' })
  updatedAt: string;
  @Field(() => String, {
    description: 'Last update timestamp of the order',
    nullable: true,
  })
  ecomOrderId: string;
  @Field(() => String, {
    description: 'Last update timestamp of the order',
    nullable: true,
  })
  ecomSubOrderId: string;
  @Field(() => String, {
    description: 'Last update timestamp of the order',
    nullable: true,
  })
  ecomInvoiceId: string;

  @Field(() => String, {
    description: 'Bulk STN Code',
    nullable: true,
  })
  bulkSTNCode: string;
}

@ObjectType()
export class STN {
  @Field(() => STNData, { nullable: true, description: 'List of STN data' })
  data: STNData;

  @Field(() => String, { nullable: true, description: 'Response message' })
  message?: string;

  @Field(() => Int, { nullable: true, description: 'Response status' })
  status?: number;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Response success flag',
  })
  success?: boolean;
}
@ObjectType()
export class STNs {
  @Field(() => [STNData], { nullable: true, description: 'List of STN data' })
  data: STNData[];

  @Field(() => String, { nullable: true, description: 'Response message' })
  message?: string;

  @Field(() => Int, { nullable: true, description: 'Response status' })
  status?: number;

  @Field(() => Int, { nullable: true, description: 'Response count' })
  count?: number;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Response success flag',
  })
  success?: boolean;
}

@ObjectType()
export class ExportResponse {
  @Field(() => String, { nullable: false })
  message: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
