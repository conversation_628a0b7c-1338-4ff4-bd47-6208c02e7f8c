import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
import { ListSTNInput } from '../dto/list-stns.input';
import { json2csv } from 'json-2-csv';
import moment from 'moment';
import { STNData } from '../entity/stn.entity';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { TemplateType } from 'src/common/enum/template-type';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { QueryHandlerService } from 'src/common/query-handler/query-handler';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { filterFormatter } from 'src/common/helper/filter-helper';
import { ListGRNBySTNIds } from 'src/grn/lib/list-grn-by-stn-ids';
import { STNRequestMode, STNTransferMode } from 'src/common/enum/stn';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { QueryStoresByIds } from 'src/stores/lib/list-stores-by-ids';
import { GRN } from 'src/grn/entities/grn.entity';

export class ExportSTNs {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
  ) {}

  async exportSTNCSV(stns: STNData[]) {
    if (stns.length === 0) {
      return;
    }

    const stnIds = stns.map((stn) => stn.id);
    const storeIds: string[] = [...new Set(stns.map((stn) => stn.storeId))];

    const queryStoresByIdsHandler = new QueryStoresByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    const storesData = await queryStoresByIdsHandler.queryStoresByIds(storeIds);

    const listGRNBySTNIdsHandler = new ListGRNBySTNIds(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );

    const grns = await listGRNBySTNIdsHandler.listGRNBySTNIds(stnIds);

    const data = stns.flatMap((stn) => {
      const {
        id,
        createdAt,
        products,
        storeId,
        requestedStoreId,
        transferMode,
        requestMode,
        attachmentS3Key = null,
      } = stn;
      const { sourceWarehouseMappingId } =
        storesData.find((store) => store.id === storeId) || {};

      let from = null;
      let to = null;
      if (transferMode === STNTransferMode.STORE) {
        if (requestMode === STNRequestMode.FORWARD) {
          to = storeId;
          from = requestedStoreId;
        }
      } else {
        if (requestMode === STNRequestMode.FORWARD) {
          from = sourceWarehouseMappingId;
          to = storeId;
        } else if (requestMode === STNRequestMode.REVERSE) {
          from = storeId;
          to = sourceWarehouseMappingId;
        }
      }

      const formattedCreatedAt = moment(createdAt)
        .utcOffset('+05:30')
        .format('DD/MM/yyyy HH:mm');

      const rows: any[] = [];

      products.forEach((product) => {
        const stnStatus = this.getStatus(stn, grns);
        const matchingGrns = grns.filter((grn) =>
          grn.products.some(
            (grnProd) => grnProd.sku === product.sku && grn.stnId === id,
          ),
        );

        if (matchingGrns.length > 0) {
          matchingGrns.forEach((matchingGrn) => {
            const grnProduct = matchingGrn.products.find(
              (grnProd) => grnProd.sku === product.sku,
            );
            const formattedGRNdate = moment(matchingGrn.createdAt)
              .utcOffset('+05:30')
              .format('DD/MM/yyyy HH:mm');
            const formattedPOdate = moment(matchingGrn.poCreatedAt)
              .utcOffset('+05:30')
              .format('DD/MM/yyyy HH:mm');

            // Push a row with the corresponding PO ID and GRN ID
            rows.push({
              From: from,
              To: to,
              'STN ID': id,
              'SKU ID': product.sku,
              'GRN ID': matchingGrn.grnId || '-', // Get GRN ID or set to '-'

              'PO ID': matchingGrn.poId || '-', // Get PO ID or set to '-',

              'STN Quantity': product.quantity,
              'PO Quantity': grnProduct
                ? grnProduct.assignedQuantity || '-'
                : '-',
              'GRN Quantity': grnProduct
                ? grnProduct.receivedQuantity || '-'
                : '-',
              'Pending STN Quantity':
                Number(grnProduct.assignedQuantity || 0) -
                Number(grnProduct?.receivedQuantity || 0),
              'STN Status': stnStatus,
              'Request Mode': requestMode,
              'Transfer Mode': transferMode,
              Remarks: matchingGrn?.remarks || '-',
              'Partial GRN reason': matchingGrn?.partialGRNReason || '-',
              'Reverse STN Attachment link': attachmentS3Key || '-',
              'GRN Attachment link': matchingGrn?.attachmentS3Key || '-',
              'STN created Date': formattedCreatedAt,
              'PO created Date': formattedPOdate,
              'GRN Date': formattedGRNdate,
            });
          });
        } else {
          // No matching GRN for this SKU, create a row with dashes
          rows.push({
            From: from,
            To: to,
            'STN ID': id,
            'SKU ID': product.sku,
            'GRN ID': '-',
            'PO ID': '-',
            'STN Quantity': product.quantity,
            'PO Quantity': '-',
            'GRN Quantity': '-',
            'Pending STN Quantity': product.quantity,
            'STN Status': stnStatus,
            'Request Mode': requestMode,
            'Transfer Mode': transferMode,
            Remarks: '-',
            'Partial GRN reason': '-',
            'Reverse STN Attachment link': attachmentS3Key || '-',
            'GRN Attachment link': '-',
            'STN created Date': formattedCreatedAt,
            'PO created Date': '-',
            'GRN Date': '-',
          });
        }
      });

      return rows; // Return the array of rows created for the current STN
    });

    // Convert data to CSV using json2csv
    return await json2csv(data, {});
  }

  async exportSTNs(
    email: string,
    filter: ListSTNInput,
    isAllStore: boolean = false,
  ) {
    const STN_TABLE = await this.configParameters.getSTNTableName();
    console.log('export');

    const storeId = filter?.termSearchFields?.storeId;
    const storeIds = filter?.termSearchFields?.storeIds;
    if (!storeId && !isAllStore && !storeIds) {
      throw new CustomError('storeId is required', 400);
    }
    if (storeId && storeIds) {
      throw new CustomError('storeId and storeIds are mutually exclusive', 400);
    }

    const searchArray = [];

    const { searchArray: filteredSearch } = await filterFormatter(
      sortingFilter,
      searchingFilter,
      sortingFilterType,
      filter,
    );

    searchArray.push(...filteredSearch);
    if (storeIds && storeIds.length > 0) {
      searchArray.push({ terms: { 'storeId.keyword': storeIds } });
    }
    const { fromDate, toDate } = filter || {};
    if (fromDate && toDate) {
      searchArray.push({
        range: {
          createdAt: {
            gte: fromDate,
            lte: toDate,
          },
        },
      });
    }
    const esHandler = new ElasticClient(this.configService, this.ssmClient);
    const queryHandler = new QueryHandlerService(esHandler);
    const sortItems = [{ 'id.keyword': { order: 'desc' } }];
    const stns = await queryHandler.queryAll({
      index: STN_TABLE,
      filter: {
        bool: {
          must: [...searchArray],
        },
      },
      nextToken: null,
      sortItems,
    });
    console.log('stns', stns);
    if (stns.length === 0) {
      throw new CustomError('No matching STNs found', 404);
    }

    const stnCSV = await this.exportSTNCSV(stns);

    const mailService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    await mailService.sendEmailWithFileAttachment(
      email,
      'STN Export',
      'Here is your STN export file',
      'text/csv',
      TemplateType.REPORT,
      'csv',
      [{ name: 'STNs', content: stnCSV }],
    );

    return { message: 'Email has been sent' };
  }
  getStatus(stn: STNData, grns: GRN[]) {
    // Filter GRNs that belong to the given STN
    const matchingGrns = grns.filter((grn) => grn.stnId === stn.id);

    // If no GRNs exist for the STN, return PENDING
    if (matchingGrns.length === 0) {
      return 'PENDING';
    }

    // Create a map to track the accumulated received quantity for each SKU
    const receivedQuantityMap: Record<string, number> = {};

    matchingGrns.forEach((grn) => {
      grn.products.forEach((product) => {
        receivedQuantityMap[product.sku] =
          (receivedQuantityMap[product.sku] || 0) + product.receivedQuantity;
      });
    });

    // Check each product in the STN
    let isPartiallyDone = false;

    for (const product of stn.products) {
      const receivedQuantity = receivedQuantityMap[product.sku] || 0;

      if (receivedQuantity < product.quantity) {
        isPartiallyDone = true;
      }

      if (receivedQuantity > product.quantity) {
        throw new Error(
          `Received quantity for SKU ${product.sku} exceeds the STN quantity.`,
        );
      }
    }

    // Return the status based on the conditions
    return isPartiallyDone ? 'PARTIALLY_DONE' : 'COMPLETED';
  }
}
