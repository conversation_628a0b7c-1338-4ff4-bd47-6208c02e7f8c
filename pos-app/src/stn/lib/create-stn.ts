// src/stn/create-stn.ts

import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { CreateSTNInput } from '../dto/create-stn.input';
import { STNData } from '../entity/stn.entity';
import { PutCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import {
  STNProgressStatus,
  STNRequestMode,
  STNStatus,
  STNTransferMode,
} from 'src/common/enum/stn';
import { GenerateCode } from 'src/common/helper/generate-code';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { QueryInventoryTrackingByIds } from 'src/inventory-tracking/lib/list-inventory-tracking-by-ids';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { STNValidator } from './validate-stn';
export class CreateSTN {
  constructor(
    private docClient: AppDocumentClient,
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createSTN(createSTNInput: STNData | CreateSTNInput): Promise<STNData> {
    posLogger.info('STN', 'createSTN', {
      input: createSTNInput,
    });
    try {
      const STN_TABLE_NAME = await this.configParameters.getSTNTableName();
      const { transferMode } = createSTNInput;
      const codeHandler = new GenerateCode(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const easyEcomHandler = new EasyEcomService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      let Item: STNData;

      if (!('id' in createSTNInput)) {
        const {
          storeId,
          status,
          requestedStoreId = null,
          requestMode,
          products,
          attachmentS3Key,
        } = createSTNInput;
        if (requestMode === STNRequestMode.FORWARD) {
          const validateSTNHandler = new STNValidator(
            this.configService,
            this.ssmClient,
            this.configParameters,
            this.docClient,
          );
          await validateSTNHandler.validateSTN(createSTNInput);
        }
        if (!requestedStoreId && transferMode === STNTransferMode.STORE) {
          throw new CustomError(
            'Requested Store ID is required for Store Transfer Mode',
            500,
          );
        }
        if (!attachmentS3Key && requestMode === STNRequestMode.REVERSE) {
          throw new CustomError(
            'Attachment S3 Key is required for Reverse Request Mode',
            500,
          );
        }

        if (requestMode === STNRequestMode.REVERSE) {
          await this.validateReturnQuantities(
            storeId,
            products.map((item) => ({
              id: item.sku,
              quantity: item.quantity,
              title: item.title,
            })),
          );
        }
        console.log();
        const id = await codeHandler.generateCode(
          storeId,
          'STN_COUNT',
          `STN${storeId}-`,
        );

        Item = {
          ...createSTNInput,
          id,
          createdAt: moment().toISOString(),
          updatedAt: moment().toISOString(),
          ...(transferMode === STNTransferMode.STORE &&
            status === STNStatus.CREATED && {
              progressStatus: STNProgressStatus.PENDING,
            }),
          ecomInvoiceId: null,
          ecomOrderId: null,
          ecomSubOrderId: null,
        };

        if (status === STNStatus.CREATED) {
          posLogger.info('STN', 'EasyEcomPayload', Item);

          const {
            posProducts,
            locationKey,
            data: { OrderID, SuborderID, InvoiceID },
          } = await easyEcomHandler.createSTNOrder(
            createSTNInput,
            id,
            createSTNInput?.sourceWarehouseMappingId,
          );

          if (requestMode === STNRequestMode.REVERSE) {
            await new Promise((resolve) => setTimeout(resolve, 5000));
            const data = await easyEcomHandler.getOrderDetails(id, locationKey);

            for (const orderItem of data.order_items) {
              if (orderItem.item_quantity !== orderItem.assigned_quantity) {
                await easyEcomHandler.cancelOrder(id, locationKey);
                throw new CustomError(
                  'Store Inventory is not synced/matching with WMS, please contact HO to resolve this issue.',
                  400,
                );
              }
            }

            await easyEcomHandler.generateB2BInvoiceAPI(InvoiceID, locationKey);
          }

          Item = {
            ...Item,
            products: posProducts,
            ecomInvoiceId: InvoiceID,
            ecomOrderId: OrderID,
            ecomSubOrderId: SuborderID,
          };
        }
      } else {
        Item = createSTNInput;
      }

      posLogger.info('STN', 'createSTN', Item);
      const command = new PutCommand({
        TableName: STN_TABLE_NAME,
        Item,
        ConditionExpression: 'attribute_not_exists(id)',
      });

      await this.docClient.createItem(command);
      return Item;
    } catch (e) {
      posLogger.error('STN', 'createSTN', {
        error: e,
      });
      throw e;
    }
  }

  async updateSTNToCreated(stn: STNData): Promise<STNData> {
    posLogger.info('STN', 'updateSTNToCreated', {
      stnId: stn.id,
      storeId: stn.storeId,
    });

    try {
      const STN_TABLE_NAME = await this.configParameters.getSTNTableName();
      const easyEcomHandler = new EasyEcomService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      // Validate STN ID and storeId
      if (!stn?.id || !stn?.storeId) {
        throw new CustomError('STN ID or Store ID is missing', 400);
      }

      // Validate STN for FORWARD mode
      if (stn.requestMode === STNRequestMode.FORWARD) {
        const validateSTNHandler = new STNValidator(
          this.configService,
          this.ssmClient,
          this.configParameters,
          this.docClient,
        );
        const stnInput: CreateSTNInput = {
          ...stn,
          requestedStoreId: stn.requestedStoreId ?? null,
        };
        await validateSTNHandler.validateSTN(stnInput);
      }

      // Prepare EasyEcom payload
      const stnInput: CreateSTNInput = {
        storeId: stn.storeId,
        requestMode: stn.requestMode,
        transferMode: stn.transferMode,
        products: stn.products.map((product) => ({
          ...product,
          quantity: Number(product.quantity), // Convert quantity to number
        })),
        remarks1: stn.remarks1,
        remarks2: stn.remarks2,
        sourceWarehouseMappingId: stn.sourceWarehouseMappingId,
        requestedStoreId: stn.requestedStoreId ?? null,
        status: STNStatus.DRAFT,
        bulkSTNCode: stn?.bulkSTNCode,
      };

      // Integrate with EasyEcom
      const {
        posProducts,
        data: { OrderID, SuborderID, InvoiceID },
      } = await easyEcomHandler.createSTNOrder(
        stnInput,
        stn.id,
        stn.sourceWarehouseMappingId,
      );

      // Prepare updated fields
      const Item: STNData = {
        ...stn,
        status: STNStatus.CREATED,
        updatedAt: moment().toISOString(),
        products: posProducts,
        ecomInvoiceId: InvoiceID,
        ecomOrderId: OrderID,
        ecomSubOrderId: SuborderID,
        ...(stn.transferMode === STNTransferMode.STORE && {
          progressStatus: STNProgressStatus.PENDING,
        }),
      };

      // Build dynamic UpdateExpression
      let updateExpressionString: string = 'SET ';
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, any> = {};

      Object.keys(Item).forEach((key) => {
        if (key !== 'id') {
          updateExpressionString += `#${key} = :${key}, `;
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = Item[key];
        }
      });

      updateExpressionString = updateExpressionString.slice(0, -2); // Remove trailing comma

      // Update STN in DynamoDB
      const updateCommand = new UpdateCommand({
        TableName: STN_TABLE_NAME,
        Key: {
          id: stn.id,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: STNData } =
        await this.docClient.updateItem(updateCommand);

      posLogger.info('STN', 'updateSTNToCreated', {
        message: 'Successfully updated STN to CREATED',
        stnId: Attributes.id,
        storeId: Attributes.storeId,
        status: Attributes.status,
      });

      return Attributes;
    } catch (error) {
      posLogger.error('STN', 'updateSTNToCreated', {
        error: error.message,
        stnId: stn.id,
        storeId: stn.storeId,
        key: { id: stn?.id },
      });
      throw new CustomError(
        `Failed to update STN ${stn.id}: ${error.message}`,
        500,
      );
    }
  }

  private async validateReturnQuantities(
    storeId: string,
    products: { id: string; quantity: number; title: string }[],
  ): Promise<void> {
    const queryHandler = new QueryInventoryTrackingByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    const ids = products.map((item) => item.id);
    console.log(ids);
    const currentInventory = await queryHandler.queryInventoryTrackingByIds(
      storeId,
      ids,
    );
    for (const storePo of products) {
      const item = currentInventory.data.find((item) => item.id === storePo.id);
      if (!item) {
        throw new CustomError(
          `Inventory for SKU ${storePo.id}-${storePo.title} not found`,
          400,
        );
      }
      const sellableQuantity = item.quantity - (item?.displayItemQuantity || 0);

      if (Number(storePo.quantity) > sellableQuantity) {
        throw new CustomError(
          `The return quantity for SKU ${storePo.id}-${storePo.title} exceeds the available sellable inventory.`,
          400,
        );
      }
    }
    posLogger.info(
      'stn',
      'validateReturnQuantities',
      'store return quantity validated',
    );
  }
}
