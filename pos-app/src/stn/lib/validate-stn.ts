import { Injectable } from '@nestjs/common';
import { STNData, STNs } from '../entity/stn.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { ListSTNInput } from '../dto/list-stns.input';
import { QuerySTNs } from './list-stns';
import { QueryProducts } from 'src/products/lib/list-products';
import { QueryProductsByIds } from 'src/products/lib/list-products-by-ids';
import { CreateSTNInput, STNProductInput } from '../dto/create-stn.input';
import { STNRequestMode } from 'src/common/enum/stn';

@Injectable()
export class STNValidator {
  constructor(
    private readonly configService: ConfigService,
    private readonly ssmClient: AppSsmClient,
    private readonly configParameters: AppConfigParameters,
    private readonly docClient: AppDocumentClient,
  ) {}

  async validateSTN(stn: STNData | Partial<CreateSTNInput>): Promise<boolean> {
    try {
      const { storeId, products } = stn;
      const getHandler = new GetStore(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const store = await getHandler.getStore(storeId);
      const { createdAt: storeCreatedAt } = store;

      const productsDetailsWithType: any =
        await this.fetchProductDetailsBySkus(products);

      const noValidateProductTypes = ['Pillows'];
      const productsToBeValidated = productsDetailsWithType.filter(
        (product) => !noValidateProductTypes.includes(product.productType),
      );

      const isWithin45Days = this.isWithin45Days(storeCreatedAt);

      if (isWithin45Days) {
        const past45DaysSTNs = await this.querySTNsForStoreAndPastDays(
          storeId,
          45,
        );

        this.validateProductQuantitiesByItem(
          productsToBeValidated,
          past45DaysSTNs.data,
        );
      } else {
        await this.validateSTNQuantitiesByCategory(
          storeId,
          productsToBeValidated,
        );
      }

      posLogger.info('stn-validation', 'validateSTN', {
        stnId: stn?.requestMode,
        status: 'valid',
      });

      return true;
    } catch (error) {
      posLogger.error('stn-validation', 'validateSTN', {
        stnId: stn?.storeId,
        error: error.message,
      });
      throw error;
    }
  }

  private isWithin45Days(dateInput: string | Date): boolean {
    try {
      const inputDate = new Date(dateInput);
      const currentDate = new Date();
      const timeDifference = currentDate.getTime() - inputDate.getTime();
      const daysDifference = Math.abs(timeDifference / (1000 * 3600 * 24));
      return daysDifference <= 45;
    } catch (error) {
      posLogger.error('stn-validation', 'isWithin45Days', {
        dateInput,
        error: error.message,
      });
      throw new CustomError('Invalid date comparison', 400);
    }
  }

  async querySTNsForStoreAndPastDays(
    storeId: string,
    numberOfDays: number,
  ): Promise<STNs> {
    try {
      const currentDate = new Date();
      const fromDate = new Date(currentDate);
      fromDate.setDate(currentDate.getDate() - numberOfDays);

      const toDate = currentDate.toISOString();

      const filter: ListSTNInput = {
        fromDate: fromDate.toISOString(),
        toDate: toDate,
        termSearchFields: {
          storeId: storeId,
        },
        textSearchFields: {
          requestMode: STNRequestMode.FORWARD,
        },
      };

      const queryHandler = new QuerySTNs(
        this.configService,
        this.ssmClient,
        this.configParameters,
      );
      return await queryHandler.querySTNs(filter);
    } catch (error) {
      posLogger.error('stn-validation', 'querySTNsForStoreAndPastDays', {
        error: error.message,
      });
      throw error;
    }
  }

  accumulateQuantityBySku(stns: STNData[]): Record<string, number> {
    const skuQuantityMap: Record<string, number> = {};

    stns.forEach((stn) => {
      stn.products.forEach((product) => {
        if (product.sku === 'TSC3061') {
        }
        if (skuQuantityMap[product.sku]) {
          skuQuantityMap[product.sku] += product.quantity;
        } else {
          skuQuantityMap[product.sku] = product.quantity;
        }
      });
    });

    return skuQuantityMap;
  }
  private validateProductQuantitiesByItem(
    products: any,
    pastSTNs: STNData[],
  ): void {
    const pastSkuQuantities = this.accumulateQuantityBySku(pastSTNs);

    for (const product of products) {
      const pastQuantity = pastSkuQuantities[product.sku] || 0;
      const totalQuantity = pastQuantity + product.quantity;

      if (
        product.productType !== 'Custom-Mattresses' &&
        product.productType !== 'Mattresses' &&
        totalQuantity > 15
      ) {
        throw new CustomError(
          `SKU ${product.sku} exceeds allowed limit of 15 in 45 days.`,
          400,
        );
      }
    }
  }
  async fetchProductDetailsBySkus(
    products: STNProductInput[],
  ): Promise<STNProductInput[]> {
    const skus = [...new Set(products.map((product) => product.sku))];

    const queryProductsHandler = new QueryProducts(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );

    const BATCH_SIZE = 25;
    const batchedQueries = [];
    for (let i = 0; i < skus.length; i += BATCH_SIZE) {
      batchedQueries.push(skus.slice(i, i + BATCH_SIZE).join(','));
    }

    let batchDataList: any[] = [];
    for (const skuBatch of batchedQueries) {
      const batchData = await queryProductsHandler.queryProducts(skuBatch);
      batchDataList = batchDataList.concat(batchData.data);
    }

    const skuToProductIdMap: Record<string, string> = {};
    batchDataList.forEach((product) => {
      const productId =
        product.pk === 'VARIANT' ? product.product_id : product.id;
      skuToProductIdMap[product.sku] = productId;
    });

    const productIds = [...new Set(Object.values(skuToProductIdMap))];

    const queryProductsByIdsHandler = new QueryProductsByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );

    const productDetailsList: any[] = [];
    for (let i = 0; i < productIds.length; i += BATCH_SIZE) {
      const batchProductDetails =
        await queryProductsByIdsHandler.queryProductsByIds(
          productIds.slice(i, i + BATCH_SIZE),
        );
      productDetailsList.push(...batchProductDetails);
    }

    const productTypeMap = productDetailsList.reduce(
      (acc, product) => {
        acc[product.id] = product?.product_type || null;
        return acc;
      },
      {} as Record<string, string>,
    );

    return products.map((product) => ({
      ...product,
      productType: productTypeMap[skuToProductIdMap[product.sku]] || null,
    }));
  }
  validateSTNQuantitiesByCategory = async (
    storeId: string,
    currentProducts: { productType: string; quantity: number; sku: string }[],
  ) => {
    const past30DaysSTNs = await this.querySTNsForStoreAndPastDays(storeId, 30);
    const productsOfAllSTNs = past30DaysSTNs.data.flatMap(
      (stn) => stn.products,
    );

    const pastSTNProductsWithTypes: any =
      await this.fetchProductDetailsBySkus(productsOfAllSTNs);

    const filteredPastProducts = pastSTNProductsWithTypes.filter(
      (pastProduct) =>
        currentProducts.some((current) => current.sku === pastProduct.sku),
    );

    const productsToValidate: any = [
      ...filteredPastProducts,
      ...currentProducts,
    ];

    const productQuantities: Record<string, number> = {};
    const validProductTypes = new Set([
      'Mattresses',
      'Custom-Mattresses',
      'Grey-Mattresses',
      'Chairs',
      'Sofas',
      'Beds & Bed Frames',
      'Desk',
    ]);

    for (const product of productsToValidate) {
      if (!validProductTypes.has(product.productType)) continue;

      productQuantities[product.productType] =
        (productQuantities[product.productType] || 0) + product.quantity;
    }

    for (const [productType, totalQuantity] of Object.entries(
      productQuantities,
    )) {
      if (totalQuantity > 15) {
        throw new CustomError(
          `Total quantity for product type '${productType}' exceeds limit of 15.`,
          400,
        );
      }
    }
  };
}
