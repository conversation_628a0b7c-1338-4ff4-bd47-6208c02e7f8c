// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { posLogger } from 'src/common/logger';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import moment from 'moment';
import { AppConfigParameters } from 'src/config/config';
import { STNData } from '../entity/stn.entity';
import { GetSTN } from './get-stn-by-id';
import { STNStatus } from 'src/common/enum/stn';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

export class RemoveSTN {
  constructor(
    private docClient: AppDocumentClient,

    private configParameters: AppConfigParameters,
  ) {}

  async removeSTN(id: string): Promise<STNData> {
    posLogger.info('STN', 'removeSTN', { input: id });
    try {
      const STN_TABLE = await this.configParameters.getSTNTableName();
      const getSTN = new GetSTN(this.docClient, this.configParameters);
      const stn = await getSTN.getSTN(id);
      const { status } = stn;

      if (status !== STNStatus.DRAFT) {
        throw new CustomError(`Only draft STN can be updated`, 404);
      }

      const command = new UpdateCommand({
        TableName: STN_TABLE,
        Key: {
          id,
        },
        UpdateExpression: 'SET #isDeleted = :isDeleted, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
          '#isDeleted': 'isDeleted',
        },
        ExpressionAttributeValues: {
          ':isDeleted': true,
          ':updatedAt': moment().toISOString(),
        },
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes } = await this.docClient.updateItem(command);

      if (Attributes) return Attributes as STNData;
    } catch (e) {
      posLogger.error('STN', 'removeHandler', e);
      throw e;
    }
  }
}
