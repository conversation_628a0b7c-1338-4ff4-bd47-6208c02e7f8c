import { filterFormatter } from 'src/common/helper/filter-helper';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { ListSTNInput } from '../dto/list-stns.input';
import { STNData, STNs } from '../entity/stn.entity';
import { AppConfigParameters } from 'src/config/config';

export class QuerySTNs {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async querySTNs(filter: ListSTNInput): Promise<STNs> {
    posLogger.info('stn', 'querySTNs', {
      input: filter,
    });
    try {
      const STN_TABLE = await this.configParameters.getSTNTableName();
      const { fromDate, toDate } = filter;

      const shouldFilter = [];

      const { searchArray, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );
      if (fromDate && toDate) {
        searchArray.push({
          range: {
            createdAt: {
              gte: fromDate,
              lte: toDate,
            },
          },
        });
      }

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: STN_TABLE,
        });

        size = bodyRes?.count;
      }
      if (searchArray.length) {
        console.log(
          'searchArray',
          JSON.stringify({
            index: STN_TABLE,
            body: {
              query: {
                bool: {
                  should: [...shouldFilter],
                  minimum_should_match: 1,
                  must: [...searchArray],
                },
              },
            },
          }),
        );
        const { body: bodyRes } = await esHandler.count({
          index: STN_TABLE,
          body: {
            query: {
              bool: {
                should: [...shouldFilter],

                must: [...searchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: STN_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],

                should: [...shouldFilter],
              },
            },
            sort: [sortObject],
          },
        });
        const data: STNData[] = response.body.hits.hits.map(
          (hit) => hit._source,
        );

        return { data, count: bodyRes?.count };
      }
      const { body: bodyRes } = await esHandler.count({
        index: STN_TABLE,
      });

      const response = await esHandler.search({
        index: STN_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data: STNData[] = response.body.hits.hits.map((hit) => hit._source);

      return { data, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('stn', 'querySTNs', { error: e });
      throw e;
    }
  }
}
