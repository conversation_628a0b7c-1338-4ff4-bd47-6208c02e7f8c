import { Injectable } from '@nestjs/common';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { UpdateSTNInput } from '../dto/update-stn.input';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { STNData } from '../entity/stn.entity';
import { posLogger } from 'src/common/logger';
import { GetSTN } from './get-stn-by-id';
import {
  STNProgressStatus,
  STNRequestMode,
  STNStatus,
  STNTransferMode,
} from 'src/common/enum/stn';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GetInventoryCredential } from 'src/inventory-credentials/lib/get-inventory-credential';
import { QueryInventoryTrackingByIds } from 'src/inventory-tracking/lib/list-inventory-tracking-by-ids';
import { STNValidator } from './validate-stn';

@Injectable()
export class UpdateSTN {
  constructor(
    private docClient: AppDocumentClient,
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateSTN(
    id: string,
    updateSTNInput: UpdateSTNInput,
  ): Promise<STNData> {
    posLogger.info('STN', 'updateSTN', {
      input: { id, updateSTNInput },
    });

    try {
      const STN_TABLE = await this.configParameters.getSTNTableName();

      posLogger.info('STN', 'updateSTN', { updateSTNInput });

      let updateExpressionString: string = 'SET ';
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, any> = {};

      const getSTN = new GetSTN(this.docClient, this.configParameters);
      const { status: newStatus } = updateSTNInput;
      const stn = await getSTN.getSTN(id);
      const {
        status: oldStatus,
        requestedStoreId = null,
        storeId,
        transferMode,
        progressStatus,
      } = stn;
      const { requestMode } = updateSTNInput;

      if (requestMode === STNRequestMode.FORWARD) {
        const validateSTNHandler = new STNValidator(
          this.configService,
          this.ssmClient,
          this.configParameters,
          this.docClient,
        );
        await validateSTNHandler.validateSTN({ ...updateSTNInput, storeId });
      }

      if (oldStatus !== STNStatus.DRAFT) {
        throw new CustomError(`Only draft STN can be updated`, 404);
      }

      if (
        progressStatus === STNProgressStatus.COMPLETED ||
        progressStatus === STNProgressStatus.REJECTED
      ) {
        throw new CustomError(
          'Cannot update COMPLETED or REJECTED orders',
          400,
        );
      }
      const { products, attachmentS3Key } = updateSTNInput;
      if (requestMode === STNRequestMode.REVERSE) {
        await this.validateReturnQuantities(
          storeId,
          products.map((item) => ({
            id: item.sku,
            quantity: item.quantity,
            title: item.title,
          })),
        );
      }
      if (!attachmentS3Key && requestMode === STNRequestMode.REVERSE) {
        throw new CustomError(
          'Attachment S3 Key is required for Reverse Request Mode',
          500,
        );
      }
      let Item: STNData = {
        ...stn,
        ...updateSTNInput,
        updatedAt: moment().toISOString(),
      };
      if (newStatus === STNStatus.CREATED) {
        const easyEcomHandler = new EasyEcomService(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );

        const {
          posProducts,
          locationKey,
          data: { OrderID, SuborderID, InvoiceID },
        } = await easyEcomHandler.createSTNOrder(
          { ...updateSTNInput, storeId, transferMode, requestedStoreId },
          id,
          stn?.sourceWarehouseMappingId,
        );

        if (requestMode === STNRequestMode.REVERSE) {
          await new Promise((resolve) => setTimeout(resolve, 5000));
          const data = await easyEcomHandler.getOrderDetails(id, locationKey);

          for (const orderItem of data.order_items) {
            if (orderItem.item_quantity !== orderItem.assigned_quantity) {
              await easyEcomHandler.cancelOrder(id, locationKey);
              throw new CustomError(
                'Store Inventory is not synced/matching with WMS, please contact HO to resolve this issue.',
                400,
              );
            }
          }

          await easyEcomHandler.generateB2BInvoiceAPI(InvoiceID, locationKey);
        }

        Item = {
          ...stn,
          ...updateSTNInput,

          ...(transferMode === STNTransferMode.STORE &&
            newStatus === STNStatus.CREATED && {
              progressStatus: STNProgressStatus.PENDING,
            }),
          products: posProducts,
          ecomInvoiceId: InvoiceID,
          ecomOrderId: OrderID,
          ecomSubOrderId: SuborderID,
        };
      }

      Object.keys(Item).forEach((key) => {
        if (key !== 'id' && !expressionAttributeNames[`#${key}`]) {
          updateExpressionString += `#${key} = :${key}, `;
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = Item[key];
        }
      });

      updateExpressionString = updateExpressionString.slice(0, -2);

      const command = new UpdateCommand({
        TableName: STN_TABLE,
        Key: {
          id,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: STNData } =
        await this.docClient.updateItem(command);

      return Attributes;
    } catch (e) {
      posLogger.error('STN', 'updateSTN', {
        error: e,
      });
      throw e;
    }
  }
  async updateSTNProgressStatus(
    id: string,
    {
      progressStatus,
      queueId = null,
    }: {
      progressStatus: STNProgressStatus;
      queueId?: number;
    },
  ): Promise<STNData> {
    posLogger.info('STN', 'updateSTNProgressStatus', {
      input: { progressStatus },
    });

    try {
      const STN_TABLE = await this.configParameters.getSTNTableName();
      if (progressStatus === STNProgressStatus.ACCEPTED && !queueId) {
        throw new CustomError('Queue not found', 404);
      }
      const updatedAt = moment().toISOString();
      posLogger.info('STN', 'updateSTNProgressStatus', { id, progressStatus });

      let updateExpressionString: string = 'SET updatedAt = :updatedAt, ';
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, any> = {
        ':updatedAt': updatedAt,
      };

      const getSTN = new GetSTN(this.docClient, this.configParameters);

      const stn = await getSTN.getSTN(id);
      const { transferMode, requestedStoreId } = stn;
      if (transferMode !== STNTransferMode.STORE) {
        throw new CustomError(
          `Only Store STN progress status can be updated`,
          404,
        );
      }
      const { progressStatus: oldProgressStatus } = stn;

      if (
        oldProgressStatus !== STNProgressStatus.PENDING &&
        oldProgressStatus !== STNProgressStatus.ACCEPTED
      ) {
        throw new CustomError(`This STN can not be updated`, 404);
      }
      if (progressStatus === STNProgressStatus.REJECTED) {
        const easyEcomHandler = new EasyEcomService(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        if (!requestedStoreId) {
          throw new CustomError('Requested store not found', 404);
        }
        const getInventoryCredentialHandler = new GetInventoryCredential(
          this.docClient,
          this.configParameters,
        );
        const { locationKey } =
          await getInventoryCredentialHandler.getInventoryCredential(
            requestedStoreId,
          );
        await easyEcomHandler.cancelOrder(id, locationKey);
      }

      let Item: any = {
        progressStatus,
      };
      if (queueId) {
        Item = { ...Item, queueId };
      }

      Object.keys(Item).forEach((key) => {
        if (key !== 'id') {
          updateExpressionString += `#${key} = :${key}, `;
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = Item[key];
        }
      });

      updateExpressionString = updateExpressionString.slice(0, -2);

      const command = new UpdateCommand({
        TableName: STN_TABLE,
        Key: {
          id,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: STNData } =
        await this.docClient.updateItem(command);

      return Attributes;
    } catch (e) {
      posLogger.error('STN', 'updateSTNProgressStatus', {
        error: e,
      });
      throw e;
    }
  }
  private async validateReturnQuantities(
    storeId: string,
    products: { id: string; quantity: number; title: string }[],
  ): Promise<void> {
    const queryHandler = new QueryInventoryTrackingByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    const ids = products.map((item) => item.id);
    const currentInventory = await queryHandler.queryInventoryTrackingByIds(
      storeId,
      ids,
    );

    for (const storePo of products) {
      const item = currentInventory.data.find((item) => item.id === storePo.id);
      if (!item) {
        throw new CustomError(
          `Inventory for SKU ${storePo.id}-${storePo.title} not found`,
          400,
        );
      }
      const sellableQuantity = item.quantity - (item?.displayItemQuantity || 0);

      if (Number(storePo.quantity) > sellableQuantity) {
        throw new CustomError(
          `The return quantity for SKU ${storePo.id}-${storePo.title} exceeds the available sellable inventory.`,
          400,
        );
      }
    }
    posLogger.info(
      'stn',
      'validateReturnQuantities',
      'store return quantity validated',
    );
  }
}
