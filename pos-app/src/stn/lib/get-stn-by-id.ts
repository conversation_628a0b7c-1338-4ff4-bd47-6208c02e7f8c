import { Injectable } from '@nestjs/common';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { STNData } from '../entity/stn.entity';
import { GetCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

@Injectable()
export class GetSTN {
  constructor(
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getSTN(id: string): Promise<STNData> {
    posLogger.info('STN', 'getSTN', { id });

    try {
      const STN_TABLE = await this.configParameters.getSTNTableName();
      const command = new GetCommand({
        TableName: STN_TABLE,
        Key: {
          id,
        },
      });

      const { Item }: { Item: STNData } = await this.docClient.getItem(command);
      if (!Item) {
        throw new CustomError(`STN ${id} not found`, 404);
      }

      return Item;
    } catch (e) {
      posLogger.error('STN', 'getSTN', { error: e });
      throw e;
    }
  }

  async getSTNByBulkSTNCode(bulkSTNCode: string): Promise<STNData[]> {
    posLogger.info('STN', 'getSTNByBulkSTNCode', { bulkSTNCode });
    console.log('getSTNByBulkSTNCodeInput', { bulkSTNCode });

    try {
      const STN_TABLE = await this.configParameters.getSTNTableName();

      const queryParams = {
        TableName: STN_TABLE,
        IndexName: 'bulkSTNCode-index',
        KeyConditionExpression: 'bulkSTNCode = :bulkSTNCode',
        ExpressionAttributeValues: {
          ':bulkSTNCode': bulkSTNCode,
        },
      };

      const queryCommand = new QueryCommand(queryParams);
      const queryResult = await this.docClient.queryItems(queryCommand);

      if (!queryResult.Items || queryResult.Items.length === 0) {
        throw new CustomError(
          `No STNs found for bulkSTNCode: ${bulkSTNCode}`,
          404,
        );
      }

      posLogger.info('STN', 'getSTNByBulkSTNCode', {
        message: `Found ${queryResult.Items.length} STNs for bulkSTNCode: ${bulkSTNCode}`,
      });

      return queryResult.Items as STNData[];
    } catch (e) {
      posLogger.error('STN', 'getSTNByBulkSTNCode', { error: e, bulkSTNCode });
      throw e;
    }
  }
}
