import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { CreateSTN } from './lib/create-stn';
import { CreateSTNInput } from './dto/create-stn.input';
import { UpdateSTNInput } from './dto/update-stn.input';
import { UpdateSTN } from './lib/update-stn';
import { RemoveSTN } from './lib/remove-stn';
import { GetSTN } from './lib/get-stn-by-id';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ListSTNInput } from './dto/list-stns.input';
import { QuerySTNs } from './lib/list-stns';
import { STNProgressStatus } from 'src/common/enum/stn';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { ExportSTNs } from './lib/export-stns';

@Injectable()
export class STNService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
  ) {}

  async create(createSTNInput: CreateSTNInput) {
    const createHandler = new CreateSTN(
      this.docClient,
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return createHandler.createSTN(createSTNInput);
  }
  async update(id: string, updateSTNInput: UpdateSTNInput) {
    const updateHandler = new UpdateSTN(
      this.docClient,
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return updateHandler.updateSTN(id, updateSTNInput);
  }
  async updateSTNProgressStatus(
    id: string,
    {
      progressStatus,
      queueId,
    }: { progressStatus: STNProgressStatus; queueId?: number },
  ) {
    const updateHandler = new UpdateSTN(
      this.docClient,
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return updateHandler.updateSTNProgressStatus(id, {
      progressStatus,
      queueId,
    });
  }

  async remove(id: string) {
    const removeHandler = new RemoveSTN(this.docClient, this.configParameters);
    return removeHandler.removeSTN(id);
  }
  async findOne(id: string) {
    const getHandler = new GetSTN(this.docClient, this.configParameters);
    return getHandler.getSTN(id);
  }
  async findAll(listSTNInput: ListSTNInput) {
    const querySTNsHandler = new QuerySTNs(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return querySTNsHandler.querySTNs(listSTNInput);
  }

  async exportSTNs(email: string, listSTNInput: ListSTNInput) {
    const exportHandler = new ExportSTNs(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    exportHandler.exportSTNs(email, listSTNInput).catch((error) => {
      console.error('STNs', 'exportSTNs', error);
    });
    return { message: 'File will be sent on mail' };
  }

  // Implement additional methods as needed for querying, updating, and deleting STNs
  // Example:
  // async findAll() {
  //   // Implement find all logic
  // }

  // async findOne(key: string) {
  //   // Implement find one logic
  // }

  // async update(key: string, updateSTNInput: UpdateSTNInput) {
  //   // Implement update logic
  // }

  // async remove(key: string, updatedBy: string) {
  //   // Implement remove logic
  // }
}
