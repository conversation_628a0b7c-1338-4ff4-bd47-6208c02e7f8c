import { InputType, Field } from '@nestjs/graphql';
import { STNRequestMode, STNStatus } from 'src/common/enum/stn';
import { STNProductInput } from './create-stn.input';

@InputType()
export class UpdateSTNInput {
  @Field(() => String, {
    description: 'Type of the order',
  })
  id: string;

  @Field(() => String, {
    nullable: true,
    description: 'Additional remarks for the order',
  })
  remarks1?: string;

  @Field(() => String, {
    description: 'attachement',
    nullable: true,
  })
  attachmentS3Key?: string;

  @Field(() => [STNProductInput], {
    description: 'List of products included in the order',
  })
  products: STNProductInput[];

  @Field(() => STNRequestMode, {
    description: 'Forward or Reverse',
    nullable: false,
  })
  requestMode: STNRequestMode;

  @Field(() => STNStatus, {
    description: 'Unique identifier for the customer',
  })
  status: STNStatus;

  @Field(() => String, {
    description: 'Bulk STN Code',
    nullable: true,
  })
  bulkSTNCode: string;
}
