import { InputType, Field, registerEnumType } from '@nestjs/graphql';
import {
  STNProgressStatus,
  STNRequestMode,
  STNStatus,
  STNTransferMode,
} from 'src/common/enum/stn';

registerEnumType(STNStatus, { name: 'STNStatus' });
registerEnumType(STNRequestMode, { name: 'STNRequestMode' });
registerEnumType(STNTransferMode, { name: 'STNTransferMode' });
registerEnumType(STNProgressStatus, { name: 'STNProgressStatus' });

@InputType()
export class STNProductInput {
  @Field(() => String, { description: 'SKU of the product' })
  sku: string;

  @Field(() => String, { description: 'title of the product' })
  title: string;

  @Field(() => Number, { description: 'Quantity of the product ordered' })
  quantity: number;
}

@InputType()
export class CreateSTNInput {
  @Field(() => String, {
    nullable: true,
    description: 'Additional remarks for the order',
  })
  remarks1?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Additional remarks for the order',
  })
  remarks2?: string;

  @Field(() => [STNProductInput], {
    description: 'List of products included in the order',
  })
  products: STNProductInput[];

  @Field(() => String, {
    description: 'attachement',
    nullable: true,
  })
  attachmentS3Key?: string;

  @Field(() => String, {
    description: 'Unique identifier for the customer',
    nullable: false,
  })
  storeId: string;

  @Field(() => String, {
    description: 'Requested store ID',
    nullable: true,
  })
  requestedStoreId: string;

  @Field(() => String, {
    nullable: true,
    description: 'Order number',
  })
  sourceWarehouseMappingId?: string;

  @Field(() => STNRequestMode, {
    description: 'Forward or Reverse',
    nullable: false,
  })
  requestMode: STNRequestMode;

  @Field(() => STNTransferMode, {
    description: 'Forward or Reverse',
    nullable: false,
  })
  transferMode: STNTransferMode;

  @Field(() => STNStatus, {
    description: 'Unique identifier for the customer',
  })
  status: STNStatus;

  @Field(() => String, {
    description: 'Bulk STN Code',
    nullable: true,
  })
  bulkSTNCode: string;
}
