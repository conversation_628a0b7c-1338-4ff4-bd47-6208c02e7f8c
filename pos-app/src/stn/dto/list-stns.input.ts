import { InputType, Field } from '@nestjs/graphql';
import { STNRequestMode, STNStatus } from 'src/common/enum/stn';

@InputType()
export class STNTermSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  storeId?: string;

  @Field(() => [String], {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  storeIds?: string[];

  @Field(() => String, {
    nullable: true,
    description: 'requested store id',
  })
  requestedStoreId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class STNTextSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Unique identifier for the STN',
  })
  id?: string;

  @Field(() => STNStatus, {
    nullable: true,
    description: 'Status of the STN',
  })
  status?: STNStatus;

  @Field(() => STNRequestMode, {
    nullable: true,
    description: 'Status of the STN',
  })
  requestMode?: STNRequestMode;
}

@InputType()
export class STNSortingFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class ListSTNInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size?: string;

  @Field(() => STNTermSearchFieldsInput, {
    nullable: true,
    description: 'Search term fields to filter the records',
  })
  termSearchFields?: STNTermSearchFieldsInput;

  @Field(() => STNTextSearchFieldsInput, {
    nullable: true,
    description: 'Search text fields to filter the records',
  })
  textSearchFields?: STNTextSearchFieldsInput;

  @Field(() => STNSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { createdAt: 'desc' },
  })
  sortBy?: STNSortingFieldsInput;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  fromDate?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  toDate?: string;
}
