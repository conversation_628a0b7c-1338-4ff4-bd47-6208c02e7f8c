import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { STNService } from './stn.service';
import { STN, STNs, ExportResponse } from './entity/stn.entity';
import { CreateSTNInput } from './dto/create-stn.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus } from '@nestjs/common';
import { UpdateSTNInput } from './dto/update-stn.input';
import { ListSTNInput } from './dto/list-stns.input';
import { STNProgressStatus } from 'src/common/enum/stn';

@Resolver(() => STN)
// @UseGuards(CustomAuthGuard, StaffGuard)
export class STNResolver {
  constructor(
    private readonly stnService: STNService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: <PERSON>rror<PERSON>and<PERSON>,
  ) {}

  @Mutation(() => STN)
  async createSTN(@Args('createSTNInput') createSTNInput: CreateSTNInput) {
    try {
      const data = await this.stnService.create(createSTNInput);

      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create stock transfer note',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => STN)
  async updateSTN(@Args('updateSTNInput') updateSTNInput: UpdateSTNInput) {
    try {
      const data = await this.stnService.update(
        updateSTNInput.id,
        updateSTNInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update STN',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Mutation(() => STN)
  async updateSTNProgressStatus(
    @Args('id') id: string,
    @Args('progressStatus') progressStatus: STNProgressStatus,
  ) {
    try {
      const data = await this.stnService.updateSTNProgressStatus(id, {
        progressStatus,
      });
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update STN',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => STN)
  async removeSTN(@Args('id') id: string) {
    try {
      const data = await this.stnService.remove(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to delete STN',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => STN)
  async getSTN(@Args('id') id: string) {
    try {
      const data = await this.stnService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to retrieve STN',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Query(() => STNs, { name: 'listSTNs' })
  async findAll(
    @Args('listSTNInput', { nullable: true }) listSTNInput: ListSTNInput,
  ) {
    try {
      const { data, count } = await this.stnService.findAll(listSTNInput);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list STNs',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Query(() => ExportResponse, { name: 'exportSTNs' })
  async exportSTNs(
    @Args('email', { type: () => String }) email: string,
    @Args('listSTNInput', { nullable: true }) listSTNInput: ListSTNInput,
  ) {
    try {
      const { message } = await this.stnService.exportSTNs(email, listSTNInput);
      return {
        message,
        success: true,
        status: 200,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to export STNs',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
