export const searchingFilter = new Map<
  string,
  'string' | 'nonString' | 'arrayString' | 'boolean' | 'date'
>([
  ['status', 'arrayString'],
  ['requestMode', 'arrayString'],
  ['storeId', 'string'],
  ['requestedStoreId', 'string'],
  ['id', 'string'],

  ['createdAt', 'date'],
  ['updatedAt', 'date'],
]);

export const sortingFilter = new Map<string, 'string' | 'nonString'>([
  ['createdAt', 'string'],
  ['updatedAt', 'string'],
]);

export const sortingFilterType = {
  createdAt: 'date',
  updatedAt: 'date',
};
