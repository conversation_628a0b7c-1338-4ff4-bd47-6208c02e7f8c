import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { GetDeliveryDateService } from './get-delivery-date.service';
import { SAPCustomAuthGuard } from 'src/auth/sap.guard';

@Controller('getDeliveryDate')
export class GetDeliveryDateController {
  constructor(
    private readonly getDeliveryDateService: GetDeliveryDateService,
  ) {}

  @Get(':id')
  @UseGuards(SAPCustomAuthGuard)
  findOne(@Param('id') id: string) {
    return this.getDeliveryDateService.findOne(id);
  }
}
