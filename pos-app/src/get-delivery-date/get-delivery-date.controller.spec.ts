import { Test, TestingModule } from '@nestjs/testing';
import { GetDeliveryDateController } from './get-delivery-date.controller';
import { GetDeliveryDateService } from './get-delivery-date.service';

describe('GetDeliveryDateController', () => {
  let controller: GetDeliveryDateController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GetDeliveryDateController],
      providers: [GetDeliveryDateService],
    }).compile();

    controller = module.get<GetDeliveryDateController>(
      GetDeliveryDateController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
