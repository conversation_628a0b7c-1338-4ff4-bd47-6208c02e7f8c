import { Module } from '@nestjs/common';
import { GetDeliveryDateService } from './get-delivery-date.service';
import { GetDeliveryDateController } from './get-delivery-date.controller';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';

@Module({
  imports: [SsmClientModule, ConfigParametersModule],
  controllers: [GetDeliveryDateController],
  providers: [GetDeliveryDateService],
})
export class GetDeliveryDateModule {}
