import moment from 'moment-timezone';
import { Injectable } from '@nestjs/common';
import { QueryOrders } from 'src/orders/lib/list-orders';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { ListOrderInput } from 'src/orders/dto/list-order.input';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { OrderFilterBy } from 'src/common/enum/order';

@Injectable()
export class GetDeliveryDateService {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async findOne(id: string) {
    const getHandler = new QueryOrders(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    const listOrderInput: ListOrderInput = {
      from: '0',
      size: '15',
      textSearchFields: { shopifyOrderName: id },
      filterBy: OrderFilterBy.SHOPIFY,
    };
    const data = await getHandler.queryOrders(listOrderInput);

    if (data.data.length > 0) {
      const order = data.data[0];
      let { deliveryDate, minDeliveryDate } = order;
      const { deliveryStatus, holdOrder } = order;
      console.log(
        'deliveryDate, minDeliveryDate :>> ',
        deliveryDate,

        moment.utc(deliveryDate).tz('Asia/Kolkata').format('DD-MM-YYYY'),
        moment(deliveryDate).format('DD-MM-YYYY'),
      );

      deliveryDate =
        deliveryDate && deliveryDate !== ''
          ? moment.utc(deliveryDate).tz('Asia/Kolkata').format('DD-MM-YYYY')
          : null;

      minDeliveryDate =
        minDeliveryDate && minDeliveryDate !== ''
          ? moment.utc(minDeliveryDate).tz('Asia/Kolkata').format('DD-MM-YYYY')
          : null;

      const dates = {
        maxDeliveryDate: deliveryDate,
        minDeliveryDate: minDeliveryDate,
        holdOrder: !!holdOrder,
        deliveryType: deliveryStatus,
      };

      return dates;
    } else {
      throw new CustomError(
        'No delivery date found for the given shopify order ID',
        404,
      );
    }
  }
}
