import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GqlExecutionContext } from '@nestjs/graphql';
import * as jwt from 'jsonwebtoken';
import jwkToPem, { JWK } from 'jwk-to-pem';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';

@Injectable()
export class CustomAuthGuard implements CanActivate {
  private apiPublicKeys: JWK[];
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async init() {
    if (!this.apiPublicKeys || !this.apiPublicKeys.length) {
      const COGNITO_USER_POOL_ID = await this.configParameters.getUserPoolId();
      const COGNITO_REGION = this.configService.get('REGION');

      posLogger.info(
        'COGNITO_USER_POOL_ID :>> ',
        COGNITO_USER_POOL_ID,
        COGNITO_REGION,
      );

      this.apiPublicKeys = await this.getPublicKey(
        `${COGNITO_REGION}`,
        `${COGNITO_USER_POOL_ID}`,
      );
    }
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req;

    try {
      const [authType, token] = request.headers.authorization?.split(' ');
      if (authType.toLowerCase() !== 'bearer') {
        return false;
      }
      if (!token) {
        posLogger.error('auth', 'canActivate', 'Token Invalid token type');
        return false;
      }

      const decodedToken = jwt.decode(token, { complete: true });
      const { payload }: jwt.JwtPayload = decodedToken;
      const role = payload['custom:roles'];
      request.user = { role };

      if (payload.exp < Math.floor(new Date().getMilliseconds() / 1000)) {
        posLogger.error(
          'auth',
          'canActivate',
          'payload.exp Invalid token type',
        );
        return false;
      }
      const kid = decodedToken.header.kid;

      await this.init();

      const publicKey = this.apiPublicKeys.find((k: any) => k.kid === kid);
      if (!publicKey) {
        posLogger.error('auth', 'canActivate', 'publicKey Invalid token type');
        return false;
      }

      const pem = jwkToPem(publicKey);
      jwt.verify(token, pem, { algorithms: ['RS256'] });

      return true;
    } catch (error) {
      posLogger.error(
        'auth',
        'canActivate',
        `error Invalid token type ${error}`,
      );
      return false;
    }
  }

  async getPublicKey(region: string, userPoolId: string): Promise<JWK[]> {
    const url = `https://cognito-idp.${region}.amazonaws.com/${userPoolId}/.well-known/jwks.json`;
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Failed to fetch public keys');
    }
    const { keys } = await response.json();
    return keys;
  }
}
