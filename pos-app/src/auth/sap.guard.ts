import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { posLogger } from 'src/common/logger';

@Injectable()
export class SAPCustomAuthGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req;

    try {
      const authHeader = request.headers.authorization;
      if (!authHeader) {
        posLogger.error('auth', 'canActivate', 'Token Invalid token type');
        return false;
      }
      const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) // Remove "Bearer " (7 characters)
      : authHeader;
      posLogger.info('auth', 'canActivate', 'Token Valid token type');
      console.log("token",token)
      return token === '57631294-erwl-7437-wtxz-0a5c7500e0df';
    } catch {}
  }
}
