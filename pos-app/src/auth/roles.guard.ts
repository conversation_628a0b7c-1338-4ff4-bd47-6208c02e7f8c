import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { RolesHierarchy } from 'src/common/enum/roles';
import { posLogger } from 'src/common/logger';

function hasRoleOrHigher(userRole: string, requiredRole: string): boolean {
  posLogger.info('auth', 'roles', { input: { userRole, requiredRole } });
  const userRoleIndex = RolesHierarchy[userRole];
  const requiredRoleIndex = RolesHierarchy[requiredRole];
  return userRoleIndex >= requiredRoleIndex;
}

@Injectable()
export class ManagerGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context).getContext();
    const user = ctx.req.user;
    posLogger.info('Roles', 'ManagerGuard', `${user.role}`);
    return hasRoleOrHigher(user.role, 'MANAGER');
  }
}

@Injectable()
export class AdminGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context).getContext();
    const user = ctx.req.user;
    posLogger.info('Roles', 'AdminGuard', `${user.role}`);
    return hasRoleOrHigher(user.role, 'ADMIN');
  }
}

@Injectable()
export class StaffGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context).getContext();
    const user = ctx.req.user;
    posLogger.info('Roles', 'StaffGuard', `${user.role}`);
    return hasRoleOrHigher(user.role, 'STAFF');
  }
}

@Injectable()
export class StoreGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context).getContext();
    const user = ctx.req.user;
    posLogger.info('Roles', 'StoreGuard', `${user.role}`);
    return hasRoleOrHigher(user.role, 'STORE_LOGIN');
  }
}

@Injectable()
export class CRMGuard implements CanActivate {
  async canActivate(c: ExecutionContext): Promise<boolean> {
    const context = GqlExecutionContext.create(c);
    const ctx = context.getContext();
    const info = context.getInfo();
    const user = ctx.req.user;

    const isOrderOperation =
      info.fieldName === 'listOrders' ||
      info.fieldName === 'getOrder' ||
      info.fieldName === 'getEmployee' ||
      info.fieldName === 'exportOrders' ||
      info.fieldName === 'getGlobalConfiguration' ||
      info.fieldName === 'listProducts' ||
      info.fieldName === 'getProductById' ||
      info.fieldName === 'listVariants' ||
      info.fieldName === 'createPayment' ||
      info.fieldName === 'getPaymentsByOrderID' ||
      info.fieldName === 'verifyPaymentTransaction' ||
      info.fieldName === 'resendPaymentTransaction' ||
      info.fieldName === 'cancelPaymentTransaction' ||
      info.fieldName === 'listAccessories' ||
      info.fieldName === 'listProductsByIds';
    posLogger.info('Roles', 'CRMGuard', `${user.role}`);
    return user.role === 'CRM' ? isOrderOperation : true;
  }
}
