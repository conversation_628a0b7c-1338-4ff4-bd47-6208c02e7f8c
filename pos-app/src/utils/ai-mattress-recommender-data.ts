export const questionAnswersMappings = {
  'Who will be sleeping on <PERSON><PERSON>?': {
    Me: 'a',
    '<PERSON> & My Partner': 'b',
    'My Parents': 'c',
    Guest: 'd',
    'My Children': 'e',
    key: 'q1',
  },
  'What’s disrupting your sleep?': {
    'Back Pain': 'a',
    'Feeling Hot': 'b',
    'Irregular Mattress Shape': 'c',
    'Daily Life Stress': 'd',
    'Tossing & Turning': 'e',
    'None of the Above': 'f',
    key: 'q3',
  },
  'What Kind of mattress experience are you seeking?': {
    'Back Support Expertise': 'a',
    'Soft Cloud-Like Comfort': 'b',
    'Hotel-Grade Comfort': 'c',
    'Pure Luxury': 'd',
    key: 'q4',
  },
  'What mattress firmness level do you prefer the most?': {
    Firm: 'a',
    'Medium Firm': 'b',
    'Medium Soft': 'c',
    'Soft & Luxurious': 'd',
    key: 'q5',
  },
};

export const questionAnswersPdfMappings = {
  aaaa: 'OrthoPro_OrthoRoyale_Ortho',
  aaab: 'OrthoPro_OrthoRoyale_Ortho',
  aaac: 'OrthoPro_OrthoRoyale_Luxe',
  aaad: 'OrthoPro_OrthoRoyale_Ortho',
  aaba: 'OrthoPro_OrthoRoyale_Ortho',
  aabb: 'OrthoPro_OrthoRoyale_Ortho',
  aabc: 'OrthoPro_OrthoRoyale_Ortho',
  aabd: 'OrthoPro_OrthoRoyale_Ortho',
  aaca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  aacb: 'Ortho_OrthoRoyale_OrthoHybrid',
  aacc: 'Ortho_OrthoRoyale_OrthoHybrid',
  aacd: 'Ortho_OrthoRoyale_OrthoHybrid',
  aada: 'OrthoPro_OrthoRoyale_Ortho',
  aadb: 'OrthoPro_OrthoRoyale_Ortho',
  aadc: 'OrthoPro_OrthoRoyale_Ortho',
  aadd: 'OrthoPro_OrthoRoyale_Ortho',
  abaa: 'OrthoPro_OrthoRoyale_Ortho',
  abab: 'OrthoPro_OrthoRoyale_Ortho',
  abac: 'OrthoPro_OrthoRoyale_Ortho',
  abad: 'OrthoPro_OrthoRoyale_Ortho',
  abba: 'OrthoPro_OrthoRoyale_Ortho',
  abbb: 'OrthoPro_OrthoRoyale_Ortho',
  abbc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  abbd: 'LuxeSnowTec_LuxeRoyale_Ortho',
  abca: 'OrthoPro_OrthoRoyale_Ortho',
  abcb: 'OrthoPro_OrthoRoyale_Ortho',
  abcc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  abcd: 'OrthoHybrid_LuxeRoyale_LuxeSnowTec',
  abda: 'OrthoPro_OrthoRoyale_Ortho',
  abdb: 'OrthoPro_OrthoRoyale_Ortho',
  abdc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  abdd: 'LuxeSnowTec_LuxeRoyale_Ortho',
  acaa: 'OrthoPro_OrthoRoyale_Ortho',
  acab: 'OrthoPro_OrthoRoyale_Ortho',
  acac: 'Ortho_OrthoRoyale_Luxe',
  acad: 'Ortho_OrthoRoyale_Luxe',
  acba: 'OrthoPro_OrthoRoyale_Ortho',
  acbb: 'Luxe_OrthoRoyale_OrthoHybrid',
  acbc: 'Luxe_OrthoRoyale_OrthoHybrid',
  acbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  acca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  accb: 'Ortho_OrthoRoyale_OrthoHybrid',
  accc: 'Luxe_LuxeRoyale_OrthoHybrid',
  accd: 'Luxe_LuxeRoyale_OrthoHybrid',
  acda: 'OrthoPro_OrthoRoyale_Ortho',
  acdb: 'OrthoPro_OrthoRoyale_Ortho',
  acdc: 'Luxe_LuxeRoyale_OrthoHybrid',
  acdd: 'Luxe_LuxeRoyale_OrthoHybrid',
  adaa: 'OrthoPro_OrthoRoyale_Ortho',
  adab: 'OrthoPro_OrthoRoyale_Ortho',
  adac: 'Ortho_OrthoRoyale_Luxe',
  adad: 'Ortho_OrthoRoyale_Luxe',
  adba: 'OrthoPro_OrthoRoyale_Ortho',
  adbb: 'Luxe_OrthoRoyale_OrthoHybrid',
  adbc: 'Luxe_OrthoRoyale_OrthoHybrid',
  adbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  adca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  adcb: 'Ortho_OrthoRoyale_OrthoHybrid',
  adcc: 'Luxe_LuxeRoyale_OrthoHybrid',
  adcd: 'Luxe_LuxeRoyale_OrthoHybrid',
  adda: 'OrthoPro_OrthoRoyale_Ortho',
  addb: 'OrthoPro_OrthoRoyale_Ortho',
  addc: 'Luxe_LuxeRoyale_OrthoHybrid',
  addd: 'Luxe_LuxeRoyale_OrthoHybrid',
  aeaa: 'OrthoPro_OrthoRoyale_Ortho',
  aeab: 'OrthoPro_OrthoRoyale_Ortho',
  aeac: 'Ortho_OrthoRoyale_Luxe',
  aead: 'Ortho_OrthoRoyale_Luxe',
  aeba: 'OrthoPro_OrthoRoyale_Ortho',
  aebb: 'Luxe_OrthoRoyale_OrthoHybrid',
  aebc: 'Luxe_OrthoRoyale_OrthoHybrid',
  aebd: 'Luxe_LuxeRoyale_OrthoHybrid',
  aeca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  aecb: 'Ortho_OrthoRoyale_OrthoHybrid',
  aecc: 'Luxe_LuxeRoyale_OrthoHybrid',
  aecd: 'Luxe_LuxeRoyale_OrthoHybrid',
  aeda: 'OrthoPro_OrthoRoyale_Ortho',
  aedb: 'OrthoPro_OrthoRoyale_Ortho',
  aedc: 'Luxe_LuxeRoyale_OrthoHybrid',
  aedd: 'Luxe_LuxeRoyale_OrthoHybrid',
  afaa: 'OrthoPro_OrthoRoyale_Ortho',
  afab: 'OrthoPro_OrthoRoyale_Ortho',
  afac: 'Ortho_OrthoRoyale_Luxe',
  afad: 'Ortho_OrthoRoyale_Luxe',
  afba: 'OrthoPro_OrthoRoyale_Ortho',
  afbb: 'Luxe_OrthoRoyale_OrthoHybrid',
  afbc: 'Luxe_OrthoRoyale_OrthoHybrid',
  afbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  afca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  afcb: 'Ortho_OrthoRoyale_OrthoHybrid',
  afcc: 'Luxe_LuxeRoyale_OrthoHybrid',
  afcd: 'Luxe_LuxeRoyale_OrthoHybrid',
  afda: 'OrthoPro_OrthoRoyale_Ortho',
  afdb: 'OrthoPro_OrthoRoyale_Ortho',
  afdc: 'Luxe_LuxeRoyale_OrthoHybrid',
  afdd: 'Luxe_LuxeRoyale_OrthoHybrid',
  baaa: 'OrthoPro_OrthoRoyale_Ortho',
  baab: 'OrthoPro_OrthoRoyale_Ortho',
  baac: 'OrthoPro_OrthoRoyale_Luxe',
  baad: 'OrthoPro_OrthoRoyale_Ortho',
  baba: 'OrthoPro_OrthoRoyale_Ortho',
  babb: 'OrthoPro_OrthoRoyale_Ortho',
  babc: 'OrthoPro_OrthoRoyale_Ortho',
  babd: 'OrthoPro_OrthoRoyale_Ortho',
  baca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  bacb: 'Ortho_OrthoRoyale_OrthoHybrid',
  bacc: 'Ortho_OrthoRoyale_OrthoHybrid',
  bacd: 'Ortho_OrthoRoyale_OrthoHybrid',
  bada: 'OrthoPro_OrthoRoyale_Ortho',
  badb: 'OrthoPro_OrthoRoyale_Ortho',
  badc: 'OrthoPro_OrthoRoyale_Ortho',
  badd: 'OrthoPro_OrthoRoyale_Ortho',
  bbaa: 'OrthoPro_OrthoRoyale_Ortho',
  bbab: 'OrthoPro_OrthoRoyale_Ortho',
  bbac: 'OrthoPro_OrthoRoyale_Ortho',
  bbad: 'OrthoPro_OrthoRoyale_Ortho',
  bbba: 'OrthoPro_OrthoRoyale_Ortho',
  bbbb: 'OrthoPro_OrthoRoyale_Ortho',
  bbbc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  bbbd: 'LuxeSnowTec_LuxeRoyale_Ortho',
  bbca: 'OrthoPro_OrthoRoyale_Ortho',
  bbcb: 'OrthoPro_OrthoRoyale_Ortho',
  bbcc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  bbcd: 'OrthoHybrid_LuxeRoyale_LuxeSnowTec',
  bbda: 'OrthoPro_OrthoRoyale_Ortho',
  bbdb: 'OrthoPro_OrthoRoyale_Ortho',
  bbdc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  bbdd: 'LuxeSnowTec_LuxeRoyale_Ortho',
  bcaa: 'OrthoPro_OrthoRoyale_Ortho',
  bcab: 'OrthoPro_OrthoRoyale_Ortho',
  bcac: 'Ortho_OrthoRoyale_Luxe',
  bcad: 'Ortho_OrthoRoyale_Luxe',
  bcba: 'OrthoPro_OrthoRoyale_Ortho',
  bcbb: 'Luxe_OrthoRoyale_OrthoHybrid',
  bcbc: 'Luxe_OrthoRoyale_OrthoHybrid',
  bcbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  bcca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  bccb: 'Ortho_OrthoRoyale_OrthoHybrid',
  bccc: 'Luxe_LuxeRoyale_OrthoHybrid',
  bccd: 'Luxe_LuxeRoyale_OrthoHybrid',
  bcda: 'OrthoPro_OrthoRoyale_Ortho',
  bcdb: 'OrthoPro_OrthoRoyale_Ortho',
  bcdc: 'Luxe_LuxeRoyale_OrthoHybrid',
  bcdd: 'Luxe_LuxeRoyale_OrthoHybrid',
  bdaa: 'OrthoPro_OrthoRoyale_Ortho',
  bdab: 'OrthoPro_OrthoRoyale_Ortho',
  bdac: 'Ortho_OrthoRoyale_Luxe',
  bdad: 'Ortho_OrthoRoyale_Luxe',
  bdba: 'OrthoPro_OrthoRoyale_Ortho',
  bdbb: 'Luxe_OrthoRoyale_OrthoHybrid',
  bdbc: 'Luxe_OrthoRoyale_OrthoHybrid',
  bdbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  bdca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  bdcb: 'Ortho_OrthoRoyale_OrthoHybrid',
  bdcc: 'Luxe_LuxeRoyale_OrthoHybrid',
  bdcd: 'Luxe_LuxeRoyale_OrthoHybrid',
  bdda: 'OrthoPro_OrthoRoyale_Ortho',
  bddb: 'OrthoPro_OrthoRoyale_Ortho',
  bddc: 'Luxe_LuxeRoyale_OrthoHybrid',
  bddd: 'Luxe_LuxeRoyale_OrthoHybrid',
  beaa: 'OrthoPro_OrthoRoyale_Ortho',
  beab: 'OrthoPro_OrthoRoyale_Ortho',
  beac: 'Ortho_OrthoRoyale_Luxe',
  bead: 'Ortho_OrthoRoyale_Luxe',
  beba: 'OrthoPro_OrthoRoyale_Ortho',
  bebb: 'Luxe_OrthoRoyale_OrthoHybrid',
  bebc: 'Luxe_OrthoRoyale_OrthoHybrid',
  bebd: 'Luxe_LuxeRoyale_OrthoHybrid',
  beca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  becb: 'Ortho_OrthoRoyale_OrthoHybrid',
  becc: 'Luxe_LuxeRoyale_OrthoHybrid',
  becd: 'Luxe_LuxeRoyale_OrthoHybrid',
  beda: 'OrthoPro_OrthoRoyale_Ortho',
  bedb: 'OrthoPro_OrthoRoyale_Ortho',
  bedc: 'Luxe_LuxeRoyale_OrthoHybrid',
  bedd: 'Luxe_LuxeRoyale_OrthoHybrid',
  bfaa: 'OrthoPro_OrthoRoyale_Ortho',
  bfab: 'OrthoPro_OrthoRoyale_Ortho',
  bfac: 'Ortho_OrthoRoyale_Luxe',
  bfad: 'Ortho_OrthoRoyale_Luxe',
  bfba: 'OrthoPro_OrthoRoyale_Ortho',
  bfbb: 'Luxe_OrthoRoyale_OrthoHybrid',
  bfbc: 'Luxe_OrthoRoyale_OrthoHybrid',
  bfbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  bfca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  bfcb: 'Ortho_OrthoRoyale_OrthoHybrid',
  bfcc: 'Luxe_LuxeRoyale_OrthoHybrid',
  bfcd: 'Luxe_LuxeRoyale_OrthoHybrid',
  bfda: 'OrthoPro_OrthoRoyale_Ortho',
  bfdb: 'OrthoPro_OrthoRoyale_Ortho',
  bfdc: 'Luxe_LuxeRoyale_OrthoHybrid',
  bfdd: 'Luxe_LuxeRoyale_OrthoHybrid',
  caaa: 'OrthoPro_OrthoRoyale_Ortho',
  caab: 'OrthoPro_OrthoRoyale_Ortho',
  caac: 'OrthoPro_OrthoRoyale_Luxe',
  caad: 'OrthoPro_OrthoRoyale_Ortho',
  caba: 'OrthoPro_OrthoRoyale_Ortho',
  cabb: 'OrthoPro_OrthoRoyale_Ortho',
  cabc: 'OrthoPro_OrthoRoyale_Ortho',
  cabd: 'OrthoPro_OrthoRoyale_Ortho',
  caca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  cacb: 'Ortho_OrthoRoyale_OrthoHybrid',
  cacc: 'Ortho_OrthoRoyale_OrthoHybrid',
  cacd: 'Ortho_OrthoRoyale_OrthoHybrid',
  cada: 'OrthoPro_OrthoRoyale_Ortho',
  cadb: 'OrthoPro_OrthoRoyale_Ortho',
  cadc: 'OrthoPro_OrthoRoyale_Ortho',
  cadd: 'OrthoPro_OrthoRoyale_Ortho',
  cbaa: 'OrthoPro_OrthoRoyale_Ortho',
  cbab: 'OrthoPro_OrthoRoyale_Ortho',
  cbac: 'OrthoPro_OrthoRoyale_Ortho',
  cbad: 'OrthoPro_OrthoRoyale_Ortho',
  cbba: 'OrthoPro_OrthoRoyale_Ortho',
  cbbb: 'OrthoPro_OrthoRoyale_Ortho',
  cbbc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  cbbd: 'LuxeSnowTec_LuxeRoyale_Ortho',
  cbca: 'OrthoPro_OrthoRoyale_Ortho',
  cbcb: 'OrthoPro_OrthoRoyale_Ortho',
  cbcc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  cbcd: 'OrthoHybrid_LuxeRoyale_LuxeSnowTec',
  cbda: 'OrthoPro_OrthoRoyale_Ortho',
  cbdb: 'OrthoPro_OrthoRoyale_Ortho',
  cbdc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  cbdd: 'LuxeSnowTec_LuxeRoyale_Ortho',
  ccaa: 'OrthoPro_OrthoRoyale_Ortho',
  ccab: 'OrthoPro_OrthoRoyale_Ortho',
  ccac: 'OrthoPro_Luxe_LuxeSnowTec',
  ccad: 'LuxeRoyale_OrthoPro_Ortho',
  ccba: 'OrthoPro_OrthoRoyale_Ortho',
  ccbb: 'OrthoPro_OrthoRoyale_Ortho',
  ccbc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  ccbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  ccca: 'OrthoPro_Ortho_OrthoHybrid',
  cccb: 'Luxe_OrthoRoyale_OrthoHybrid',
  cccc: 'Luxe_LuxeRoyale_OrthoHybrid',
  cccd: 'Luxe_LuxeRoyale_OrthoHybrid',
  ccda: 'OrthoPro_OrthoRoyale_Ortho',
  ccdb: 'OrthoPro_OrthoRoyale_Ortho',
  ccdc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  ccdd: 'OrthoPro_OrthoRoyale_Ortho',
  cdaa: 'OrthoPro_OrthoRoyale_Ortho',
  cdab: 'OrthoPro_OrthoRoyale_Ortho',
  cdac: 'OrthoPro_Luxe_LuxeSnowTec',
  cdad: 'LuxeRoyale_OrthoPro_Ortho',
  cdba: 'OrthoPro_OrthoRoyale_Ortho',
  cdbb: 'OrthoPro_OrthoRoyale_Ortho',
  cdbc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  cdbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  cdca: 'OrthoPro_Ortho_OrthoHybrid',
  cdcb: 'Luxe_OrthoRoyale_OrthoHybrid',
  cdcc: 'Luxe_LuxeRoyale_OrthoHybrid',
  cdcd: 'Luxe_LuxeRoyale_OrthoHybrid',
  cdda: 'OrthoPro_OrthoRoyale_Ortho',
  cddb: 'OrthoPro_OrthoRoyale_Ortho',
  cddc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  cddd: 'OrthoPro_OrthoRoyale_Ortho',
  ceaa: 'OrthoPro_OrthoRoyale_Ortho',
  ceab: 'OrthoPro_OrthoRoyale_Ortho',
  ceac: 'OrthoPro_Luxe_LuxeSnowTec',
  cead: 'LuxeRoyale_OrthoPro_Ortho',
  ceba: 'OrthoPro_OrthoRoyale_Ortho',
  cebb: 'OrthoPro_OrthoRoyale_Ortho',
  cebc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  cebd: 'Luxe_LuxeRoyale_OrthoHybrid',
  ceca: 'OrthoPro_Ortho_OrthoHybrid',
  cecb: 'Luxe_OrthoRoyale_OrthoHybrid',
  cecc: 'Luxe_LuxeRoyale_OrthoHybrid',
  cecd: 'Luxe_LuxeRoyale_OrthoHybrid',
  ceda: 'OrthoPro_OrthoRoyale_Ortho',
  cedb: 'OrthoPro_OrthoRoyale_Ortho',
  cedc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  cedd: 'OrthoPro_OrthoRoyale_Ortho',
  cfaa: 'OrthoPro_OrthoRoyale_Ortho',
  cfab: 'OrthoPro_OrthoRoyale_Ortho',
  cfac: 'OrthoPro_Luxe_LuxeSnowTec',
  cfad: 'LuxeRoyale_OrthoPro_Ortho',
  cfba: 'OrthoPro_OrthoRoyale_Ortho',
  cfbb: 'OrthoPro_OrthoRoyale_Ortho',
  cfbc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  cfbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  cfca: 'OrthoPro_Ortho_OrthoHybrid',
  cfcb: 'Luxe_OrthoRoyale_OrthoHybrid',
  cfcc: 'Luxe_LuxeRoyale_OrthoHybrid',
  cfcd: 'Luxe_LuxeRoyale_OrthoHybrid',
  cfda: 'OrthoPro_OrthoRoyale_Ortho',
  cfdb: 'OrthoPro_OrthoRoyale_Ortho',
  cfdc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  cfdd: 'OrthoPro_OrthoRoyale_Ortho',
  daaa: 'OrthoPro_OrthoRoyale_Ortho',
  daab: 'OrthoPro_OrthoRoyale_Ortho',
  daac: 'OrthoPro_OrthoRoyale_Luxe',
  daad: 'OrthoPro_OrthoRoyale_Ortho',
  daba: 'OrthoPro_OrthoRoyale_Ortho',
  dabb: 'OrthoPro_OrthoRoyale_Ortho',
  dabc: 'OrthoPro_OrthoRoyale_Ortho',
  dabd: 'OrthoPro_OrthoRoyale_Ortho',
  daca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  dacb: 'Ortho_OrthoRoyale_OrthoHybrid',
  dacc: 'Ortho_OrthoRoyale_OrthoHybrid',
  dacd: 'Ortho_OrthoRoyale_OrthoHybrid',
  dada: 'OrthoPro_OrthoRoyale_Ortho',
  dadb: 'OrthoPro_OrthoRoyale_Ortho',
  dadc: 'OrthoPro_OrthoRoyale_Ortho',
  dadd: 'OrthoPro_OrthoRoyale_Ortho',
  dbaa: 'OrthoPro_OrthoRoyale_Ortho',
  dbab: 'OrthoPro_OrthoRoyale_Ortho',
  dbac: 'OrthoPro_OrthoRoyale_Ortho',
  dbad: 'OrthoPro_OrthoRoyale_Ortho',
  dbba: 'OrthoPro_OrthoRoyale_Ortho',
  dbbb: 'OrthoPro_OrthoRoyale_Ortho',
  dbbc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  dbbd: 'LuxeSnowTec_LuxeRoyale_Ortho',
  dbca: 'OrthoPro_OrthoRoyale_Ortho',
  dbcb: 'OrthoPro_OrthoRoyale_Ortho',
  dbcc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  dbcd: 'OrthoHybrid_LuxeRoyale_LuxeSnowTec',
  dbda: 'OrthoPro_OrthoRoyale_Ortho',
  dbdb: 'OrthoPro_OrthoRoyale_Ortho',
  dbdc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  dbdd: 'LuxeSnowTec_LuxeRoyale_Ortho',
  dcaa: 'OrthoPro_OrthoRoyale_Ortho',
  dcab: 'OrthoPro_OrthoRoyale_Ortho',
  dcac: 'OrthoPro_Luxe_LuxeSnowTec',
  dcad: 'LuxeRoyale_OrthoPro_Ortho',
  dcba: 'OrthoPro_OrthoRoyale_Ortho',
  dcbb: 'OrthoPro_OrthoRoyale_Ortho',
  dcbc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  dcbd: 'LuxeRoyale_Luxe_LuxeSnowTec',
  dcca: 'OrthoPro_Ortho_OrthoHybrid',
  dccb: 'Luxe_OrthoRoyale_OrthoHybrid',
  dccc: 'Luxe_LuxeRoyale_OrthoHybrid',
  dccd: 'Luxe_LuxeRoyale_OrthoHybrid',
  dcda: 'OrthoPro_OrthoRoyale_Ortho',
  dcdb: 'OrthoPro_OrthoRoyale_Ortho',
  dcdc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  dcdd: 'Luxe_LuxeRoyale_OrthoHybrid',
  ddaa: 'OrthoPro_OrthoRoyale_Ortho',
  ddab: 'OrthoPro_OrthoRoyale_Ortho',
  ddac: 'OrthoPro_Luxe_LuxeSnowTec',
  ddad: 'LuxeRoyale_OrthoPro_Ortho',
  ddba: 'OrthoPro_OrthoRoyale_Ortho',
  ddbb: 'OrthoPro_OrthoRoyale_Ortho',
  ddbc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  ddbd: 'LuxeRoyale_Luxe_LuxeSnowTec',
  ddca: 'OrthoPro_Ortho_OrthoHybrid',
  ddcb: 'Luxe_OrthoRoyale_OrthoHybrid',
  ddcc: 'Luxe_LuxeRoyale_OrthoHybrid',
  ddcd: 'Luxe_LuxeRoyale_OrthoHybrid',
  ddda: 'OrthoPro_OrthoRoyale_Ortho',
  dddb: 'OrthoPro_OrthoRoyale_Ortho',
  dddc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  dddd: 'Luxe_LuxeRoyale_OrthoHybrid',
  deaa: 'OrthoPro_OrthoRoyale_Ortho',
  deab: 'OrthoPro_OrthoRoyale_Ortho',
  deac: 'OrthoPro_Luxe_LuxeSnowTec',
  dead: 'LuxeRoyale_OrthoPro_Ortho',
  deba: 'OrthoPro_OrthoRoyale_Ortho',
  debb: 'OrthoPro_OrthoRoyale_Ortho',
  debc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  debd: 'LuxeRoyale_Luxe_LuxeSnowTec',
  deca: 'OrthoPro_Ortho_OrthoHybrid',
  decb: 'Luxe_OrthoRoyale_OrthoHybrid',
  decc: 'Luxe_LuxeRoyale_OrthoHybrid',
  decd: 'Luxe_LuxeRoyale_OrthoHybrid',
  deda: 'OrthoPro_OrthoRoyale_Ortho',
  dedb: 'OrthoPro_OrthoRoyale_Ortho',
  dedc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  dedd: 'Luxe_LuxeRoyale_OrthoHybrid',
  dfaa: 'OrthoPro_OrthoRoyale_Ortho',
  dfab: 'OrthoPro_OrthoRoyale_Ortho',
  dfac: 'OrthoPro_Luxe_LuxeSnowTec',
  dfad: 'LuxeRoyale_OrthoPro_Ortho',
  dfba: 'OrthoPro_OrthoRoyale_Ortho',
  dfbb: 'OrthoPro_OrthoRoyale_Ortho',
  dfbc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  dfbd: 'LuxeRoyale_Luxe_LuxeSnowTec',
  dfca: 'OrthoPro_Ortho_OrthoHybrid',
  dfcb: 'Luxe_OrthoRoyale_OrthoHybrid',
  dfcc: 'Luxe_LuxeRoyale_OrthoHybrid',
  dfcd: 'Luxe_LuxeRoyale_OrthoHybrid',
  dfda: 'OrthoPro_OrthoRoyale_Ortho',
  dfdb: 'OrthoPro_OrthoRoyale_Ortho',
  dfdc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  dfdd: 'Luxe_LuxeRoyale_OrthoHybrid',
  eaaa: 'OrthoPro_OrthoRoyale_Ortho',
  eaab: 'OrthoPro_OrthoRoyale_Ortho',
  eaac: 'OrthoPro_OrthoRoyale_Luxe',
  eaad: 'OrthoPro_OrthoRoyale_Ortho',
  eaba: 'OrthoPro_OrthoRoyale_Ortho',
  eabb: 'OrthoPro_OrthoRoyale_Ortho',
  eabc: 'OrthoPro_OrthoRoyale_Ortho',
  eabd: 'OrthoPro_OrthoRoyale_Ortho',
  eaca: 'OrthoPro_OrthoRoyale_OrthoHybrid',
  eacb: 'Ortho_OrthoRoyale_OrthoHybrid',
  eacc: 'Ortho_OrthoRoyale_OrthoHybrid',
  eacd: 'Ortho_OrthoRoyale_OrthoHybrid',
  eada: 'OrthoPro_OrthoRoyale_Ortho',
  eadb: 'OrthoPro_OrthoRoyale_Ortho',
  eadc: 'OrthoPro_OrthoRoyale_Ortho',
  eadd: 'OrthoPro_OrthoRoyale_Ortho',
  ebaa: 'OrthoPro_OrthoRoyale_Ortho',
  ebab: 'OrthoPro_OrthoRoyale_Ortho',
  ebac: 'OrthoPro_OrthoRoyale_Ortho',
  ebad: 'OrthoPro_OrthoRoyale_Ortho',
  ebba: 'OrthoPro_OrthoRoyale_Ortho',
  ebbb: 'OrthoPro_OrthoRoyale_Ortho',
  ebbc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  ebbd: 'LuxeSnowTec_LuxeRoyale_Ortho',
  ebca: 'OrthoPro_OrthoRoyale_Ortho',
  ebcb: 'OrthoPro_OrthoRoyale_Ortho',
  ebcc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  ebcd: 'OrthoHybrid_LuxeRoyale_LuxeSnowTec',
  ebda: 'OrthoPro_OrthoRoyale_Ortho',
  ebdb: 'OrthoPro_OrthoRoyale_Ortho',
  ebdc: 'LuxeSnowTec_LuxeRoyale_Ortho',
  ebdd: 'LuxeSnowTec_LuxeRoyale_Ortho',
  ecaa: 'OrthoPro_OrthoRoyale_Ortho',
  ecab: 'OrthoPro_OrthoRoyale_Ortho',
  ecac: 'OrthoPro_Luxe_LuxeSnowTec',
  ecad: 'LuxeRoyale_OrthoPro_Ortho',
  ecba: 'OrthoPro_OrthoRoyale_Ortho',
  ecbb: 'OrthoPro_OrthoRoyale_Ortho',
  ecbc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  ecbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  ecca: 'OrthoPro_Ortho_OrthoHybrid',
  eccb: 'Luxe_OrthoRoyale_OrthoHybrid',
  eccc: 'Luxe_LuxeRoyale_OrthoHybrid',
  eccd: 'Luxe_LuxeRoyale_OrthoHybrid',
  ecda: 'OrthoPro_OrthoRoyale_Ortho',
  ecdb: 'Ortho_OrthoRoyale_Luxe',
  ecdc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  ecdd: 'Luxe_LuxeRoyale_OrthoHybrid',
  edaa: 'OrthoPro_OrthoRoyale_Ortho',
  edab: 'OrthoPro_OrthoRoyale_Ortho',
  edac: 'OrthoPro_Luxe_LuxeSnowTec',
  edad: 'LuxeRoyale_OrthoPro_Ortho',
  edba: 'OrthoPro_OrthoRoyale_Ortho',
  edbb: 'OrthoPro_OrthoRoyale_Ortho',
  edbc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  edbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  edca: 'OrthoPro_Ortho_OrthoHybrid',
  edcb: 'Luxe_OrthoRoyale_OrthoHybrid',
  edcc: 'Luxe_LuxeRoyale_OrthoHybrid',
  edcd: 'Luxe_LuxeRoyale_OrthoHybrid',
  edda: 'OrthoPro_OrthoRoyale_Ortho',
  eddb: 'Ortho_OrthoRoyale_Luxe',
  eddc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  eddd: 'Luxe_LuxeRoyale_OrthoHybrid',
  eeaa: 'OrthoPro_OrthoRoyale_Ortho',
  eeab: 'OrthoPro_OrthoRoyale_Ortho',
  eeac: 'OrthoPro_Luxe_LuxeSnowTec',
  eead: 'LuxeRoyale_OrthoPro_Ortho',
  eeba: 'OrthoPro_OrthoRoyale_Ortho',
  eebb: 'OrthoPro_OrthoRoyale_Ortho',
  eebc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  eebd: 'Luxe_LuxeRoyale_OrthoHybrid',
  eeca: 'OrthoPro_Ortho_OrthoHybrid',
  eecb: 'Luxe_OrthoRoyale_OrthoHybrid',
  eecc: 'Luxe_LuxeRoyale_OrthoHybrid',
  eecd: 'Luxe_LuxeRoyale_OrthoHybrid',
  eeda: 'OrthoPro_OrthoRoyale_Ortho',
  eedb: 'Ortho_OrthoRoyale_Luxe',
  eedc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  eedd: 'Luxe_LuxeRoyale_OrthoHybrid',
  efaa: 'OrthoPro_OrthoRoyale_Ortho',
  efab: 'OrthoPro_OrthoRoyale_Ortho',
  efac: 'OrthoPro_Luxe_LuxeSnowTec',
  efad: 'LuxeRoyale_OrthoPro_Ortho',
  efba: 'OrthoPro_OrthoRoyale_Ortho',
  efbb: 'OrthoPro_OrthoRoyale_Ortho',
  efbc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  efbd: 'Luxe_LuxeRoyale_OrthoHybrid',
  efca: 'OrthoPro_Ortho_OrthoHybrid',
  efcb: 'Luxe_OrthoRoyale_OrthoHybrid',
  efcc: 'Luxe_LuxeRoyale_OrthoHybrid',
  efcd: 'Luxe_LuxeRoyale_OrthoHybrid',
  efda: 'OrthoPro_OrthoRoyale_Ortho',
  efdb: 'Ortho_OrthoRoyale_Luxe',
  efdc: 'OrthoRoyale_Luxe_LuxeSnowTec',
  efdd: 'Luxe_LuxeRoyale_OrthoHybrid',
};
