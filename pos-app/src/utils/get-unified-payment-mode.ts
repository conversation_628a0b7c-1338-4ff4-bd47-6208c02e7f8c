export function getUnifiedPaymentMode(rawPaymentMethod) {
  if (!rawPaymentMethod) return null;
  const paymentMethodMapping = [
    { methods: ['card', 'CC', 'CARD'], standard: 'CARD' },
    { methods: ['UPI', 'UPI-BHIM', 'UPI SALE'], standard: 'UPI' },
    { methods: ['netbanking', 'NET', 'Net Banking'], standard: 'NETBANKING' },
    {
      methods: ['emi', 'EMI', 'Equated Monthly Installment', 'BRAND EMI'],
      standard: 'EMI',
    },
  ];

  const normalizedInput = rawPaymentMethod.toLowerCase();

  for (const mapping of paymentMethodMapping) {
    if (
      mapping.methods.some((method) =>
        method.toLowerCase().includes(normalizedInput),
      )
    ) {
      return mapping.standard;
    }
  }

  return rawPaymentMethod;
}
