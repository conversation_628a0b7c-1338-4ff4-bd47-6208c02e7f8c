import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';

export class ElasticClient {
  private esClient;

  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {
    this.init();
  }

  async init() {
    if (this.esClient) return;
    // const [endpoint, username, password] = await Promise.all([
    //   await this.ssmClient.getSSMParamByKey(
    //     new GetParameterCommand({
    //       Name: `/${this.configService.get('STACK_NAME')}/opensearch/endpoint`,
    //       WithDecryption: true,
    //     }),
    //   ),
    //   await this.ssmClient.getSSMParamByKey(
    //     new GetParameterCommand({
    //       Name: `/${this.configService.get('STACK_NAME')}/opensearch/username`,
    //       WithDecryption: true,
    //     }),
    //   ),
    //   await this.ssmClient.getSSMParamByKey(
    //     new GetParameterCommand({
    //       Name: `/${this.configService.get('STACK_NAME')}/opensearch/password`,
    //       WithDecryption: true,
    //     }),
    //   ),
    // ]);

    // const {
    //   Parameter: { Value: ENDPOINT },
    // } = endpoint;
    // const {
    //   Parameter: { Value: USERNAME },
    // } = username;
    // const {
    //   Parameter: { Value: PASSWORD },
    // } = password;

    this.esClient = new Client({
      node: `https://search-pos-prod-es-kej2yfeachy4owakc4x6vgv7iy.ap-south-1.es.amazonaws.com`,
      auth: {
        username: 'admin',
        password: 'Admin@1234',
      },
    });
  }

  async count(body) {
    await this.init();
    return this.esClient.count(body);
  }

  async search(body) {
    await this.init();
    return this.esClient.search(body);
  }
}
