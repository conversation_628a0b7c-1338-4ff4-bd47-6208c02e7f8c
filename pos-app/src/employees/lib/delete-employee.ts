// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { AdminDeleteUserCommand } from '@aws-sdk/client-cognito-identity-provider';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppCognitoClient } from 'src/common/cognito-identity-client/cognito-identity-client';
import { posLogger } from 'src/common/logger';
import moment from 'moment';
import { EmployeeData } from '../entities/employee.entity';
import { AppConfigParameters } from 'src/config/config';

export class RemoveEmployee {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private cognitoClient: AppCognitoClient,
    private configParameters: AppConfigParameters,
  ) {}

  async removeEmployee(id: string, cognitoId?: string): Promise<EmployeeData> {
    posLogger.info('employee', 'removeEmployee', { input: { id } });
    try {
      const [USERPOOL_ID, EMPLOYEE_TABLE] = await Promise.all([
        await this.configParameters.getUserPoolId(),
        await this.configParameters.getEmployeeTableName(),
      ]);

      if (cognitoId) {
        const input = {
          UserPoolId: USERPOOL_ID, // Replace with your user pool ID
          Username: cognitoId,
        };
        const command = new AdminDeleteUserCommand(input);
        await this.cognitoClient.adminDeleteUser(command);
      }

      const deleteCommand = new UpdateCommand({
        TableName: EMPLOYEE_TABLE,
        Key: { id },
        UpdateExpression: `SET isDeleted = :isDeleted, updatedAt = :updatedAt`,
        ExpressionAttributeValues: {
          ':isDeleted': true,
          ':updatedAt': moment().toISOString(),
        },
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const response = await this.docClient.updateItem(deleteCommand);

      return response.Attributes;
    } catch (e) {
      posLogger.error('employee', 'removeEmployee', { error: { e } });
      throw e;
    }
  }
}
