// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { EmployeeData } from '../entities/employee.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
export class GetEmployee {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getEmployee(id: string): Promise<EmployeeData> {
    posLogger.info('employee', 'getEmployee', { input: id });
    try {
      const EMPLOYEE_TABLE = await this.configParameters.getEmployeeTableName();

      const command = new GetCommand({
        TableName: EMPLOYEE_TABLE,
        Key: { id },
      });

      const employee = (await this.docClient.getItem(command)).Item;

      if (!employee || employee.isDeleted) {
        throw new CustomError(`Employee ${id} not found`, 404);
      }

      return employee;
    } catch (e) {
      posLogger.error('employee', 'getEmployee', { error: e });
      throw e;
    }
  }

  // async getEmployeeByCognitoId(cognitoId: string): Promise<EmployeeData> {
  //   posLogger.info('employee', 'getEmployeeByCognitoId', { input: cognitoId });

  //   const BY_COGNITO_ID_EMPLOYEE_INDEX = 'byCognitoIdEmployeeindex';
  //   try {
  //     const EMPLOYEE_TABLE = await this.configParameters.getEmployeeTableName();

  //     const command = new QueryCommand({
  //       TableName: EMPLOYEE_TABLE,
  //       IndexName: BY_COGNITO_ID_EMPLOYEE_INDEX,
  //       KeyConditionExpression: '#cognitoId = :cognitoId',
  //       ExpressionAttributeNames: {
  //         '#cognitoId': 'cognitoId',
  //       },
  //       ExpressionAttributeValues: {
  //         ':cognitoId': cognitoId,
  //       },
  //     });

  //     const [employee] = (await this.docClient.queryItems(command)).Items;

  //     if (!employee || employee.isDeleted) {
  //       throw new CustomError(
  //         `Employee with cognito ID ${cognitoId} not found`,
  //         404,
  //       );
  //     }

  //     return employee;
  //   } catch (e) {
  //     posLogger.error('employee', 'getEmployee', { error: e });
  //     throw e;
  //   }
  // }
}
