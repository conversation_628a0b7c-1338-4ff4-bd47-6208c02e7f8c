// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { UpdateEmployeeInput } from '../dto/update-employee.input';
import {
  AdminDisableUserCommand,
  AdminEnableUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppCognitoClient } from 'src/common/cognito-identity-client/cognito-identity-client';
import { posLogger } from 'src/common/logger';
import { EmployeeData } from '../entities/employee.entity';
import { GetEmployee } from './get-employee';
import { AppConfigParameters } from 'src/config/config';

export class UpdateEmployee {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private cognitoClient: AppCognitoClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateEmployee(
    id: string,
    { id: {}, ...updateEmployeeInput }: UpdateEmployeeInput,
  ): Promise<EmployeeData> {
    posLogger.info('employee', 'updateEmployee', {
      input: { id, ...updateEmployeeInput },
    });
    try {
      const [USERPOOL_ID, EMPLOYEE_TABLE] = await Promise.all([
        await this.configParameters.getUserPoolId(),
        await this.configParameters.getEmployeeTableName(),
      ]);

      const getHandler = new GetEmployee(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const { cognitoId } = await getHandler.getEmployee(id);

      if (cognitoId && updateEmployeeInput.isActive == false) {
        const input = {
          UserPoolId: USERPOOL_ID,
          Username: cognitoId,
        };
        const command = new AdminDisableUserCommand(input);
        await this.cognitoClient.adminDisableUser(command);
      }

      if (cognitoId && updateEmployeeInput.isActive) {
        const input = {
          UserPoolId: USERPOOL_ID,
          Username: cognitoId,
        };
        const command = new AdminEnableUserCommand(input);
        await this.cognitoClient.adminEnableUser(command);
      }

      const updateExpression: string[] = ['updatedAt = :updatedAt'];
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, any> = {
        ':updatedAt': moment().toISOString(),
      };

      Object.keys(updateEmployeeInput).forEach((field) => {
        const attributeValueKey = `:${field}`;
        updateExpression.push(`#${field} = ${attributeValueKey}`);
        expressionAttributeValues[attributeValueKey] =
          updateEmployeeInput[field];

        if (field === 'type') {
          expressionAttributeNames['#type'] = field;
        } else {
          expressionAttributeNames[`#${field}`] = field;
        }
      });

      const command = new UpdateCommand({
        TableName: EMPLOYEE_TABLE,
        Key: { id },
        UpdateExpression: `SET ${updateExpression.join(', ')}`,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const response = await this.docClient.updateItem(command);

      return response.Attributes;
    } catch (e) {
      posLogger.error('employee', 'updateEmployee', { error: e });
      throw e;
    }
  }
}
