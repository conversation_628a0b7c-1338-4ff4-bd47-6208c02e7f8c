// modules
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import {
  AdminCreateUserCommand,
  AdminCreateUserCommandInput,
} from '@aws-sdk/client-cognito-identity-provider';
import { CreateEmployeeInput } from '../dto/create-employee.input';
import { EmployeeData } from '../entities/employee.entity';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppCognitoClient } from 'src/common/cognito-identity-client/cognito-identity-client';
import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { Roles } from 'src/common/enum/roles';

export class CreateEmployee {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private cognitoClient: AppCognitoClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createCognitoUser(createEmployeeInput) {
    try {
      posLogger.info('employee', 'createCognitoUser', {
        email: createEmployeeInput.email,
      });
      const USERPOOL_ID = await this.configParameters.getUserPoolId();

      const input: AdminCreateUserCommandInput = {
        UserPoolId: USERPOOL_ID,
        Username: createEmployeeInput.email,
        UserAttributes: [
          {
            Name: 'email_verified',
            Value: 'true',
          },
          {
            Name: 'email',
            Value: createEmployeeInput.email,
          },
          {
            Name: 'custom:roles',
            Value: createEmployeeInput.role,
          },
          {
            Name: 'profile',
            Value: createEmployeeInput.id,
          },
        ],
        TemporaryPassword: 'Test@1234',
        MessageAction: 'SUPPRESS',
        DesiredDeliveryMediums: [],
      };

      const command = new AdminCreateUserCommand(input);
      const cognitoResponse = await this.cognitoClient.adminCreateUser(command);

      const {
        User: { Attributes: response },
      } = cognitoResponse;

      return response;
    } catch (error) {
      posLogger.error('employee', 'createEmployee', {
        msg: 'Error while creating user on cognito',
        error,
      });
      throw new CustomError(error.message, 400);
    }
  }

  async createEmployee(
    createEmployeeInput: CreateEmployeeInput,
  ): Promise<EmployeeData> {
    posLogger.info('employee', 'createEmployee', {
      input: createEmployeeInput,
    });

    try {
      const { role, id, designation } = createEmployeeInput;
      const EMPLOYEE_TABLE = await this.configParameters.getEmployeeTableName();

      if (
        role &&
        (role === Roles.ADMIN || role === Roles.CRM || role === Roles.CRM_SALES)
      ) {
        createEmployeeInput.cognitoId = (
          await this.createCognitoUser(createEmployeeInput)
        ).find((e) => e.Name === 'sub').Value;
      }

      const employeePayload: EmployeeData = {
        ...createEmployeeInput,
        id,
        role,
        cognitoId: createEmployeeInput.cognitoId || null,
        designation,
        isActive: true,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      };

      const employeeCommand = new PutCommand({
        TableName: EMPLOYEE_TABLE,
        Item: { ...employeePayload },
        // ConditionExpression: 'attribute_not_exists(id)',
      });

      await this.docClient.createItem(employeeCommand);
      return employeePayload;
    } catch (e) {
      posLogger.error('employee', 'createEmployee', { error: e });
      throw e;
    }
  }
}
