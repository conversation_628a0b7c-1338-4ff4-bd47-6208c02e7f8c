import { Module } from '@nestjs/common';
import { EmployeesService } from './employees.service';
import { EmployeesResolver } from './employees.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { CognitoIdentityClientModule } from 'src/common/cognito-identity-client/cognito-identity-client.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { ConfigParametersModule } from 'src/config/config.module';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    CognitoIdentityClientModule,
    ConfigParametersModule,
  ],
  providers: [
    EmployeesResolver,
    EmployeesService,
    SuccessHandler,
    <PERSON>rror<PERSON><PERSON><PERSON>,
  ],
})
export class EmployeesModule {}
