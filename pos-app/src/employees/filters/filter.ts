export const searchingFilter = new Map<
  string,
  'string' | 'nonString' | 'arrayString' | 'boolean' | 'date'
>([
  ['firstName', 'string'],
  ['lastName', 'string'],
  ['id', 'string'],
  ['stores', 'arrayString'],
  ['role', 'string'],
  ['email', 'string'],
  ['cognitoId', 'string'],
  ['isActive', 'boolean'],
  ['createdAt', 'date'],
  ['updatedAt', 'date'],
]);

export const sortingFilter = new Map<string, 'string' | 'nonString'>([
  ['createdAt', 'string'],
  ['updatedAt', 'string'],
]);

export const sortingFilterType = {
  createdAt: 'date',
  updatedAt: 'date',
};
