import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Roles } from 'src/common/enum/roles';

@ObjectType()
export class EmployeeData {
  @Field(() => ID, { nullable: false })
  id: string;

  @Field(() => String, { nullable: false })
  firstName: string;

  @Field(() => String, { nullable: false })
  lastName: string;

  @Field(() => String, { nullable: true })
  email?: string;

  @Field(() => String, { nullable: true })
  cognitoId: string;

  @Field(() => String, { nullable: true })
  phone?: string;

  @Field(() => Boolean, { nullable: false })
  isActive: boolean;

  @Field(() => String, {
    nullable: true,
    description: 'Role to a specific store',
    defaultValue: Roles.STAFF,
  })
  role: Roles;

  @Field(() => [String], {
    nullable: true,
    description: 'Access to a store',
  })
  stores?: string[];

  @Field(() => String, { nullable: false })
  createdAt: string;

  @Field(() => String, { nullable: false })
  updatedAt: string;

  @Field(() => String, {
    nullable: true,
    description: 'Designation of an employee',
  })
  designation?: string;
}

@ObjectType()
export class Employees {
  @Field(() => [EmployeeData], { nullable: true })
  data: EmployeeData[];

  @Field(() => Number, { description: 'Count of Total data', nullable: true })
  count: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class Employee {
  @Field(() => EmployeeData, { nullable: true })
  data: EmployeeData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
