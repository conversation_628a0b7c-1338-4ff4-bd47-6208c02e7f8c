import { Injectable } from '@nestjs/common';
import { CreateEmployeeInput } from './dto/create-employee.input';
import { UpdateEmployeeInput } from './dto/update-employee.input';
import { CreateEmployee } from './lib/create-employee';
import { QueryEmployees } from './lib/list-employees';
import { GetEmployee } from './lib/get-employee';
import { UpdateEmployee } from './lib/update-employee';
import { RemoveEmployee } from './lib/delete-employee';
import { ListEmployeeInput } from './dto/list-employee.input';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppCognitoClient } from 'src/common/cognito-identity-client/cognito-identity-client';
import { AppConfigParameters } from 'src/config/config';

@Injectable()
export class EmployeesService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private cognitoClient: AppCognitoClient,
    private configParameters: AppConfigParameters,
  ) {}

  async create(createEmployeeInput: CreateEmployeeInput) {
    const createHandler = new CreateEmployee(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.cognitoClient,
      this.configParameters,
    );
    return createHandler.createEmployee(createEmployeeInput);
  }

  async findAll(listEmployeeInput: ListEmployeeInput) {
    const queryHandler = new QueryEmployees(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return queryHandler.queryEmployees(listEmployeeInput);
  }

  async findOne(id: string) {
    const getHandler = new GetEmployee(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.getEmployee(id);
  }

  // async findOneByCognitoId(cognitoId: string) {
  //   const getHandler = new GetEmployee(
  //     this.configService,
  //     this.docClient,
  //     this.ssmClient,
  //     this.configParameters,
  //   );
  //   return getHandler.getEmployeeByCognitoId(cognitoId);
  // }

  async update(id: string, updateEmployeeInput: UpdateEmployeeInput) {
    const updateHandler = new UpdateEmployee(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.cognitoClient,
      this.configParameters,
    );
    return updateHandler.updateEmployee(id, updateEmployeeInput);
  }

  async remove(id: string, cognitoId?: string) {
    const removeHandler = new RemoveEmployee(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.cognitoClient,
      this.configParameters,
    );
    return removeHandler.removeEmployee(id, cognitoId);
  }
}
