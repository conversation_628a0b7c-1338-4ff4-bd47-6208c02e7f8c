import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { EmployeesService } from './employees.service';
import { Employee, Employees } from './entities/employee.entity';
import { CreateEmployeeInput } from './dto/create-employee.input';
import { UpdateEmployeeInput } from './dto/update-employee.input';
import { ListEmployeeInput } from './dto/list-employee.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import {
  AdminGuard,
  CRMGuard,
  ManagerGuard,
  StaffGuard,
  StoreGuard,
} from 'src/auth/roles.guard';

@Resolver(() => Employees)
@UseGuards(CustomAuthGuard, CRMGuard)
export class EmployeesResolver {
  constructor(
    private readonly employeesService: EmployeesService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: <PERSON>rrorHandler,
  ) {}

  @Mutation(() => Employee)
  @UseGuards(StoreGuard)
  async createEmployee(
    @Args('createEmployeeInput') createEmployeeInput: CreateEmployeeInput,
  ) {
    try {
      const data = await this.employeesService.create(createEmployeeInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create employee',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Employees, { name: 'listEmployees' })
  @UseGuards(StaffGuard)
  async findAll(
    @Args('listEmployeeInput', { nullable: true })
    listEmployeeInput: ListEmployeeInput,
  ) {
    try {
      const { data, count } =
        await this.employeesService.findAll(listEmployeeInput);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list employees',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Employee, { name: 'getEmployee' })
  @UseGuards(StaffGuard)
  async findOne(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.employeesService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get employee',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  // @Query(() => Employee, { name: 'getEmployeeByCognitoId' })
  // @UseGuards(StaffGuard)
  // async findOneByCognitoId(
  //   @Args('cognitoId', { type: () => String, nullable: false })
  //   cognitoId: string,
  // ) {
  //   try {
  //     const data = await this.employeesService.findOneByCognitoId(cognitoId);
  //     return this.successHandler.getSuccessResponse({
  //       data,
  //       code: 200,
  //     });
  //   } catch (error) {
  //     const errorResponse = this.errorHandler.getErrorResponse({
  //       message: error.message || 'Failed to get employee',
  //       code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
  //     });
  //     return errorResponse;
  //   }
  // }

  @Mutation(() => Employee, { name: 'updateEmployee' })
  @UseGuards(ManagerGuard)
  async updateEmployee(
    @Args('updateEmployeeInput') updateEmployeeInput: UpdateEmployeeInput,
  ) {
    try {
      const data = await this.employeesService.update(
        updateEmployeeInput.id,
        updateEmployeeInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update employee',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Employee, { name: 'deleteEmployee' })
  @UseGuards(AdminGuard)
  async removeEmployee(
    @Args('id', { type: () => String }) id: string,
    @Args('cognitoId', { type: () => String, nullable: true })
    cognitoId?: string,
  ) {
    try {
      const data = await this.employeesService.remove(id, cognitoId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to delete employee',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
