import { InputType, Field, registerEnumType } from '@nestjs/graphql';
import { Roles } from 'src/common/enum/roles';

// Register the enum with GraphQL
registerEnumType(Roles, { name: 'Role' });

@InputType()
export class CreateEmployeeInput {
  @Field(() => String, {
    nullable: false,
    description: 'ID of the note author',
  })
  id: string;

  @Field(() => String, {
    nullable: false,
    description: 'First name of the note author',
  })
  firstName: string;

  @Field(() => String, {
    nullable: false,
    description: 'Last name of the note author',
  })
  lastName: string;

  @Field(() => String, {
    nullable: true,
    description: 'Email address associated with the note',
  })
  email?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Phone number associated with the note',
  })
  phone?: string;

  @Field(() => Roles, {
    nullable: true,
    description: 'Role to a specific store',
    defaultValue: Roles.STAFF,
  })
  role?: Roles;

  @Field(() => [String], {
    nullable: true,
    description: 'Access to a store',
  })
  stores?: string[];

  @Field(() => String, {
    nullable: true,
    description: 'Cognito ID of an employee',
  })
  cognitoId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Designation of an employee',
  })
  designation?: string;
}
