import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class UpdateEmployeeInput {
  @Field(() => String, {
    nullable: false,
    description: 'ID of the employee',
  })
  id: string;

  @Field(() => String, {
    nullable: true,
    description: 'First name of the note author',
  })
  firstName?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Last name of the note author',
  })
  lastName?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Phone number associated with the note',
  })
  phone?: string;

  @Field(() => Boolean, {
    nullable: true,
    description: 'First name of the note author',
  })
  isActive?: boolean;

  @Field(() => [String], {
    nullable: true,
    description: 'Access to a store',
  })
  stores?: string[];

  @Field(() => String, {
    nullable: true,
    description: 'Designation of an employee',
  })
  designation?: string;
}
