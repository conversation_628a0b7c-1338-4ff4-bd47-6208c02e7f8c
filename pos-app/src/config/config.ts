import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';

@Injectable()
export class AppConfigParameters {
  private stackName: string;
  private userPoolId: string;
  private webClientId: string;
  private ApiGatewayUrl: string;
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {}

  async init() {
    if (!this.stackName) this.stackName = this.configService.get('STACK_NAME');
  }

  async getUserPoolId() {
    await this.init();
    if (!this.userPoolId) {
      const {
        Parameter: { Value: userPoolId },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.stackName}/cognito/userpool_id`,
          WithDecryption: true,
        }),
      );

      this.userPoolId = userPoolId;
      return userPoolId;
    }
    return this.userPoolId;
  }

  async getApiGatewayUrl() {
    await this.init();
    if (!this.ApiGatewayUrl) {
      const {
        Parameter: { Value: ApiGatewayUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.stackName}/api-gateway/baseurl`,
          WithDecryption: true,
        }),
      );

      this.ApiGatewayUrl = ApiGatewayUrl;
      return ApiGatewayUrl;
    }
    return this.ApiGatewayUrl;
  }

  async getWebClientId() {
    await this.init();
    if (!this.webClientId) {
      const {
        Parameter: { Value: webClientId },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.stackName}/cognito/client_id`,
          WithDecryption: true,
        }),
      );

      this.webClientId = webClientId;
      return webClientId;
    }
    return this.webClientId;
  }

  async getRegion() {
    await this.init();
    return this.configService.get('REGION');
  }

  async getStoreTableName() {
    await this.init();
    return `${this.stackName}-store-table`;
  }

  async getProductTableName() {
    await this.init();
    return `${this.stackName}-product-table`;
  }

  async getCollectionTableName() {
    await this.init();
    return `${this.stackName}-collection-table`;
  }

  async getGlobalConfigurationName() {
    await this.init();
    return `${this.stackName}-global-configuration-table`;
  }

  async getLocalConfigurationName() {
    await this.init();
    return `${this.stackName}-local-configuration-table`;
  }

  async getCouponTableName() {
    await this.init();
    return `${this.stackName}-coupon-table`;
  }

  async getCustomerSpecificCouponTableName() {
    await this.init();
    return `${this.stackName}-customer-specific-coupon-table`;
  }
  async getSTNTableName() {
    await this.init();
    return `${this.stackName}-stn-table`;
  }
  async getStorePOTableName() {
    await this.init();
    return `${this.stackName}-store-po-table`;
  }

  async getEmployeeTableName() {
    await this.init();
    return `${this.stackName}-employee-table`;
  }

  async getCampaignCouponTableName() {
    await this.init();
    return `${this.stackName}-campaign-coupon-table`;
  }

  async getCMSTableName() {
    await this.init();
    return `${this.stackName}-cms-table`;
  }

  async getEmiToolTableName() {
    await this.init();
    return `${this.stackName}-emi-tool-table`;
  }

  async getQuotationTableName() {
    await this.init();
    return `${this.stackName}-quotation-table`;
  }

  async getOrderTableName() {
    await this.init();
    return `${this.stackName}-order-table`;
  }

  async getCancelledOrderTableName() {
    await this.init();
    return `${this.stackName}-cancel-table`;
  }

  async getReplacementOrderTableName() {
    await this.init();
    return `${this.stackName}-replacement-table`;
  }

  async getRefundDetailTableName() {
    await this.init();
    return `${this.stackName}-refund-table`;
  }

  async getOrderItemTableName() {
    await this.init();
    return `${this.stackName}-order-item-table`;
  }

  async getInteriorArchitectureTableName() {
    await this.init();
    return `${this.stackName}-interior-architecture-table`;
  }

  async getHSNCodeTableName() {
    await this.init();
    return `${this.stackName}-hsn-code-table`;
  }

  async getPriceMasterTableName() {
    await this.init();
    return `${this.stackName}-price-master-table`;
  }

  async getIOTableName() {
    await this.init();
    return `${this.stackName}-io-table`;
  }

  async getCartTableName() {
    await this.init();
    return `${this.stackName}-cart-table`;
  }

  async getPaymentTableName() {
    await this.init();
    return `${this.stackName}-payment-table`;
  }

  async getPincodeTableName() {
    await this.init();
    return `${this.stackName}-pincode-table`;
  }
  async getInventoryCredentialsTableName() {
    await this.init();
    return `${this.stackName}-inventory-credentials-table`;
  }

  async getInventoryTrackingTableName() {
    await this.init();
    return `${this.stackName}-inventory-tracking-table`;
  }
  async getGRNTableName() {
    await this.init();
    return `${this.stackName}-grn-table`;
  }
  async getNotificationTableName() {
    await this.init();
    return `${this.stackName}-notifications-table`;
  }
  async getNotificationSqsQueueUrl() {
    await this.init();
    return `${this.stackName}-notification-queue`;
  }
  async getFirebaseTokenTableName() {
    await this.init();
    return `${this.stackName}-firebase-token-table`;
  }
  async getNotificationLogsTableName() {
    await this.init();
    return `${this.stackName}-notification-logs-table`;
  }
  async getSpinTheWheelCouponConfigTableName() {
    await this.init();
    return `${this.stackName}-spin-the-wheel-coupon-config-table`;
  }
  async getCustomDiscountVerificationTableName() {
    await this.init();
    return `${this.stackName}-custom-discount-verification-table`;
  }

  async getStoreOrdersApiKeysTableName() {
    await this.init();
    return `${this.stackName}-store-orders-api-keys-table`;
  }

  async getBulkCouponGenerationTableName() {
    await this.init();
    return `${this.stackName}-bulk-coupon-generation-table`;
  }
  async getPriceTableName() {
    await this.init();
    return `${this.stackName}-price-table`;
  }

  async getMasterEmployeeTableName() {
    await this.init();
    return `${this.stackName}-employee-master-table`;
  }

  async getCustomerAddressTableName() {
    await this.init();
    return `${this.stackName}-customer-address-table`;
  }

  async getOTPTableName() {
    await this.init();
    return `${this.stackName}-otp-table`;
  }

  async getMattressRecommendationTableName() {
    await this.init();
    return `${this.stackName}-mattress-recommendation-table`;
  }

  async getOrdersLogTableName() {
    await this.init();
    return `${this.stackName}-orders-log-table`;
  }
}
// export default {
//   dev: {
//     ORDER_PREFIX_CODE: 'TSC',
//     QUOTATION_EXPIRE_INTERVAL_IN_DAYS: 10,
//     MAX_CUSTOM_DISCOUNT_IN_PERCENTAGE: 40,
//     MAX_CUSTOM_DISCOUNT_IN_VALUES: 500,
//     MAX_COD_IN_VALUES: 1000,

//     AWS_PROFILE: 'tsc-pos',
//     REGION: 'ap-south-1',
//     USERPOOL_ID: 'ap-south-1_cVVkX1nFo',

//     SHOPIFY_ACCESS_TOKEN: '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',
//     SHOPIFY_ADMIN_BASE_URL:
//       'https://quickstart-a353a111.myshopify.com/admin/api/2024-01',

//     EMPLOYEE_TABLE: 'tsc-pos-api-dev-employee-table',
//     BY_NAME_EMPLOYEE_INDEX: 'byNameEmployeeIndex',
//     BY_EMAIL_EMPLOYEE_INDEX: 'byEmailEmployeeIndex',

//     COUPONS_TABLE: 'tsc-pos-api-dev-coupon-table',
//     PRODUCT_TABLE: 'tsc-pos-api-dev-product-table',
//     BY_PRODUCT_HANDLE_INDEX: 'byHandleProductIndex',
//     BY_PRODUCTTYPE_PRODUCT_INDEX: 'byProductTypeProductIndex',
//     BY_TITLE_PRODUCT_INDEX: 'byTitleProductIndex',

//     QUOTATIONS_TABLE: 'tsc-pos-api-dev-quotation-table',
//     BY_CUSTOMERID_CREATEDAT_QUOTATION_INDEX:
//       'byCustomerIdCreatedAtQuotationIndex',
//     BY_EMPLOYEEID_CREATEDAT_QUOTATION_INDEX:
//       'byEmployeeIdCreatedAtQuotationIndex',

//     STORE_TABLE: 'tsc-pos-api-dev-store-table',

//     CART_TABLE: 'tsc-pos-api-dev-cart-table',
//     BY_ID_CART_INDEX: 'byIdCartIndex',
//     BY_EMPLOYEEID_CREATEDAT_CART_INDEX: 'byEmployeeIdCreatedAtCartIndex',

//     PINCODE_TABLE: 'tsc-pos-api-dev-pincode-table',
//     WISHLIST_URL:
//       'https://n503a0isif.execute-api.ap-south-1.amazonaws.com/develop/wishlist',
//     WISHLIST_API_ACCESS_TOKEN: 'Jl3739KUDTqQutdueiueaLjDuobervO1Tt66Raqb',
//   },
//   stage: {
//     ORDER_PREFIX_CODE: 'TSC',
//     QUOTATION_EXPIRE_INTERVAL_IN_DAYS: 10,
//     MAX_CUSTOM_DISCOUNT_IN_PERCENTAGE: 40,
//     MAX_CUSTOM_DISCOUNT_IN_VALUES: 500,
//     MAX_COD_IN_VALUES: 1000,

//     AWS_PROFILE: 'tsc-pos',
//     REGION: 'ap-south-1',
//     USERPOOL_ID: 'ap-south-1_4eWvmB0nf',

//     SHOPIFY_ACCESS_TOKEN: '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',
//     SHOPIFY_ADMIN_BASE_URL:
//       'https://quickstart-a353a111.myshopify.com/admin/api/2024-01',

//     EMPLOYEE_TABLE: 'tsc-pos-api-stage-employee-table',
//     BY_NAME_EMPLOYEE_INDEX: 'byNameEmployeeIndex',
//     BY_EMAIL_EMPLOYEE_INDEX: 'byEmailEmployeeIndex',

//     COUPONS_TABLE: 'tsc-pos-api-stage-coupon-table',

//     PRODUCT_TABLE: 'tsc-pos-api-stage-product-table',
//     BY_PRODUCT_HANDLE_INDEX: 'byHandleProductIndex',
//     BY_PRODUCTTYPE_PRODUCT_INDEX: 'byProductTypeProductIndex',
//     BY_TITLE_PRODUCT_INDEX: 'byTitleProductIndex',

//     QUOTATIONS_TABLE: 'tsc-pos-api-stage-quotation-table',
//     BY_CUSTOMERID_CREATEDAT_QUOTATION_INDEX:
//       'byCustomerIdCreatedAtQuotationIndex',
//     BY_EMPLOYEEID_CREATEDAT_QUOTATION_INDEX:
//       'byEmployeeIdCreatedAtQuotationIndex',

//     STORE_TABLE: 'tsc-pos-api-stage-store-table',

//     CART_TABLE: 'tsc-pos-api-stage-cart-table',
//     BY_ID_CART_INDEX: 'byIdCartIndex',
//     BY_EMPLOYEEID_CREATEDAT_CART_INDEX: 'byEmployeeIdCreatedAtCartIndex',

//     PINCODE_TABLE: 'tsc-pos-api-stage-pincode-table',
//     WISHLIST_URL:
//       'https://n503a0isif.execute-api.ap-south-1.amazonaws.com/develop/wishlist',
//     WISHLIST_API_ACCESS_TOKEN: 'Jl3739KUDTqQutdueiueaLjDuobervO1Tt66Raqb',
//   },
//   prod: {},
// };
