import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from '../common/ssm-client/ssm-client';

@Injectable()
export class AppConfigParametersHardcoded {
  private stackName: string;
  private userPoolId: string;
  private webClientId: string;
  private ApiGatewayUrl: string;

  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {}

  async init() {
    if (!this.stackName) this.stackName = this.configService.get('STACK_NAME');
  }

  // HARDCODED VERSION - No SSM API calls, no rate limiting
  async getUserPoolId() {
    await this.init();
    if (!this.userPoolId) {
      // Use hardcoded value instead of SSM API call
      this.userPoolId = this.ssmClient.getHardcodedCognitoUserPoolId(this.stackName);
      return this.userPoolId;
    }
    return this.userPoolId;
  }

  // HARDCODED VERSION - No SSM API calls, no rate limiting
  async getApiGatewayUrl() {
    await this.init();
    if (!this.ApiGatewayUrl) {
      // Use hardcoded value instead of SSM API call
      this.ApiGatewayUrl = this.ssmClient.getHardcodedSSMParameter(
        `/${this.stackName}/api-gateway/baseurl`,
        this.stackName
      );
      return this.ApiGatewayUrl;
    }
    return this.ApiGatewayUrl;
  }

  // HARDCODED VERSION - No SSM API calls, no rate limiting
  async getWebClientId() {
    await this.init();
    if (!this.webClientId) {
      // Use hardcoded value instead of SSM API call
      this.webClientId = this.ssmClient.getHardcodedSSMParameter(
        `/${this.stackName}/cognito/client_id`,
        this.stackName
      );
      return this.webClientId;
    }
    return this.webClientId;
  }

  // Example of getting multiple parameters at once
  async getAllCognitoConfig() {
    await this.init();
    
    return {
      userPoolId: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/cognito/userpool_id`, this.stackName),
      clientId: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/cognito/client_id`, this.stackName),
    };
  }

  // Example of getting payment gateway configs
  async getPaymentGatewayConfigs() {
    await this.init();
    
    return {
      razorpay: {
        apiKey: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/direct-razorpay/api-key`, this.stackName),
        apiSecret: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/direct-razorpay/api-secret`, this.stackName),
        callbackUrl: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/direct-razorpay/callback-url`, this.stackName),
      },
      payu: {
        key: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/payu-pos/key`, this.stackName),
        salt: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/payu-pos/salt`, this.stackName),
        merchantId: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/payu-pos/merchant-id`, this.stackName),
      },
      pineLabs: {
        merchantId: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/pine-labs/merchant-id`, this.stackName),
        storeId: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/pine-labs/store-id`, this.stackName),
        securityToken: this.ssmClient.getHardcodedSSMParameter(`/${this.stackName}/pine-labs/security-token`, this.stackName),
      }
    };
  }

  // The specific map you requested
  async getCognitoUserPoolIdMap() {
    await this.init();
    const parameterName = `/${this.stackName}/cognito/userpool_id`;
    const value = this.ssmClient.getHardcodedSSMParameter(parameterName, this.stackName);
    
    // Returns: {"/pos-prod/cognito/userpool_id": "ap-south-1_Y3OJo6T4R"}
    return {
      [parameterName]: value
    };
  }
}
