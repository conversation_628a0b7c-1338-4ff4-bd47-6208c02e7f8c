import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { StoreOrdersApiKeyService } from './store-orders-api-key.service';
import {
  StoreOrdersApiKey,
  StoreOrdersApiKeys,
} from './entities/store-orders-api-key.entity';
import { CreateStoreOrdersApiKeyInput } from './dto/create-store-orders-api-key.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus } from '@nestjs/common';
import { UpdateStoreOrdersApiKeyInput } from './dto/update-store-orders-api-key.input';

@Resolver(() => StoreOrdersApiKey)
export class StoreOrdersApiKeyResolver {
  constructor(
    private readonly service: StoreOrdersApiKeyService,
    private readonly successHandler: Success<PERSON><PERSON><PERSON>,
    private readonly errorHandler: <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  ) {}

  @Mutation(() => StoreOrdersApiKey, { name: 'createStoreOrdersApiKey' })
  async createApiKey(@Args('input') input: CreateStoreOrdersApiKeyInput) {
    try {
      const data = await this.service.create(input);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create API key',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => StoreOrdersApiKey, { name: 'updateStoreOrdersApiKey' })
  async updateApiKey(@Args('input') input: UpdateStoreOrdersApiKeyInput) {
    try {
      const data = await this.service.update(input);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update API key',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => StoreOrdersApiKeys, { name: 'listStoreOrdersApiKeys' })
  async listStoreOrdersApiKeys() {
    try {
      const data = await this.service.listStoreOrdersApiKeys();
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to fetch API keys',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
