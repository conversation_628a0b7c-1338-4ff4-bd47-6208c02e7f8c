import { Test, TestingModule } from '@nestjs/testing';
import { StoreOrdersApiKeyResolver } from './store-orders-api-key.resolver';
import { StoreOrdersApiKeyService } from './store-orders-api-key.service';

describe('StoreOrdersApiKeyResolver', () => {
  let resolver: StoreOrdersApiKeyResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [StoreOrdersApiKeyResolver, StoreOrdersApiKeyService],
    }).compile();

    resolver = module.get<StoreOrdersApiKeyResolver>(StoreOrdersApiKeyResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
