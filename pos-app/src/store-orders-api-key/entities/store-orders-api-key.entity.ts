import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class StoreOrdersApiKeyData {
  @Field(() => String, { description: 'Unique API Key' })
  apiKey: string;

  @Field(() => String, { description: 'Name associated with the API key' })
  name: string;

  @Field(() => [String], { description: 'Store IDs linked to the API key' })
  storeIds: string[];

  @Field(() => String, { description: 'Creation timestamp' })
  createdAt: string;

  @Field(() => String, { description: 'Last updated timestamp' })
  updatedAt: string;
}

@ObjectType()
export class StoreOrdersApiKey {
  @Field(() => StoreOrdersApiKeyData, { nullable: true })
  data: StoreOrdersApiKeyData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class StoreOrdersApiKeys {
  @Field(() => [StoreOrdersApiKeyData], { nullable: true })
  data: StoreOrdersApiKeyData[];

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
