import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateStoreOrdersApiKeyInput } from './dto/create-store-orders-api-key.input';
import { CreateStoreOrdersApiKeyHandler } from './lib/create-store-orders-api-key';
import { AppConfigParameters } from 'src/config/config';
import { UpdateStoreOrdersApiKeyInput } from './dto/update-store-orders-api-key.input';
import { UpdateStoreOrdersApiKeyHandler } from './lib/update-store-orders-api-key';
import { ListStoreOrdersApiKeysHandler } from './lib/create-store-orders-api-key copy';

@Injectable()
export class StoreOrdersApiKeyService {
  constructor(
    private readonly configService: ConfigService,
    private readonly docClient: AppDocumentClient,
    private readonly ssmClient: AppSsmClient,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async create(createStoreOrdersApiKeyInput: CreateStoreOrdersApiKeyInput) {
    const createHandler = new CreateStoreOrdersApiKeyHandler(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return createHandler.createStoreOrdersApiKey(createStoreOrdersApiKeyInput);
  }

  async update(input: UpdateStoreOrdersApiKeyInput) {
    const updateHandler = new UpdateStoreOrdersApiKeyHandler(
      this.configService,
      this.docClient,
      this.configParameters,
    );
    return updateHandler.updateStoreOrdersApiKey(input);
  }

  async listStoreOrdersApiKeys() {
    const listHandler = new ListStoreOrdersApiKeysHandler(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return listHandler.listStoreOrdersApiKeys(); // Calls the handler to fetch the list of API keys
  }
}
