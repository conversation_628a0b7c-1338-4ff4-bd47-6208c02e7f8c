import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { UpdateStoreOrdersApiKeyInput } from '../dto/update-store-orders-api-key.input';
import { AppConfigParameters } from 'src/config/config';

export class UpdateStoreOrdersApiKeyHandler {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateStoreOrdersApiKey(
    updateStoreOrdersApiKeyInput: UpdateStoreOrdersApiKeyInput,
  ) {
    posLogger.info('StoreOrdersApiKey', 'updateStoreOrdersApiKey', {
      input: updateStoreOrdersApiKeyInput,
    });

    try {
      const STORE_ORDERS_API_KEYS_TABLE =
        await this.configParameters.getStoreOrdersApiKeysTableName();

      const { apiKey, storeIds, name } = updateStoreOrdersApiKeyInput;

      const updateExpression = [];
      const expressionAttributeValues: Record<string, any> = {};
      const expressionAttributeNames: Record<string, string> = {};

      if (storeIds) {
        updateExpression.push('storeIds = :storeIds');
        expressionAttributeValues[':storeIds'] = storeIds;
      }

      if (name) {
        updateExpression.push('#name = :name');
        expressionAttributeValues[':name'] = name;
        expressionAttributeNames['#name'] = 'name';
      }

      const conditionExpression = 'attribute_exists(apiKey)';

      const command = new UpdateCommand({
        TableName: STORE_ORDERS_API_KEYS_TABLE,
        Key: { apiKey },
        UpdateExpression: `SET ${updateExpression.join(', ')}`,
        ExpressionAttributeValues: expressionAttributeValues,
        ExpressionAttributeNames: expressionAttributeNames,
        ConditionExpression: conditionExpression,
        ReturnValues: 'ALL_NEW',
      });

      const result = await this.docClient.updateItem(command);

      posLogger.info('StoreOrdersApiKey', 'updateStoreOrdersApiKey', {
        success: true,
        result,
      });

      return result.Attributes;
    } catch (e) {
      posLogger.error('StoreOrdersApiKey', 'updateStoreOrdersApiKey', {
        error: e,
      });
      throw e;
    }
  }
}
