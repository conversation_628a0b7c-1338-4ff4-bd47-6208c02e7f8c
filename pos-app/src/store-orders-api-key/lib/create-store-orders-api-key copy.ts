import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { ScanDB } from 'src/common/helper/scan-table';
import { StoreOrdersApiKeyData } from '../entities/store-orders-api-key.entity'; // Adjust entity import based on your file structure

export class ListStoreOrdersApiKeysHandler {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async listStoreOrdersApiKeys(): Promise<StoreOrdersApiKeyData[]> {
    posLogger.info('StoreOrdersApiKey', 'listStoreOrdersApiKeys', '');
    try {
      // Fetch the table name from configuration
      const STORE_ORDERS_API_KEY_TABLE =
        await this.configParameters.getStoreOrdersApiKeysTableName();

      // Using ScanDB helper to query the database
      const scanDBHandler = new ScanDB(this.docClient);
      const storeOrdersApiKeys = await scanDBHandler.scanTable(
        STORE_ORDERS_API_KEY_TABLE,
      );

      // Return the fetched store orders API keys
      return storeOrdersApiKeys;
    } catch (e) {
      // Log error if any occurs during the scan
      posLogger.error('StoreOrdersApiKey', 'listStoreOrdersApiKeys', {
        error: e,
      });
      throw e;
    }
  }
}
