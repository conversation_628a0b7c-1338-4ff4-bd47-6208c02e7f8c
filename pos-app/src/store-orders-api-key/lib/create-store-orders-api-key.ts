import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuid } from 'uuid'; // For generating a unique API key
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateStoreOrdersApiKeyInput } from '../dto/create-store-orders-api-key.input';
import { AppConfigParameters } from 'src/config/config';

export class CreateStoreOrdersApiKeyHandler {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createStoreOrdersApiKey(
    createStoreOrdersApiKeyInput: CreateStoreOrdersApiKeyInput,
  ) {
    posLogger.info('StoreOrdersApiKey', 'createStoreOrdersApiKey', {
      input: createStoreOrdersApiKeyInput,
    });
    try {
      const STORE_ORDERS_API_KEYS_TABLE =
        await this.configParameters.getStoreOrdersApiKeysTableName();

      const apiKey = uuid();

      const timestamp = moment().toISOString();
      const item = {
        apiKey,
        name: createStoreOrdersApiKeyInput.name,
        storeIds: createStoreOrdersApiKeyInput.storeIds,
        createdAt: timestamp,
        updatedAt: timestamp,
      };

      const command = new PutCommand({
        TableName: STORE_ORDERS_API_KEYS_TABLE,
        Item: item,
      });

      await this.docClient.createItem(command);

      posLogger.info('StoreOrdersApiKey', 'createStoreOrdersApiKey', {
        success: true,
        apiKey,
        item,
      });

      return item;
    } catch (e) {
      posLogger.error('StoreOrdersApiKey', 'createStoreOrdersApiKey', {
        error: e,
      });
      throw e;
    }
  }
}
