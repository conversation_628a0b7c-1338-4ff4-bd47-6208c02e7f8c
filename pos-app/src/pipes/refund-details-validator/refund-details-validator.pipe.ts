import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { CreateRefundDetailInput } from 'src/refund-details/dto/create-refund-detail.input';

@Injectable()
export class RefundDetailsValidatorPipe implements PipeTransform {
  transform(refundDetail: CreateRefundDetailInput) {
    const {
      isBackToSource,
      bankAccountName,
      accountHolderName,
      accountNo,
      ifscCode,
      chequeImageKey,
    } = refundDetail;

    if (!isBackToSource) {
      if (
        !(
          bankAccountName &&
          accountHolderName &&
          accountNo &&
          ifscCode &&
          chequeImageKey
        )
      ) {
        throw new BadRequestException('All fields are required');
      }
    }

    return refundDetail;
  }
}
