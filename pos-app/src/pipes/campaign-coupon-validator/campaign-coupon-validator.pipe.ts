import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { CreateCampaignCouponInput } from 'src/campaign-coupons/dto/create-campaign-coupon.input';
import { StoreAllocationType } from 'src/common/enum/store-allocation';
import { CampaignCouponType } from 'src/common/enum/campaign-coupons';

@Injectable()
export class CampaignCouponValidatorPipe implements PipeTransform {
  async transform(_coupon: CreateCampaignCouponInput) {
    const coupon = { ..._coupon };
    const {
      campaignCouponType,
      storeAllocationType,
      entitledStoreIds,
      getYAmount,
      getYPercentage,
      getYQuantity,
      buyXQuantity,
      getYProduct,
      applicableProducts,
      applicableProductsY,
      buyMinXQuantity,
      minOrderValue,
    } = coupon;

    switch (campaignCouponType) {
      case CampaignCouponType.FIXED:
        if (!getYAmount) {
          throw new BadRequestException(
            "getYAmount is required when couponType is 'FIXED'",
          );
        }
        if (getYPercentage || getYQuantity || buyXQuantity || getYProduct) {
          delete coupon?.getYPercentage;
          delete coupon?.getYQuantity;
          delete coupon?.buyXQuantity;
          delete coupon?.getYProduct;
        }
        break;
      case CampaignCouponType.PERCENTAGE:
        if (!getYPercentage) {
          throw new BadRequestException(
            "getYPercentage is required when couponType is 'PERCENTAGE'",
          );
        }
        if (getYPercentage > 100 || getYPercentage < 0) {
          throw new BadRequestException(
            'getYPercentage must be less than or equal to 100 and greater than or equal to 0',
          );
        }

        if (buyMinXQuantity && minOrderValue)
          throw new BadRequestException(
            'buyMinXQuantity and minOrderValue must not be set at the same time',
          );

        if (getYAmount || getYQuantity || buyXQuantity || getYProduct) {
          delete coupon?.getYAmount;
          delete coupon?.getYQuantity;
          delete coupon?.buyXQuantity;
          delete coupon?.getYProduct;
        }
        break;
      case CampaignCouponType.BUY_X_GET_Y:
        if (!getYQuantity || !buyXQuantity) {
          throw new BadRequestException(
            "getYQuantity and buyXQuantity is required when couponType is 'BUY_X_GET_Y'",
          );
        }

        if (getYAmount || getYPercentage || getYProduct) {
          delete coupon?.getYAmount;
          delete coupon?.getYPercentage;
          delete coupon?.getYProduct;
        }

        break;
      case CampaignCouponType.BUY_X_GET_Y_AT_Z:
        if (
          !applicableProducts.length ||
          !applicableProductsY.length ||
          !(getYAmount || getYPercentage)
        ) {
          throw new BadRequestException(
            "applicableProducts and applicableProductsY and either getYAmount or getYPercentage is required when couponType is 'BUY_X_GET_Y_AT_Z'",
          );
        }

        if (getYAmount && getYPercentage) {
          throw new BadRequestException(
            "Either getYAmount or getYPercentage is required, both cannot be passed when couponType is 'BUY_X_GET_Y_AT_Z'",
          );
        }

        if (getYPercentage) {
          if (getYPercentage > 100 || getYPercentage < 0) {
            throw new BadRequestException(
              'getYPercentage must be less than or equal to 100 and greater than or equal to 0',
            );
          }
        }

        if (getYQuantity || getYProduct) {
          delete coupon?.getYQuantity;
          delete coupon?.getYProduct;
        }

        break;
      case CampaignCouponType.FREEBIE:
        if (!getYProduct) {
          throw new BadRequestException(
            "getYProduct  is required when couponType is 'FREEBIE'",
          );
        }

        if (getYAmount || getYPercentage || getYQuantity) {
          delete coupon?.getYAmount;
          delete coupon?.getYPercentage;
          delete coupon?.getYQuantity;
        }

        break;
    }

    switch (storeAllocationType) {
      case StoreAllocationType.ACROSS:
        if (entitledStoreIds && entitledStoreIds.length) {
          delete coupon?.entitledStoreIds;
        }
        break;
      case StoreAllocationType.EACH:
        if (!entitledStoreIds || !entitledStoreIds.length) {
          throw new BadRequestException(
            "entitledStoreIds is required when storeAllocationType is 'EACH'",
          );
        }
        break;
    }

    return coupon;
  }
}
