import {
  Field,
  Float,
  Int,
  ObjectType,
  registerEnumType,
} from '@nestjs/graphql';
import {
  CouponStatus,
  CouponUsageResetType,
  CouponApplicationType,
} from 'src/common/enum/coupons';
import { StoreAllocationType } from 'src/common/enum/store-allocation';
import { CampaignCouponType } from 'src/common/enum/campaign-coupons';

// Register the enum with GraphQL
registerEnumType(CampaignCouponType, { name: 'CampaignCouponType' });
registerEnumType(StoreAllocationType, { name: 'StoreAllocationType' });
registerEnumType(CouponStatus, { name: 'CouponStatus' });
registerEnumType(CouponUsageResetType, { name: 'CouponUsageResetType' });
registerEnumType(CouponApplicationType, { name: 'CouponApplicationType' });

@ObjectType()
export class ApplicableProducts {
  @Field(() => String)
  productId: string;

  @Field(() => String, { nullable: true })
  variantId?: string;
}
@ObjectType()
export class StoreWiseUsageCount {
  @Field(() => String)
  storeId: string;

  @Field(() => Int, { nullable: true })
  usageCount: number;
}
@ObjectType()
export class StoreWiseUsageCountLimit {
  @Field(() => String)
  storeId: string;

  @Field(() => Int, { nullable: true })
  usageLimit: number;
}

@ObjectType()
export class CampaignCouponData {
  @Field(() => String, { nullable: false })
  code: string;

  @Field(() => [StoreWiseUsageCount], { nullable: true })
  storeWiseUsageCount?: StoreWiseUsageCount[];

  @Field(() => [StoreWiseUsageCountLimit], { nullable: true })
  storeUsageLimit?: StoreWiseUsageCountLimit[];

  @Field(() => CouponUsageResetType, { nullable: true })
  usageResetType?: CouponUsageResetType;

  @Field(() => Float, { nullable: true })
  maxOrderValue?: number;

  @Field(() => Boolean, { nullable: true })
  usageLimitForEachStore?: boolean;

  @Field(() => String)
  createdAt: string;

  @Field(() => String)
  updatedAt: string;

  @Field(() => String, { nullable: true })
  description?: string;

  @Field(() => CampaignCouponType, { nullable: false })
  campaignCouponType: CampaignCouponType;

  @Field(() => Int, { nullable: true })
  buyXQuantity?: number;

  @Field(() => Int, { nullable: true })
  buyMinXQuantity?: number;

  @Field(() => CouponStatus, { nullable: true })
  status?: CouponStatus;

  @Field(() => Float, { nullable: true })
  getYAmount?: number;

  @Field(() => Float, { nullable: true })
  getYPercentage?: number;

  @Field(() => Int, { nullable: true })
  getYQuantity?: number;

  @Field(() => ApplicableProducts, { nullable: true })
  getYProduct?: ApplicableProducts;

  @Field(() => Float, { nullable: true })
  minOrderValue?: number;

  @Field(() => Float, { nullable: true })
  maxDiscount?: number;

  @Field(() => [ApplicableProducts], { nullable: true })
  applicableProducts?: ApplicableProducts[];

  @Field(() => [ApplicableProducts], { nullable: true })
  applicableProductsY?: ApplicableProducts[];

  @Field(() => String, { nullable: false })
  startsAt: string;

  @Field(() => String, { nullable: true })
  endsAt?: string;

  @Field(() => StoreAllocationType, { nullable: false })
  storeAllocationType: StoreAllocationType;

  @Field(() => [String], { nullable: true })
  entitledStoreIds?: string[];

  @Field(() => Boolean, { nullable: true })
  isActive?: boolean;

  @Field(() => Int, { nullable: true })
  usageLimit?: number;

  @Field(() => Int, { nullable: true })
  usageCount?: number;

  @Field(() => String, { nullable: true })
  customOrderValueErrorMsg?: string;

  @Field(() => String, { nullable: true })
  promoApplicable?: string;

  @Field(() => [String], { nullable: true })
  promoCoupons?: string[];

  @Field(() => CouponApplicationType, { nullable: true })
  couponApplicationType?: CouponApplicationType;
}

@ObjectType()
export class CampaignCoupons {
  @Field(() => [CampaignCouponData], { nullable: true })
  data: CampaignCouponData[];

  @Field(() => Int, { nullable: true })
  count?: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class CampaignCoupon {
  @Field(() => CampaignCouponData, { nullable: true })
  data: CampaignCouponData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
