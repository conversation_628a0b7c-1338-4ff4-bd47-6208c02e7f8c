export const searchingFilter = new Map<
  string,
  'string' | 'nonString' | 'arrayString' | 'boolean' | 'date'
>([
  ['code', 'string'],
  ['storeAllocationType', 'string'],
  ['entitledStoreIds', 'arrayString'],
  ['status', 'string'],
  ['startsAt', 'date'],
  ['endsAt', 'date'],
  ['createdAt', 'date'],
  ['updatedAt', 'date'],
  ['campaignCouponType', 'string'],
]);

export const sortingFilter = new Map<string, 'string' | 'nonString'>([
  ['createdAt', 'string'],
  ['updatedAt', 'string'],
]);

export const sortingFilterType = {
  createdAt: 'date',
  updatedAt: 'date',
};
