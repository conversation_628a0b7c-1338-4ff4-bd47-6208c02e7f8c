import { Injectable } from '@nestjs/common';
import { CreateCampaignCouponInput } from './dto/create-campaign-coupon.input';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateCampaignCoupon } from './lib/create-campaign-coupon';
import { GetCampaignCoupon } from './lib/get-campaign-coupon';
import { UpdateCampaignCouponInput } from './dto/update-campaign-coupon.input';
import { UpdateCampaignCoupon } from './lib/update-campaign-coupon';
import { ListCampaignCouponInput } from './dto/list-campaign-coupons.input';
import { QueryCampaignCoupons } from './lib/list-campaign-coupons';
import { RemoveCampaignCoupon } from './lib/delete-campaign-coupon';
import { AppConfigParameters } from 'src/config/config';
import { CronService } from 'src/cron/cron.service';
import { posLogger } from 'src/common/logger';

@Injectable()
export class CampaignCouponsService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private readonly cronService: CronService,
  ) {}
  async create(createCampaignCouponInput: CreateCampaignCouponInput) {
    const createHandler = new CreateCampaignCoupon(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return createHandler.createCampaignCoupon(createCampaignCouponInput);
  }

  async findOne(code: string) {
    const getHandler = new GetCampaignCoupon(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.getCampaignCoupon(code);
  }

  async findAll(listCampaignCouponInput: ListCampaignCouponInput) {
    const queryCampaignCouponsHandler = new QueryCampaignCoupons(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return queryCampaignCouponsHandler.queryCampaignCoupons(
      listCampaignCouponInput,
    );
  }

  async update(
    code: string,
    updateCampaignCouponInput: UpdateCampaignCouponInput,
  ) {
    const updateHandler = new UpdateCampaignCoupon(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    return updateHandler.updateCampaignCoupon(code, updateCampaignCouponInput);
  }

  async remove(code: string) {
    const removeHandler = new RemoveCampaignCoupon(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return removeHandler.removeCampaignCoupon(code);
  }
  async resetDailyUsage() {
    this.cronService.updateDailyUsageCountCampaignCoupons().catch((error) => {
      posLogger.error('COUPONS', 'resetUsage', error);
    });
    return { message: 'Reseting Daily Usage task has been initiated' };
  }
  async resetWeeklyUsage() {
    this.cronService.updateWeeklyUsageCountCampaignCoupons().catch((error) => {
      posLogger.error('COUPONS', 'resetUsage', error);
    });
    return { message: 'Reseting Usage task has been initiated' };
  }
  async resetMonthlyUsage() {
    this.cronService.updateMonthlyUsageCountCampaignCoupons().catch((error) => {
      posLogger.error('COUPONS', 'resetUsage', error);
    });
    return { message: 'Reseting Usage task has been initiated' };
  }
}
