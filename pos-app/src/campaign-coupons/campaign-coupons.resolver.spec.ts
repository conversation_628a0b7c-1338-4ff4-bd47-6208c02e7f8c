import { Test, TestingModule } from '@nestjs/testing';
import { CampaignCouponsResolver } from './campaign-coupons.resolver';
import { CampaignCouponsService } from './campaign-coupons.service';

describe('CampaignCouponsResolver', () => {
  let resolver: CampaignCouponsResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CampaignCouponsResolver, CampaignCouponsService],
    }).compile();

    resolver = module.get<CampaignCouponsResolver>(CampaignCouponsResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
