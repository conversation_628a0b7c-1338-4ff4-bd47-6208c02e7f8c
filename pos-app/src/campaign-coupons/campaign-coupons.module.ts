import { Module } from '@nestjs/common';
import { CampaignCouponsService } from './campaign-coupons.service';
import { CampaignCouponsResolver } from './campaign-coupons.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { CampaignCouponValidatorPipe } from 'src/pipes/campaign-coupon-validator/campaign-coupon-validator.pipe';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { ConfigParametersModule } from 'src/config/config.module';
import { CronService } from 'src/cron/cron.service';
import { ShopifyModule } from 'src/common/shopify/shopify.module';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';

@Module({
  imports: [
    S3ClientModule,
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
    ShopifyModule,
  ],
  providers: [
    CampaignCouponsResolver,
    CampaignCouponsService,
    CampaignCouponValidatorPipe,
    SuccessHandler,
    ErrorHandler,
    CronService,
  ],
})
export class CampaignCouponsModule {}
