import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';

export class BxGyAtZCoupon {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validateBxGyAtZCoupon(minOrderValue, total) {
    if (minOrderValue && minOrderValue > total) return false;
    return true;
  }

  async calculateCartDiscount(cartItems, discountPercentage) {
    let totalDiscount = 0;

    const discountedItems = cartItems.map((item) => {
      const pricePerItem = parseFloat(item.price);
      const quantity = item.quantity;
      const totalItemPrice = pricePerItem * quantity;
      const discountAmount = (totalItemPrice * discountPercentage) / 100;
      totalDiscount += discountAmount;

      return {
        ...item,
        discountAmount: discountAmount.toFixed(2),
      };
    });

    return {
      discountedItems,
      totalDiscount: totalDiscount.toFixed(2),
    };
  }

  async getBxGyAtZDiscount(
    totalAmount,
    quantity,
    getYAmount = null,
    getYPercentage = null,
    maxDiscount = null,
    couponApplicationType = null,
    cartTotal,
    applicableItemsY,
  ) {
    let finalDiscount;

    if (getYAmount) {
      if (!couponApplicationType) {
        finalDiscount = Math.round(Math.min(Number(getYAmount || totalAmount)));
      } else {
        if (couponApplicationType == 'CART') {
          finalDiscount = Math.round(
            Math.min(Number(getYAmount || totalAmount)),
          );
        }
        if (couponApplicationType == 'PRODUCT') {
          finalDiscount = Math.round(
            Math.min(Number(quantity * getYAmount || totalAmount)),
          );
        }
      }
    }
    if (getYPercentage) {
      if (!couponApplicationType) {
        finalDiscount = Math.round(
          Number((totalAmount * getYPercentage) / 100),
        );
      } else {
        if (couponApplicationType == 'CART') {
          finalDiscount = Math.round(
            Number((cartTotal * getYPercentage) / 100),
          );
        }
        if (couponApplicationType == 'PRODUCT') {
          const finalCalculated = this.calculateCartDiscount(
            applicableItemsY,
            getYPercentage,
          );
          finalDiscount = (await finalCalculated).totalDiscount;
        }
      }
    }

    return {
      finalDiscount: Math.min(
        maxDiscount || Infinity,
        totalAmount,
        finalDiscount,
      ),
    };
  }
}
