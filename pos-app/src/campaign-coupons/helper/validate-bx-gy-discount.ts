import { getCartCount } from 'src/common/helper/get-cart-count';

export const getBxGyDiscount = (cartItems, discountedQty: number) => {
  const products = [];
  //spread  all the products with quantity 1 and sort in accending order
  cartItems?.forEach((c) => {
    products.push(...Array(c.quantity || 1).fill({ ...c, quantity: 1 }));
    products.sort((a, b) => (Number(b.price) > Number(a.price) ? 1 : -1));
  });

  //slice it out discounted items
  const discountedItems = products.slice(-discountedQty);

  //merge the same products and sum its quantity
  const mergedProducts = Object.values(
    discountedItems.reduce((acc, curr) => {
      const key = `${curr.productId}-${curr.variantId}`;
      if (acc[key]) {
        acc[key].quantity += curr.quantity;
      } else {
        acc[key] = { ...curr };
      }
      return acc;
    }, {}),
  );

  return {
    discount: Number(discountedItems.reduce((a, b) => a + Number(b.price), 0)),
    freeProducts: mergedProducts,
  };
};

export const getBxGyFreeQuantity = (
  getYQuantity: number,
  buyXQuantity: number,
  cartItems,
) => {
  const totalItems = getCartCount(cartItems);
  return (
    Math.floor(
      (totalItems -
        (totalItems * buyXQuantity) / (buyXQuantity + getYQuantity)) /
        getYQuantity,
    ) * getYQuantity
  );
};

export const validateBxGy = (coupon, cartItems) => {
  const { buyXQuantity, getYQuantity } = coupon;
  const totalItems = getCartCount(cartItems);

  if (totalItems < buyXQuantity + getYQuantity) {
    return false;
  }
  return true;
};
