import { CouponStatus } from 'src/common/enum/coupons';
import { StoreAllocationType } from 'src/common/enum/store-allocation';
import { getPriceRuleStatus } from 'src/common/helper/get-price-rule-status';
import { CampaignCouponData } from '../entities/campaign-coupon.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { validateCouponUsageLimit } from 'src/common/helper/validate-usage-limits';
import { posLogger } from 'src/common/logger';

export const validatingCampaignCoupon = (
  campaignCouponData: CampaignCouponData,
  storeId: string,
  cartTotal: number,
  isEditingQuotation = false,
) => {
  const {
    minOrderValue,
    maxOrderValue,
    startsAt,
    endsAt,
    entitledStoreIds,
    storeAllocationType,
    code,
    isActive,
    usageLimit,
    usageCount: accumulatedUsageCount,
    usageLimitForEachStore = false,
    storeWiseUsageCount = [],
    storeUsageLimit = [],
  } = campaignCouponData;

  try {
    let usageCount = null;

    if (!usageLimitForEachStore) {
      usageCount = accumulatedUsageCount;
      const { isValidated } = validateCouponUsageLimit(usageLimit, usageCount);
      if (!isValidated) {
        return { isValidated: false };
      }
    } else {
      usageCount =
        storeWiseUsageCount.find((usage) => usage.storeId === storeId)
          ?.usageCount || 0;
      const storeUsageLimitCount =
        storeUsageLimit?.find((limit) => limit.storeId === storeId) || null;

      const { isValidated } = validateCouponUsageLimit(
        storeUsageLimitCount?.usageLimit,
        usageCount,
      );
      if (!isValidated) {
        return { isValidated: false };
      }
    }

    const couponStatus = getPriceRuleStatus(
      startsAt,
      endsAt,
      isActive,
      isEditingQuotation,
    );

    if (
      storeAllocationType === StoreAllocationType.EACH &&
      entitledStoreIds?.length
    ) {
      if (!entitledStoreIds.includes(storeId)) {
        throw new CustomError(
          `Store ID ${storeId} cannot apply this coupon.`,
          400,
        );
      }
    }

    if (couponStatus !== CouponStatus.ACTIVE) {
      throw new CustomError(
        `Coupon ${code} is in ${couponStatus} status.`,
        400,
      );
    }

    if (minOrderValue && cartTotal < minOrderValue) {
      throw new CustomError('Cart total is less than min order value.', 400);
    }
    if (maxOrderValue && cartTotal > maxOrderValue) {
      throw new CustomError('Cart total is greater than max order value ', 400);
    }

    return { isValidated: true };
  } catch (error) {
    posLogger.error('campaign-coupon', 'validatingCampaignCoupon', error);
    throw error;
  }
};
