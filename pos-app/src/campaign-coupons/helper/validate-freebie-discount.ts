import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { ValidateProducts } from 'src/common/helper/validate-products';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';

export class FreebieCoupon {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validateFreebieCoupon(minOrderValue, total, buyXQuantity, totalItems) {
    if (minOrderValue && minOrderValue > total) return false;
    if (buyXQuantity && buyXQuantity > totalItems) return false;
    return true;
  }

  async getFreebieQuantity(buyXQuantity, totalItems) {
    const yQuantity = Math.floor(totalItems / buyXQuantity);
    return yQuantity;
  }

  async getFreebieDiscount(getYProduct, freebieQuantity) {
    const handler = new ValidateProducts(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    const [res] = await handler.validateProducts([getYProduct]);

    const { product, variant, price } = res;
    const finalDiscountedPrice = price * freebieQuantity;
    return {
      productId: product.id,
      variantId: variant.id,
      finalDiscountedPrice,
      price,
    };

    // const {} = res
  }
}
