export const getApplicableProductItems = (applicableProducts, cartItems) => {
  let applicableItems = cartItems;
  if (applicableProducts && applicableProducts.length > 0) {
    applicableItems = cartItems.filter((item) =>
      applicableProducts.some(
        (product) =>
          product.productId === item.productId &&
          (!product.variantId || item.variantId === product.variantId),
      ),
    );
  }

  return applicableItems;
};
