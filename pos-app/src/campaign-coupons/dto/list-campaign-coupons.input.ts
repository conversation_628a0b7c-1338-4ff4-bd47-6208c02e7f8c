import { InputType, Field } from '@nestjs/graphql';
import { CampaignCouponType } from 'src/common/enum/campaign-coupons';

@InputType()
export class CampaignCouponTermSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  startsAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  endsAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  updatedAt?: string;
}

@InputType()
export class CampaignCouponTextSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  code?: string;

  @Field(() => CampaignCouponType, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  campaignCouponType?: CampaignCouponType;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  storeAllocationType?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  entitledStoreIds: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  status?: string;
}

@InputType()
export class CampaignCouponSortingFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class ListCampaignCouponInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size: string;

  @Field(() => CampaignCouponTermSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  termSearchFields: CampaignCouponTermSearchFieldsInput;

  @Field(() => CampaignCouponTextSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  textSearchFields: CampaignCouponTextSearchFieldsInput;

  @Field(() => CampaignCouponSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { createdAt: 'desc' },
  })
  sortBy: CampaignCouponSortingFieldsInput;
}
