import {
  InputType,
  Field,
  Int,
  Float,
  registerEnumType,
} from '@nestjs/graphql';
import { StoreAllocationType } from 'src/common/enum/store-allocation';
import { CampaignCouponType } from 'src/common/enum/campaign-coupons';
import {
  ApplicableProductsInput,
  StoreWiseUsageCountInput,
} from './create-campaign-coupon.input';

import {
  CouponUsageResetType,
  CouponApplicationType,
} from 'src/common/enum/coupons';
registerEnumType(CouponUsageResetType, { name: 'CouponUsageResetType' });
registerEnumType(CouponApplicationType, { name: 'CouponApplicationType' });

@InputType()
export class UpdateCampaignCouponInput {
  @Field(() => String, { nullable: false })
  code: string;

  @Field(() => String, { nullable: true })
  description?: string;

  @Field(() => CampaignCouponType, { nullable: false })
  campaignCouponType: CampaignCouponType;

  @Field(() => Int, { nullable: true })
  buyXQuantity?: number;

  @Field(() => Float, { nullable: true })
  getYAmount?: number;

  @Field(() => Float, { nullable: true })
  getYPercentage?: number;

  @Field(() => Int, { nullable: true })
  getYQuantity?: number;

  @Field(() => Int, { nullable: true })
  buyMinXQuantity?: number;

  @Field(() => Float, { nullable: true })
  minOrderValue?: number;

  @Field(() => Float, { nullable: true })
  maxDiscount?: number;

  @Field(() => [ApplicableProductsInput], { nullable: true })
  applicableProducts?: ApplicableProductsInput[];

  @Field(() => [ApplicableProductsInput], { nullable: true })
  applicableProductsY?: ApplicableProductsInput[];

  @Field(() => String, { nullable: false })
  startsAt: string;

  @Field(() => String, { nullable: true })
  endsAt?: string;

  @Field(() => StoreAllocationType, { nullable: true })
  storeAllocationType: StoreAllocationType;

  @Field(() => [String], { nullable: true })
  entitledStoreIds?: string[];

  @Field(() => ApplicableProductsInput, { nullable: true })
  getYProduct?: ApplicableProductsInput;

  @Field(() => Boolean, { nullable: true })
  isActive?: boolean;

  @Field(() => Int, { nullable: true })
  usageLimit?: number;

  @Field(() => Float, { nullable: true })
  maxOrderValue?: number;

  @Field(() => Boolean, { nullable: true })
  usageLimitForEachStore?: boolean;

  @Field(() => [StoreWiseUsageCountInput], { nullable: true })
  storeUsageLimit?: StoreWiseUsageCountInput[];

  @Field(() => CouponUsageResetType, { nullable: true })
  usageResetType?: CouponUsageResetType;

  @Field(() => String, { nullable: true })
  customOrderValueErrorMsg?: string;

  @Field(() => String, { nullable: true })
  promoApplicable?: string;

  @Field(() => [String], { nullable: true })
  promoCoupons?: string[];

  @Field(() => CouponApplicationType, { nullable: true })
  couponApplicationType?: CouponApplicationType;
}
