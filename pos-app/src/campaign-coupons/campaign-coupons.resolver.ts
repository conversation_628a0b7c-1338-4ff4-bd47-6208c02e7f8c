import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { CampaignCouponsService } from './campaign-coupons.service';
import { CreateCampaignCouponInput } from './dto/create-campaign-coupon.input';
import { UpdateCampaignCouponInput } from './dto/update-campaign-coupon.input';
import { HttpStatus, UseGuards, UsePipes } from '@nestjs/common';
import { ListCampaignCouponInput } from './dto/list-campaign-coupons.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import {
  CampaignCoupon,
  CampaignCoupons,
} from './entities/campaign-coupon.entity';
import { CampaignCouponValidatorPipe } from 'src/pipes/campaign-coupon-validator/campaign-coupon-validator.pipe';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { AdminGuard, CRMGuard, StaffGuard } from 'src/auth/roles.guard';
import { Response } from 'src/products/entities/product.entity';

@Resolver(() => CampaignCoupon)
@UseGuards(CustomAuthGuard, CRMGuard)
export class CampaignCouponsResolver {
  constructor(
    private readonly campaignCouponsService: CampaignCouponsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => CampaignCoupon)
  @UseGuards(AdminGuard)
  @UsePipes(CampaignCouponValidatorPipe)
  async createCampaignCoupon(
    @Args('createCampaignCouponInput')
    createCampaignCouponInput: CreateCampaignCouponInput,
  ) {
    try {
      const data = await this.campaignCouponsService.create(
        createCampaignCouponInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create campaign coupon',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => CampaignCoupon, { name: 'getCampaignCoupon' })
  @UseGuards(StaffGuard)
  async findOne(@Args('code', { type: () => String }) code: string) {
    try {
      const data = await this.campaignCouponsService.findOne(code);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get campaign coupon',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => CampaignCoupons, { name: 'listCampaignCoupons' })
  @UseGuards(StaffGuard)
  async findAll(
    @Args('listCampaignCouponInput', { nullable: true })
    listCampaignCouponInput: ListCampaignCouponInput,
  ) {
    try {
      const { data, count } = await this.campaignCouponsService.findAll(
        listCampaignCouponInput,
      );
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list campaign coupons',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => CampaignCoupon, { name: 'updateCampaignCoupon' })
  @UseGuards(AdminGuard)
  @UsePipes(CampaignCouponValidatorPipe)
  async updateCampaignCoupon(
    @Args('updateCampaignCouponInput')
    updateCampaignCouponInput: UpdateCampaignCouponInput,
  ) {
    try {
      const data = await this.campaignCouponsService.update(
        updateCampaignCouponInput.code,
        updateCampaignCouponInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update campaign coupon',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => CampaignCoupon, { name: 'deleteCampaignCoupon' })
  @UseGuards(AdminGuard)
  async removeCampaignCoupon(
    @Args('code', { type: () => String }) code: string,
  ) {
    try {
      const data = await this.campaignCouponsService.remove(code);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to delete campaign coupon',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Response, { name: 'resetDailyUsage' })
  @UseGuards(AdminGuard)
  async resetDailyUsage() {
    return await this.campaignCouponsService.resetDailyUsage();
  }

  @Query(() => Response, { name: 'resetWeeklyUsage' })
  @UseGuards(AdminGuard)
  async resetWeeklyUsage() {
    return await this.campaignCouponsService.resetWeeklyUsage();
  }

  @Query(() => Response, { name: 'resetMonthlyUsage' })
  @UseGuards(AdminGuard)
  async resetMonthlyUsage() {
    return await this.campaignCouponsService.resetMonthlyUsage();
  }
}
