import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CampaignCouponData } from '../entities/campaign-coupon.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';

export class GetCampaignCoupon {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getCampaignCoupon(code: string): Promise<CampaignCouponData> {
    posLogger.info('campaign-coupon', 'getCampaignCoupon', {
      input: { code },
    });
    try {
      const STORE_COUPON_TABLE =
        await this.configParameters.getCampaignCouponTableName();

      const param = new GetCommand({
        TableName: STORE_COUPON_TABLE,
        Key: { code },
      });

      const { Item: data } = await this.docClient.getItem(param);
      if (data && !data.isDeleted) return data;

      throw new CustomError(
        `Invalid CODE! No campaign coupon found for given code ${code}.`,
        404,
      );
    } catch (e) {
      posLogger.error('campaign-coupon', 'getCampaignCouponByCode', {
        error: e,
      });
      throw e;
    }
  }
}
