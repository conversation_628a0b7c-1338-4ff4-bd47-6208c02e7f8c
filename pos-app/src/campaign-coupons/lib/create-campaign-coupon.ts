import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import moment from 'moment';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateCampaignCouponInput } from '../dto/create-campaign-coupon.input';
import { getPriceRuleStatus } from 'src/common/helper/get-price-rule-status';
import { CampaignCouponData } from '../entities/campaign-coupon.entity';
import { AppConfigParameters } from 'src/config/config';

export class CreateCampaignCoupon {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createCampaignCoupon(
    createCampaignCouponInput: CreateCampaignCouponInput,
  ): Promise<CampaignCouponData> {
    try {
      posLogger.info('campaign-coupon', 'createCampaignCoupon', {
        input: { createCampaignCouponInput },
      });

      const STORE_COUPON_TABLE =
        await this.configParameters.getCampaignCouponTableName();

      const { startsAt, endsAt } = createCampaignCouponInput;
      const status = getPriceRuleStatus(startsAt, endsAt);

      const campaignCouponPayload: CampaignCouponData = {
        ...createCampaignCouponInput,
        startsAt: moment(startsAt).toISOString(),
        endsAt: endsAt ? moment(endsAt).toISOString() : null,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
        usageCount: 0,
        isActive: true,
        status,
      };
      const campaignCouponCommand = new PutCommand({
        TableName: STORE_COUPON_TABLE,
        Item: campaignCouponPayload,
        ConditionExpression: 'attribute_not_exists(code)',
      });
      await this.docClient.createItem(campaignCouponCommand);

      return campaignCouponPayload;
    } catch (error) {
      posLogger.error('campaign-coupon', 'createCampaignCoupon', error);
      throw error;
    }
  }
}
