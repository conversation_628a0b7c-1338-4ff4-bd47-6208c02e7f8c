// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CampaignCouponData } from '../entities/campaign-coupon.entity';
import moment from 'moment';
import { AppConfigParameters } from 'src/config/config';

export class RemoveCampaignCoupon {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async removeCampaignCoupon(code: string): Promise<CampaignCouponData> {
    posLogger.info('campaign-coupon', 'removeCampaignCoupon', {
      input: { code },
    });
    try {
      const STORE_COUPON_TABLE =
        await this.configParameters.getCampaignCouponTableName();

      const command = new UpdateCommand({
        TableName: STORE_COUPON_TABLE,
        Key: {
          code: code,
        },
        UpdateExpression: 'SET #isDeleted = :isDeleted, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
          '#isDeleted': 'isDeleted',
        },
        ExpressionAttributeValues: {
          ':isDeleted': true,
          ':updatedAt': moment().toISOString(),
        },
        ConditionExpression: 'attribute_exists(code)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: CampaignCouponData } =
        await this.docClient.updateItem(command);

      if (Attributes) return Attributes;
    } catch (error) {
      posLogger.error('campaign-coupon', 'removeCampaignCoupon', { error });
      throw error;
    }
  }
}
