// modules

import { filterFormatter } from 'src/common/helper/filter-helper';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { ListCampaignCouponInput } from '../dto/list-campaign-coupons.input';
import {
  CampaignCouponData,
  CampaignCoupons,
} from '../entities/campaign-coupon.entity';
import { StoreAllocationType } from 'src/common/enum/store-allocation';
import { AppConfigParameters } from 'src/config/config';

export class QueryCampaignCoupons {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryCampaignCoupons(
    filter: ListCampaignCouponInput,
  ): Promise<CampaignCoupons> {
    posLogger.info('campaign-coupon', 'queryCampaignCoupons', {
      input: filter,
    });
    try {
      const STORE_COUPON_TABLE =
        await this.configParameters.getCampaignCouponTableName();

      const { textSearchFields } = filter;
      const shouldFilter = [];

      if (textSearchFields?.entitledStoreIds) {
        shouldFilter.push(
          {
            match_phrase_prefix: {
              storeAllocationType: StoreAllocationType.ACROSS,
            },
          },
          {
            match_phrase_prefix: {
              entitledStoreIds: textSearchFields?.entitledStoreIds,
            },
          },
        );

        delete textSearchFields.entitledStoreIds;
        filter = { ...filter, textSearchFields };
      }

      const { searchArray, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: STORE_COUPON_TABLE,
        });

        size = bodyRes?.count;
      }
      if (searchArray.length) {
        const { body: bodyRes } = await esHandler.count({
          index: STORE_COUPON_TABLE,
          body: {
            query: {
              bool: {
                should: [...shouldFilter],
                minimum_should_match: 1,
                must: [...searchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: STORE_COUPON_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
                minimum_should_match: 1,
                should: [...shouldFilter],
              },
            },
            sort: [sortObject],
          },
        });
        const data: CampaignCouponData[] = response.body.hits.hits.map(
          (hit) => hit._source,
        );

        return { data, count: bodyRes?.count };
      }
      const { body: bodyRes } = await esHandler.count({
        index: STORE_COUPON_TABLE,
      });

      const response = await esHandler.search({
        index: STORE_COUPON_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data: CampaignCouponData[] = response.body.hits.hits.map(
        (hit) => hit._source,
      );

      return { data, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('campaign-coupon', 'queryCampaignCoupons', { error: e });
      throw e;
    }
  }
}
