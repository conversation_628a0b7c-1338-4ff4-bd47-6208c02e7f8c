import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { UpdateCampaignCouponInput } from '../dto/update-campaign-coupon.input';
import moment from 'moment';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { posLogger } from 'src/common/logger';
import { CampaignCouponData } from '../entities/campaign-coupon.entity';
import { getPriceRuleStatus } from 'src/common/helper/get-price-rule-status';
import { AppConfigParameters } from 'src/config/config';

export class UpdateCampaignCoupon {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}
  async updateCampaignCoupon(
    code: string = '',
    {
      code: {},
      startsAt,
      endsAt,
      isActive,
      ...updateCampaignCouponInput
    }: UpdateCampaignCouponInput,
  ): Promise<CampaignCouponData> {
    try {
      posLogger.info('campaign-coupon', 'updateCampaignCoupon', {
        input: { code, ...updateCampaignCouponInput },
      });

      const STORE_COUPON_TABLE =
        await this.configParameters.getCampaignCouponTableName();

      const status = getPriceRuleStatus(startsAt, endsAt, isActive);

      let updateExpressionString: string =
        'SET #updatedAt = :updatedAt, #status = :status, startsAt = :startsAt, endsAt = :endsAt, isActive = :isActive, ';
      const expressionAttributeNames: Record<string, string> = {
        '#status': 'status',
        '#updatedAt': 'updatedAt',
      };
      const expressionAttributesValues: Record<string, any> = {
        ':status': status,
        ':updatedAt': moment().toISOString(),
        ':startsAt': moment(startsAt).toISOString(),
        ':endsAt': endsAt ? moment(endsAt).toISOString() : null,
        ':isActive': isActive || false,
      };

      Object.keys(updateCampaignCouponInput).map((key) => {
        updateExpressionString += `#${key} = :${key}, `;
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributesValues[`:${key}`] = updateCampaignCouponInput[key];
      });

      updateExpressionString = updateExpressionString.substring(
        0,
        updateExpressionString.length - 2,
      );

      const command = new UpdateCommand({
        TableName: STORE_COUPON_TABLE,
        Key: { code },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributesValues,
        ConditionExpression: 'attribute_exists(code)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: CampaignCouponData } =
        await this.docClient.updateItem(command);

      return Attributes;
    } catch (e) {
      posLogger.error('campaign-coupon', 'UpdateCampaignCoupon', e);
      throw e;
    }
  }
}
