import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { CampaignCouponType } from 'src/common/enum/campaign-coupons';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GetCampaignCoupon } from './get-campaign-coupon';
import {
  getBxGyDiscount,
  getBxGyFreeQuantity,
  validateBxGy,
} from '../helper/validate-bx-gy-discount';
import { getApplicableProductItems } from '../helper/applicable-product-items';
import { getTotalAmount } from 'src/common/helper/get-total-amount';
import { FreebieCoupon } from '../helper/validate-freebie-discount';
import { getCartCount } from 'src/common/helper/get-cart-count';
import { CampaignCouponData } from '../entities/campaign-coupon.entity';
import { validatingCampaignCoupon } from '../helper/validate-campaign-coupon';
import { AppConfigParameters } from 'src/config/config';
import { BxGyAtZCoupon } from '../helper/validate-bx-gy-atz-discount';
import { validateMinOrderValue } from 'src/common/helper/validate-min-order-value';
import { validateMinCartQuantity } from 'src/common/helper/validate-min-product-quantity';
import { getBuyXGetYProducts } from 'src/common/helper/get-bx-gy-products-by-min-quantity';

export class CalculateCampaignCouponDiscountAmount {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async calculateCampaignCouponDiscountAmount({
    code: campaignCoupon,
    cartItems,
    cartTotal,
    storeId,
    promotionalDiscountAmount,
    isEditingQuotation = false,
  }) {
    const res = { isValidated: false, discount: 0, products: null };

    try {
      const handler = new GetCampaignCoupon(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const coupon: CampaignCouponData =
        await handler.getCampaignCoupon(campaignCoupon);

      const { isValidated } = validatingCampaignCoupon(
        coupon,
        storeId,
        cartTotal,
        isEditingQuotation,
      );

      if (!isValidated) {
        return res;
      }
      const {
        campaignCouponType,
        buyXQuantity,
        getYQuantity,
        getYPercentage,
        getYAmount,
        maxDiscount,
        minOrderValue,
        getYProduct,
        buyMinXQuantity,
        applicableProducts: couponApplicableProducts,
        applicableProductsY: couponApplicableProductsY,
        promoApplicable,
        promoCoupons,
        couponApplicationType,
      } = coupon;

      let applicableItems = cartItems;
      let applicableItemsY = cartItems;

      if (couponApplicableProducts?.length) {
        applicableItems = getApplicableProductItems(
          couponApplicableProducts,
          cartItems,
        );
      }

      if (!applicableItems.length) {
        return res;
      }

      switch (campaignCouponType) {
        case CampaignCouponType.FIXED:
          let totalApplicableQuantity = 1;

          if (couponApplicableProducts?.length) {
            totalApplicableQuantity = applicableItems.reduce(
              (total, { quantity }) => {
                return total + quantity;
              },
              0,
            );
          }

          res.discount = Math.min(
            maxDiscount || Infinity,
            cartTotal,
            totalApplicableQuantity * getYAmount,
          );

          res.isValidated = true;
          break;

        case CampaignCouponType.PERCENTAGE:
          if (!validateMinCartQuantity(buyMinXQuantity, applicableItems)) {
            res.isValidated = false;
            res.discount = 0;
            break;
          }
          const perDiscount =
            getTotalAmount(applicableItems) * (getYPercentage / 100);

          res.discount = Math.min(
            maxDiscount || Infinity,
            cartTotal,
            perDiscount,
          );
          res.isValidated = true;
          break;

        case CampaignCouponType.BUY_X_GET_Y:
          const isValid = validateBxGy(coupon, applicableItems);

          if (!isValid) {
            return res;
          }
          const discountedQuantity = getBxGyFreeQuantity(
            getYQuantity,
            buyXQuantity,
            applicableItems,
          );

          const { discount: bxgyDiscount, freeProducts } = getBxGyDiscount(
            applicableItems,
            discountedQuantity,
          );

          res.discount = bxgyDiscount;
          res.isValidated = true;
          res.products = freeProducts;
          break;

        case CampaignCouponType.BUY_X_GET_Y_AT_Z:
          const buyXGetYAtZHandler = new BxGyAtZCoupon(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
          );

          if (
            buyMinXQuantity &&
            couponApplicableProducts &&
            couponApplicableProductsY
          ) {
            const data = getBuyXGetYProducts(
              couponApplicableProducts,
              couponApplicableProductsY,
              cartItems,
              buyMinXQuantity,
            );
            applicableItemsY = data.yProducts;
          }

          if (
            applicableItems?.length &&
            !buyMinXQuantity &&
            couponApplicableProductsY?.length
          ) {
            applicableItemsY = getApplicableProductItems(
              couponApplicableProductsY,
              cartItems,
            );
          }
          if (!applicableItemsY.length) {
            return res;
          }
          const currentTotal =
            promoCoupons?.length || promoApplicable != 'ALL'
              ? getTotalAmount(applicableItems) +
                getTotalAmount(applicableItemsY) -
                Number(promotionalDiscountAmount || 0)
              : getTotalAmount(applicableItems) +
                getTotalAmount(applicableItemsY);

          const isValidAtZ = validateMinOrderValue(minOrderValue, currentTotal);

          if (!isValidAtZ) {
            return res;
          }

          const { finalDiscount } = await buyXGetYAtZHandler.getBxGyAtZDiscount(
            getTotalAmount(applicableItemsY),
            getCartCount(applicableItemsY),
            getYAmount,
            getYPercentage,
            maxDiscount,
            couponApplicationType,
            getTotalAmount(cartItems),
            applicableItemsY,
          );

          res.discount = finalDiscount;
          res.isValidated = true;
          break;

        case CampaignCouponType.FREEBIE:
          const freebieHandler = new FreebieCoupon(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
          );

          if (!couponApplicableProducts?.length) {
            applicableItems = applicableItems
              .map(({ productId, variantId, quantity, ...rest }) => {
                if (
                  getYProduct?.productId === productId &&
                  getYProduct?.variantId === variantId
                ) {
                  if (quantity === 1) {
                    return null;
                  } else {
                    return {
                      ...rest,
                      productId,
                      variantId,
                      quantity: quantity - 1,
                    };
                  }
                } else {
                  return {
                    ...rest,
                    productId,
                    variantId,
                    quantity,
                  };
                }
              })
              .filter(Boolean);
          }

          const isValidFreebie = await freebieHandler.validateFreebieCoupon(
            minOrderValue,
            getTotalAmount(applicableItems),
            buyXQuantity,
            getCartCount(applicableItems),
          );

          if (!isValidFreebie) {
            return res;
          }
          let freebieQuantity = 1;

          if (buyXQuantity && couponApplicableProducts?.length) {
            freebieQuantity = await freebieHandler.getFreebieQuantity(
              buyXQuantity,
              getCartCount(applicableItems),
            );
          }

          if (freebieQuantity == 0) {
            return res;
          }

          const res_from_freebie = await freebieHandler.getFreebieDiscount(
            getYProduct,
            freebieQuantity,
          );
          const { productId, variantId, finalDiscountedPrice, price } =
            res_from_freebie;

          res.discount = finalDiscountedPrice;
          res.isValidated = true;
          res.products = [
            { productId, variantId, quantity: freebieQuantity, price },
          ];
          break;

        default:
          res.discount = 0;
          res.isValidated = false;
          break;
      }

      return { ...res, discount: Math.round(res.discount) };
    } catch (err) {
      throw err;
    }
  }
}
