import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { Injectable } from '@nestjs/common';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { StorePOData } from '../entities/store-po.entity';
import { posLogger } from 'src/common/logger';
import moment from 'moment';
import { UpdateStorePOInput } from '../dto/update-store-po.input';

@Injectable()
export class UpdateStorePO {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async updateStorePO(
    stnId: string,
    poId: string,
    updateData: Partial<UpdateStorePOInput>,
  ): Promise<StorePOData> {
    posLogger.info('StorePO', 'updateStorePO', {
      stnId,
      poId,
      updateData,
    });

    try {
      const STORE_PO_TABLE_NAME =
        await this.configParameters.getStorePOTableName();
      const updatedAt = moment().toISOString();

      let updateExpressionString = 'SET updatedAt = :updatedAt, ';
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, any> = {
        ':updatedAt': updatedAt,
      };

      // Prepare update expression
      Object.keys(updateData).forEach((key) => {
        updateExpressionString += `#${key} = :${key}, `;
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributeValues[`:${key}`] = (updateData as any)[key];
      });

      updateExpressionString = updateExpressionString.slice(0, -2); // Remove trailing comma

      const command = new UpdateCommand({
        TableName: STORE_PO_TABLE_NAME,
        Key: {
          stnId,
          poId,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ConditionExpression:
          'attribute_exists(stnId) AND attribute_exists(poId)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: StorePOData } =
        await this.docClient.updateItem(command);

      return Attributes;
    } catch (error) {
      posLogger.error('StorePO', 'updateStorePO', {
        error,
      });
      throw error;
    }
  }
}
