import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';

import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { StorePOData } from '../entities/store-po.entity';
import { posLogger } from 'src/common/logger';

export class ListStorePOBySTN {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configService: ConfigService,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async listStorePOBySTN(stnId: string): Promise<StorePOData[]> {
    posLogger.info('StorePO', 'listStorePOBySTN', {
      stnId,
    });

    try {
      const STORE_PO_TABLE_NAME =
        await this.configParameters.getStorePOTableName();
      const command = new QueryCommand({
        TableName: STORE_PO_TABLE_NAME,
        KeyConditionExpression: 'stnId = :stnId',
        ExpressionAttributeValues: {
          ':stnId': stnId,
        },
      });
      console.log('listStorePOBySTN', command);

      const result = await this.docClient.queryItems(command);
      console.log('listStorePOBySTNN', result);

      return result.Items as StorePOData[];
    } catch (error) {
      posLogger.error('StorePO', 'listStorePOBySTN', {
        error,
      });
      throw error;
    }
  }
}
