// src/stn/create-stn.ts

import { posLogger } from 'src/common/logger';

import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import {
  CreateStorePOInput,
  CreateStorePOProductsInput,
} from '../dto/create-store-po.input';
import { GetSTN } from 'src/stn/lib/get-stn-by-id';

import { STNProgressStatus, STNTransferMode } from 'src/common/enum/stn';
import { UpdateSTN } from 'src/stn/lib/update-stn';
import { QueryInventoryTrackingByIds } from 'src/inventory-tracking/lib/list-inventory-tracking-by-ids';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import { GetInventoryCredential } from 'src/inventory-credentials/lib/get-inventory-credential';
import { STNData } from 'src/stn/entity/stn.entity';
export class CreateStorePO {
  constructor(
    private docClient: AppDocumentClient,
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createStorePO(
    createStorePOInput: CreateStorePOInput,
  ): Promise<STNData> {
    posLogger.info('CreateStorePOInput', 'createStorePO', createStorePOInput);
    try {
      const getSTNHandler = new GetSTN(this.docClient, this.configParameters);
      const updateSTNHandler = new UpdateSTN(
        this.docClient,
        this.configService,
        this.ssmClient,
        this.configParameters,
      );

      const { stnId } = createStorePOInput;
      const {
        requestedStoreId = null,
        transferMode,
        ecomInvoiceId,
        products: stnProducts,
      } = await getSTNHandler.getSTN(stnId);
      const products: CreateStorePOProductsInput[] = stnProducts.map((p) => {
        return {
          id: p.sku,
          assignedQuantity: Number(p.quantity),
        };
      });
      await this.validateStorePOQuantities(requestedStoreId, products);

      if (transferMode !== STNTransferMode.STORE) {
        throw new CustomError(
          'PO can only be created if stn is Store type',
          400,
        );
      }
      const easyEcomHandler = new EasyEcomService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const getInventoryCredentialHandler = new GetInventoryCredential(
        this.docClient,
        this.configParameters,
      );
      const { locationKey } =
        await getInventoryCredentialHandler.getInventoryCredential(
          requestedStoreId,
        );
      const {
        data: { queueId },
      } = await easyEcomHandler.generateB2BInvoiceAPI(
        ecomInvoiceId,
        locationKey,
      );

      const Item = await updateSTNHandler.updateSTNProgressStatus(stnId, {
        progressStatus: STNProgressStatus.ACCEPTED,
        queueId,
      });

      return Item;
    } catch (e) {
      posLogger.error('CreateStorePOInput', 'createStorePO', e);
      throw e;
    }
  }
  private async validateStorePOQuantities(
    storeId: string,
    storePOProducts: CreateStorePOProductsInput[],
  ): Promise<void> {
    const queryHandler = new QueryInventoryTrackingByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    const ids = storePOProducts.map((item) => item.id);
    const currentInventory = await queryHandler.queryInventoryTrackingByIds(
      storeId,
      ids,
    );
    for (const storePo of storePOProducts) {
      const item = currentInventory.data.find((item) => item.id === storePo.id);
      if (!item) {
        throw new CustomError(`Inventory for SKU ${storePo.id} not found`, 400);
      }
      const { title } = item;
      const remainingQuantity = item.quantity - item?.displayItemQuantity || 0;
      if (Number(storePo.assignedQuantity) > remainingQuantity) {
        throw new CustomError(
          `SKU ${storePo.id} ${title} in not available in the Sellable store inventory.`,
          400,
        );
      }
    }
    posLogger.info(
      'storePO',
      'validateStorePOQuantities',
      'store po quantity validated',
    );
  }
}
