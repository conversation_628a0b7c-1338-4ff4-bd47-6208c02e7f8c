import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { StorePoService } from './store-po.service';
import { StorePO, StorePOs } from './entities/store-po.entity';
import { CreateStorePOInput } from './dto/create-store-po.input';
import { UpdateStorePOInput } from './dto/update-store-po.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus } from '@nestjs/common/enums';
import { STN } from 'src/stn/entity/stn.entity';

@Resolver(() => StorePO)
export class StorePoResolver {
  constructor(
    private readonly storePoService: StorePoService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: <PERSON>rrorHand<PERSON>,
  ) {}

  @Mutation(() => STN)
  async createStorePo(
    @Args('createStorePOInput') createStorePOInput: CreateStorePOInput,
  ) {
    try {
      const data = await this.storePoService.create(createStorePOInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create store purchase order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => StorePOs, { name: 'listStorePoBySTN' })
  async findAllBySTN(@Args('stnId', { type: () => String }) stnId: string) {
    try {
      const data = await this.storePoService.findAll(stnId);

      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list store purchase orders by STN',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => StorePO, { name: 'storePo' })
  async findOne(@Args('id', { type: () => Int }) id: number) {
    try {
      const data = await this.storePoService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to retrieve store purchase order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => StorePO)
  async updateStorePo(
    @Args('updateStorePoInput') updateStorePoInput: UpdateStorePOInput,
  ) {
    try {
      const data = await this.storePoService.update(updateStorePoInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update store purchase order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => StorePO)
  async removeStorePo(@Args('id', { type: () => Int }) id: number) {
    try {
      const data = await this.storePoService.remove(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to delete store purchase order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
