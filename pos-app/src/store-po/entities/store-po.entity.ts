import { ObjectType, Field } from '@nestjs/graphql';
@ObjectType()
export class StorePOData {
  @Field(() => String, { nullable: false })
  poId: string;

  @Field(() => String, { nullable: false })
  stnId: string;

  @Field(() => String, { nullable: false })
  requestedStoreId: string;

  @Field(() => [POProductData], {
    description: 'List of products included in the order',
  })
  products: POProductData[];

  @Field(() => String, { description: 'Creation timestamp of the order' })
  createdAt: string;

  @Field(() => String, { description: 'Last update timestamp of the order' })
  updatedAt: string;
}
@ObjectType()
export class StorePO {
  @Field(() => StorePOData, { nullable: true, description: 'PO data' })
  data: StorePOData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class StorePOs {
  @Field(() => [StorePOData], { nullable: true, description: 'PO data' })
  data: StorePOData[];

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
@ObjectType()
class POProductData {
  @Field(() => String, { description: 'SKU of the product' })
  id: string;

  @Field(() => String, { description: 'Title of the product' })
  title: string;

  @Field(() => Number, { description: 'Price of the product', nullable: true })
  price?: number;

  @Field(() => Number, { description: 'Quantity of the product ordered' })
  assignedQuantity: number;

  @Field(() => Number, {
    description: 'Pending Inward Quantity of the product ordered',
  })
  pendingQuantity: number;
}
