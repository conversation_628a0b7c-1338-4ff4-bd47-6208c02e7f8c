import { Injectable } from '@nestjs/common';
import { CreateStorePOInput } from './dto/create-store-po.input';
import { UpdateStorePOInput } from './dto/update-store-po.input';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateStorePO } from './lib/create-store-po';
import { ListStorePOBySTN } from './lib/list-store-po-by-id';
import { UpdateStorePO } from './lib/update-store-po';

@Injectable()
export class StorePoService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private ssmClient: AppSsmClient,
  ) {}
  async create(createStorePOInput: CreateStorePOInput) {
    const createHandler = new CreateStorePO(
      this.docClient,
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return createHandler.createStorePO(createStorePOInput);
  }

  findAll(stnId: string) {
    const listStorePOBySTNHandler = new ListStorePOBySTN(
      this.docClient,
      this.configService,
      this.configParameters,
    );
    return listStorePOBySTNHandler.listStorePOBySTN(stnId);
  }

  findOne(id: number) {
    return `This action returns a #${id} storePo`;
  }

  update(updateStorePoInput: UpdateStorePOInput) {
    const { stnId, poId, products } = updateStorePoInput;
    const updateStorePOHandler = new UpdateStorePO(
      this.docClient,
      this.configParameters,
    );

    return updateStorePOHandler.updateStorePO(stnId, poId, { products });
  }

  remove(id: number) {
    return `This action removes a #${id} storePo`;
  }
}
