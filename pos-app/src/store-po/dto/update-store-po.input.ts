import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class UpdateStorePOInput {
  @Field(() => String, { nullable: false })
  stnId: string;

  @Field(() => String, { nullable: false })
  poId: string;

  @Field(() => [UpdateStorePOProductsInput], {
    description: 'List of products included in the order',
  })
  products: UpdateStorePOProductsInput[];
}

@InputType()
export class UpdateStorePOProductsInput {
  @Field(() => String, { description: 'SKU of the product' })
  id: string;

  @Field(() => String, { description: 'Quantity of the product ordered' })
  pendingQuantity: number;

  @Field(() => Number, {
    description: 'Quantity to update',
  })
  assignedQuantity: number;
}
