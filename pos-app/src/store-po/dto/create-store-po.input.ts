import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CreateStorePOInput {
  @Field(() => String, { description: 'Example field (placeholder)' })
  stnId: string;
}
@InputType()
export class CreateStorePOProductsInput {
  @Field(() => String, { description: 'SKU of the product' })
  id: string;

  @Field(() => String, { description: 'Quantity of the product ordered' })
  assignedQuantity: number;
}
