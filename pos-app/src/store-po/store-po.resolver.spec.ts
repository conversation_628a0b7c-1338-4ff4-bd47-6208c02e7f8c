import { Test, TestingModule } from '@nestjs/testing';
import { StorePoResolver } from './store-po.resolver';
import { StorePoService } from './store-po.service';

describe('StorePoResolver', () => {
  let resolver: StorePoResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [StorePoResolver, StorePoService],
    }).compile();

    resolver = module.get<StorePoResolver>(StorePoResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
