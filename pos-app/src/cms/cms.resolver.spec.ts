import { Test, TestingModule } from '@nestjs/testing';
import { CMSResolver } from './cms.resolver';
import { CMSService } from './cms.service';

describe('CMSResolver', () => {
  let resolver: CMSResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CMSResolver, CMSService],
    }).compile();

    resolver = module.get<CMSResolver>(CMSResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
