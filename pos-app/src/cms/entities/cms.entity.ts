import { Field, Int, ObjectType } from '@nestjs/graphql';
import { CMSStatus } from 'src/common/enum/cms';

@ObjectType()
export class CMSIds {
  @Field(() => String, { description: 'Order ID' })
  orderId: string;

  @Field(() => String, { description: 'Transaction ID' })
  transactionId: string;
}

@ObjectType()
export class CMSData {
  @Field(() => String, { description: 'ID' })
  id: string;

  @Field(() => Number, { description: 'Total Cash' })
  totalCash: number;

  @Field(() => Number, { description: 'Total Orders' })
  totalOrders: number;

  @Field(() => CMSStatus, {
    description: 'Status',
    defaultValue: CMSStatus.DRAFT,
  })
  status: CMSStatus;

  @Field(() => [CMSIds], { description: 'CMS Order IDs' })
  transactionsCovered: CMSIds[];

  @Field(() => String, { description: 'Employee ID', nullable: true })
  employeeId?: string;

  @Field(() => String, { description: 'Image URL', nullable: true })
  imageUrl?: string;

  @Field(() => String, { description: 'Handover ID', nullable: true })
  handoverId?: string;

  @Field(() => Number, { description: 'Confirm Cash HandOver', nullable: true })
  confirmCashHandOver?: number;

  @Field(() => String, { description: 'Handover Date', nullable: true })
  handoverDate?: string;

  @Field(() => String, { description: 'Store ID' })
  storeId: string;

  @Field(() => String)
  createdAt: string;

  @Field(() => String)
  updatedAt: string;
}

@ObjectType()
export class CMSs {
  @Field(() => [CMSData], { nullable: true })
  data: CMSData[];

  @Field(() => Int, { nullable: true })
  count?: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class CMS {
  @Field(() => CMSData, { nullable: true })
  data: CMSData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
