import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class CMSDashboardReportData {
  @Field(() => Number, { nullable: true })
  totalOrdersPending?: number;

  @Field(() => Number, { nullable: true })
  totalAmountPending?: number;
}

@ObjectType()
export class CMSDashboardReport {
  @Field(() => CMSDashboardReportData, { nullable: true })
  data: CMSDashboardReportData;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
