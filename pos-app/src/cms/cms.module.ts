import { Module } from '@nestjs/common';
import { CMSService } from './cms.service';
import { CMSResolver } from './cms.resolver';
import { ConfigParametersModule } from 'src/config/config.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';

@Module({
  imports: [DocumentClientModule, SsmClientModule, ConfigParametersModule],
  providers: [CMSResolver, CMSService, SuccessHandler, ErrorHandler],
})
export class CMSModule {}
