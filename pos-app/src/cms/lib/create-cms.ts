import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import moment from 'moment';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { CreateCMSInput } from '../dto/create-cms.input';
import { CMSData } from '../entities/cms.entity';
import { GenerateCode } from 'src/common/helper/generate-code';
import { CMSStatus } from 'src/common/enum/cms';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { ElasticClient } from 'src/utils/elasticsearch.config';

export class CreateCMS {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createCMS(createCMSInput: CreateCMSInput): Promise<CMSData> {
    try {
      posLogger.info('cms', 'createCMS', {
        input: { createCMSInput },
      });

      const { storeId } = createCMSInput;
      const CMS_TABLE = await this.configParameters.getCMSTableName();

      const totalDrafts = await this.getDraftCMS(CMS_TABLE, storeId);

      if (totalDrafts) {
        throw new CustomError(
          `Error Creating new CMS request, already one draft request exists for ${storeId}`,
          400,
        );
      }

      const codeHandler = new GenerateCode(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const code = await codeHandler.generateCode(
        storeId,
        'CMS-COUNT',
        `CMS${storeId}-`,
      );

      const cmsPayload: CMSData = {
        ...createCMSInput,
        id: code,
        status: CMSStatus.DRAFT,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      };
      const cmsCommand = new PutCommand({
        TableName: CMS_TABLE,
        Item: cmsPayload,
        ConditionExpression: 'attribute_not_exists(id)',
      });
      await this.docClient.createItem(cmsCommand);

      return cmsPayload;
    } catch (error) {
      posLogger.error('cms', 'createCMS', error);
      throw error;
    }
  }

  async getDraftCMS(CMS_TABLE: string, storeId: string): Promise<number> {
    try {
      const esHandler = new ElasticClient(this.configService, this.ssmClient);
      const response = await esHandler.search({
        index: CMS_TABLE,
        body: {
          query: {
            bool: {
              must: [
                {
                  match: {
                    status: CMSStatus.DRAFT,
                  },
                },
                {
                  match: {
                    storeId,
                  },
                },
              ],
            },
          },
        },
      });
      const data = response?.body?.hits?.total?.value || 0;

      return data;
    } catch (error) {
      posLogger.error('cms', 'getDraftCMS', error);
      throw error;
    }
  }
}
