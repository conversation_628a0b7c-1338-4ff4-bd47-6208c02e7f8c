// modules
import { DeleteCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CMSData } from '../entities/cms.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { GetCMS } from './get-cms';
import { CMSStatus } from 'src/common/enum/cms';
export class RemoveCMS {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async removeCMS(id: string): Promise<CMSData> {
    posLogger.info('cms', 'removeCMS', { input: { id } });
    try {
      const getCMSHandler = new GetCMS(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const cms: CMSData = await getCMSHandler.getCMS(id);

      if (cms.status == CMSStatus.COLLECTED)
        throw new CustomError(
          `Cannot be deleted as it's already Collected ${CMSStatus.COLLECTED}`,
          400,
        );

      const CMS_TABLE = await this.configParameters.getCMSTableName();

      const param = new DeleteCommand({
        TableName: CMS_TABLE,
        Key: {
          id,
        },
        ReturnValues: 'ALL_OLD',
      });

      const { Attributes }: { Attributes: CMSData } =
        await this.docClient.deleteItem(param);

      if (!Attributes) {
        throw new CustomError('Invalid ID! No CMS found for given ID.', 404);
      }

      return Attributes;
    } catch (e) {
      posLogger.error('cms', 'removeCMS', { error: e });
      throw e;
    }
  }
}
