import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import moment from 'moment';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { UpdateCMSInput } from '../dto/update-cms.input';
import { AppConfigParameters } from 'src/config/config';
import { CMSStatus } from 'src/common/enum/cms';
import { CMSData } from '../entities/cms.entity';
import { GetCMS } from './get-cms';

export class UpdateCMS {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}
  async updateCMS(
    id: string = '',
    { id: {}, ...updateCMSInput }: UpdateCMSInput,
  ): Promise<CMSData> {
    try {
      posLogger.info('cms', 'updateCMS', {
        input: { id, updateCMSInput },
      });

      const getCMSHandler = new GetCMS(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const cms: CMSData = await getCMSHandler.getCMS(id);

      if (cms.status == CMSStatus.COLLECTED) return cms;

      const CMS_TABLE = await this.configParameters.getCMSTableName();

      //Updating CMS
      let updateCMSExpressionString: string = 'Set #updatedAt = :updatedAt, ';
      const cmsExpressionAttributeNames: Record<string, string> = {
        '#updatedAt': 'updatedAt',
      };
      const cmsExpressionAttributesValues: Record<string, any> = {
        ':updatedAt': moment().toISOString(),
      };

      Object.keys(updateCMSInput).map((key) => {
        updateCMSExpressionString += `#${key} = :${key}, `;
        cmsExpressionAttributeNames[`#${key}`] = key;
        cmsExpressionAttributesValues[`:${key}`] = updateCMSInput[key];
      });

      updateCMSExpressionString = updateCMSExpressionString.substring(
        0,
        updateCMSExpressionString.length - 2,
      );

      const command = new UpdateCommand({
        TableName: CMS_TABLE,
        Key: { id },
        UpdateExpression: `${updateCMSExpressionString}`,
        ExpressionAttributeNames: cmsExpressionAttributeNames,
        ExpressionAttributeValues: {
          ...cmsExpressionAttributesValues,
        },
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: CMSData } =
        await this.docClient.updateItem(command);

      return { ...Attributes };
    } catch (e) {
      posLogger.error('cms', 'updateCMS', e);
      throw e;
    }
  }
}
