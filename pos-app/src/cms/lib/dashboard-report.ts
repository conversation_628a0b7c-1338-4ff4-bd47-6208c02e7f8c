// modules
import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { AppConfigParameters } from 'src/config/config';
import { CMSDashboardReportData } from '../entities/cms-dashboard-reports.entity';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { PaymentMode, PaymentStatus } from 'src/common/enum/payment';
import { HandoverStatus } from 'src/common/enum/cms';
import { OrderStatus } from 'src/common/enum/order';

export class DashboardReports {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async dashboardReports(storeId?: string): Promise<CMSDashboardReportData> {
    posLogger.info('cms', 'dashboardReports', { input: storeId });
    try {
      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();
      const body: any = {
        size: 0,
        query: {
          bool: {
            must: [
              {
                match: {
                  handoverStatus: HandoverStatus.PENDING,
                },
              },
              {
                match: {
                  mode: PaymentMode.CASH,
                },
              },
              {
                match: {
                  status: PaymentStatus.COMPLETED,
                },
              },
              {
                match: {
                  orderStatus: OrderStatus.SENT_TO_SHOPIFY,
                },
              },
            ],
          },
        },
        aggs: {
          totalOrdersPending: {
            cardinality: {
              script: {
                source: "doc['orderId.keyword'].value",
              },
            },
          },
          totalAmountPending: {
            sum: {
              field: 'transactionAmount',
            },
          },
        },
      };

      if (storeId) {
        body.query.bool.must.push({
          term: {
            'storeId.keyword': storeId,
          },
        });
      }

      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      const response = await esHandler.search({
        index: PAYMENT_TABLE,
        body,
      });
      const {
        totalOrdersPending: { value: totalOrdersPending },
        totalAmountPending: { value: totalAmountPending },
      } = response.body.aggregations;

      // let lastHandoverDate = null;
      // if (storeId) {
      //   const getConfigHandler = new GetLocalConfiguration(
      //     this.configService,
      //     this.docClient,
      //     this.ssmClient,
      //     this.configParameters,
      //   );

      //   const { updatedAt } = await getConfigHandler.getLocalConfiguration(
      //     storeId,
      //     'CMS_LAST_HANDOVER_DATE',
      //   );
      //   lastHandoverDate = updatedAt;
      // }

      return { totalOrdersPending, totalAmountPending };
    } catch (e) {
      posLogger.error('cms', 'dashboardReports', { error: e });
      throw e;
    }
  }
}
