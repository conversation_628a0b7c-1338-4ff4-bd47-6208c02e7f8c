import { filterFormatter } from 'src/common/helper/filter-helper';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';
import { ListCMSInput } from '../dto/list-cms.input';
import { CMSData, CMSs } from '../entities/cms.entity';

export class QueryCMS {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryCMS(filter: ListCMSInput): Promise<CMSs> {
    posLogger.info('cms', 'queryCMS', {
      input: filter,
    });
    try {
      const CMS_TABLE = await this.configParameters.getCMSTableName();

      const { searchArray, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: CMS_TABLE,
        });

        size = bodyRes?.count;
      }

      if (searchArray?.length) {
        const { body: bodyRes } = await esHandler.count({
          index: CMS_TABLE,
          body: {
            query: {
              bool: {
                must: [...searchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: CMS_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data: CMSData[] = response.body.hits.hits.map(
          (hit) => hit._source,
        );

        return { data, count: bodyRes?.count };
      }

      const { body: bodyRes } = await esHandler.count({
        index: CMS_TABLE,
      });

      const response = await esHandler.search({
        index: CMS_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data: CMSData[] = response.body.hits.hits.map((hit) => hit._source);

      return { data, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('cms', 'queryCMS', { error: e });
      throw e;
    }
  }
}
