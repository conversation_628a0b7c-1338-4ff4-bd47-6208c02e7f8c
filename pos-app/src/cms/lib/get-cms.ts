import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CMSData } from '../entities/cms.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';

export class GetCMS {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getCMS(id: string): Promise<CMSData> {
    posLogger.info('cms', 'getCMS', {
      input: { id },
    });
    try {
      const CMS_TABLE = await this.configParameters.getCMSTableName();

      const param = new GetCommand({
        TableName: CMS_TABLE,
        Key: { id },
      });

      const { Item: data } = await this.docClient.getItem(param);

      if (data) return data;

      throw new CustomError(
        `Invalid CODE! No campaign coupon found for given code ${id}.`,
        404,
      );
    } catch (e) {
      posLogger.error('cms', 'getCMSByCode', {
        error: e,
      });
      throw e;
    }
  }
}
