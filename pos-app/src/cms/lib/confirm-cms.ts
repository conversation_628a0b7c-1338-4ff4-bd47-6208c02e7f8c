import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import moment from 'moment';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { AppConfigParameters } from 'src/config/config';
import { CMSStatus } from 'src/common/enum/cms';
import { CMSData } from '../entities/cms.entity';
import { GetCMS } from './get-cms';
import { ConfirmCMSInput } from '../dto/confirm-cms.input';

export class ConfirmCMS {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}
  async confirmCMS({
    id,
    ...confirmCMSInput
  }: ConfirmCMSInput): Promise<CMSData> {
    try {
      posLogger.info('cms', 'confirmCMS', {
        input: { id, confirmCMSInput },
      });

      const getCMSHandler = new GetCMS(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const cms: CMSData = await getCMSHandler.getCMS(id);

      if (cms.status == CMSStatus.COLLECTED) return cms;

      const CMS_TABLE = await this.configParameters.getCMSTableName();

      //Updating CMS
      let confirmCMSExpressionString: string =
        'Set #updatedAt = :updatedAt, #status = :status, ';
      const cmsExpressionAttributeNames: Record<string, string> = {
        '#updatedAt': 'updatedAt',
        '#status': 'status',
      };
      const cmsExpressionAttributesValues: Record<string, any> = {
        ':updatedAt': moment().toISOString(),
        ':status': CMSStatus.COLLECTED,
      };

      Object.keys(confirmCMSInput).map((key) => {
        confirmCMSExpressionString += `#${key} = :${key}, `;
        cmsExpressionAttributeNames[`#${key}`] = key;
        cmsExpressionAttributesValues[`:${key}`] = confirmCMSInput[key];
      });

      confirmCMSExpressionString = confirmCMSExpressionString.substring(
        0,
        confirmCMSExpressionString.length - 2,
      );

      const command = new UpdateCommand({
        TableName: CMS_TABLE,
        Key: { id },
        UpdateExpression: `${confirmCMSExpressionString}`,
        ExpressionAttributeNames: cmsExpressionAttributeNames,
        ExpressionAttributeValues: {
          ...cmsExpressionAttributesValues,
        },
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: CMSData } =
        await this.docClient.updateItem(command);

      return { ...Attributes };
    } catch (e) {
      posLogger.error('cms', 'confirmCMS', e);
      throw e;
    }
  }
}
