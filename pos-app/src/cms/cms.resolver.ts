import { Resolver, Query, Args, Mutation } from '@nestjs/graphql';
import { CMSService } from './cms.service';
import { CMS, CMSs } from './entities/cms.entity';
import { CMSDashboardReport } from './entities/cms-dashboard-reports.entity';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus } from '@nestjs/common';
import { CreateCMSInput } from './dto/create-cms.input';
import { ListCMSInput } from './dto/list-cms.input';
import { UpdateCMSInput } from './dto/update-cms.input';
import { ConfirmCMSInput } from './dto/confirm-cms.input';

@Resolver(() => CMS)
export class CMSResolver {
  constructor(
    private readonly cmsService: CMSService,
    private readonly successHandler: SuccessHand<PERSON>,
    private readonly errorHandler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ) {}

  @Mutation(() => CMS, { name: 'createCMS' })
  async createCMS(@Args('createCMSInput') createCMSInput: CreateCMSInput) {
    try {
      const data = await this.cmsService.create(createCMSInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create cms',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => CMSs, { name: 'listCMS' })
  async findAll(
    @Args('listCMSInput', { nullable: true })
    listCMSInput: ListCMSInput,
  ) {
    try {
      const { data, count } = await this.cmsService.findAll(listCMSInput);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list cms',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => CMSDashboardReport, { name: 'cmsDashboardReport' })
  async dashboardReports(
    @Args('storeId', { type: () => String, nullable: true }) storeId?: string,
  ) {
    try {
      const data = await this.cmsService.dashboardReports(storeId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get CMS Dashboard Reports',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => CMS, { name: 'getCMS' })
  async findOne(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.cmsService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get cms',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => CMS)
  async updateCMS(@Args('updateCMSInput') updateCMSInput: UpdateCMSInput) {
    try {
      const data = await this.cmsService.update(
        updateCMSInput.id,
        updateCMSInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update cms',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => CMS)
  async confirmCMS(@Args('confirmCMSInput') confirmCMSInput: ConfirmCMSInput) {
    try {
      const data = await this.cmsService.confirm(confirmCMSInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to confirm cms',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => CMS, { name: 'deleteCMS' })
  async removeCMS(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.cmsService.remove(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to delete cms',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
