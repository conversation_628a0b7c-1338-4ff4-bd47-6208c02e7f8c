import { InputType, Field } from '@nestjs/graphql';
import { CMSIdsInput } from './create-cms.input';

@InputType()
export class ConfirmCMSInput {
  @Field(() => String, { description: 'ID' })
  id: string;

  @Field(() => Number, { description: 'Total Cash' })
  totalCash: number;

  @Field(() => Number, { description: 'Total Orders' })
  totalOrders: number;

  @Field(() => [CMSIdsInput], { description: 'CMS Order IDs' })
  transactionsCovered: CMSIdsInput[];

  @Field(() => String, { description: 'Employee ID' })
  employeeId: string;

  @Field(() => String, { description: 'Image URL', nullable: true })
  imageUrl?: string;

  @Field(() => String, { description: 'Handover ID' })
  handoverId: string;

  @Field(() => Number, { description: 'Confirm Cash HandOver', nullable: true })
  confirmCashHandOver?: number;

  @Field(() => String, { description: 'Handover Date' })
  handoverDate: string;
}
