import { InputType, Field } from '@nestjs/graphql';
import { CMSIdsInput } from './create-cms.input';

@InputType()
export class UpdateCMSInput {
  @Field(() => String, { description: 'ID' })
  id: string;

  @Field(() => Number, { description: 'Total Cash', nullable: true })
  totalCash: number;

  @Field(() => Number, { description: 'Total Orders', nullable: true })
  totalOrders: number;

  @Field(() => [CMSIdsInput], { description: 'CMS Order IDs', nullable: true })
  transactionsCovered: CMSIdsInput[];

  @Field(() => String, { description: 'Employee ID', nullable: true })
  employeeId: string;

  @Field(() => String, { description: 'Image URL', nullable: true })
  imageUrl?: string;

  @Field(() => String, { description: 'Handover ID', nullable: true })
  handoverId: string;

  @Field(() => Number, { description: 'Confirm Cash HandOver', nullable: true })
  confirmCashHandOver?: number;

  @Field(() => String, { description: 'Handover Date', nullable: true })
  handoverDate: string;
}
