import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CMSTermSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  storeId?: string;
}

@InputType()
export class CMSSortingFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class ListCMSInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size: string;

  @Field(() => CMSTermSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  termSearchFields: CMSTermSearchFieldsInput;

  @Field(() => CMSSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { createdAt: 'desc' },
  })
  sortBy: CMSSortingFieldsInput;
}
