import { InputType, Field, registerEnumType } from '@nestjs/graphql';
import { CMSStatus } from 'src/common/enum/cms';

registerEnumType(CMSStatus, { name: 'CMSStatus' });

@InputType()
export class CMSIdsInput {
  @Field(() => String, { description: 'ID' })
  orderId: string;

  @Field(() => String, { description: 'ID' })
  transactionId: string;
}

@InputType()
export class CreateCMSInput {
  @Field(() => Number, { description: 'Total Cash' })
  totalCash: number;

  @Field(() => Number, { description: 'Total Orders' })
  totalOrders: number;

  @Field(() => [CMSIdsInput], { description: 'CMS Order IDs' })
  transactionsCovered: CMSIdsInput[];

  @Field(() => String, { description: 'Employee ID', nullable: true })
  employeeId?: string;

  @Field(() => String, { description: 'Store ID' })
  storeId: string;
}
