import { Injectable } from '@nestjs/common';
import { DashboardReports } from './lib/dashboard-report';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { CreateCMSInput } from './dto/create-cms.input';
import { CreateCMS } from './lib/create-cms';
import { ListCMSInput } from './dto/list-cms.input';
import { QueryCMS } from './lib/list-cms';
import { UpdateCMSInput } from './dto/update-cms.input';
import { UpdateCMS } from './lib/update-cms';
import { RemoveCMS } from './lib/delete-cms';
import { GetCMS } from './lib/get-cms';
import { ConfirmCMS } from './lib/confirm-cms';
import { ConfirmCMSInput } from './dto/confirm-cms.input';

@Injectable()
export class CMSService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async create(createCMSInput: CreateCMSInput) {
    const createHandler = new CreateCMS(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return createHandler.createCMS(createCMSInput);
  }

  async findAll(listCMSInput: ListCMSInput) {
    const queryCMSHandler = new QueryCMS(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return await queryCMSHandler.queryCMS(listCMSInput);
  }

  async dashboardReports(storeId?: string) {
    const reportHandler = new DashboardReports(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await reportHandler.dashboardReports(storeId);
  }

  async findOne(id: string) {
    const getHandler = new GetCMS(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await getHandler.getCMS(id);
  }

  async update(id: string, updateCMSInput: UpdateCMSInput) {
    const updateHandler = new UpdateCMS(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await updateHandler.updateCMS(id, updateCMSInput);
  }

  async confirm(confirmCMSInput: ConfirmCMSInput) {
    const updateHandler = new ConfirmCMS(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    return await updateHandler.confirmCMS(confirmCMSInput);
  }

  async remove(id: string) {
    const removeHandler = new RemoveCMS(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return removeHandler.removeCMS(id);
  }
}
