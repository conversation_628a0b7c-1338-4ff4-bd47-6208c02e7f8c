// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';
import { CollectionData } from '../entities/collection.entity';

export class GetCollection {
  constructor(
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getCollection(id: string): Promise<CollectionData> {
    posLogger.info('collection', 'getCollection', { input: id });
    try {
      const COLLECTION_TABLE =
        await this.configParameters.getCollectionTableName();

      const command = new GetCommand({
        TableName: COLLECTION_TABLE,
        Key: { id },
        ProjectionExpression: 'productIds',
      });

      const collection = (await this.docClient.getItem(command)).Item;

      return collection;
    } catch (e) {
      posLogger.error('collection', 'getCollection', { error: e });
      throw e;
    }
  }
}
