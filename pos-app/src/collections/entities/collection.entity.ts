import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class CollectionData {
  @Field(() => String, {})
  id: string;

  @Field(() => String, {})
  handle: string;

  @Field(() => String, {})
  title: string;

  @Field(() => String, {})
  updated_at: string;

  @Field(() => String, {})
  body_html: string;

  @Field(() => String, {})
  published_at: string;

  @Field(() => String, {})
  sort_order: string;

  @Field(() => String, {})
  template_suffix: string;

  @Field(() => String, { nullable: true })
  published_scope?: string;

  @Field(() => String, {})
  admin_graphql_api_id: string;

  @Field(() => [String], {})
  productIds: string[];
}

@ObjectType()
export class Collection {
  @Field(() => CollectionData, { nullable: true })
  data: CollectionData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
