import { Resolver, Query, Args } from '@nestjs/graphql';
import { CollectionsService } from './collections.service';
import { Collection } from './entities/collection.entity';
import { AdminGuard, CRMGuard } from 'src/auth/roles.guard';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { Response } from 'src/products/entities/product.entity';

@Resolver(() => Collection)
@UseGuards(CustomAuthGuard, CRMGuard)
export class CollectionsResolver {
  constructor(
    private readonly collectionsService: CollectionsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Query(() => Collection, { name: 'getCollection' })
  async findOne(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.collectionsService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get collection by id',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Response, { name: 'fetchCollections' })
  @UseGuards(AdminGuard)
  async fetchCollections() {
    return await this.collectionsService.fetchCollections();
  }
}
