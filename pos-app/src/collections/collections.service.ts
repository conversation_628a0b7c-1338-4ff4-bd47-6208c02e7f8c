import { Injectable } from '@nestjs/common';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { CronService } from 'src/cron/cron.service';
import { posLogger } from 'src/common/logger';
import { GetCollection } from './lib/get-collection';

@Injectable()
export class CollectionsService {
  constructor(
    private readonly cronService: CronService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  findOne(id: string) {
    const getHandler = new GetCollection(this.docClient, this.configParameters);
    return getHandler.getCollection(id);
  }

  async fetchCollections() {
    this.cronService.fetchShopifyCollections().catch((error) => {
      posLogger.error('COLLECTIONS', 'fetchCollections', error);
    });
    return { message: 'Fetching collections task has been initiated' };
  }
}
