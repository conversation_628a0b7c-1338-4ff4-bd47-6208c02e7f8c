import { Module } from '@nestjs/common';
import { CollectionsService } from './collections.service';
import { CollectionsResolver } from './collections.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { ShopifyModule } from 'src/common/shopify/shopify.module';
import { CronService } from 'src/cron/cron.service';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';

@Module({
  imports: [
    S3ClientModule,
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
    ShopifyModule,
  ],
  providers: [
    CollectionsResolver,
    CollectionsService,
    CronService,
    SuccessHandler,
    ErrorHandler,
  ],
})
export class CollectionsModule {}
