import { Test, TestingModule } from '@nestjs/testing';
import { S3UploaderResolver } from './s3-uploader.resolver';
import { S3UploaderService } from './s3-uploader.service';

describe('S3UploaderResolver', () => {
  let resolver: S3UploaderResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [S3UploaderResolver, S3UploaderService],
    }).compile();

    resolver = module.get<S3UploaderResolver>(S3UploaderResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
