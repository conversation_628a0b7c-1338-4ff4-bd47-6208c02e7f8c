import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class S3UploaderData {
  @Field(() => String, { description: 'Presigned Url' })
  url: string;
}

@ObjectType()
export class S3Uploader {
  @Field(() => S3UploaderData, { nullable: true, description: 'Presigned Url' })
  data: S3UploaderData;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
