import { Injectable } from '@nestjs/common';
import { GetS3PresignedUrl } from './lib/get-s3-presigned-url';
import { ConfigService } from '@nestjs/config';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { GetS3Object } from './lib/get-s3-object';

@Injectable()
export class S3UploaderService {
  constructor(
    private configService: ConfigService,
    private s3Client: AppS3Client,
  ) {}
  async findOne(filePath: string) {
    const getHandler = new GetS3PresignedUrl(this.configService, this.s3Client);
    return await getHandler.getPresignedSignedURL(filePath);
  }

  async getOne(filePath: string) {
    try {
      const getHandler = new GetS3Object(this.configService, this.s3Client);
      return await getHandler.getObject(filePath);
    } catch (error) {
      throw error;
    }
  }
}
