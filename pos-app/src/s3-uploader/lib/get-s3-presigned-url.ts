import { PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { ConfigService } from '@nestjs/config';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { S3UploaderData } from '../entities/s3-uploader.entity';
import { posLogger } from 'src/common/logger';

//types for return of function in promise generic
export class GetS3PresignedUrl {
  constructor(
    private configService: ConfigService,
    private s3Client: AppS3Client,
  ) {}

  async putObject(filePath: string) {
    const command = new PutObjectCommand({
      Bucket: `${this.configService.get('STACK_NAME')}-public-bucket`,
      Key: `${filePath}`,
    });
    const url = await getSignedUrl(this.s3Client, command, {
      expiresIn: 120 * 60,
    });

    return url;
  }

  async getPresignedSignedURL(filePath: string): Promise<S3UploaderData> {
    try {
      posLogger.info('S3Uploader', 'getPresignedSignedURL', filePath);

      const url = await this.putObject(filePath);
      return { url };
    } catch (error) {
      posLogger.error('S3Uploader', 'getPresignedSignedURL', error);
      throw error;
    }
  }
}
