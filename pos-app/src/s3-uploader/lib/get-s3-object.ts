import { GetObjectCommand } from '@aws-sdk/client-s3';
import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppS3Client } from 'src/common/s3-client/s3-client';

export class GetS3Object {
  constructor(
    private configService: ConfigService,
    private s3Client: AppS3Client,
  ) {}

  async getObject(filePath: string) {
    try {
      posLogger.info('getS3Object', 'getObject', filePath);
      const command = new GetObjectCommand({
        Bucket: `${this.configService.get('STACK_NAME')}-public-bucket`,
        Key: `${filePath}`,
      });

      return await this.s3Client.getObject(command);
    } catch (error) {
      posLogger.error('getS3Object', 'getObject', error.message);
      throw error;
    }
  }
}
