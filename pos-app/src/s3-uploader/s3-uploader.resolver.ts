import { Resolver, Query, Args } from '@nestjs/graphql';
import { S3UploaderService } from './s3-uploader.service';
import { S3Uploader } from './entities/s3-uploader.entity';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CRMGuard } from 'src/auth/roles.guard';

@Resolver(() => S3Uploader)
@UseGuards(CustomAuthGuard, CRMGuard)
export class S3UploaderResolver {
  constructor(
    private readonly s3UploaderService: S3UploaderService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Query(() => S3Uploader, { name: 'getS3PresignedUrl' })
  async findOne(@Args('filePath', { type: () => String }) filePath: string) {
    try {
      const data = await this.s3UploaderService.findOne(filePath);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get S3 Presigned Url',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  // @Query(() => S3Uploader, { name: 'getS3Object' })
  // async getOne(@Args('filePath', { type: () => String }) filePath: string) {
  //   try {
  //     const data = await this.s3UploaderService.findOne(filePath);
  //     return this.successHandler.getSuccessResponse({
  //       data,
  //       code: 201,
  //     });
  //   } catch (error) {
  //     const errorResponse = this.errorHandler.getErrorResponse({
  //       message: error.message || 'Failed to get S3 Get Object',
  //       code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
  //     });
  //     return errorResponse;
  //   }
  // }
}
