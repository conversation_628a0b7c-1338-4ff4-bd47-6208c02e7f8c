import { Module } from '@nestjs/common';
import { S3UploaderService } from './s3-uploader.service';
import { S3UploaderResolver } from './s3-uploader.resolver';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';

@Module({
  imports: [S3ClientModule, SsmClientModule, ConfigParametersModule],
  providers: [
    S3UploaderResolver,
    S3UploaderService,
    SuccessHandler,
    ErrorHandler,
  ],
})
export class S3UploaderModule {}
