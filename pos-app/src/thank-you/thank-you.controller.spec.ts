import { Test, TestingModule } from '@nestjs/testing';
import { ThankYouController } from './thank-you.controller';
import { ThankYouService } from './thank-you.service';

describe('ThankYouController', () => {
  let controller: ThankYouController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ThankYouController],
      providers: [ThankYouService],
    }).compile();

    controller = module.get<ThankYouController>(ThankYouController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
