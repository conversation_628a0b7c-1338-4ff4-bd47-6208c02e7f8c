import { Module } from '@nestjs/common';
import { ThankYouService } from './thank-you.service';
import { ThankYouController } from './thank-you.controller';
import { ConfigParametersModule } from 'src/config/config.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';

@Module({
  imports: [SsmClientModule, ConfigParametersModule, DocumentClientModule],
  controllers: [ThankYouController],
  providers: [ThankYouService],
})
export class ThankYouModule {}
