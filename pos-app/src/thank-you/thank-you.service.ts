import { GetCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import moment from 'moment';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { OrderChangeLog } from 'src/orders/lib/order-change-log';

@Injectable()
export class ThankYouService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async findOne(query?: string) {
    try {
      const ORDER_TABLE = await this.configParameters.getOrderTableName();

      const { id, isFirstTimeVisit } = JSON.parse(
        atob(decodeURIComponent(query)),
      );
      console.log('{ id, isFirstTimeVisit } :>> ', { id, isFirstTimeVisit });

      if (id) {
        const param = new GetCommand({
          TableName: ORDER_TABLE,
          Key: { id },
        });
        const { Item: order } = await this.docClient.getItem(param);

        if (!order) {
          throw new CustomError(
            `Invalid ID! No order found for given ID ${id}.`,
            404,
          );
        }

        if (isFirstTimeVisit) {
          const itemUpdateCommand = new UpdateCommand({
            TableName: ORDER_TABLE,
            Key: {
              id,
            },
            UpdateExpression:
              'SET #isFirstTimeVisit = :isFirstTimeVisit, #updatedAt = :updatedAt',
            ExpressionAttributeNames: {
              '#isFirstTimeVisit': 'isFirstTimeVisit',
              '#updatedAt': 'updatedAt',
            },
            ExpressionAttributeValues: {
              ':isFirstTimeVisit': false,
              ':updatedAt': moment().toISOString(),
            },
            ConditionExpression: 'attribute_exists(id)',
            ReturnValues: 'ALL_NEW',
          });

          console.log(
            'Update order for first time visit query before execution',
            itemUpdateCommand,
          );
          const { Attributes } =
            await this.docClient.updateItem(itemUpdateCommand);

          if (!Attributes || !Object.keys(Attributes).length) {
            console.error(
              'ERROR: Failed to update order firstTimeVisit status',
            );
          }

          const orderChangeLog = new OrderChangeLog(
            this.configParameters,
            this.docClient,
          );
          orderChangeLog.log(Attributes);

          return { ...order, transactions: null };
        } else {
          return {
            ...order,
            customer: null,
            billingAddress: null,
            shippingAddress: null,
            transactions: null,
            gstDetails: null,
            customerId: null,
            storeId: null,
            employeeId: null,
            notes: null,
            customerAcknowledgement: null,
          };
        }
      } else {
        return {
          statusCode: 400,
          message: 'No order found.',
        };
      }
    } catch (error) {
      return {
        statusCode: 400,
        message: error.message,
      };
    }
  }
}
