import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import moment from 'moment';
import { json2csv } from 'json-2-csv';
import { AppConfigParameters } from 'src/config/config';
import { OrderData } from 'src/orders/entities/order.entity';
import { PaymentMode, PaymentStatus } from 'src/common/enum/payment';
import { HandoverStatus } from 'src/common/enum/cms';
import {
  OrderFilterBy,
  OrderStatus,
  SubOrderType,
} from 'src/common/enum/order';

export class ExportPayments {
  private esHandler: ElasticClient;
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {
    this.esHandler = new ElasticClient(this.configService, this.ssmClient);
  }

  async exportPaymentCSV(
    orders: OrderData[],
    filterBy?: string,
    fromDate?: any,
    toDate?: any,
  ) {
    const fromDateObj = fromDate
      ? moment(fromDate).utcOffset('+05:30').format('DD/MM/yyyy HH:mm')
      : null;
    const toDateObj = toDate
      ? moment(toDate)
          .utcOffset('+05:30')
          .endOf('day')
          .format('DD/MM/yyyy HH:mm')
      : null;

    const data = orders.reduce((acc: any, order: any) => {
      const {
        transactions,
        shopifyOrderName,
        id: invoiceNo,
        storeId,
        loggedusername,
        createdAt,
        shopifyCreatedAt,
        subOrderType,
      } = order;
      const { transactionDetails } = transactions;

      const orderIstOffset = moment(createdAt)
        .utcOffset('+05:30')
        .format('DD/MM/yyyy HH:mm');
      const shopifyDateIstOffset = shopifyCreatedAt
        ? moment(shopifyCreatedAt)
            .utcOffset('+05:30')
            .format('DD/MM/yyyy HH:mm')
        : '';

      const payments = transactionDetails
        .map((transaction: any) => {
          const {
            splitPayment,
            status,
            transactionAmount,
            mode,
            createdAt,
            externalRefId = '',
            transactionScreenshotS3Key = '',
            paymentID = '',
            rrNumber = '',
            amountPaid = '',
            handoverId = '',
            handoverStatus = '',
            handoverDate = '',
            transactionId = '',
            transactionCompletedDate = '',
          } = transaction;

          const transactionIstOffset = moment(createdAt)
            .utcOffset('+05:30')
            .format('DD/MM/yyyy HH:mm');
          const transactionCompletedDateOffSet =
            transactionCompletedDate?.length
              ? moment(transactionCompletedDate)
                  .utcOffset('+05:30')
                  .format('DD/MM/yyyy HH:mm')
              : '';

          function isOutOfRange(
            transactionCompletedDateOffset,
            fromDateObj,
            toDateObj,
          ) {
            const transactionDate = moment(
              transactionCompletedDateOffset,
              'DD/MM/YYYY HH:mm',
            );
            const fromDate = moment(fromDateObj, 'DD/MM/YYYY HH:mm');
            const toDate = moment(toDateObj, 'DD/MM/YYYY HH:mm');

            return (
              transactionDate.isBefore(fromDate) ||
              transactionDate.isAfter(toDate)
            );
          }
          if (filterBy === OrderFilterBy.TRANSACTION) {
            if (
              !transactionCompletedDate ||
              (transactionCompletedDate &&
                isOutOfRange(
                  transactionCompletedDateOffSet,
                  fromDateObj,
                  toDateObj,
                ))
            ) {
              return null;
            }
          }

          return {
            'Order ID':
              status === PaymentStatus.COMPLETED ? shopifyOrderName || '' : '',
            'invoice no': invoiceNo,
            'transaction initiated date': transactionIstOffset,
            'transaction completed date': transactionCompletedDateOffSet,
            'invoice date': orderIstOffset,
            'order date': shopifyDateIstOffset,
            'transaction ID': transactionId,
            splitPayment: splitPayment ? 'Yes' : 'No',
            status,
            transactionAmount,
            'Amount Paid':
              amountPaid || status == 'COMPLETED' ? transactionAmount : '',
            'Payment mode': mode,
            'store code': storeId,
            'store name': loggedusername,
            'RazorpayPOS payment id':
              mode == PaymentMode.RAZORPAY_POS ? paymentID : '',
            'Bank TID':
              mode == PaymentMode.BANK_TRANSFER
                ? `\'${externalRefId || ''}`
                : '',
            'Mswipe TID':
              mode == PaymentMode.M_SWIPE ? `\'${externalRefId || ''}` : '',
            'MSwipe RRN':
              mode == PaymentMode.M_SWIPE
                ? `\'${rrNumber || externalRefId || ''}`
                : '',
            'Cheque TID':
              mode == PaymentMode.CHEQUE ? `\'${externalRefId || ''}` : '',
            'Razorpay payment id':
              mode == PaymentMode.RAZORPAY ? paymentID || '' : '',
            transactionScreenshotS3Key: transactionScreenshotS3Key || '',
            'Razorpay POS RRN':
              mode == PaymentMode.RAZORPAY_POS ? `\'${rrNumber || ''}` : '',
            'Snapmint payment id':
              mode == PaymentMode.SNAPMINT ? paymentID || '' : '',
            'Pinelabs payment id':
              mode == PaymentMode.PINELABS ? `\'${externalRefId || ''}` : '',
            'Pinelabs RRN':
              mode == PaymentMode.PINELABS ? `\'${rrNumber || ''}` : '',
            'Pinelabs TID':
              mode == PaymentMode.PINELABS ? `\'${paymentID || ''}` : '',

            'PayU payment id':
              mode == PaymentMode.PAYU ? `\'${paymentID || ''}` : '',
            'Hand over ID':
              mode == PaymentMode.CASH &&
              handoverStatus == HandoverStatus.COLLECTED
                ? handoverId
                : '',
            'Hand over status':
              mode == PaymentMode.CASH && handoverStatus ? handoverStatus : '',
            'Hand over date':
              mode == PaymentMode.CASH && handoverDate
                ? moment(handoverDate)
                    .utcOffset('+05:30')
                    .format('DD/MM/yyyy HH:mm')
                : '',
            'Booking Amount Payment':
              subOrderType !== null && subOrderType === SubOrderType.LOCK_PRICE
                ? 'TRUE'
                : 'FALSE',
            'POS Order Id': order?.id || '-',
            'Super Order Id': order?.superOrderId || '-',
            'Booking Amount':
              subOrderType === SubOrderType.LOCK_PRICE
                ? order?.finalDiscountedAmount
                : '-',
            'Booking Amount Status':
              subOrderType === SubOrderType.LOCK_PRICE
                ? order?.status === OrderStatus.CONFIRMED
                  ? 'PAID'
                  : 'UNPAID'
                : '-',
          };
        })
        .filter((payment: any) => payment !== null);

      return [...acc, ...payments];
    }, []);

    const csvData = await json2csv(data, {});
    return csvData;

    // const csvKey = `private/exports/${new Date().valueOf()}-orders.csv`;

    // await s3
    //   .putObject({
    //     Bucket: S3_BUCKET,
    //     Key: csvKey,
    //     Body: csvData,
    //     ContentType: 'text/csv',
    //   })
    //   .promise();

    // return csvKey;
  }

  async query({
    index,
    limit: size = 100,
    page = 1,
    filter,
    sort,
    nextToken: nt,
  }) {
    let searchAfter;
    if (nt) {
      searchAfter = nt
        ? JSON.parse(Buffer.from(nt, 'base64').toString('ascii'))
        : undefined;
    }

    //Building search request
    const searchParams = {
      index,
      size,
      from: (page - 1) * size,
      body: {
        version: false,
        track_total_hits: true,
        search_after: searchAfter,
        query: filter,
        sort: [...sort],
      },
    };

    // Executing the OpenSearch request
    const { body } = await this.esHandler.search(searchParams);

    const { hits } = body;
    const { hits: results = [], total } = hits;
    const lastResult = results[results.length - 1];
    const nextToken =
      lastResult && lastResult.sort
        ? Buffer.from(JSON.stringify(lastResult.sort), 'ascii').toString(
            'base64',
          )
        : null;

    return {
      page,
      pageSize: size,
      totalPages: Math.ceil(total.value / size),
      total: total.value,
      items: results.map(({ _source }) => _source),
      nextToken: nextToken,
    };
  }

  async queryAll({ index, filter, nextToken: nT }) {
    const sortItems = [{ 'transactionId.keyword': { order: 'desc' } }];

    const { items, nextToken } = await this.query({
      index,
      filter,
      sort: sortItems,
      nextToken: nT,
      limit: 9999,
    });

    if (nextToken) {
      const nextItems = await this.queryAll({
        index,
        filter,
        nextToken,
      });
      return [...items, ...nextItems];
    }

    return items;
  }

  async exportPayments(fromDate: string, toDate: string) {
    const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

    const searchArray = [];
    // const { fromDate = null, toDate = null } = filter || {};

    // if (filter) {
    //   filter.sortBy = null;
    // }

    // const { searchArray } = await filterFormatter(
    //   sortingFilter,
    //   searchingFilter,
    //   sortingFilterType,
    //   filter,
    // );

    if (fromDate && toDate) {
      searchArray.push({
        range: {
          createdAt: {
            gte: fromDate,
            lte: toDate,
          },
        },
      });
    } else if (fromDate) {
      searchArray.push({
        range: {
          createdAt: {
            gte: fromDate,
          },
        },
      });
    } else if (toDate) {
      searchArray.push({
        range: {
          createdAt: {
            lte: toDate,
          },
        },
      });
    }

    const orders = await this.queryAll({
      index: PAYMENT_TABLE,
      filter: {
        bool: {
          must: [...searchArray],
        },
      },
      nextToken: null,
    });

    return await this.exportPaymentCSV(orders);
    // const csvKey = await this.exportOrderCSV(orders);
    // return { success: true, message: csvKey };
  }
}
