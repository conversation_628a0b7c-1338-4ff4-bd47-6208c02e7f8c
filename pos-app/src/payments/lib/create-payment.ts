import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { RazorPayPosModule } from 'src/common/razorpay-pos/razorpay-pos';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreatePaymentInput } from '../dto/create-payment.input';
import { posLogger } from 'src/common/logger';
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuid } from 'uuid';
import moment from 'moment';
import { OrderPaymentHelpers } from './order-helpers';
import { PaymentHelpers } from './payment-helpers';
import { PaymentData } from '../entities/payment.entity';
import { PaymentMode, PaymentStatus } from 'src/common/enum/payment';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { DirectRazorPay } from 'src/common/direct-razorpay/direct-razorpay';
import { AppSnapmint } from 'src/common/snapmint/snapmint';
import { AppConfigParameters } from 'src/config/config';
import { PayUModule } from 'src/common/payu/payu';
import { PineLabsService } from 'src/common/pine-labs/pine-labs';
import { GetStore } from 'src/stores/lib/get-store-by-id';
// import { MswipeService } from 'src/common/mswipe/m-swipe';
import { HandoverStatus } from 'src/common/enum/cms';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';
import { AutoConfirmOrder } from 'src/orders/lib/auto-confirm-order';
import { AppShopify } from 'src/common/shopify/shopify';
import { AppS3Client } from 'src/common/s3-client/s3-client';

export class CreatePayment {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {}

  async createPayment(
    createPaymentInput: CreatePaymentInput,
  ): Promise<PaymentData> {
    posLogger.info('payment', 'createPayment', { input: createPaymentInput });
    try {
      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();
      const {
        orderId = null,
        transactionAmount = 0,
        splitPayment = false,
        mode = null,
        posId = null,
        transactionScreenshotS3Key = null,
        transactionNumber = null,
        phone = null,
        storeId = null,
      } = createPaymentInput;

      if (!orderId || !transactionAmount || !mode || transactionAmount <= 0)
        throw new CustomError('Invalid payment request!', 400);

      const getGlobalConfigurationHandler = new GetGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const { value: PAYMENT_VISIBILITY } =
        (await getGlobalConfigurationHandler.getGlobalConfiguration(
          'PAYMENT_VISIBILITY',
        )) || { value: null };

      if (PAYMENT_VISIBILITY) {
        const paymentVisibility = JSON.parse(PAYMENT_VISIBILITY);
        const paymentVisibilityObj = paymentVisibility?.find(
          (item) => item.mode === mode,
        );

        if (paymentVisibilityObj) {
          if (!paymentVisibilityObj.VISIBILITY)
            throw new CustomError('Payment mode is not enabled!', 400);
        }
      }

      const orderHelper = new OrderPaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const order = await orderHelper.getOrderInfoByOrderID(orderId);
      const {
        totalAmount,
        status: orderStatus,
        quotationId,
        customer,
        isFirstTimeVisit,
        orderType,
      } = order;
      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const {
        transactionDetails: previousPayments,
        totalPaidAmount,
        requestedAmount,
      } = await paymentHelper.getAllPaymentsByOrderId(orderId);

      posLogger.info('Payments', 'createPayment', {
        message: 'Previous Payments and already paid amount',
        payload: {
          Items: previousPayments,
          totalPaidAmount,
          totalAmount,
          requestedAmount,
          transactionAmount,
          requestExceeded: totalAmount - totalPaidAmount - requestedAmount,
        },
      });

      if (
        transactionAmount >
        Math.ceil(
          Number((totalAmount - totalPaidAmount - requestedAmount).toFixed(2)),
        )
      ) {
        throw new CustomError(
          'Order requested payment has been exceeded!',
          400,
        );
      }

      if (totalPaidAmount >= totalAmount) {
        throw new CustomError('Order payment is already completed!', 400);
      }

      let status = PaymentStatus.CREATED;
      let externalRefId = null;
      let shortUrl = null;
      const refId = null;
      let qrImageUrl = null;
      const transactionId = uuid();

      if (!splitPayment) {
        posLogger.info('Payments', 'createPayment', {
          message: 'non split payment request',
        });
        if (Math.ceil(transactionAmount) !== Math.ceil(totalAmount))
          throw new CustomError(
            'In case of non split payments, transaction amount should be same as total order amount!',
            400,
          );
      } else {
        posLogger.info('Payments', 'createPayment', {
          message: 'split payment request',
        });
        if (transactionAmount + totalPaidAmount > Math.ceil(totalAmount))
          throw new CustomError(
            'Invalid payment request, please check the transaction amount against total order amount and already paid amount!',
            400,
          );
      }

      if (mode === PaymentMode.CASH) status = PaymentStatus.COMPLETED;
      if (mode === PaymentMode.M_SWIPE) {
        throw new CustomError('M Swipe is not supported', 400);
      }

      if (mode === PaymentMode.CHEQUE || mode === PaymentMode.BANK_TRANSFER) {
        if (!transactionNumber) {
          throw new CustomError('Transaction Number is compulsory', 400);
        }
        externalRefId = transactionNumber;
        status = PaymentStatus.COMPLETED;
      }

      if (mode === PaymentMode.RAZORPAY_POS) {
        const posMachineSDK = new RazorPayPosModule(
          this.configService,
          this.ssmClient,
        );
        const { success, p2pRequestId } =
          await posMachineSDK.createPaymentRequest(
            transactionAmount,
            orderId,
            transactionId,
            posId,
            'ALL',
          );
        posLogger.info('Payment', 'createPayment', {
          message: 'POS MACHINE REQUEST Result',
          payload: { success, p2pRequestId },
        });

        if (!success)
          throw new CustomError(
            'Failed to initiate request on POS machine!',
            403,
          );
        externalRefId = p2pRequestId;
      } else if (mode === PaymentMode.RAZORPAY) {
        const directRazorPayHandler = new DirectRazorPay(
          this.configService,
          this.docClient,
          this.ssmClient,
        );
        const {
          referenceId,
          status: responseStatus,
          amount,
          short_url = '',
        } = await directRazorPayHandler.createRazorPayPaymentLink(
          orderId,
          transactionId,
          transactionAmount,
          phone,
          isFirstTimeVisit,
        );

        posLogger.info('Payment', 'createPayment', {
          message: 'RAZORPAY REQUEST Result',
          payload: { referenceId, responseStatus, amount },
        });

        if (!referenceId || !responseStatus || !amount)
          throw new CustomError('Failed to initiate RAZORPAY request!', 403);
        externalRefId = referenceId;
        status = responseStatus;
        shortUrl = short_url;
      } else if (mode === PaymentMode.PAYU) {
        const directPayuHandler = new PayUModule(
          this.configService,
          this.ssmClient,
        );
        const { firstName = '', lastName = '', email } = customer;
        const name = `${firstName} ${lastName}`;
        const {
          result,
          status: responseStatus,
          message,
        } = await directPayuHandler.createPayuPayment(
          orderId,
          transactionId,
          transactionAmount,
          phone,
          email,
          name,
          isFirstTimeVisit,
        );
        if (responseStatus !== 0) {
          throw new CustomError(message, 403);
        }
        const { invoiceNumber } = result;
        externalRefId = invoiceNumber;
        status = PaymentStatus.CREATED;
      } else if (mode === PaymentMode.SNAPMINT) {
        if (!storeId) throw new CustomError('Store Id not found', 400);
        const getStoreHandler = new GetStore(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );

        const { snapmintInfo } = await getStoreHandler.getStore(storeId);

        if (!snapmintInfo)
          throw new CustomError('Snapmint details not found!', 400);

        const AppSnapmintHandler = new AppSnapmint(
          this.configService,
          this.ssmClient,
        );
        const {
          id,
          status: responseStatus,
          qr_image: qrImage,
        } = await AppSnapmintHandler.createSnapmintPaymentLink(
          order,
          transactionId,
          transactionAmount,
          phone,
          snapmintInfo,
        );

        posLogger.info('Payment', 'createPayment', {
          message: 'SNAPMINT REQUEST Result',
          payload: { externalRefId, responseStatus },
        });

        if (!id || responseStatus != 'Success')
          throw new CustomError('Failed to initiate SNAPMINT request!', 403);

        externalRefId = id;
        qrImageUrl = qrImage;
      } else if (mode === PaymentMode.PINELABS) {
        const pinelabsHandler = new PineLabsService(
          this.configService,
          this.ssmClient,
        );
        const getStoreHandler = new GetStore(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const { pinelabsInfo } = await getStoreHandler.getStore(storeId);
        if (!pinelabsInfo)
          throw new CustomError('Pinelabs details not found!', 400);
        const pinelabStoreId = pinelabsInfo.find(
          (pinelab) => pinelab.posId === posId,
        )?.storeId;
        if (!pinelabStoreId)
          throw new CustomError(
            'Pinelabs details not found for this store!',
            400,
          );

        const { ResponseCode, PlutusTransactionReferenceID } =
          await pinelabsHandler.createPaymentLink(
            transactionId,
            Number(transactionAmount),
            posId,
            pinelabStoreId,
            customer.phone,
          );
        if (ResponseCode === 0) {
          externalRefId = PlutusTransactionReferenceID.toString();
        } else {
          throw new CustomError('Failed to initiate PINELABS request!', 403);
        }
      }
      // else if (mode === PaymentMode.M_SWIPE && posId) {
      //   const mswipeHandler = new MswipeService(
      //     this.configService,
      //     this.ssmClient,
      //   );
      //   refId = orderId + '-' + previousPayments.length;

      //   if (!posId || !transactionType) {
      //     throw new CustomError('posId or Transaction type not found', 400);
      //   }

      //   const { token = null } = await mswipeHandler.createPayment(
      //     transactionAmount,
      //     posId,
      //     refId,
      //     transactionType,
      //     orderId,
      //     transactionId,
      //   );

      //   if (token) {
      //     externalRefId = token;
      //   } else {
      //     throw new CustomError('Failed to initiate Mswipe request!', 403);
      //   }
      // }
      let paymentMethod = null;
      if (
        mode === PaymentMode.CASH ||
        mode === PaymentMode.CHEQUE ||
        mode === PaymentMode.BANK_TRANSFER
      ) {
        paymentMethod = mode;
      }

      const Item: PaymentData = {
        ...createPaymentInput,
        paymentMethod,
        transactionId,
        refId,
        externalRefId,
        shortUrl,
        status,
        transactionScreenshotS3Key: transactionScreenshotS3Key || null,
        phone: phone || null,
        qrImageUrl: qrImageUrl,
        storeId,
        handoverStatus:
          mode === PaymentMode.CASH ? HandoverStatus.PENDING : null,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      };

      if (status === PaymentStatus.COMPLETED) {
        Item.transactionCompletedDate = moment().toISOString();
      }

      const putCommand = new PutCommand({
        TableName: PAYMENT_TABLE,
        Item,
      });

      const result = await this.docClient.createItem(putCommand);

      if (!result) throw new Error('Payment request failed! Please try again!');

      await orderHelper.validateOrderStatus(
        orderId,
        orderStatus,
        totalAmount,
        quotationId,
        orderType,
      );

      if (mode === PaymentMode.CHEQUE || mode === PaymentMode.BANK_TRANSFER) {
        const handler = new AutoConfirmOrder(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
          this.shopifyClient,
          this.s3Client,
        );
        handler.verifyPaymentAndConfirmOrder(orderId);
      }

      return Item;
    } catch (e) {
      posLogger.error('payment', 'createPayment', { error: e });
      throw e;
    }
  }
}
