import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { RazorPayPosModule } from 'src/common/razorpay-pos/razorpay-pos';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { PaymentHelpers } from './payment-helpers';
import { PaymentMode, PaymentStatus } from 'src/common/enum/payment';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { DirectRazorPay } from 'src/common/direct-razorpay/direct-razorpay';
import { UpdatePaymentStatus } from './update-payment-status';
import { CancelPaymentTransactionInput } from '../dto/cancel-payment-transactioin.input';
import { OrderPaymentHelpers } from './order-helpers';
import { AppConfigParameters } from 'src/config/config';
import { PaymentsService } from '../payments.service';
import { PineLabsService } from 'src/common/pine-labs/pine-labs';
import { PayUModule } from 'src/common/payu/payu';
import { MswipeService } from 'src/common/mswipe/m-swipe';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { AppShopify } from 'src/common/shopify/shopify';
import { AppS3Client } from 'src/common/s3-client/s3-client';

export class CancelPaymentTransaction {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {}

  async cancelPaymentTransaction({
    orderId,
    transactionId,
  }: CancelPaymentTransactionInput) {
    const orderHelper = new OrderPaymentHelpers(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const paymentHelper = new PaymentHelpers(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const {
      totalAmount,
      status: orderStatus,
      quotationId,
      orderType,
    } = await orderHelper.getOrderInfoByOrderID(orderId);
    const transactionInfo =
      await paymentHelper.getPaymentByOrderAndTransactionID(
        orderId,
        transactionId,
      );

    if (!transactionInfo)
      throw new Error('Invalid request, resource not found!');

    const {
      mode,
      externalRefId,
      status,
      transactionAmount,
      posId,
      refId = null,
      storeId = null,
    } = transactionInfo;

    posLogger.info('payment', 'cancelPaymentTransaction', {
      mode,
    });

    if (mode === PaymentMode.BANK_TRANSFER) {
      throw new CustomError('Bank transfer payment cannot be cancelled!', 400);
    }

    if (mode !== PaymentMode.CASH) {
      if (status === PaymentStatus.CREATED) {
        const verifyHandler = new PaymentsService(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
          this.shopifyClient,
          this.s3Client,
        );

        const res = await verifyHandler.verifyPaymentTransaction({
          orderId,
          transactionId,
        });

        if (
          res.status === PaymentStatus.COMPLETED ||
          res.status === PaymentStatus.EXPIRED
        ) {
          return res;
        }

        if (mode === PaymentMode.RAZORPAY) {
          const directRazorPayHandler = new DirectRazorPay(
            this.configService,
            this.docClient,
            this.ssmClient,
          );

          const directCancelResult =
            await directRazorPayHandler.cancelRazorPayLinkByLinkId(
              externalRefId,
            );

          if (directCancelResult.status !== 'cancelled')
            throw new CustomError('Cancellation failed!', 400);
        } else if (mode === PaymentMode.RAZORPAY_POS) {
          const posHandler = new RazorPayPosModule(
            this.configService,
            this.ssmClient,
          );
          const cancelPosResult =
            await posHandler.cancelTransactionRequest(externalRefId);

          if (
            cancelPosResult.success == false &&
            (cancelPosResult.errorCode != 'EZETAP_0000609' ||
              cancelPosResult.errorCode != 'EZETAP_0000611')
          ) {
            throw new CustomError('Cancellation failed!', 400);
          }
        } else if (mode === PaymentMode.PAYU) {
          const payuHandler = new PayUModule(
            this.configService,
            this.ssmClient,
          );
          const { result, errorCode, message } =
            await payuHandler.cancelPaymentLink(externalRefId);

          if (result?.status !== 'inactive' && errorCode != 134)
            throw new CustomError(message, 400);
        } else if (mode === PaymentMode.SNAPMINT) {
        } else if (mode === PaymentMode.PINELABS) {
          const pinelabsHandler = new PineLabsService(
            this.configService,
            this.ssmClient,
          );

          const getStoreHandler = new GetStore(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
          );
          const { pinelabsInfo } = await getStoreHandler.getStore(storeId);
          if (!pinelabsInfo)
            throw new CustomError('Pinelabs details not found!', 400);
          const pinelabStoreId = pinelabsInfo.find(
            (pinelab) => pinelab.posId === posId,
          )?.storeId;
          if (!pinelabStoreId)
            throw new CustomError(
              'Pinelabs details not found for this store!',
              400,
            );

          const { ResponseMessage, ResponseCode } =
            await pinelabsHandler.cancelPaymentLink(
              externalRefId,
              transactionAmount,
              posId,
              pinelabStoreId,
            );

          if (ResponseCode !== 0 && ResponseMessage !== 'TRANSACTION NOT FOUND')
            throw new CustomError(ResponseMessage, 400);
        } else if (mode === PaymentMode.M_SWIPE) {
          const mswipeClient = new MswipeService(
            this.configService,
            this.ssmClient,
          );
          if (!refId || !externalRefId)
            throw new CustomError('RefId and ExternalRefId are required', 400);
          const { ResponseCode } = await mswipeClient.cancelPayment(
            externalRefId,
            refId,
          );

          if (ResponseCode !== '00')
            throw new CustomError('Error in cancelling transaction!', 400);
        } else {
          throw new CustomError('Invalid payment mode!', 400);
        }
      } else {
        return transactionInfo;
      }
    }

    const cancelHandler = new UpdatePaymentStatus(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );

    const cancelResult = await cancelHandler.updatePaymentStatus(
      orderId,
      transactionId,
      PaymentStatus.CANCELLED,
    );

    if (!cancelResult) throw new Error('Cancellation failed!');

    await orderHelper.validateOrderStatus(
      orderId,
      orderStatus,
      totalAmount,
      quotationId,
      orderType,
    );

    return cancelResult;
  }
}
