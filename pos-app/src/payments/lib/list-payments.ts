// modules

import { ConfigService } from '@nestjs/config';
import { filterFormatter } from 'src/common/helper/filter-helper';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ListPaymentInput } from '../dto/list-payment.input';
import { PaymentData, Payments } from '../entities/payment.entity';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { AppConfigParameters } from 'src/config/config';
import { PaymentMode, PaymentStatus } from 'src/common/enum/payment';

export class QueryPayments {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryPayments(filter: ListPaymentInput): Promise<Payments> {
    posLogger.info('payment', 'queryPayment', { input: filter });
    try {
      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();
      const { fromDate = null, toDate = null } = filter || {};
      const searchArray: any = [
        {
          match: {
            mode: PaymentMode.CASH,
          },
        },
        {
          match: {
            status: PaymentStatus.COMPLETED,
          },
        },
      ];

      const { searchArray: filteredSearch, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );

      if (fromDate && toDate) {
        searchArray.push({
          range: {
            orderCreatedAt: {
              gte: fromDate,
              lte: toDate,
            },
          },
        });
      } else if (fromDate) {
        searchArray.push({
          range: {
            orderCreatedAt: {
              gte: fromDate,
            },
          },
        });
      } else if (toDate) {
        searchArray.push({
          range: {
            orderCreatedAt: {
              lte: toDate,
            },
          },
        });
      }

      searchArray.push(...filteredSearch);

      const size = Number(filter?.size) || 10000;
      const from = Number(filter?.from) || 0;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      // if (!paginate) {
      //   const { body: bodyRes } = await esHandler.count({
      //     index: PAYMENT_TABLE,
      //   });

      //   size = bodyRes?.count;
      // }

      if (searchArray.length) {
        const { body: bodyRes } = await esHandler.count({
          index: PAYMENT_TABLE,
          body: {
            query: {
              bool: {
                must: [...searchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: PAYMENT_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data: PaymentData[] = response.body.hits.hits.map(
          (hit) => hit._source,
        );

        return { data, count: bodyRes?.count };
      }

      const response = await esHandler.search({
        index: PAYMENT_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data: PaymentData[] = response.body.hits.hits.map(
        (hit) => hit._source,
      );

      const { body: bodyRes } = await esHandler.count({
        index: PAYMENT_TABLE,
      });

      return { data, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('payment', 'queryPayments', { error: e });
      throw e;
    }
  }
}
