import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { UpdateOrderStatus } from 'src/orders/lib/update-order-status';
import { PaymentHelpers } from './payment-helpers';
import { OrderStatus, OrderType } from 'src/common/enum/order';
import { AppConfigParameters } from 'src/config/config';
import { UpdateQuotationStatus } from 'src/quotations/lib/set-quotation-status';
import { QuotationStatus } from 'src/common/enum/quotations';

export class OrderPaymentHelpers {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getOrderInfoByOrderID(id: string) {
    posLogger.info('Payments', 'getOrderInfoByOrderID', {
      input: { id },
    });
    try {
      const ORDER_TABLE = await this.configParameters.getOrderTableName();

      const param = new GetCommand({
        TableName: ORDER_TABLE,
        Key: { id },
      });
      const { Item: order } = await this.docClient.getItem(param);

      if (!order) {
        throw new CustomError(
          `Invalid ID! No order found for given ID ${id}.`,
          404,
        );
      }

      return {
        ...order,
        totalAmount: order.finalDiscountedAmount,
        customer: order.customer,
        status: order.status,
      };
    } catch (error) {
      posLogger.error('order', 'getOrder', { error });
      throw error;
    }
  }

  async validateOrderStatus(
    orderId: string,
    orderStatus: string,
    totalAmount: number,
    quotationId: string,
    orderType: OrderType,
  ) {
    posLogger.info('Payments', 'validateOrderStatus', {
      input: { orderId, orderStatus },
    });

    try {
      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const orderStatusInstance = new UpdateOrderStatus(
        this.configService,
        this.ssmClient,
        this.docClient,
        this.configParameters,
      );

      const { totalPaidAmount: totalPaidAmountAfterLastTransaction } =
        await paymentHelper.getAllPaymentsByOrderId(orderId);

      if (
        totalPaidAmountAfterLastTransaction === 0 &&
        orderStatus !== OrderStatus.PENDING
      ) {
        await orderStatusInstance.updateOrderStatus(
          orderId,
          OrderStatus.PENDING,
        );

        if (
          orderType !== OrderType.REPLACEMENT &&
          orderType !== OrderType.RETURN
        ) {
          const handler = new UpdateQuotationStatus(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
          );

          await handler.updateQuotationStatus(
            quotationId,
            QuotationStatus.ORDER_CREATED,
            orderId,
          );
        }
      } else if (
        totalPaidAmountAfterLastTransaction > 0 &&
        totalPaidAmountAfterLastTransaction !== totalAmount &&
        orderStatus !== OrderStatus.PARTIALLY_CONFIRMED
      ) {
        await orderStatusInstance.updateOrderStatus(
          orderId,
          OrderStatus.PARTIALLY_CONFIRMED,
        );

        if (
          orderType !== OrderType.REPLACEMENT &&
          orderType !== OrderType.RETURN
        ) {
          const handler = new UpdateQuotationStatus(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
          );

          await handler.updateQuotationStatus(
            quotationId,
            QuotationStatus.PAYMENT_INITIATED,
            orderId,
          );
        }
      } else if (
        totalPaidAmountAfterLastTransaction === totalAmount &&
        orderStatus !== OrderStatus.CONFIRMED
      ) {
        await orderStatusInstance.updateOrderStatus(
          orderId,
          OrderStatus.CONFIRMED,
        );
      }
    } catch (error) {
      posLogger.error('order', 'validateOrderStatus', { error });
      throw new CustomError(error, 500);
    }
  }
}
