import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { PaymentHelpers } from './payment-helpers';
import { RazorPayPosModule } from 'src/common/razorpay-pos/razorpay-pos';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { VerifyPaymentTransactionInput } from '../dto/verify-razor-pay-pos-payment.input';
import { PaymentMode, PaymentStatus } from 'src/common/enum/payment';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import moment from 'moment';
import { PaymentData } from '../entities/payment.entity';
import { DirectRazorPay } from 'src/common/direct-razorpay/direct-razorpay';
import { AppConfigParameters } from 'src/config/config';
import { PayUModule } from 'src/common/payu/payu';
import { AppSnapmint } from 'src/common/snapmint/snapmint';
import { PineLabsService } from 'src/common/pine-labs/pine-labs';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { MswipeService } from 'src/common/mswipe/m-swipe';

export class VerifyPaymentTransaction {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async verifyPOSMachineTransaction(
    verifyPaymentTransactionInput: VerifyPaymentTransactionInput,
  ): Promise<PaymentData> {
    posLogger.info('Payment', 'verifyPOSMachineTransaction', {
      input: { verifyPaymentTransactionInput },
    });

    try {
      const { orderId = false, transactionId = false } =
        verifyPaymentTransactionInput;
      if (!orderId || !transactionId)
        throw new Error(
          'Invalid request, orderId & transactionId is required!',
        );

      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const Item: PaymentData =
        await paymentHelper.getPaymentByOrderAndTransactionID(
          orderId,
          transactionId,
        );

      const { mode, status: oldStatus, externalRefId } = Item || {};
      if (mode !== PaymentMode.RAZORPAY_POS)
        throw new CustomError(
          'Invalid request, transaction is not made from RazorPayPos Machine!',
          400,
        );

      if (oldStatus == PaymentStatus.COMPLETED) return Item;

      if (!externalRefId)
        throw new CustomError(
          'External reference ID not available for this transaction.',
          400,
        );

      const posMachineSDK = new RazorPayPosModule(
        this.configService,
        this.ssmClient,
      );
      const {
        success = false,
        status,
        messageCode,
        rrNumber,
        txnId,
        amountOriginal: amountPaid,
        paymentMode: paymentMethod,
      } = await posMachineSDK.fetchTransactionStatus(externalRefId);

      if (!success) throw new CustomError('Payment status fetch failed!', 403);

      posLogger.info('Payment', 'verifyPOSMachineTransaction', {
        success,
        status,
        messageCode,
      });

      if (messageCode === 'P2P_DEVICE_RECEIVED') return Item;

      let newStatus: PaymentStatus = oldStatus;

      if (
        messageCode === 'P2P_DEVICE_CANCELED' ||
        messageCode === 'P2P_STATUS_IN_CANCELED_FROM_EXTERNAL_SYSTEM'
      )
        newStatus = PaymentStatus.CANCELLED;
      if (messageCode === 'P2P_DEVICE_TXN_DONE' && status === 'AUTHORIZED')
        newStatus = PaymentStatus.COMPLETED;
      if (
        messageCode === 'P2P_DEVICE_TXN_DONE' &&
        (status === 'FAILED' || status === 'REVERSED')
      )
        newStatus = PaymentStatus.FAILED;
      if (
        (messageCode === 'P2P_DEVICE_TXN_DONE' && status === 'EXPIRED') ||
        messageCode === 'P2P_STATUS_EXPIRED'
      )
        newStatus = PaymentStatus.EXPIRED;

      const updateExpression = [
        'SET #status = :status, #updatedAt = :updatedAt, rrNumber = :rrNumber, paymentID = :paymentID, amountPaid = :amountPaid, paymentMethod = :paymentMethod',
      ];

      const expressionAttributeValues: Record<string, any> = {
        ':status': newStatus,
        ':updatedAt': moment().toISOString(),
        ':rrNumber': rrNumber ? `${rrNumber}` : null,
        ':paymentID': txnId || null,
        ':amountPaid': amountPaid || null,
        ':paymentMethod': paymentMethod || null,
      };

      if (newStatus !== oldStatus) {
        updateExpression.push(
          'transactionCompletedDate = :transactionCompletedDate',
        );
        expressionAttributeValues[':transactionCompletedDate'] =
          moment().toISOString(); // Get the current date in ISO format
      }

      const { Attributes } = await this.docClient.updateItem(
        new UpdateCommand({
          TableName: PAYMENT_TABLE,
          Key: {
            orderId,
            transactionId,
          },
          UpdateExpression: updateExpression.join(', '),
          ExpressionAttributeNames: {
            '#status': 'status',
            '#updatedAt': 'updatedAt',
          },
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: 'ALL_NEW',
          ConditionExpression:
            'attribute_exists(orderId) AND attribute_exists(transactionId)',
        }),
      );
      return Attributes;
    } catch (e) {
      posLogger.error('Payment', 'verifyPOSMachineTransaction', e);
      throw e;
    }
  }

  async verifyRazorPayPaymentLinkTransaction(
    verifyPaymentTransactionInput: VerifyPaymentTransactionInput,
  ): Promise<PaymentData> {
    posLogger.info('Payment', 'verifyRazorPayPaymentLinkTransaction', {
      input: { verifyPaymentTransactionInput },
    });

    try {
      const { orderId = false, transactionId = false } =
        verifyPaymentTransactionInput;
      if (!orderId || !transactionId)
        throw new Error(
          'Invalid request, orderId & transactionId are required!',
        );

      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const Item: PaymentData =
        await paymentHelper.getPaymentByOrderAndTransactionID(
          orderId,
          transactionId,
        );

      const { mode, status: oldStatus, externalRefId } = Item || {};
      if (mode !== PaymentMode.RAZORPAY)
        throw new CustomError(
          'Invalid request, transaction is not made from RazorPay Link!',
          400,
        );

      if (oldStatus == PaymentStatus.COMPLETED) return Item;

      if (!externalRefId)
        throw new CustomError(
          'External reference ID not available for this transaction.',
          400,
        );

      const razorPayClient = new DirectRazorPay(
        this.configService,
        this.docClient,
        this.ssmClient,
      );
      let amountPaid;
      const { status, payments } =
        await razorPayClient.fetchRazorPayLinkByLinkId(externalRefId);

      posLogger.info('Payment', 'verifyRazorPayPaymentLinkTransaction', {
        status,
      });

      let newStatus: PaymentStatus = status,
        paymentID = null;
      let paymentMethod = null;
      if (status === PaymentStatus.PAID && payments && payments.length) {
        paymentID = payments[0].payment_id;
        paymentMethod = payments[0].method;
        newStatus = PaymentStatus.COMPLETED;
        const verifyResponse =
          await razorPayClient.getActualPaidRazorPayAmount(paymentID);
        amountPaid = verifyResponse?.amount;
      } else if (status === PaymentStatus.EXPIRED) {
        newStatus = PaymentStatus.EXPIRED;
      }

      const updateExpression = [
        'SET #status = :status, #updatedAt = :updatedAt, paymentID = :paymentID, amountPaid = :amountPaid , paymentMethod = :paymentMethod',
      ];

      const expressionAttributeValues: Record<string, any> = {
        ':status': newStatus,
        ':paymentID': paymentID || null,
        ':updatedAt': moment().toISOString(),
        ':amountPaid': amountPaid ? Number(amountPaid) / 100 : null,
        ':paymentMethod': paymentMethod || null,
      };

      if (newStatus !== oldStatus) {
        updateExpression.push(
          'transactionCompletedDate = :transactionCompletedDate',
        );
        expressionAttributeValues[':transactionCompletedDate'] =
          moment().toISOString(); // Current date in ISO format
      }

      const { Attributes } = await this.docClient.updateItem(
        new UpdateCommand({
          TableName: PAYMENT_TABLE,
          Key: {
            orderId,
            transactionId,
          },
          UpdateExpression: updateExpression.join(', '),
          ExpressionAttributeNames: {
            '#status': 'status',
            '#updatedAt': 'updatedAt',
          },
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: 'ALL_NEW',
          ConditionExpression:
            'attribute_exists(orderId) AND attribute_exists(transactionId)',
        }),
      );
      return Attributes;
    } catch (e) {
      posLogger.error('Payment', 'verifyRazorPayPaymentLinkTransaction', {
        error: e,
      });
      throw e;
    }
  }

  async verifyPayUPaymentLinkTransaction(
    verifyPaymentTransactionInput: VerifyPaymentTransactionInput,
  ): Promise<PaymentData> {
    posLogger.info('Payment', 'verifyPayUPaymentLinkTransaction', {
      input: { verifyPaymentTransactionInput },
    });

    try {
      const { orderId = false, transactionId = false } =
        verifyPaymentTransactionInput;
      if (!orderId || !transactionId)
        throw new Error(
          'Invalid request, orderId & transactionId are required!',
        );

      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const Item: PaymentData =
        await paymentHelper.getPaymentByOrderAndTransactionID(
          orderId,
          transactionId,
        );

      const { mode, status: oldStatus, externalRefId } = Item || {};
      if (mode !== PaymentMode.PAYU)
        throw new CustomError(
          'Invalid request, transaction is not made from PayU Link!',
          400,
        );

      if (oldStatus == PaymentStatus.COMPLETED) return Item;

      if (!externalRefId)
        throw new CustomError(
          'External reference ID not available for this transaction.',
          400,
        );

      // Replace this with appropriate PayU client initialization and API calls
      const payUClient = new PayUModule(this.configService, this.ssmClient);
      const {
        status,
        amountPaid,
        mihpayid,
        mode: paymentMethod,
      } = await payUClient.verifyPayuPayment(transactionId);

      posLogger.info('Payment', 'verifyPayUPaymentLinkTransaction', {
        status,
        amountPaid,
      });

      let newStatus: PaymentStatus = status;
      const paymentID = mihpayid;
      if (!status) {
        newStatus = oldStatus;
      }
      if (status === 'SUCCESS') {
        newStatus = PaymentStatus.COMPLETED;
      } else if (status === 'PENDING') {
        newStatus = PaymentStatus.CREATED;
      } else if (status === 'FAILURE') {
        newStatus = PaymentStatus.FAILED;
      } else {
        newStatus = oldStatus;
      }

      // Prepare the update expression and attribute values
      const updateExpression = [
        'SET #status = :status, #updatedAt = :updatedAt, paymentID = :paymentID, amountPaid = :amountPaid,paymentMethod = :paymentMethod',
      ];

      const expressionAttributeValues: Record<string, any> = {
        ':status': newStatus,
        ':paymentID': paymentID || null,
        ':updatedAt': moment().toISOString(),
        ':amountPaid': amountPaid ? Number(amountPaid) : null,
        ':paymentMethod': paymentMethod || null,
      };

      // Add transactionCompletedDate if newStatus is different from oldStatus
      if (newStatus !== oldStatus) {
        updateExpression.push(
          'transactionCompletedDate = :transactionCompletedDate',
        );
        expressionAttributeValues[':transactionCompletedDate'] =
          moment().toISOString(); // Current date in ISO format
      }

      const { Attributes } = await this.docClient.updateItem(
        new UpdateCommand({
          TableName: PAYMENT_TABLE,
          Key: {
            orderId,
            transactionId,
          },
          UpdateExpression: updateExpression.join(', '),
          ExpressionAttributeNames: {
            '#status': 'status',
            '#updatedAt': 'updatedAt',
          },
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: 'ALL_NEW',
          ConditionExpression:
            'attribute_exists(orderId) AND attribute_exists(transactionId)',
        }),
      );
      return Attributes;
    } catch (e) {
      posLogger.error('Payment', 'verifyPayUPaymentLinkTransaction', {
        error: e,
      });
      throw e;
    }
  }

  async verifySnapmintTransaction(
    verifyPaymentTransactionInput: VerifyPaymentTransactionInput,
  ): Promise<PaymentData> {
    posLogger.info('Payment', 'verifySnapmintTransaction', {
      input: { verifyPaymentTransactionInput },
    });

    try {
      const { orderId = false, transactionId = false } =
        verifyPaymentTransactionInput;
      if (!orderId || !transactionId)
        throw new Error(
          'Invalid request, orderId & transactionId is required!',
        );

      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const Item: PaymentData =
        await paymentHelper.getPaymentByOrderAndTransactionID(
          orderId,
          transactionId,
        );

      const { mode, status: oldStatus, storeId } = Item || {};

      if (!storeId) throw new CustomError('Store Id not found', 400);
      const getStoreHandler = new GetStore(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const { snapmintInfo } = await getStoreHandler.getStore(storeId);

      if (!snapmintInfo)
        throw new CustomError('Snapmint details not found!', 400);

      if (mode !== PaymentMode.SNAPMINT)
        throw new CustomError(
          'Invalid request, transaction is not made from Snapmint Link!',
          400,
        );

      if (oldStatus == PaymentStatus.COMPLETED) return Item;

      const snapmintClient = new AppSnapmint(
        this.configService,
        this.ssmClient,
      );

      const {
        status: snapmintStatus,
        order_status,
        snapmint_id,
        message,
      } = await snapmintClient.fetchTransactionStatus(
        transactionId,
        snapmintInfo.accessToken,
      );

      if (snapmintStatus !== 'Success') {
        throw new CustomError(message, 400);
      }

      if (order_status === 'PENDING_STATUS') {
        return Item;
      }

      let newStatus: PaymentStatus = oldStatus;
      const paymentMethod = PaymentMode.SNAPMINT;

      if (
        order_status === 'CAPTURED' ||
        order_status === 'SETTLED' ||
        order_status === 'AUTHORIZED'
      ) {
        newStatus = PaymentStatus.COMPLETED;
      } else if (order_status === 'NOT_FOUND') {
        newStatus = PaymentStatus.FAILED;
      } else if (order_status === 'CANCELLED') {
        newStatus = PaymentStatus.CANCELLED;
      }

      // Prepare the update expression and attribute values
      const updateExpression = [
        'SET #status = :status, #updatedAt = :updatedAt, paymentID = :paymentID, paymentMethod = :paymentMethod',
      ];

      const expressionAttributeValues: Record<string, any> = {
        ':status': newStatus,
        ':updatedAt': moment().toISOString(),
        ':paymentID': snapmint_id || null,
        ':paymentMethod': paymentMethod || null,
      };

      // Add transactionCompletedDate if newStatus is different from oldStatus
      if (newStatus !== oldStatus) {
        updateExpression.push(
          'transactionCompletedDate = :transactionCompletedDate',
        );
        expressionAttributeValues[':transactionCompletedDate'] =
          moment().toISOString(); // Current date in ISO format
      }

      const { Attributes } = await this.docClient.updateItem(
        new UpdateCommand({
          TableName: PAYMENT_TABLE,
          Key: {
            orderId,
            transactionId,
          },
          UpdateExpression: updateExpression.join(', '),
          ExpressionAttributeNames: {
            '#status': 'status',
            '#updatedAt': 'updatedAt',
          },
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: 'ALL_NEW',
          ConditionExpression:
            'attribute_exists(orderId) AND attribute_exists(transactionId)',
        }),
      );

      return Attributes;
    } catch (error) {
      posLogger.error('Payment', 'verifySnapmintTransaction', error);
      throw error;
    }
  }

  async verifyPineLabsPaymentLinkTransaction(
    verifyPaymentTransactionInput: VerifyPaymentTransactionInput,
  ): Promise<PaymentData> {
    posLogger.info('Payment', 'verifyPineLabsPaymentLinkTransaction', {
      input: { verifyPaymentTransactionInput },
    });

    try {
      const { orderId = false, transactionId = false } =
        verifyPaymentTransactionInput;
      if (!orderId || !transactionId)
        throw new Error(
          'Invalid request, orderId & transactionId are required!',
        );

      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const Item: PaymentData =
        await paymentHelper.getPaymentByOrderAndTransactionID(
          orderId,
          transactionId,
        );

      const {
        mode,
        status: oldStatus,
        externalRefId,
        posId,
        storeId = null,
      } = Item || {};
      const getStoreHandler = new GetStore(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const { pinelabsInfo } = await getStoreHandler.getStore(storeId);
      if (!pinelabsInfo)
        throw new CustomError('Pinelabs details not found!', 400);
      const pinelabStoreId = pinelabsInfo.find(
        (pinelab) => pinelab.posId === posId,
      )?.storeId;
      if (!pinelabStoreId)
        throw new CustomError(
          'Pinelabs details not found for this store!',
          400,
        );

      if (mode !== PaymentMode.PINELABS)
        throw new CustomError(
          'Invalid request, transaction is not made from Pine Labs Link!',
          400,
        );

      if (oldStatus == PaymentStatus.COMPLETED) return Item;

      if (!externalRefId)
        throw new CustomError(
          'External reference ID not available for this transaction.',
          400,
        );

      // Replace this with appropriate Pine Labs client initialization and API calls
      const pineLabsClient = new PineLabsService(
        this.configService,
        this.ssmClient,
      );
      const data = await pineLabsClient.verifyPaymentLink(
        externalRefId,
        posId,
        pinelabStoreId,
      );

      let newStatus: PaymentStatus = oldStatus;
      let amountPaid = 0;
      let paymentID = null;
      let refId = null;
      let rrNumber = null;
      let paymentMethod = null;
      let extraData = undefined;

      if (data.ResponseMessage === 'TXN UPLOADED') {
        newStatus = PaymentStatus.CREATED;
      } else if (data.ResponseMessage === 'TXN APPROVED') {
        newStatus = PaymentStatus.COMPLETED;
        const amountTag = data.TransactionData.find(
          (item) => item.Tag === 'Amount',
        );
        const tid = data.TransactionData.find((item) => item.Tag === 'TID');
        const rrn = data.TransactionData.find((item) => item.Tag === 'RRN');
        const transactionLogId = data.TransactionData.find(
          (item) => item.Tag === 'TransactionLogId',
        );
        const paymentMethodObj = data.TransactionData.find(
          (item) => item.Tag === 'PaymentMode',
        );
        const cardNumber = data.TransactionData.find(
          (item) => item.Tag === 'Card Number',
        );
        const cardType = data.TransactionData.find(
          (item) => item.Tag === 'Card Type',
        );
        const cardHolderName = data.TransactionData.find(
          (item) => item.Tag === 'Card Holder Name',
        );
        const bankName = data.TransactionData.find(
          (item) => item.Tag === 'Acquirer Name',
        );
        const upiId = data.TransactionData.find(
          (item) => item.Tag === 'Customer VPA',
        );

        if (paymentMethodObj && paymentMethodObj?.Value === 'UPI SALE') {
          extraData = {
            upiId: upiId?.Value || '',
            rrn: rrn?.Value || '',
          };
        }

        if (paymentMethodObj && paymentMethodObj?.Value === 'CARD') {
          extraData = {
            bankName: bankName?.Value || '',
            rrn: rrn?.Value || '',
            cardDetails: {
              cardHolderName: cardHolderName?.Value?.trim() || '',
              last4Digits: cardNumber?.Value?.replace(/\*/g, '') || '',
              network: cardType?.Value || '',
            },
          };
        }

        if (paymentMethodObj) {
          paymentMethod = paymentMethodObj.Value;
        }
        if (amountTag) {
          amountPaid = Number(amountTag.Value);
        }
        if (tid) {
          paymentID = tid.Value;
        }
        if (rrn) {
          rrNumber = rrn.Value;
        }
        if (transactionLogId) {
          refId = transactionLogId.Value;
        }
      } else if (data.ResponseMessage === 'INVALID PLUTUS TXN REF ID') {
        newStatus = PaymentStatus.CANCELLED;
      }

      const updateExpression = [
        'SET #status = :status, #updatedAt = :updatedAt, paymentID = :paymentID, amountPaid = :amountPaid, rrNumber = :rrNumber, refId = :refId, paymentMethod = :paymentMethod',
      ];

      const expressionAttributeValues: Record<string, any> = {
        ':status': newStatus,
        ':paymentID': paymentID || null,
        ':refId': refId || null,
        ':rrNumber': rrNumber || null,
        ':updatedAt': moment().toISOString(),
        ':amountPaid': amountPaid ? Number(amountPaid) : null,
        ':paymentMethod': paymentMethod || null,
      };

      if (extraData) {
        Object.entries(extraData).map(([key, value]) => {
          updateExpression.push(`${key} = :${key}`);
          expressionAttributeValues[`:${key}`] = value;
        });
      }

      // Add transactionCompletedDate if newStatus is different from oldStatus
      if (newStatus !== oldStatus) {
        updateExpression.push(
          'transactionCompletedDate = :transactionCompletedDate',
        );
        expressionAttributeValues[':transactionCompletedDate'] =
          moment().toISOString(); // Current date in ISO format
      }

      const { Attributes } = await this.docClient.updateItem(
        new UpdateCommand({
          TableName: PAYMENT_TABLE,
          Key: {
            orderId,
            transactionId,
          },
          UpdateExpression: updateExpression.join(', '),
          ExpressionAttributeNames: {
            '#status': 'status',
            '#updatedAt': 'updatedAt',
          },
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: 'ALL_NEW',
          ConditionExpression:
            'attribute_exists(orderId) AND attribute_exists(transactionId)',
        }),
      );

      return Attributes;
    } catch (e) {
      posLogger.error('Payment', 'verifyPineLabsPaymentLinkTransaction', {
        error: e,
      });
      throw e;
    }
  }

  async verifyMswipePaymentLinkTransaction(
    verifyPaymentTransactionInput: VerifyPaymentTransactionInput,
  ): Promise<PaymentData> {
    posLogger.info('Payment', 'verifyMswipePaymentLinkTransaction', {
      input: { verifyPaymentTransactionInput },
    });

    try {
      const { orderId = false, transactionId = false } =
        verifyPaymentTransactionInput;
      if (!orderId || !transactionId)
        throw new Error(
          'Invalid request, orderId & transactionId are required!',
        );

      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const Item: PaymentData =
        await paymentHelper.getPaymentByOrderAndTransactionID(
          orderId,
          transactionId,
        );

      const { mode, status: oldStatus, refId = null } = Item || {};

      if (mode !== PaymentMode.M_SWIPE)
        throw new CustomError(
          'Invalid request, transaction is not made from Mswipe Link!',
          400,
        );

      if (oldStatus == PaymentStatus.COMPLETED) return Item;

      if (!refId)
        throw new CustomError(
          'Reference ID not available for this transaction.',
          400,
        );

      // Initialize Mswipe client and verify payment link
      const mswipeClient = new MswipeService(
        this.configService,
        this.ssmClient,
      );
      const { status, VerificationData = null } =
        await mswipeClient.verifyPayment(refId);

      let newStatus: PaymentStatus = oldStatus;
      let amountPaid = 0;
      let paymentID = null;
      let rrNumber = null;
      let transactionCompletedDate: string | null = null;

      if (status === 'true') {
        newStatus = PaymentStatus.COMPLETED;
        transactionCompletedDate = moment().toISOString();
        if (VerificationData && VerificationData.Amount) {
          const { Amount, RRNO, JV_Voucher_No } = VerificationData;
          amountPaid = Number(Amount);
          paymentID = JV_Voucher_No;
          rrNumber = RRNO;
        }
        // Set transactionCompletedDate if status changes to COMPLETED
      }

      const updateExpression = [
        'SET #status = :status',
        '#updatedAt = :updatedAt',
        'paymentID = :paymentID',
        'amountPaid = :amountPaid',
        'rrNumber = :rrNumber',
      ];
      const expressionAttributeValues: any = {
        ':status': newStatus,
        ':paymentID': paymentID || null,
        ':updatedAt': moment().toISOString(),
        ':amountPaid': amountPaid ? Number(amountPaid) : null,
        ':rrNumber': rrNumber,
      };

      // Add transactionCompletedDate to update expression if it is set
      if (transactionCompletedDate) {
        updateExpression.push(
          'transactionCompletedDate = :transactionCompletedDate',
        );
        expressionAttributeValues[':transactionCompletedDate'] =
          transactionCompletedDate;
      }

      const { Attributes } = await this.docClient.updateItem(
        new UpdateCommand({
          TableName: PAYMENT_TABLE,
          Key: { orderId, transactionId },
          UpdateExpression: updateExpression.join(', '),
          ExpressionAttributeNames: {
            '#status': 'status',
            '#updatedAt': 'updatedAt',
          },
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: 'ALL_NEW',
          ConditionExpression:
            'attribute_exists(orderId) AND attribute_exists(transactionId)',
        }),
      );
      return Attributes;
    } catch (e) {
      posLogger.error('Payment', 'verifyMswipePaymentLinkTransaction', {
        error: e,
      });
      throw e;
    }
  }
  async manualVerification(
    verifyPaymentTransactionInput: VerifyPaymentTransactionInput,
  ): Promise<any> {
    posLogger.info('Payment', 'manualVerification', {
      input: {
        verifyPaymentTransactionInput,
      },
    });

    try {
      const { transactionId, orderId, manualVerification } =
        verifyPaymentTransactionInput;

      // Check if manualVerification.paymentID is provided
      if (!manualVerification?.paymentID) {
        throw new CustomError(
          'PaymentID is required for manual verification.',
          400,
        );
      }

      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();
      const {
        amountPaid = null,
        paymentMethod = null,
        paymentID,
      } = manualVerification;

      const updateExpression = [
        'SET #status = :status',
        '#paymentID = :paymentID',
        '#updatedAt = :updatedAt',
        '#amountPaid = :amountPaid',
        '#paymentMethod = :paymentMethod',
        '#transactionCompletedDate = :transactionCompletedDate',
        '#isManualVerification = :isManualVerification',
      ];

      const expressionAttributeValues: Record<string, any> = {
        ':status': PaymentStatus.COMPLETED,
        ':paymentID': paymentID,
        ':updatedAt': moment().toISOString(),
        ':amountPaid': Number(amountPaid),
        ':paymentMethod': paymentMethod,
        ':transactionCompletedDate': moment().toISOString(),
        ':isManualVerification': true,
      };

      const { Attributes } = await this.docClient.updateItem(
        new UpdateCommand({
          TableName: PAYMENT_TABLE,
          Key: { transactionId, orderId },
          UpdateExpression: updateExpression.join(', '),
          ExpressionAttributeNames: {
            '#status': 'status',
            '#paymentID': 'paymentID',
            '#updatedAt': 'updatedAt',
            '#amountPaid': 'amountPaid',
            '#paymentMethod': 'paymentMethod',
            '#transactionCompletedDate': 'transactionCompletedDate',
            '#isManualVerification': 'isManualVerification',
          },
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: 'ALL_NEW',
          ConditionExpression:
            'attribute_exists(orderId) AND attribute_exists(transactionId)',
        }),
      );

      posLogger.info('Payment', 'manualVerification - Update Success', {
        updatedAttributes: Attributes,
      });

      return Attributes;
    } catch (error) {
      posLogger.error('Payment', 'manualVerification - Update Failed', {
        error,
      });
      throw error;
    }
  }
}
