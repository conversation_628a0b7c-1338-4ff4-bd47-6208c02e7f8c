import { GetCommand, QueryCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { PaymentStatus } from 'src/common/enum/payment';
import { PaymentData } from '../entities/payment.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { PaymentDetails } from 'src/orders/entities/order.entity';
import { AppConfigParameters } from 'src/config/config';
import moment from 'moment';

export class PaymentHelpers {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateAllPayments({
    transactionDetails,
    createdAt,
    shopifyCreatedAt,
    shopifyOrderName,
    orderStatus,
    shopifyOrderId,
    finalDiscountedAmount,
  }): Promise<PaymentData[]> {
    try {
      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      return await Promise.all(
        transactionDetails.map(async ({ transactionId, orderId }) => {
          const command = new UpdateCommand({
            TableName: PAYMENT_TABLE,
            Key: {
              orderId,
              transactionId,
            },
            UpdateExpression:
              'SET orderCreatedAt = :orderCreatedAt, orderShopifyCreatedAt = :orderShopifyCreatedAt, orderTotalAmount = :orderTotalAmount, orderStatus = :orderStatus, shopifyOrderId = :shopifyOrderId, shopifyOrderName =:shopifyOrderName, #updatedAt = :updatedAt',
            ExpressionAttributeNames: {
              '#updatedAt': 'updatedAt',
            },
            ExpressionAttributeValues: {
              ':orderCreatedAt': moment(createdAt).toISOString(),
              ':orderShopifyCreatedAt': moment(shopifyCreatedAt).toISOString(),
              ':orderTotalAmount': finalDiscountedAmount,
              ':orderStatus': orderStatus,
              ':shopifyOrderId': shopifyOrderId,
              ':shopifyOrderName': shopifyOrderName,
              ':updatedAt': moment().toISOString(),
            },
            ConditionExpression:
              'attribute_exists(orderId) AND attribute_exists(transactionId)',
            ReturnValues: 'ALL_NEW',
          });

          const { Attributes } = await this.docClient.updateItem(command);
          return Attributes;
        }),
      );
    } catch (error) {
      posLogger.error('payment', 'updateAllPayments', error);
      throw new CustomError(error.message, 400);
    }
  }

  async getAllPaymentsByOrderId(orderId: string): Promise<PaymentDetails> {
    posLogger.info('payment', 'getAllPaymentsByOrderId', { input: orderId });
    try {
      const [PAYMENT_TABLE, ORDER_TABLE] = await Promise.all([
        await this.configParameters.getPaymentTableName(),
        await this.configParameters.getOrderTableName(),
      ]);

      const orderQuery = new GetCommand({
        TableName: ORDER_TABLE,
        Key: {
          id: orderId,
        },
      });

      const { Item } = await this.docClient.getItem(orderQuery);
      if (!Item || !Object.keys(Item).length)
        throw new Error(`Order not found with this ID ${orderId}!`);
      const { finalDiscountedAmount: totalOrderAmount } = Item;

      const queryCommand = new QueryCommand({
        TableName: PAYMENT_TABLE,
        KeyConditions: {
          orderId: {
            ComparisonOperator: 'EQ',
            AttributeValueList: [orderId],
          },
        },
      });

      const result = await this.docClient.queryItems(queryCommand);
      if (result && result.Items && result.Items.length) {
        let { Items = [] }: { Items: PaymentData[] } = result;
        Items = Items.sort(
          (a: PaymentData, b: PaymentData) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );

        const totalAmount = Items.reduce((acc, curr) => {
          if (curr.status === PaymentStatus.COMPLETED) {
            acc = acc + curr.transactionAmount;
          }
          return acc;
        }, 0);

        const requestedAmount = Items.reduce((acc, curr) => {
          if (curr.status === PaymentStatus.CREATED) {
            acc = acc + curr.transactionAmount;
          }
          return acc;
        }, 0);

        //THIS IS ONLY USED FOR REPLACEMENT ORDER PAYMENT AS OF NOW.
      

        return {
          transactionDetails: Items,
          totalPaidAmount: totalAmount,
          requestedAmount: requestedAmount,
          remainingAmount: totalOrderAmount - totalAmount,
          
        };
      } else {
        return {
          transactionDetails: [],
          totalPaidAmount: 0,
          requestedAmount: 0,
          remainingAmount: totalOrderAmount,
        };
      }
    } catch (e) {
      posLogger.error('payment', 'getAllPaymentsByOrderId', { error: { e } });
      throw e;
    }
  }

  async getPaymentByOrderAndTransactionID(
    orderId: string,
    transactionId: string,
    storeId?: string,
  ): Promise<PaymentData> {
    posLogger.info('Payment', 'getPaymentByOrderAndTransactionID', {
      input: { orderId, transactionId },
    });
    try {
      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      const { Item } = await this.docClient.getItem(
        new GetCommand({
          TableName: PAYMENT_TABLE,
          Key: {
            orderId,
            transactionId,
            storeId,
          },
        }),
      );

      if (!Item || !Object.keys(Item).length)
        throw new CustomError(
          `No transaction is available with this ID ${orderId} and transaction ID ${transactionId}!`,
          404,
        );

      return Item;
    } catch (e) {
      posLogger.error('Payment', 'getPaymentByOrderAndTransactionID', {
        error: e,
      });
      throw e;
    }
  }
}
