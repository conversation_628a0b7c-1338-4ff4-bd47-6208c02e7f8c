import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { PaymentHelpers } from './payment-helpers';
import { PaymentMode, PaymentStatus } from 'src/common/enum/payment';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { CancelPaymentTransactionInput } from '../dto/cancel-payment-transactioin.input';
import { OrderPaymentHelpers } from './order-helpers';
import { CancelPaymentTransaction } from './cancel-payment-transaction';
import { CreatePayment } from './create-payment';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';
import { AppS3Client } from 'src/common/s3-client/s3-client';

export class ResendPaymentTransaction {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {}

  async resendPaymentTransaction({
    orderId,
    transactionId,
    storeId,
  }: CancelPaymentTransactionInput) {
    const orderHelper = new OrderPaymentHelpers(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const paymentHelper = new PaymentHelpers(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const { remainingAmount } =
      await paymentHelper.getAllPaymentsByOrderId(orderId);

    if (!remainingAmount) {
      throw new CustomError('Order payment is already completed!', 400);
    }

    const {
      totalAmount,
      status: orderStatus,
      quotationId,
      orderType,
    } = await orderHelper.getOrderInfoByOrderID(orderId);
    const transactionInfo =
      await paymentHelper.getPaymentByOrderAndTransactionID(
        orderId,
        transactionId,
      );

    if (!transactionInfo)
      throw new CustomError('Invalid request, resource not found!', 400);

    const cancelHandler = new CancelPaymentTransaction(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
      this.s3Client,
    );
    const createHandler = new CreatePayment(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
      this.s3Client,
    );
    let newTransaction = null;

    const {
      mode = null,
      status = null,
      transactionAmount = null,
      splitPayment = null,
      posId = null,
      phone = null,
      transactionType = null,
    } = transactionInfo;

    posLogger.info('payment', 'cancelPaymentTransaction', {
      mode,
    });

    if (
      mode === PaymentMode.BANK_TRANSFER ||
      mode === PaymentMode.CASH ||
      mode === PaymentMode.CHEQUE
    ) {
      throw new CustomError(`Resend not available for ${mode} payment!`, 400);
    }

    if (
      mode === PaymentMode.RAZORPAY_POS ||
      mode === PaymentMode.RAZORPAY ||
      mode === PaymentMode.PAYU ||
      mode === PaymentMode.PINELABS ||
      mode === PaymentMode.SNAPMINT ||
      mode === PaymentMode.M_SWIPE
    ) {
      if (status === PaymentStatus.CREATED) {
        await cancelHandler.cancelPaymentTransaction({
          orderId,
          transactionId,
        });
      }
    }

    newTransaction = await createHandler.createPayment({
      transactionAmount: transactionAmount,
      splitPayment: splitPayment,
      mode,
      orderId,
      posId,
      phone,
      storeId,
      transactionType,
    });

    if (!newTransaction) throw new CustomError('Resend failed!', 400);

    await orderHelper.validateOrderStatus(
      orderId,
      orderStatus,
      totalAmount,
      quotationId,
      orderType,
    );

    return newTransaction;
  }
}
