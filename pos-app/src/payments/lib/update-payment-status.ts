import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import moment from 'moment';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { PaymentStatus } from 'src/common/enum/payment';
import { PaymentData } from '../entities/payment.entity';
import { AppConfigParameters } from 'src/config/config';

export class UpdatePaymentStatus {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updatePaymentStatus(
    orderId: string,
    transactionId: string,
    status: PaymentStatus,
  ): Promise<PaymentData> {
    try {
      posLogger.info('payment', 'updatePaymentStatus', {
        input: { orderId, transactionId, status },
      });

      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      // Build the update expression based on the status
      let updateExpression = 'SET #status = :status, #updatedAt = :updatedAt';
      const expressionAttributeNames: Record<string, string> = {
        '#status': 'status',
        '#updatedAt': 'updatedAt',
      };
      const expressionAttributeValues: Record<string, any> = {
        ':status': status,
        ':updatedAt': moment().toISOString(),
      };

      // If the status is CANCELLED, add the transactionCompletedDate field
      if (status === PaymentStatus.CANCELLED) {
        updateExpression +=
          ', #transactionCompletedDate = :transactionCompletedDate';
        expressionAttributeNames['#transactionCompletedDate'] =
          'transactionCompletedDate';
        expressionAttributeValues[':transactionCompletedDate'] =
          moment().toISOString();
      }

      const { Attributes }: { Attributes: PaymentData } =
        await this.docClient.updateItem(
          new UpdateCommand({
            TableName: PAYMENT_TABLE,
            Key: {
              orderId,
              transactionId,
            },
            UpdateExpression: updateExpression,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ConditionExpression:
              'attribute_exists(orderId) AND attribute_exists(transactionId)',
            ReturnValues: 'ALL_NEW',
          }),
        );

      return Attributes;
    } catch (e) {
      posLogger.error('payment', 'updatePaymentStatus', e);
      throw e;
    }
  }
}
