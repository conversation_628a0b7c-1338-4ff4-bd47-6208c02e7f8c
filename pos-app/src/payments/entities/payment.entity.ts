import { ObjectType, Field, Float, registerEnumType } from '@nestjs/graphql';
import { HandoverStatus } from 'src/common/enum/cms';

import {
  PaymentMode,
  PaymentStatus,
  TransactionType,
} from 'src/common/enum/payment';
import { PaymentDetails } from 'src/orders/entities/order.entity';

registerEnumType(PaymentStatus, { name: 'PaymentStatus' });
registerEnumType(PaymentMode, { name: 'PaymentMode' });
registerEnumType(TransactionType, { name: 'TransactionType' });
registerEnumType(HandoverStatus, { name: 'HandoverStatus' });

@ObjectType()
export class CardData {
  @Field(() => String, { nullable: true })
  cardHolderName: string;

  @Field(() => String, { nullable: true })
  last4Digits: string;

  @Field(() => String, { nullable: true })
  network: string;

  @Field(() => String, { nullable: true })
  cardType: string;

  @Field(() => String, { nullable: true })
  issuer: string;

  @Field(() => String, { nullable: true })
  emiEligible: string;

  @Field(() => String, { nullable: true })
  international: string;
}

@ObjectType()
export class PaymentData {
  @Field(() => String, { nullable: false })
  transactionId: string;

  @Field(() => String, { nullable: false })
  orderId: string;

  @Field(() => String, { nullable: true })
  shopifyOrderId?: string;

  @Field(() => Boolean, { nullable: true })
  splitPayment?: boolean;

  @Field(() => PaymentStatus, { nullable: true })
  status?: PaymentStatus;

  @Field(() => Float, { nullable: true })
  transactionAmount?: number;

  @Field(() => PaymentMode, { nullable: true })
  mode?: PaymentMode;

  @Field(() => Float, { nullable: true })
  remainingAmount?: number;

  @Field(() => Float, { nullable: true })
  amountPaid?: number;

  @Field(() => String, { nullable: true })
  createdAt?: string;

  @Field(() => String, { nullable: true })
  paymentMethod?: string;

  @Field(() => String, { nullable: true })
  updatedAt?: string;

  @Field(() => String, { nullable: true })
  externalRefId?: string;

  @Field(() => TransactionType, { nullable: true })
  transactionType?: TransactionType;

  @Field(() => String, { nullable: true })
  transactionCompletedDate?: string;

  @Field(() => String, { nullable: true })
  refId?: string;

  @Field(() => String, { nullable: true })
  posId?: string;

  @Field(() => String, { nullable: true })
  phone?: string;

  @Field(() => String, { nullable: true })
  transactionScreenshotS3Key?: string;

  @Field(() => String, { nullable: true })
  paymentID?: string;

  @Field(() => String, { nullable: true })
  qrImageUrl?: string;

  @Field(() => String, { nullable: true })
  storeId?: string;

  @Field(() => String, { nullable: true })
  orderCreatedAt?: string;

  @Field(() => String, { nullable: true })
  orderShopifyCreatedAt?: string;

  @Field(() => String, { nullable: true })
  orderTotalAmount?: string;

  @Field(() => String, { nullable: true })
  orderStatus?: string;

  @Field(() => String, { nullable: true })
  orderShopifyId?: string;

  @Field(() => String, { nullable: true })
  shopifyOrderName?: string;

  @Field(() => String, { nullable: true })
  cmsId?: string;

  @Field(() => String, { nullable: true })
  handoverId?: string;

  @Field(() => HandoverStatus, { nullable: true })
  handoverStatus?: HandoverStatus;

  @Field(() => String, { nullable: true })
  handoverDate?: string;

  @Field(() => String, { nullable: true })
  POrderID?: string;

  @Field(() => String, { nullable: true })
  transactionNumber?: string;

  @Field(() => String, { nullable: true })
  shortUrl?: string;

  @Field(() => String, { nullable: true })
  upiId?: string;

  @Field(() => String, { nullable: true })
  rrn?: string;

  @Field(() => String, { nullable: true })
  bankName?: string;

  @Field(() => CardData, { nullable: true })
  cardDetails?: CardData;

  @Field(() => Boolean, { nullable: true })
  isManualVerification?: boolean;
}

@ObjectType()
export class Payment {
  @Field(() => PaymentData, { nullable: true })
  data: PaymentData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class Payments {
  @Field(() => [PaymentData], { nullable: true })
  data: PaymentData[];

  @Field(() => Number, { nullable: true, description: 'count' })
  count?: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class PaymentMetaDetails {
  @Field(() => PaymentDetails, { nullable: true })
  data: PaymentDetails;

  @Field(() => Number, { nullable: true, description: 'count' })
  count?: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
