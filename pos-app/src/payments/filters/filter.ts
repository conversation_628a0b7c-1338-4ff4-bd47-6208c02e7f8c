export const searchingFilter = new Map<
  string,
  'string' | 'nonString' | 'arrayString' | 'boolean' | 'date'
>([
  ['storeId', 'string'],
  ['handoverId', 'string'],
  ['orderStatus', 'string'],
  ['handoverStatus', 'string'],
]);

export const sortingFilter = new Map<string, 'string' | 'nonString'>([
  ['orderCreatedAt', 'string'],
  ['updatedAt', 'string'],
]);

export const sortingFilterType = {
  orderCreatedAt: 'date',
  updatedAt: 'date',
};
