import { Injectable } from '@nestjs/common';
import { CreatePaymentInput } from './dto/create-payment.input';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { CreatePayment } from './lib/create-payment';
import { VerifyPaymentTransaction } from './lib/verify-payment-transction';
import { VerifyPaymentTransactionInput } from './dto/verify-razor-pay-pos-payment.input';
import { PaymentHelpers } from './lib/payment-helpers';
import { PaymentDetails } from 'src/orders/entities/order.entity';
import { OrderPaymentHelpers } from './lib/order-helpers';
import { PaymentMode } from 'src/common/enum/payment';
import { CancelPaymentTransactionInput } from './dto/cancel-payment-transactioin.input';
import { ResendPaymentTransaction } from './lib/resend-payment-transaction';
import { CancelPaymentTransaction } from './lib/cancel-payment-transaction';
import { AppConfigParameters } from 'src/config/config';
import { PaymentData } from './entities/payment.entity';
import { QueryPayments } from './lib/list-payments';
import { ListPaymentInput } from './dto/list-payment.input';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { GetOrder } from 'src/orders/lib/get-order';
import { ValidateUsageLimit } from 'src/coupons/lib/validate-usage-limit';
import { CouponUsageHandler } from 'src/common/helper/coupon-usage-handler';
import { VerificationEmployeeDiscountOTP } from 'src/quotations/lib/verification-employee-discount-otp';
import moment from 'moment';
import { EmployeeDiscountStatus } from 'src/quotations/entities/quotation.entity';
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { AppShopify } from 'src/common/shopify/shopify';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AutoConfirmOrder } from 'src/orders/lib/auto-confirm-order';

@Injectable()
export class PaymentsService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {}

  async handlePaymentAction(
    orderId: string,
    action: (input: any) => Promise<any>, // Generic payment action
    input:
      | VerifyPaymentTransactionInput
      | CancelPaymentTransactionInput
      | CreatePaymentInput,
    isCreating = false,
  ) {
    const dataBefore = await this.findAllByOrderID(orderId);
    if (
      dataBefore &&
      dataBefore?.transactionDetails?.length > 0 &&
      'storeId' in input
    ) {
      const firstPaymentStoreId = dataBefore?.transactionDetails[0]?.storeId;
      const currentStoreId = input.storeId;
      if (firstPaymentStoreId !== currentStoreId) {
        throw new CustomError(
          'Store ID mismatch. First payment store ID: ' +
            firstPaymentStoreId +
            ', Current store ID: ' +
            currentStoreId,
          400,
        );
      }
    }

    const tpb = dataBefore?.totalPaidAmount || 0;
    const rpb = dataBefore?.requestedAmount || 0;

    const getOrderHandler = new GetOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const {
      campaignCode,
      promotionalCode,
      storeId,
      employeeDiscountDetails,
      customDiscountAmount,
      quotationId,
    } = await getOrderHandler.getOrder(orderId);
    if (tpb + rpb === 0 && isCreating) {
      if (campaignCode || promotionalCode || employeeDiscountDetails) {
        const validateCouponUsageLimitHandler = new ValidateUsageLimit(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        if (
          employeeDiscountDetails &&
          employeeDiscountDetails.status === EmployeeDiscountStatus.APPROVED
        ) {
          const { phone } = employeeDiscountDetails;
          const EMPLOYEE_MASTER_TABLE =
            await this.configParameters.getMasterEmployeeTableName();

          const employeeMasterCommand = new GetCommand({
            TableName: EMPLOYEE_MASTER_TABLE,
            Key: { phone },
          });

          const employeeData = await this.docClient.getItem(
            employeeMasterCommand,
          );

          if (!employeeData?.Item) {
            throw new CustomError('Employee not found.', 400);
          }

          if (employeeData.Item?.discountDetails) {
            throw new CustomError(
              'Employee already used an employee discount.',
              400,
            );
          }
        }

        if (campaignCode) {
          const { isValidated } =
            await validateCouponUsageLimitHandler.validateUsageLimit(
              campaignCode,
              storeId,
              'CAMPAIGN',
            );
          if (!isValidated) {
            throw new CustomError(
              `Coupon usage limit exceeded for the campaign ${campaignCode}`,
              400,
            );
          }
        }

        if (promotionalCode) {
          const { isValidated } =
            await validateCouponUsageLimitHandler.validateUsageLimit(
              promotionalCode,
              storeId,
              'PROMOTIONAL',
            );
          if (!isValidated) {
            throw new CustomError(
              `Coupon usage limit exceeded for the promotional code ${promotionalCode}`,
              400,
            );
          }
        }
      }
    }

    const result = await action(input);

    const dataAfter = await this.findAllByOrderID(orderId);
    const tpa = dataAfter?.totalPaidAmount || 0;
    const rpa = dataAfter?.requestedAmount || 0;

    if (tpb + rpb === 0 && tpa + rpa !== 0) {
      const coupounUsageHandler = new CouponUsageHandler(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      await coupounUsageHandler.handleCouponUsage(
        {
          promotionalCode: promotionalCode,
          campaignCode: campaignCode,
          storeId: storeId,
        },
        'inc',
      );

      if (
        employeeDiscountDetails &&
        employeeDiscountDetails.status === EmployeeDiscountStatus.APPROVED
      ) {
        const requestHandler = new VerificationEmployeeDiscountOTP(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        await requestHandler.updateEmployeeMasterDetails(
          employeeDiscountDetails.phone,
          await this.configParameters.getMasterEmployeeTableName(),
          {
            discountValue: customDiscountAmount,
            quotationId,
            appliedAt: moment().toISOString(),
          },
        );
      }
    } else if (tpa + rpa === 0 && tpb + rpb !== 0) {
      const coupounUsageHandler = new CouponUsageHandler(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      await coupounUsageHandler.handleCouponUsage(
        {
          promotionalCode: promotionalCode,
          campaignCode: campaignCode,
          storeId: storeId,
        },
        'dec',
      );
      if (
        employeeDiscountDetails &&
        employeeDiscountDetails.status === EmployeeDiscountStatus.APPROVED
      ) {
        const requestHandler = new VerificationEmployeeDiscountOTP(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        await requestHandler.updateEmployeeMasterDetails(
          employeeDiscountDetails.phone,
          await this.configParameters.getMasterEmployeeTableName(),
          null,
        );
      }
    }

    return result;
  }

  async create(createPaymentInput: CreatePaymentInput) {
    const createHandler = new CreatePayment(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
      this.s3Client,
    );

    return await this.handlePaymentAction(
      createPaymentInput.orderId,
      (input) => createHandler.createPayment(input),
      createPaymentInput,
      true,
    );
  }

  async verifyPaymentTransaction(
    verifyPaymentTransactionInput: VerifyPaymentTransactionInput,
  ): Promise<PaymentData> {
    const verifyHandler = new VerifyPaymentTransaction(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const verifyAction = async (input: VerifyPaymentTransactionInput) => {
      const orderHelper = new OrderPaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const {
        totalAmount,
        status: orderStatus,
        quotationId,
        orderType,
      } = await orderHelper.getOrderInfoByOrderID(input.orderId);

      const transactionInfo =
        await paymentHelper.getPaymentByOrderAndTransactionID(
          input.orderId,
          input.transactionId,
        );

      if (!transactionInfo) {
        throw new CustomError('Invalid request, resource not found!', 400);
      }

      const { mode, transactionAmount } = transactionInfo;

      let autoConfirm = false;
      let finalResponse = null;
      if (
        mode === PaymentMode.CASH ||
        mode === PaymentMode.CHEQUE ||
        mode === PaymentMode.BANK_TRANSFER ||
        ((mode === PaymentMode.RAZORPAY || mode === PaymentMode.PAYU) &&
          input.isPolling)
      ) {
        console.log('returning from here for :::::', mode);
        console.log(transactionInfo, '::::: transactionInfo');
        finalResponse = transactionInfo;
      } else if (input.manualVerification) {
        if (mode !== PaymentMode.RAZORPAY && mode !== PaymentMode.PAYU) {
          throw new CustomError(
            'Manual verification is only applicable for Razorpay and PayU modes',
            400,
          );
        }
        const { amountPaid } = input.manualVerification;
        if (amountPaid > transactionAmount) {
          throw new CustomError(
            'Amount Paid is greater than transaction amount',
            400,
          );
        }
        finalResponse = await verifyHandler.manualVerification(input);
      } else {
        autoConfirm = true;
        switch (mode) {
          case PaymentMode.RAZORPAY:
            autoConfirm = false;
            finalResponse =
              await verifyHandler.verifyRazorPayPaymentLinkTransaction(input);
            break;
          case PaymentMode.RAZORPAY_POS:
            finalResponse =
              await verifyHandler.verifyPOSMachineTransaction(input);
            break;
          case PaymentMode.PAYU:
            autoConfirm = false;
            finalResponse =
              await verifyHandler.verifyPayUPaymentLinkTransaction(input);
            break;
          case PaymentMode.SNAPMINT:
            finalResponse =
              await verifyHandler.verifySnapmintTransaction(input);
            break;
          case PaymentMode.PINELABS:
            finalResponse =
              await verifyHandler.verifyPineLabsPaymentLinkTransaction(input);
            break;
          case PaymentMode.M_SWIPE:
            finalResponse =
              await verifyHandler.verifyMswipePaymentLinkTransaction(input);
            break;
          default:
            throw new CustomError('Invalid payment mode!', 400);
        }
      }

      await orderHelper.validateOrderStatus(
        input.orderId,
        orderStatus,
        totalAmount,
        quotationId,
        orderType,
      );

      if (!input.manualVerification && autoConfirm) {
        const handler = new AutoConfirmOrder(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
          this.shopifyClient,
          this.s3Client,
        );
        handler.verifyPaymentAndConfirmOrder(input.orderId);
      }

      return finalResponse;
    };

    return await this.handlePaymentAction(
      verifyPaymentTransactionInput.orderId,
      verifyAction,
      verifyPaymentTransactionInput,
    );
  }

  async cancelPaymentTransaction(
    cancelPaymentTransactionInput: CancelPaymentTransactionInput,
  ) {
    const cancelHandler = new CancelPaymentTransaction(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
      this.s3Client,
    );

    return await this.handlePaymentAction(
      cancelPaymentTransactionInput.orderId,
      (input) => cancelHandler.cancelPaymentTransaction(input),
      cancelPaymentTransactionInput,
    );
  }

  async resendPaymentTransaction(
    resendPaymentTransactionInput: CancelPaymentTransactionInput,
  ) {
    const resendHelper = new ResendPaymentTransaction(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
      this.s3Client,
    );

    return await this.handlePaymentAction(
      resendPaymentTransactionInput.orderId,
      (input) => resendHelper.resendPaymentTransaction(input),
      resendPaymentTransactionInput,
    );
  }

  async findAll(listPaymentInput: ListPaymentInput) {
    const getHandler = new QueryPayments(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return await getHandler.queryPayments(listPaymentInput);
  }

  async findAllByOrderID(orderId: string): Promise<PaymentDetails> {
    const paymentHelper = new PaymentHelpers(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await paymentHelper.getAllPaymentsByOrderId(orderId);
  }
}
