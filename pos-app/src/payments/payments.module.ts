import { Module } from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { PaymentsResolver } from './payments.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { RazorpayPosModule } from 'src/common/razorpay-pos/razorpay-pos.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { ConfigParametersModule } from 'src/config/config.module';
import { ShopifyModule } from 'src/common/shopify/shopify.module';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    RazorpayPosModule,
    ConfigParametersModule,
    ShopifyModule,
    S3ClientModule,
  ],
  providers: [PaymentsResolver, PaymentsService, SuccessHandler, ErrorHandler],
})
export class PaymentsModule {}
