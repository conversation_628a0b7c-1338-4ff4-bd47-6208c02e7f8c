import { InputType, Field, Float } from '@nestjs/graphql';
import { PaymentMode, TransactionType } from 'src/common/enum/payment';

@InputType()
export class CreatePaymentInput {
  @Field(() => Float, { nullable: false })
  transactionAmount: number;

  @Field(() => Boolean, { nullable: false })
  splitPayment: boolean;

  @Field(() => PaymentMode, { nullable: false })
  mode: PaymentMode;

  @Field(() => String, { nullable: false })
  orderId: string;

  @Field(() => String, { nullable: true })
  posId?: string;

  @Field(() => TransactionType, { nullable: true })
  transactionType?: TransactionType;

  @Field(() => String, { nullable: true })
  phone?: string;

  @Field(() => String, { nullable: true })
  transactionNumber?: string;

  @Field(() => String, { nullable: true })
  transactionScreenshotS3Key?: string;

  @Field(() => String, { nullable: false })
  storeId: string;
}
