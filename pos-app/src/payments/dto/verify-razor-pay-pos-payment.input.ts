import { InputType, Field, Float } from '@nestjs/graphql';

@InputType()
export class ManualVerificationInput {
  @Field(() => String, { nullable: false })
  paymentID: string;

  @Field(() => Float, { nullable: false })
  amountPaid: number;

  @Field(() => String, { nullable: false })
  paymentMethod: string;
}

@InputType()
export class VerifyPaymentTransactionInput {
  @Field(() => String, { nullable: false })
  transactionId: string;

  @Field(() => String, { nullable: false })
  orderId: string;

  @Field(() => ManualVerificationInput, { nullable: true })
  manualVerification?: ManualVerificationInput;

  @Field(() => Boolean, { nullable: true })
  isPolling?: boolean;
}
