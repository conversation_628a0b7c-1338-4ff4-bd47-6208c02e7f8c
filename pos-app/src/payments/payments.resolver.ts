import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { PaymentsService } from './payments.service';
import {
  Payment,
  PaymentMetaDetails,
  Payments,
} from './entities/payment.entity';
import { CreatePaymentInput } from './dto/create-payment.input';
import { VerifyPaymentTransactionInput } from './dto/verify-razor-pay-pos-payment.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CancelPaymentTransactionInput } from './dto/cancel-payment-transactioin.input';
import { CRMGuard } from 'src/auth/roles.guard';
import { ListPaymentInput } from './dto/list-payment.input';

@Resolver(() => Payment)
@UseGuards(CustomAuthGuard, CRMGuard)
export class PaymentsResolver {
  constructor(
    private readonly paymentsService: PaymentsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => Payment, { name: 'createPayment' })
  async createPayment(
    @Args('createPaymentInput') createPaymentInput: CreatePaymentInput,
  ) {
    try {
      const data = await this.paymentsService.create(createPaymentInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create payment',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Payment, { name: 'verifyPaymentTransaction' })
  async verifyPaymentTransaction(
    @Args('verifyPaymentTransactionInput', {
      type: () => VerifyPaymentTransactionInput,
    })
    verifyPaymentTransactionInput: VerifyPaymentTransactionInput,
  ) {
    try {
      const data = await this.paymentsService.verifyPaymentTransaction(
        verifyPaymentTransactionInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to verify razorpay POS payment',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Payment, { name: 'cancelPaymentTransaction' })
  async cancelPaymentTransaction(
    @Args('cancelPaymentTransactionInput', {
      type: () => CancelPaymentTransactionInput,
    })
    cancelPaymentTransactionInput: CancelPaymentTransactionInput,
  ) {
    try {
      const data = await this.paymentsService.cancelPaymentTransaction(
        cancelPaymentTransactionInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to cancel payment',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Payment, { name: 'resendPaymentTransaction' })
  async resendPaymentTransaction(
    @Args('resendPaymentTransactionInput', {
      type: () => CancelPaymentTransactionInput,
    })
    resendPaymentTransactionInput: CancelPaymentTransactionInput,
  ) {
    try {
      const data = await this.paymentsService.resendPaymentTransaction(
        resendPaymentTransactionInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to resend payment',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Payments, { name: 'listPayments' })
  async findAll(
    @Args('listPaymentInput', { nullable: true })
    listPaymentInput: ListPaymentInput,
  ) {
    try {
      const { data, count } =
        await this.paymentsService.findAll(listPaymentInput);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list payments',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => PaymentMetaDetails, { name: 'getPaymentsByOrderID' })
  async findAllByOrderID(
    @Args('orderId', { type: () => String }) orderId: string,
  ) {
    try {
      const data = await this.paymentsService.findAllByOrderID(orderId);
      return this.successHandler.getSuccessResponse({
        data: { ...data },
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list payment details',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  // @Mutation(() => Payment)
  // cancelPayment(
  //   @Args('transaction_id', { type: () => String }) transaction_id: string,
  //   @Args('order_id', { type: () => String }) order_id: string,
  // ) {
  //   return this.paymentsService.cancelPayment(order_id, transaction_id);
  // }
}
