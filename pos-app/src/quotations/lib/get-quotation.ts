// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { QuotationData } from 'src/quotations/entities/quotation.entity';
import { ValidateQuotationsStatus } from './validate-status';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { QuotationStatus } from 'src/common/enum/quotations';
import { ValidateProducts } from 'src/common/helper/validate-products';
import { PaymentHelpers } from 'src/payments/lib/payment-helpers';

export class GetQuotation {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getQuotation(
    id: string,
    checkShopifyInventory = false,
  ): Promise<QuotationData> {
    posLogger.info('quotation', 'getQuotation', { input: { id } });
    try {
      const QUOTATION_TABLE =
        await this.configParameters.getQuotationTableName();

      const param = new GetCommand({
        TableName: QUOTATION_TABLE,
        Key: {
          id,
        },
      });

      const { Item: data }: { Item: QuotationData } =
        await this.docClient.getItem(param);

      const validatedQuotationHandler = new ValidateQuotationsStatus(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const [{ status }] =
        await validatedQuotationHandler.validateQuotationsStatus([data]);

      data.status = status;
      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      let isPaymentCompleted = false;

      if (data?.orderId) {
        const payments = await paymentHelper.getAllPaymentsByOrderId(
          data.orderId,
        );
        if (
          payments &&
          payments?.transactionDetails &&
          payments?.transactionDetails.length > 0
        ) {
          if (
            payments.transactionDetails.some(
              (payment) => payment.status === 'COMPLETED',
            )
          ) {
            isPaymentCompleted = true;
          }
        }
      }

      if (status != QuotationStatus.EXPIRED && !isPaymentCompleted) {
        const validateProducthandler = new ValidateProducts(
          this.configService,
          this.ssmClient,
          this.docClient,
          this.configParameters,
        );

        await validateProducthandler.validateProducts(
          data.quotationProducts,
          true,
          true,
          false,
          checkShopifyInventory,
        );
      }

      if (data) {
        return data;
      }

      throw new CustomError(
        `Invalid Quotation ID! No Quotation found for given Quotation ID ${id}.`,
        404,
      );
    } catch (e) {
      posLogger.error('quotation', 'getQuotation', { error: e });
      throw e;
    }
  }
}
