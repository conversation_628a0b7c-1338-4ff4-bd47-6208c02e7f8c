// modules
import { ConfigService } from '@nestjs/config';
import moment from 'moment';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { QuotationStatus } from 'src/common/enum/quotations';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { QuotationData } from 'src/quotations/entities/quotation.entity';
import { UpdateQuotationStatus } from './set-quotation-status';
import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';

export class ValidateQuotationsStatus {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validateQuotationsStatus(
    quotations: QuotationData[],
  ): Promise<QuotationData[]> {
    const updateQuotationStatusHandler = new UpdateQuotationStatus(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const data = await Promise.all(
      quotations.map(async (item) => {
        const { id, status, expiresAt } = item;
        if (
          (status === QuotationStatus.ACTIVE ||
            status === QuotationStatus.ORDER_CREATED) &&
          moment(expiresAt).toDate() < moment().toDate()
        ) {
          try {
            await updateQuotationStatusHandler.updateQuotationStatus(
              id,
              QuotationStatus.EXPIRED,
            );

            return {
              ...item,
              status: QuotationStatus.EXPIRED,
            };
          } catch (error) {
            posLogger.error('quotation', 'validateQuotationsStatus', error);
          }
        }
        return item;
      }),
    );

    return data;
  }
}
