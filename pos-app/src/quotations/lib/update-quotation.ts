// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { QuotationData } from '../entities/quotation.entity';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';
import { AppConfigParameters } from 'src/config/config';
import { GetQuotation } from './get-quotation';
import { ValidateCouponUsage } from 'src/orders/lib/validate-coupon-usage';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { GetOrderAndStoreAndSendPdf } from 'src/common/helper/get-order-and-store';
import { CreateQuotation } from './create-quotation';
import { AppShopify } from 'src/common/shopify/shopify';
import { UpdateQuotationInput } from '../dto/update-quotation.input';
import { ValidatePinCode } from 'src/pincodes/lib/get-pincode';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { getDiscountAndFinalPrice } from 'src/orders/helpers/get-item-discount';
import { getGstRateCalculator } from 'src/common/helper/gst-calculator';
import { QuotationType } from 'src/common/enum/quotations';
import { PaymentStatus } from 'src/common/enum/payment';
import { DeliveryChargeApprovalService } from './create-delivery-charge-otp-request';
import { GetOrder } from 'src/orders/lib/get-order';

export class UpdateQuotation {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
    private shopifyClient: AppShopify,
  ) {}

  async updateQuotation(
    id: string = '',
    { id: {}, ...updateCartInput }: Partial<UpdateQuotationInput>,
  ): Promise<QuotationData> {
    posLogger.info('quotation', 'updateQuotation', {
      input: { id, updateCartInput },
    });

    try {
      const getQuotationHandler = new GetQuotation(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const {
        campaignCode,
        storeId,
        quotationProducts,
        totalAmount,
        campaignDiscountAmount,
        promotionalDiscountAmount,
        customDiscountAmount,
        type,
        expiresAt,
        deliveryChargeApprovalDetails = null,
        bookingAmount,
        bookingAmountStatus,
        superOrderId,
      } = await getQuotationHandler.getQuotation(
        id,
        updateCartInput.checkShopifyInventory || true,
      );
      if (
        type === QuotationType.BNPL &&
        bookingAmountStatus === PaymentStatus.PAID &&
        superOrderId
      ) {
        const getSuperOrderHandler = new GetOrder(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const { storeId: superOrderStoreId } =
          await getSuperOrderHandler.getOrder(superOrderId);
        if (superOrderStoreId !== storeId) {
          throw new CustomError('Store id does not match', 400);
        }
      }

      const getGlobalConfigurationHandler = new GetGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const { value: QUOTATION_EXPIRE_INTERVAL_IN_DAYS } =
        await getGlobalConfigurationHandler.getGlobalConfiguration(
          'QUOTATION_EXPIRE_INTERVAL_IN_DAYS',
        );

      let quotationExpiresAt = moment()
        .add(Number(QUOTATION_EXPIRE_INTERVAL_IN_DAYS), 'days')
        .toISOString();

      if (type === QuotationType.BNPL) {
        if (
          updateCartInput.bookingAmountStatus &&
          updateCartInput.bookingAmountStatus === PaymentStatus.PAID
        ) {
          quotationExpiresAt = updateCartInput.expiresAt || expiresAt;
        } else {
          quotationExpiresAt = expiresAt;
        }
      }

      const validateCouponUsageHandler = new ValidateCouponUsage(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      await validateCouponUsageHandler.validateCampaignCouponUsage(
        campaignCode,
        storeId,
      );

      const {
        additionalPromotionalCoupons,
        shippingAddress,
        finalDiscountedAmount: cartFinalDiscountedAmount,
        deliveryCharge = 0,
      } = updateCartInput;
      let amountToBeComparedFinally =
        Number(totalAmount.toFixed(2)) -
        Number(promotionalDiscountAmount) -
        Number(campaignDiscountAmount) -
        Number(customDiscountAmount);

      if (bookingAmountStatus === PaymentStatus.PAID) {
        amountToBeComparedFinally = amountToBeComparedFinally - bookingAmount;
      }

      const { pinCode: shippingPinCode } = shippingAddress;
      const validatePincodeHandler = new ValidatePinCode(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const { applicability } =
        await validatePincodeHandler.validatePincode(shippingPinCode);

      if (
        applicability &&
        applicability === 'Non Serviceable' &&
        additionalPromotionalCoupons &&
        additionalPromotionalCoupons.length > 0
      ) {
        if (additionalPromotionalCoupons.length > 1) {
          throw new CustomError(
            'Only DIYINSTALL additional promotional coupon is allowed',
            400,
          );
        }
        const quotationHandler = new CreateQuotation(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
          this.shopifyClient,
          this.s3Client,
        );

        const DIYINSTALL_COUPON_DATA = additionalPromotionalCoupons.find(
          (c) => c.promotionalCode === 'DIYINSTALL',
        );
        if (!DIYINSTALL_COUPON_DATA) {
          throw new CustomError('DIYINSTALL is Applicable only', 400);
        }
        const {
          promotionalCode: DIYINSTALL_PROMOTIONAL_CODE,
          promotionalDiscountAmount: DIYINSTALL_PROMOTIONAL_DISCOUNT_AMOUNT,
        } = DIYINSTALL_COUPON_DATA;

        const { finalDiscountedAmount } =
          await quotationHandler.validatePromotionalCode({
            promotionalCode: DIYINSTALL_PROMOTIONAL_CODE,
            cartPromotionalDiscountAmount:
              DIYINSTALL_PROMOTIONAL_DISCOUNT_AMOUNT,
            quotationProductItems: quotationProducts,
            updatedQuotationProductItems: [],
            type: 'CREATE',
            totalAmount,
            finalDiscountedAmount: amountToBeComparedFinally,
          });
        amountToBeComparedFinally = finalDiscountedAmount;
      } else {
        if (
          additionalPromotionalCoupons &&
          additionalPromotionalCoupons.length > 0
        ) {
          throw new CustomError(
            'additional promotional coupon is not allowed',
            400,
          );
        }
      }

      const verifyApprovalRequestHandler = new DeliveryChargeApprovalService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const calculatedDeliveryCharge =
        await verifyApprovalRequestHandler.getDeliveryCharge(
          quotationProducts,
          shippingPinCode,
          deliveryChargeApprovalDetails,
        );

      if (calculatedDeliveryCharge > 0) {
        if (!deliveryCharge || calculatedDeliveryCharge !== deliveryCharge) {
          throw new CustomError('Delivery Charge Mismatch', 400);
        }
        amountToBeComparedFinally =
          amountToBeComparedFinally + calculatedDeliveryCharge;
      }

      if (amountToBeComparedFinally !== cartFinalDiscountedAmount) {
        throw new CustomError('Cart Final Discounted Amount mismatch', 400);
      }
      const updatedQuotationProducts = quotationProducts.map((item) => {
        const { quantity, price, productType } = item;
        const { itemDiscount, finalItemPrice } = getDiscountAndFinalPrice(
          quantity,
          price,
          totalAmount - cartFinalDiscountedAmount + (deliveryCharge || 0),
          totalAmount,
        );

        const { gstRate, gstAmount, finalItemPriceWithoutGst, gstPercentage } =
          getGstRateCalculator({
            productType,
            price,
            finalItemPrice,
          });

        return {
          ...item,
          itemDiscount,
          finalItemPrice,
          finalItemPriceWithoutGst,
          gstRate,
          gstAmount,
          gstPercentage,
        };
      });

      const QUOTATION_TABLE =
        await this.configParameters.getQuotationTableName();

      let updateExpressionString: string =
        'Set #updatedAt = :updatedAt, #expiresAt = :expiresAt, #quotationProducts = :quotationProducts, ';
      const expressionAttributeNames: Record<string, string> = {
        '#expiresAt': 'expiresAt',
        '#updatedAt': 'updatedAt',
        '#quotationProducts': 'quotationProducts',
      };
      const expressionAttributesValues: Record<string, any> = {
        ':updatedAt': moment().toISOString(),
        ':expiresAt': quotationExpiresAt,
        ':quotationProducts': updatedQuotationProducts,
      };

      delete updateCartInput.expiresAt;

      Object.keys(updateCartInput).map((key) => {
        updateExpressionString += `#${key} = :${key}, `;
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributesValues[`:${key}`] = updateCartInput[key];
      });

      updateExpressionString = updateExpressionString.substring(
        0,
        updateExpressionString.length - 2,
      );

      const command = new UpdateCommand({
        TableName: QUOTATION_TABLE,
        Key: {
          id,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributesValues,
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: QuotationData } =
        await this.docClient.updateItem(command);
      const updateHandler = new GetOrderAndStoreAndSendPdf(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.s3Client,
        this.configParameters,
      );
      await updateHandler.getQuotationAndStoreAndSendPdf(id, 'DOWNLOAD');

      return Attributes;
    } catch (e) {
      posLogger.error('quotation', 'updateQuotation', { error: e });
      throw e;
    }
  }
}
