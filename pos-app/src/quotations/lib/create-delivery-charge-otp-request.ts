import { Injectable } from '@nestjs/common';
import {
  DeliveryChargeApprovalDetails,
  DeliveryChargeApprovalStatus,
  QuotationData,
} from 'src/quotations/entities/quotation.entity';

import { GetQuotation } from './get-quotation';
import { DeliveryChargeApprovalInput } from '../dto/update-quotation.input';
import { ValidatePinCode } from 'src/pincodes/lib/get-pincode';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { generateOtp } from 'src/common/helper/generate-otp';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';

import { UpdateCommand, UpdateCommandInput } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';
import { CartProduct } from 'src/carts/entities/cart.entity';
import { OtpService } from 'src/common/helper/send-otp';
import { GetStore } from 'src/stores/lib/get-store-by-id';

@Injectable()
export class DeliveryChargeApprovalService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}
  async isDeliveryChargeable(
    pinCode: string,
    productIds: string[],
  ): Promise<boolean> {
    const validatePincodeHandler = new ValidatePinCode(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const { isDeliverable } =
      await validatePincodeHandler.validatePincode(pinCode);

    if (isDeliverable) {
      return false;
    }

    const getGlobalConfigHandler = new GetGlobalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const DELIVERY_CHARGES_PRODUCTS_CONFIG =
      await getGlobalConfigHandler.getGlobalConfiguration(
        'DELIVERY_CHARGES_PRODUCTS',
      );

    let DELIVERY_CHARGES_PRODUCTS_VALUE: string[] = [];

    try {
      DELIVERY_CHARGES_PRODUCTS_VALUE = JSON.parse(
        DELIVERY_CHARGES_PRODUCTS_CONFIG?.value || '[]',
      );
    } catch (error) {
      console.error('Failed to parse DELIVERY_CHARGES_PRODUCTS_CONFIG:', error);
    }

    return productIds.some((productId) =>
      DELIVERY_CHARGES_PRODUCTS_VALUE.includes(productId),
    );
  }

  async getDeliveryCharge(
    quotationProducts: CartProduct[],
    pincode: string,
    deliveryChargeApprovalDetails: DeliveryChargeApprovalDetails,
  ) {
    const productsIds = quotationProducts.map((product) => product.productId);
    const isDeliveryChargeable = await this.isDeliveryChargeable(
      pincode,
      productsIds,
    );
    let deliveryCharge = 0;
    if (isDeliveryChargeable) {
      if (
        deliveryChargeApprovalDetails &&
        deliveryChargeApprovalDetails.status ===
          DeliveryChargeApprovalStatus.APPROVED
      ) {
        deliveryCharge = deliveryChargeApprovalDetails.requestedCharge;
      } else {
        deliveryCharge = 15000;
      }
    }
    return deliveryCharge;
  }

  private async updateQuotationDeliveryCharge(
    quotationId: string,
    quotation: QuotationData,
    updatedApprovalDetails: DeliveryChargeApprovalDetails,
    updateAdditionalFields = false,
  ) {
    const updatedAt = moment().toISOString();
    const QUOTATION_TABLE = await this.configParameters.getQuotationTableName();

    let updateExpression = `
      SET deliveryChargeApprovalDetails = :deliveryChargeApprovalDetails,
          updatedAt = :updatedAt
    `;

    const expressionAttributeValues = {
      ':deliveryChargeApprovalDetails': updatedApprovalDetails,
      ':updatedAt': updatedAt,
    };

    if (updateAdditionalFields) {
      const approvedDeliveryCharge = updatedApprovalDetails.requestedCharge;

      const {
        totalAmount = 0,
        promotionalDiscountAmount = 0,
        campaignDiscountAmount = 0,
        customDiscountAmount = 0,
        additionalPromotionalCoupons = null,
      } = quotation;

      const additionalPromotionalCouponsDiscount =
        additionalPromotionalCoupons?.[0]?.promotionalDiscountAmount || 0;

      const totalDiscount =
        promotionalDiscountAmount +
        campaignDiscountAmount +
        customDiscountAmount +
        additionalPromotionalCouponsDiscount;
      const finalDiscountedAmount =
        totalAmount + approvedDeliveryCharge - totalDiscount;

      updateExpression += `,
        deliveryCharge = :approvedDeliveryCharge,
        finalDiscountedAmount = :finalDiscountedAmount
      `;

      expressionAttributeValues[':approvedDeliveryCharge'] =
        approvedDeliveryCharge;
      expressionAttributeValues[':finalDiscountedAmount'] =
        finalDiscountedAmount;
    }

    const params: UpdateCommandInput = {
      TableName: QUOTATION_TABLE,
      Key: { id: quotationId },
      UpdateExpression: updateExpression,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'ALL_NEW',
    };

    const result = await this.docClient.updateItem(new UpdateCommand(params));
    return result.Attributes as QuotationData;
  }

  async createApprovalRequest(
    quotationId: string,
    input: DeliveryChargeApprovalInput,
    resend: boolean,
  ): Promise<QuotationData> {
    const getQuotationHandler = new GetQuotation(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const quotation = await getQuotationHandler.getQuotation(quotationId);
    const { deliveryChargeApprovalDetails: oldDeliveryChargeApprovalDetails } =
      quotation;
    const { status: oldStatus } = oldDeliveryChargeApprovalDetails || {};

    if (
      resend &&
      oldDeliveryChargeApprovalDetails &&
      oldStatus === DeliveryChargeApprovalStatus.APPROVED
    ) {
      throw new CustomError(
        'Delivery charge request is already approved.',
        400,
      );
    }
    const otp = generateOtp();
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString();

    await this.sendOTPViaSMS(
      input.phone,
      otp,
      input.requestedCharge,
      quotation.storeId,
    );
    const deliveryChargeApprovalDetails: DeliveryChargeApprovalDetails = {
      name: input.name,
      phone: input.phone,
      requestedCharge: input.requestedCharge,
      status: DeliveryChargeApprovalStatus.CREATED,
      otp,
      expiresAt,
      createdAt: moment().toISOString(),
      updatedAt: moment().toISOString(),
    };

    const updatedQuotation = await this.updateQuotationDeliveryCharge(
      quotationId,
      quotation,
      deliveryChargeApprovalDetails,
    );

    return updatedQuotation;
  }

  private async sendOTPViaSMS(
    phoneNumber: string,
    otp: string,
    requestedCharge: number,
    storeId: string,
  ): Promise<void> {
    const otpHandler = new OtpService(this.ssmClient, this.configService);
    const getStoreHandler = new GetStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const { name } = await getStoreHandler.getStore(storeId);

    const payload = {
      templateName: 'deliveryCharges',
      storeName: name,
      deliveryCharge: requestedCharge?.toString(),
      OTP: otp,
      phoneNo: phoneNumber,
    };

    const data = await otpHandler.sendSms(payload);
    console.log('🔥data', data);
  }

  async verifyApprovalRequest(
    quotationId: string,
    otp: string,
  ): Promise<QuotationData> {
    const getQuotationHandler = new GetQuotation(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const quotation = await getQuotationHandler.getQuotation(quotationId);

    if (!quotation?.deliveryChargeApprovalDetails) {
      throw new CustomError('No delivery charge approval request found.', 400);
    }

    const deliveryChargeApprovalDetails =
      quotation.deliveryChargeApprovalDetails;
    const currentTime = moment().toISOString();

    if (
      deliveryChargeApprovalDetails.status ===
        DeliveryChargeApprovalStatus.APPROVED ||
      deliveryChargeApprovalDetails.status ===
        DeliveryChargeApprovalStatus.APPLIED
    ) {
      return quotation;
    }

    if (moment(deliveryChargeApprovalDetails.expiresAt).isBefore(currentTime)) {
      return await this.updateQuotationDeliveryCharge(quotationId, quotation, {
        ...deliveryChargeApprovalDetails,
        status: DeliveryChargeApprovalStatus.EXPIRED,
        updatedAt: currentTime,
      });
    }

    if (
      deliveryChargeApprovalDetails.status ===
      DeliveryChargeApprovalStatus.CREATED
    ) {
      if (deliveryChargeApprovalDetails.otp !== otp) {
        throw new CustomError('Invalid OTP.', 400);
      }

      const updatedDetails = {
        ...deliveryChargeApprovalDetails,
        status: DeliveryChargeApprovalStatus.APPROVED,
        updatedAt: currentTime,
      };

      return await this.updateQuotationDeliveryCharge(
        quotationId,
        quotation,
        updatedDetails,
        true,
      );
    }

    throw new CustomError('Invalid request state.', 400);
  }
}
