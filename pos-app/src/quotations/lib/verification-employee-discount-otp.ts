import { GetCommand, PutCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import moment from 'moment';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetQuotation } from './get-quotation';
import { EmployeeDiscountStatus } from '../entities/quotation.entity';
import { AppShopify } from 'src/common/shopify/shopify';
import { getTotalAmount } from 'src/common/helper/get-total-amount';
import { CustomCode } from 'src/carts/entities/cart.entity';
import { CouponType } from 'src/common/enum/coupons';

export class VerificationEmployeeDiscountOTP {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async execute(quotationId: string, otp: string) {
    const getQuotationHandler = new GetQuotation(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const quotationData = await getQuotationHandler.getQuotation(quotationId);
    if (!quotationData) {
      throw new CustomError('Quotation not found.', 400);
    }

    const {
      employeeDiscountDetails,
      customDiscountAmount: cartCustomDiscountAmount,
      campaignDiscountAmount,
      promotionalDiscountAmount: cartPromotionalDiscountAmount,
    } = quotationData;

    if (!employeeDiscountDetails || !employeeDiscountDetails.phone) {
      throw new CustomError('No employee discount details found.', 400);
    }

    const EMPLOYEE_MASTER_TABLE =
      await this.configParameters.getMasterEmployeeTableName();
    const QUOTATION_TABLE = await this.configParameters.getQuotationTableName();
    const employeeMasterCommand = new GetCommand({
      TableName: EMPLOYEE_MASTER_TABLE,
      Key: { phone: employeeDiscountDetails.phone },
    });

    const employeeData = (await this.docClient.getItem(employeeMasterCommand))
      .Item;

    if (!employeeData) {
      throw new CustomError('Employee not found.', 400);
    }

    if (employeeData?.discountDetails) {
      throw new CustomError('Employee already has used discount.', 400);
    }

    if (cartCustomDiscountAmount > 0 || campaignDiscountAmount > 0) {
      throw new CustomError(
        'This discount can not be applied with Campaign discount or Custom discount or Promotional discount.',
        400,
      );
    }

    const now = moment();
    if (moment(employeeDiscountDetails.expiresAt).isBefore(now)) {
      await this.updateQuotationStatus(
        quotationId,
        EmployeeDiscountStatus.EXPIRED,
        QUOTATION_TABLE,
      );
      throw new CustomError('OTP expired.', 400);
    }

    if (employeeDiscountDetails.otp !== otp) {
      throw new CustomError('Invalid OTP.', 400);
    }

    if (employeeDiscountDetails.status === EmployeeDiscountStatus.EXPIRED) {
      throw new CustomError('OTP is Expired .', 400);
    }
    if (employeeDiscountDetails.status === EmployeeDiscountStatus.APPROVED) {
      throw new CustomError('Employee Discount is already approved.', 400);
    }

    const { quotationProducts, finalDiscountedAmount } = quotationData;
    const cartTotal = getTotalAmount(quotationProducts);
    const customDiscountAmount =
      (Number(cartTotal - cartPromotionalDiscountAmount) * 35) / 100;
    const newFinalDiscountedAmount =
      finalDiscountedAmount - customDiscountAmount;
    const customCode: CustomCode = {
      value_type: CouponType.PERCENTAGE,
      approver: employeeDiscountDetails.name,
      value: 35,
    };
    const updatedQuotation = await this.updateQuotationStatus(
      quotationId,
      EmployeeDiscountStatus.APPROVED,
      QUOTATION_TABLE,
      customDiscountAmount,
      customCode,
      newFinalDiscountedAmount,
    );

    return updatedQuotation.Attributes;
  }

  private async updateQuotationStatus(
    quotationId: string,
    status: string,
    quotationTable: string,
    customDiscountAmount?: number,
    customCode?: CustomCode,
    finalDiscountedAmount?: number,
  ) {
    let updateExpression =
      'SET employeeDiscountDetails.#status = :status, updatedAt = :updatedAt';

    const expressionAttributeValues: Record<string, any> = {
      ':status': status,
      ':updatedAt': moment().toISOString(),
    };

    const expressionAttributeNames: Record<string, string> = {
      '#status': 'status', // Avoid reserved keyword issue
    };

    if (status === EmployeeDiscountStatus.APPROVED) {
      updateExpression +=
        ', customDiscountAmount = :customDiscountAmount, ' +
        'customCode = :customCode, ' +
        'finalDiscountedAmount = :finalDiscountedAmount';

      expressionAttributeValues[':customDiscountAmount'] = customDiscountAmount;
      expressionAttributeValues[':customCode'] = customCode;
      expressionAttributeValues[':finalDiscountedAmount'] =
        finalDiscountedAmount;
    }

    console.log('expressionAttributeValues', expressionAttributeValues);

    const updatedQuotation = await this.docClient.updateItem(
      new UpdateCommand({
        TableName: quotationTable,
        Key: { id: quotationId },
        ExpressionAttributeNames: expressionAttributeNames,
        UpdateExpression: updateExpression,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW',
      }),
    );

    //here you also have to updatethe  EMPLOYEE_MASTER_DETAILS update field

    return updatedQuotation;
  }
  async updateEmployeeMasterDetails(
    phone: string,
    employeeMasterTable: string,
    discountDetails: {
      discountValue: number;
      quotationId: string;
      appliedAt: string;
    },
  ) {
    const updateExpression =
      'SET discountDetails = :discountDetails, updatedAt = :updatedAt';

    const expressionAttributeValues: Record<string, any> = {
      ':discountDetails': discountDetails,
      ':updatedAt': moment().toISOString(),
    };

    console.log('expressionAttributeValues', expressionAttributeValues);

    const updatedEmployeeMaster = await this.docClient.updateItem(
      new UpdateCommand({
        TableName: employeeMasterTable,
        Key: { phone },
        UpdateExpression: updateExpression,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW',
      }),
    );

    console.log('updatedEmployeeMaster', updatedEmployeeMaster);

    return updatedEmployeeMaster;
  }
}
