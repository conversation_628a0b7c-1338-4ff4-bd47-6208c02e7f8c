import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import {
  buildDateRangeFilter,
  filterFormatter,
} from 'src/common/helper/filter-helper';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import moment from 'moment-timezone';
import { QuotationData } from '../entities/quotation.entity';
import { json2csv } from 'json-2-csv';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { TemplateType } from 'src/common/enum/template-type';
import { AppConfigParameters } from 'src/config/config';
import { ListQuotationInput } from '../dto/list-quotation.input';

export class ExportQuotations {
  private esHandler: ElasticClient;
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
  ) {
    this.esHandler = new ElasticClient(this.configService, this.ssmClient);
  }

  async exportQuotationCSV(
    quotations: (QuotationData & {
      shopifyOrderId: string;
      shopifyOrderName: string;
    })[],
  ) {
    const data = quotations.reduce((acc, quotation) => {
      const {
        id,
        orderId,
        customerId,
        quotationProducts,
        storeId,
        employeeId,
        campaignCode,
        customCode,
        promotionalCode,
        notes,
        source,
        customer,
        shippingAddress,
        billingAddress,
        gstDetails,
        interiorArchitecture,
        shopifyOrderId,
        shopifyOrderName,
        deliveryCharge,
        totalAmount,
        finalDiscountedAmount,
        promotionalDiscountAmount,
        additionalPromotionalCoupons,
        customDiscountAmount,
        campaignDiscountAmount,
        status,
        customerIdentityProof,
        type,
        // deliveryChargeApprovalDetails,
        // customDiscountVerificationDetails,
        // employeeDiscountDetails,
        // freeProducts,
        createdAt,
        // updatedAt,
        expiresAt,
        // customerAcknowledgement,
        bookingAmount,
        bookingAmountStatus,
        superOrderId,
      } = quotation;

      const sku = [],
        productType = [],
        title = [];

      (quotationProducts || []).map(
        ({ sku: dSku, title: dTitle, productType: dProductType }) => {
          productType.push(dProductType);
          title.push(dTitle);
          sku.push(dSku);
        },
      );
      const DIYINSTALL_DISCOUNT =
        additionalPromotionalCoupons?.find(
          (item) => item?.promotionalCode === 'DIYINSTALL',
        )?.promotionalDiscountAmount || 0;

      return [
        ...acc,
        {
          'Quotation ID': id || '',
          'Order ID': shopifyOrderId || '',
          'Shopify Order Name': shopifyOrderName || '',
          customerphone: customer?.phone || '',
          'customer name': `${customer?.firstName || ''} ${customer?.lastName || ''}`,
          customeremail: customer?.email || '',
          'Employee Id': employeeId,
          'Shipping address':
            Object.values(shippingAddress || {}).join(', ') || '',
          'Shipping Pincode': shippingAddress?.pinCode || '',
          'Billing address':
            Object.values(billingAddress || {}).join(', ') || '',
          'Quotation status': status || '',
          'Quotation Type': type || '',
          customCode: customCode
            ? `${customCode?.value_type || ''} ${customCode?.value || ''}`
            : '',
          'Discount Approved By': customCode?.approver || '',
          customDiscountAmount: customDiscountAmount || 0,
          promotionalCode: promotionalCode || '',
          promotionalDiscountAmount: promotionalDiscountAmount || 0,
          campaignCode: campaignCode || '',
          campaignDiscountAmount: campaignDiscountAmount || 0,
          DIYINSTALL: DIYINSTALL_DISCOUNT > 0 ? DIYINSTALL_DISCOUNT : '',
          'Delivery Charge applied': deliveryCharge > 0 ? 'Yes' : 'No',
          'Delivery Charge': deliveryCharge > 0 ? deliveryCharge || '' : '',
          totalAmount,
          'order value': finalDiscountedAmount,
          'discount amount': totalAmount - finalDiscountedAmount,
          'Shopify Customer ID': customerId || '',
          'company name': gstDetails ? gstDetails.companyName : '',
          gstnumber: gstDetails ? gstDetails.gstNumber : '',
          'Pos Order ID': orderId || '',
          'invoice no': orderId || '',
          storeId: storeId || '',
          notes: notes || '',
          Source: source || '',
          'Interior Architecture': interiorArchitecture
            ? JSON.stringify(interiorArchitecture || {})
            : '',
          SKU: sku.join(', ') || '',
          'Product Title': title.join(', ') || '',
          'Product Type': productType.join(', ') || '',
          'customer Attachment': customerIdentityProof || '',
          'Booking Amount': bookingAmount || '-',
          'Booking Amount Status': bookingAmountStatus || '-',
          'Super Order ID': superOrderId || '-',
          'Created At': moment(createdAt).format('DD-MM-YYYY HH:mm'),
          'Expired At': moment(expiresAt).format('DD-MM-YYYY HH:mm'),
        },
      ];
    }, []);
    const csvData = await json2csv(data, {});
    return csvData;

    // const csvKey = `private/exports/${new Date().valueOf()}-orders.csv`;

    // await s3
    //   .putObject({
    //     Bucket: S3_BUCKET,
    //     Key: csvKey,
    //     Body: csvData,
    //     ContentType: 'text/csv',
    //   })
    //   .promise();

    // return csvKey;
  }

  async query({
    index,
    limit: size = 100,
    page = 1,
    filter,
    sort,
    nextToken: nt,
  }) {
    let searchAfter;
    if (nt) {
      searchAfter = nt
        ? JSON.parse(Buffer.from(nt, 'base64').toString('ascii'))
        : undefined;
    }

    const searchParams = {
      index,
      size,
      from: (page - 1) * size,
      body: {
        version: false,
        track_total_hits: true,
        search_after: searchAfter,
        query: filter,
        sort: [...sort],
      },
    };

    // Executing the OpenSearch request
    const { body } = await this.esHandler.search(searchParams);

    const { hits } = body;
    const { hits: results = [], total } = hits;
    const lastResult = results[results.length - 1];
    const nextToken =
      lastResult && lastResult.sort
        ? Buffer.from(JSON.stringify(lastResult.sort), 'ascii').toString(
            'base64',
          )
        : null;

    return {
      page,
      pageSize: size,
      totalPages: Math.ceil(total.value / size),
      total: total.value,
      items: results.map(({ _source }) => _source),
      nextToken: nextToken,
    };
  }

  async queryAll({ index, filter, nextToken: nT, sort }) {
    const { items, nextToken } = await this.query({
      index,
      filter,
      sort,
      nextToken: nT,
      limit: 9999,
    });

    if (nextToken) {
      const nextItems = await this.queryAll({
        index,
        filter,
        nextToken,
        sort,
      });
      return [...items, ...nextItems];
    }

    return items;
  }

  async exportQuotations(email: string, filter: ListQuotationInput) {
    const QUOTATION_TABLE = await this.configParameters.getQuotationTableName();
    const ORDER_TABLE = await this.configParameters.getOrderTableName();
    const { fromDate = null, toDate = null } = filter || {};

    if (filter) {
      filter.sortBy = null;
    }

    const { searchArray: filteredSearch } = await filterFormatter(
      sortingFilter,
      searchingFilter,
      sortingFilterType,
      filter,
    );

    const dateRangeFilter = buildDateRangeFilter({
      fromDate,
      toDate,
      field: 'createdAt',
    });

    const filteredSearchWithoutCreatedAt = filteredSearch.filter((obj) => {
      if (obj.range && obj.range.createdAt) {
        return false;
      }
      return true;
    });

    const searchArray = [
      ...filteredSearchWithoutCreatedAt,
      dateRangeFilter,
    ].filter(Boolean);

    let orders = [];
    let quotations = [];

    orders = await this.queryAll({
      index: ORDER_TABLE,
      filter: {
        bool: {
          must: [
            ...searchArray,
            {
              terms: {
                'status.keyword': ['SENT_TO_SHOPIFY', 'CANCELLED'],
              },
            },
          ],
        },
      },
      nextToken: null,
      sort: [{ createdAt: { order: 'desc' } }],
    });

    const ordersMap = orders.reduce((ac, cv) => {
      ac[cv.id] = cv;
      return ac;
    }, {});

    quotations = (
      await this.queryAll({
        index: QUOTATION_TABLE,
        filter: {
          bool: {
            must: [...searchArray],
          },
        },
        nextToken: null,
        sort: [{ createdAt: { order: 'desc' } }],
      })
    ).map((quotation) => ({
      ...quotation,
      shopifyOrderId: ordersMap[quotation.orderId]?.shopifyOrderId,
      shopifyOrderName: ordersMap[quotation.orderId]?.shopifyOrderName,
    }));

    const sortedData = quotations.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    );

    const quotationWithoutTestStore = sortedData?.filter(
      (quotation) => quotation?.storeId.toUpperCase() !== 'TEST-STORE',
    );
    const attachments = [];
    console.log('quotationWithoutTestStore', quotationWithoutTestStore?.length);

    const quotationsCsv = await this.exportQuotationCSV(
      quotationWithoutTestStore,
    );
    console.log('quotationsCsv', quotationsCsv?.length);
    attachments.push({ name: 'Quotations', content: quotationsCsv });

    const mailService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    const dateRange =
      fromDate && toDate
        ? ` | Range: ${moment(fromDate).tz('Asia/Kolkata').format('DD/MM/YYYY')} - ${moment(toDate).tz('Asia/Kolkata').format('DD/MM/YYYY')}`
        : '';
    for (const attachment of attachments) {
      await mailService.sendEmailWithFileAttachment(
        email,
        `SmartServe POS Export | Type:  ${attachment.name}${dateRange} | The Sleep Company`,
        `Following is the ${attachment.name} ${TemplateType.REPORT}`,
        'text/csv',
        TemplateType.REPORT,
        'csv',
        [attachment],
      );
    }

    return { message: 'Email has been sent' };
  }
}
