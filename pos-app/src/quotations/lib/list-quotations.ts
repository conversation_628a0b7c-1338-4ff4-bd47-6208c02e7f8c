// modules

import {
  buildDateRangeFilter,
  filterFormatter,
} from 'src/common/helper/filter-helper';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { QuotationData, Quotations } from '../entities/quotation.entity';
import { ListQuotationInput } from '../dto/list-quotation.input';
import { ValidateQuotationsStatus } from './validate-status';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';

async function transformQueryArray(queryArray) {
  return queryArray.map((clause: any) => {
    if (
      clause.match_phrase_prefix &&
      clause.match_phrase_prefix.type === 'REGULAR'
    ) {
      return {
        bool: {
          should: [
            { term: { 'type.keyword': 'REGULAR' } },
            { bool: { must_not: [{ exists: { field: 'type' } }] } },
          ],
          minimum_should_match: 1,
        },
      };
    }

    return clause;
  });
}

export class QueryQuotations {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryQuotations(filter: ListQuotationInput): Promise<Quotations> {
    posLogger.info('quotation', 'queryQuotations', { input: filter });
    try {
      const QUOTATION_TABLE =
        await this.configParameters.getQuotationTableName();

      const validatedQuotationHandler = new ValidateQuotationsStatus(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const { searchArray: filteredSearch, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );
      const { fromDate, toDate } = filter || {};
      const dateRangeFilter = buildDateRangeFilter({
        fromDate,
        toDate,
        field: 'createdAt',
      });

      const filteredSearchWithoutCreatedAt = filteredSearch.filter((obj) => {
        if (obj.range && obj.range.createdAt) {
          return false;
        }
        return true;
      });

      const searchArray = [
        ...filteredSearchWithoutCreatedAt,
        dateRangeFilter,
      ].filter(Boolean);

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: QUOTATION_TABLE,
        });

        size = bodyRes?.count;
      }

      if (searchArray.length) {
        const transformedSearchArray = await transformQueryArray(searchArray);
        const { body: bodyRes } = await esHandler.count({
          index: QUOTATION_TABLE,
          body: {
            query: {
              bool: {
                must: [...transformedSearchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: QUOTATION_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...transformedSearchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data: QuotationData[] = response.body.hits.hits.map(
          (hit) => hit._source,
        );

        const validatedQuotations =
          await validatedQuotationHandler.validateQuotationsStatus(data);

        return { data: validatedQuotations, count: bodyRes?.count };
      }

      const response = await esHandler.search({
        index: QUOTATION_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data: QuotationData[] = response.body.hits.hits.map(
        (hit) => hit._source,
      );

      const validatedQuotations =
        await validatedQuotationHandler.validateQuotationsStatus(data);

      const { body: bodyRes } = await esHandler.count({
        index: QUOTATION_TABLE,
      });

      return { data: validatedQuotations, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('quotation', 'queryQuotations', { error: e });
      throw e;
    }
  }
}
