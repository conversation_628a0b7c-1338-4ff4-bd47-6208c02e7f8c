import { CreateQuotationInput } from '../dto/create-quotation.input';
import moment from 'moment';
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { QuotationStatus, QuotationType } from 'src/common/enum/quotations';
import { ValidateProducts } from 'src/common/helper/validate-products';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { CalculateCustomCodeDiscount } from 'src/coupons/lib/calculate-custom-discount';
import { getTotalAmount } from 'src/common/helper/get-total-amount';
import { CalculatePromotionalDiscount } from 'src/coupons/lib/calculate-promotional-discount';
import { posLogger } from 'src/common/logger';
import {
  EmployeeDiscountStatus,
  QuotationData,
} from '../entities/quotation.entity';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { GenerateCode } from 'src/common/helper/generate-code';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { CalculateCampaignCouponDiscountAmount } from 'src/campaign-coupons/lib/calculate-campaign-coupon-discount';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';
import { AppConfigParameters } from 'src/config/config';
import { getDiscountAndFinalPrice } from 'src/orders/helpers/get-item-discount';
import { getGstRateCalculator } from 'src/common/helper/gst-calculator';
import { v4 as uuid } from 'uuid';
import { getCartCount } from 'src/common/helper/get-cart-count';
import { AppShopify } from 'src/common/shopify/shopify';
import { GetCampaignCoupon } from 'src/campaign-coupons/lib/get-campaign-coupon';
import { CampaignCouponData } from 'src/campaign-coupons/entities/campaign-coupon.entity';
import { GetQuotation } from './get-quotation';
import { CustomDiscountApprovalStatus } from 'src/custom-discount-verification/entities/custom-discount-verification.entity';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AdditionalPromotionalCouponInput } from '../dto/update-quotation.input';
import { PaymentStatus } from 'src/common/enum/payment';
import { DeliveryChargeApprovalService } from './create-delivery-charge-otp-request';

export class CreateQuotation {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {}
  async validatePromotionalCode(params: {
    promotionalCode: string | null;
    cartPromotionalDiscountAmount: number;
    quotationProductItems: any[];
    updatedQuotationProductItems: any[];
    type: string;
    totalAmount: number;
    finalDiscountedAmount: number;
    customerPhone?: string;
  }): Promise<{ finalDiscountedAmount: number }> {
    const {
      promotionalCode,
      cartPromotionalDiscountAmount,
      quotationProductItems,
      updatedQuotationProductItems,
      type,
      totalAmount,
      finalDiscountedAmount,
      customerPhone,
    } = params;

    let updatedFinalDiscountedAmount = finalDiscountedAmount;

    if (promotionalCode) {
      const promotionalCodeValidator = new CalculatePromotionalDiscount(
        this.configService,
        this.ssmClient,
        this.docClient,
        this.configParameters,
        this.shopifyClient,
      );

      const { discount: promotionalDiscountAmount }: { discount: number } =
        await promotionalCodeValidator.calculatePromotionalDiscount(
          {
            code: promotionalCode,
            cartItems:
              type != 'UPDATE'
                ? quotationProductItems
                : updatedQuotationProductItems,
            cartTotal: totalAmount,
            phone: customerPhone,
          },
          'QUOTATION',
          type === 'UPDATE',
        );

      if (promotionalDiscountAmount != cartPromotionalDiscountAmount) {
        posLogger.error(
          'quotation',
          'validatePromotionalCode',
          `Cart promotional discount amount ${cartPromotionalDiscountAmount} does not match ${promotionalDiscountAmount}`,
        );
        throw new CustomError(
          `Cart promotional discount amount ${cartPromotionalDiscountAmount} does not match ${promotionalDiscountAmount}`,
          400,
        );
      }

      updatedFinalDiscountedAmount = Number(
        (
          updatedFinalDiscountedAmount -
          Number(promotionalDiscountAmount.toFixed(2))
        ).toFixed(2),
      );

      if (updatedFinalDiscountedAmount < 0) {
        posLogger.error(
          'quotation',
          'validatePromotionalCode',
          'Final discounted amount cannot be less than zero during Cart promotional discount application',
        );
        throw new CustomError(
          'Final discounted amount cannot be less than zero during Cart promotional discount application',
          400,
        );
      }
    } else {
      if (cartPromotionalDiscountAmount != 0) {
        posLogger.error(
          'quotation',
          'validatePromotionalCode',
          'Cart promotional discount amount does not match',
        );
        throw new CustomError(
          'Cart promotional discount amount does not match',
          400,
        );
      }
    }

    return { finalDiscountedAmount: updatedFinalDiscountedAmount };
  }

  async createFinalQuotationPayload(
    createQuotationInput: any,
    type: string,
    id?: string,
  ) {
    try {
      const {
        quotationProducts,
        campaignCode,
        customCode,
        promotionalCode,
        additionalPromotionalCoupons,
        storeId,
        type: quotationType,
        totalAmount: cartTotalAmount,
        finalDiscountedAmount: cartFinalDiscountedAmount,
        promotionalDiscountAmount: cartPromotionalDiscountAmount,
        customDiscountAmount: cartCustomDiscountAmount,
        campaignDiscountAmount: cartCampaignDiscountAmount,
        freeProducts: cartFreeProducts,
        customDiscountVerificationDetails = {},
        bnplProduct,
        bookingAmountStatus,
        deliveryCharge,
        employeeDiscountStatus = null,
        bookingAmount,
      } = createQuotationInput;

      const isBNPL = quotationType === QuotationType.BNPL;
      const isBNPLAndPriceLocked =
        isBNPL && bookingAmountStatus === PaymentStatus.PAID;

      console.log(
        customDiscountVerificationDetails,
        'customDiscountVerificationDetails',
      );

      if (cartFinalDiscountedAmount < 0) {
        throw new CustomError(
          'Final discounted amount cannot be less than zero',
          400,
        );
      }

      let finalDiscountedAmount = 0,
        freeProducts = null;

      //validate products
      if (!quotationProducts.length) {
        throw new CustomError('Products cannot be empty', 400);
      }

      const productsValidator = new ValidateProducts(
        this.configService,
        this.ssmClient,
        this.docClient,
        this.configParameters,
      );
      let bnplItem: any;
      if (bnplProduct) {
        [bnplItem] = await productsValidator.validateProducts([bnplProduct]);
      }

      const quotationProductItems = await productsValidator.validateProducts(
        quotationProducts,
        true,
        true,
        isBNPLAndPriceLocked ? true : false,
        true,
      );

      if (!quotationProductItems.length) {
        throw new CustomError('Validated Products cannot be empty', 400);
      }
      let updatedQuotationProductItems = [];

      if (type == 'UPDATE' && id) {
        const getQuotationHandler = new GetQuotation(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const { quotationProducts: oldQuotationProducts } =
          await getQuotationHandler.getQuotation(id, true);

        updatedQuotationProductItems = quotationProductItems.map(
          (newProduct) => {
            const oldProduct = oldQuotationProducts.find(
              (oldProduct) =>
                oldProduct.productId === newProduct.productId &&
                oldProduct.variantId === newProduct.variantId,
            );

            if (oldProduct && !isBNPLAndPriceLocked) {
              return {
                ...newProduct,
                variant: {
                  ...newProduct.variant,
                  compare_at_price: oldProduct.mrp,
                  price: oldProduct.price,
                },
                price: oldProduct.price,
              };
            }

            return newProduct;
          },
        );
      }

      const totalAmount: number =
        type != 'UPDATE'
          ? getTotalAmount(quotationProductItems)
          : getTotalAmount(updatedQuotationProductItems);

      if (totalAmount != cartTotalAmount) {
        posLogger.error(
          'quotation',
          'createFinalQuotation',
          'Cart total amount does not match',
        );
        throw new CustomError(
          `Cart total amount ${cartTotalAmount} does not match ${totalAmount}`,
          400,
        );
      }
      finalDiscountedAmount = Number(totalAmount.toFixed(2));
      console.log('finalDiscountedAmount 1:>> ', finalDiscountedAmount);

      if (promotionalCode && campaignCode) {
        const handler = new GetCampaignCoupon(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const coupon: CampaignCouponData =
          await handler.getCampaignCoupon(campaignCode);

        const { promoApplicable, promoCoupons } = coupon;
        if (
          !promoApplicable ||
          promoApplicable == 'SELECTIVE' ||
          promoApplicable == 'NOT'
        )
          if (promoApplicable == 'SELECTIVE') {
            if (promoCoupons?.includes(promotionalCode))
              throw new CustomError(
                `${promotionalCode} not applicable with ${campaignCode}`,
                400,
              );
          } else if (promoApplicable == 'NOT') {
            if (promoCoupons?.includes(promotionalCode))
              throw new CustomError(
                `${promotionalCode} not applicable with ${campaignCode}`,
                400,
              );
          }
      }

      if (promotionalCode) {
        const { finalDiscountedAmount: updatedFinalDiscountedAmount } =
          await this.validatePromotionalCode({
            promotionalCode: promotionalCode,
            cartPromotionalDiscountAmount: cartPromotionalDiscountAmount,
            quotationProductItems,
            updatedQuotationProductItems,
            type,
            totalAmount,
            finalDiscountedAmount,
            customerPhone: createQuotationInput?.customer?.phone,
          });
        finalDiscountedAmount = updatedFinalDiscountedAmount;
      }
      if (additionalPromotionalCoupons?.length > 0) {
        const DIYINSTALL: AdditionalPromotionalCouponInput =
          additionalPromotionalCoupons?.find(
            (coupon) => coupon.promotionalCode === 'DIYINSTALL',
          );
        if (DIYINSTALL) {
          const { finalDiscountedAmount: updatedFinalDiscountedAmount } =
            await this.validatePromotionalCode({
              promotionalCode: DIYINSTALL.promotionalCode,
              cartPromotionalDiscountAmount:
                DIYINSTALL.promotionalDiscountAmount,
              quotationProductItems,
              updatedQuotationProductItems,
              type,
              totalAmount,
              finalDiscountedAmount,
              customerPhone: createQuotationInput?.customer?.phone,
            });
          finalDiscountedAmount = updatedFinalDiscountedAmount;
        }
      }

      if (campaignCode) {
        const campaignCodeValidator = new CalculateCampaignCouponDiscountAmount(
          this.configService,
          this.ssmClient,
          this.docClient,
          this.configParameters,
        );
        const { discount: campaignCouponDiscountAmount, products } =
          await campaignCodeValidator.calculateCampaignCouponDiscountAmount({
            code: campaignCode,
            cartItems:
              type != 'UPDATE'
                ? quotationProductItems
                : updatedQuotationProductItems,
            cartTotal: totalAmount,
            storeId,
            promotionalDiscountAmount: cartPromotionalDiscountAmount || 0,
            isEditingQuotation: type === 'UPDATE',
          });

        freeProducts = products?.map(({ productId, variantId, quantity }) => ({
          productId,
          variantId,
          quantity,
        }));

        if (campaignCouponDiscountAmount != cartCampaignDiscountAmount) {
          posLogger.error(
            'quotation',
            'createFinalQuotation',
            'Cart campaign discount amount does not match',
          );
          throw new CustomError(
            `Cart campaign discount amount ${cartCampaignDiscountAmount} does not match ${campaignCouponDiscountAmount}`,
            400,
          );
        }

        const freeProductsQuantityCount = getCartCount(freeProducts);
        const cartFreeProductsQuantityCount = getCartCount(cartFreeProducts);

        if (freeProductsQuantityCount != cartFreeProductsQuantityCount) {
          posLogger.error(
            'quotation',
            'createFinalQuotation',
            'Cart free products amount does not match',
          );
          throw new CustomError(
            `Cart free products ${cartFreeProducts?.length || 0} does not match ${freeProducts?.length || 0}`,
            400,
          );
        }

        console.log(
          'campaignCouponDiscountAmount :>> ',
          campaignCouponDiscountAmount,
        );

        finalDiscountedAmount = Number(
          (
            finalDiscountedAmount -
            Number(campaignCouponDiscountAmount.toFixed(2))
          ).toFixed(2),
        );
        console.log('finalDiscountedAmount 4:>> ', finalDiscountedAmount);

        if (finalDiscountedAmount < 0) {
          posLogger.error(
            'quotation',
            'createFinalQuotation',
            'Final discounted amount cannot be less than zero during Cart campaign discount amount application',
          );
          throw new CustomError(
            'Final discounted amount cannot be less than zero during Cart campaign discount amount application',
            400,
          );
        }
      } else {
        if (cartCampaignDiscountAmount != 0) {
          posLogger.error(
            'quotation',
            'createFinalQuotation',
            'Cart campaign discount amount does not match',
          );
          throw new CustomError(
            'Cart campaign discount amount does not match',
            400,
          );
        }

        if (cartFreeProducts?.length) {
          posLogger.error(
            'quotation',
            'createFinalQuotation',
            'Cart free products amount does not match',
          );
          throw new CustomError(
            'Cart free products amount does not match',
            400,
          );
        }
      }
      const {
        customDiscountApprovalStatus = '',
        cartTotalAmount: cartTotalAmountAtRequestedTime,
        customDiscountAmount: approvedCustomDiscountAmount = 0,
      } = customDiscountVerificationDetails || {};
      if (customCode) {
        const customCodeValidator = new CalculateCustomCodeDiscount(
          this.configService,
          this.ssmClient,
          this.docClient,
          this.configParameters,
        );
        console.log(
          'approvedCustomDiscountAmount :>> ',
          customDiscountVerificationDetails,
        );

        let customDiscountAmount: number;

        if (
          customDiscountApprovalStatus === CustomDiscountApprovalStatus.APPROVED
        ) {
          if (cartTotalAmountAtRequestedTime > cartTotalAmount) {
            throw new CustomError(
              `Verification failed: The cart total at request time (${cartTotalAmountAtRequestedTime}) cannot be less than the current cart total (${cartTotalAmount}).`,
              400,
            );
          }
          customDiscountAmount = approvedCustomDiscountAmount;
        } else if (employeeDiscountStatus === EmployeeDiscountStatus.APPROVED) {
          customDiscountAmount = cartCustomDiscountAmount;
        } else {
          const customDiscount =
            await customCodeValidator.calculateCustomDiscount({
              code: customCode,
              cartTotal: totalAmount,
              config: 'CUSTOM_DISCOUNT',
              totalDiscounts:
                Number(cartCampaignDiscountAmount) +
                Number(cartPromotionalDiscountAmount),
              promotionalDiscountAmount: cartPromotionalDiscountAmount,
              campaignDiscountAmount: cartCampaignDiscountAmount,
            });
          customDiscountAmount = customDiscount.discount;
        }

        if (customDiscountAmount != cartCustomDiscountAmount) {
          posLogger.error(
            'quotation',
            'createFinalQuotation',
            'Cart custom discount amount does not match',
          );
          throw new CustomError(
            `Cart custom discount amount ${cartCustomDiscountAmount} does not match ${customDiscountAmount}`,
            400,
          );
        }

        finalDiscountedAmount = Number(
          (
            finalDiscountedAmount - Number(customDiscountAmount.toFixed(2))
          ).toFixed(2),
        );

        if (finalDiscountedAmount < 0) {
          posLogger.error(
            'quotation',
            'createFinalQuotation',
            'Final discounted amount cannot be less than zero during Cart custom discount amount application',
          );
          throw new CustomError(
            'Final discounted amount cannot be less than zero during Cart custom discount amount application',
            400,
          );
        }

        console.log('finalDiscountedAmount 2:>> ', finalDiscountedAmount);
      } else {
        if (cartCustomDiscountAmount != 0) {
          posLogger.error(
            'quotation',
            'createFinalQuotation',
            'Cart custom discount amount does not match',
          );
          throw new CustomError(
            `Cart custom discount amount does not match`,
            400,
          );
        }
      }

      if (id) {
        const getQuotationHandler = new GetQuotation(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const { shippingAddress, deliveryChargeApprovalDetails } =
          await getQuotationHandler.getQuotation(id, true);

        const verifyApprovalRequestHandler = new DeliveryChargeApprovalService(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const { pinCode: shippingPinCode } = shippingAddress || {};
        let calculatedDeliveryCharge = 0;
        if (shippingPinCode) {
          calculatedDeliveryCharge =
            await verifyApprovalRequestHandler.getDeliveryCharge(
              quotationProducts,
              shippingPinCode,
              deliveryChargeApprovalDetails,
            );
        }
        if (calculatedDeliveryCharge > 0) {
          if (!deliveryCharge || calculatedDeliveryCharge !== deliveryCharge) {
            throw new CustomError('Delivery Charge Mismatch', 400);
          }
          finalDiscountedAmount =
            finalDiscountedAmount + calculatedDeliveryCharge;
        }
      }

      if (bookingAmount && isBNPLAndPriceLocked) {
        finalDiscountedAmount = finalDiscountedAmount - bookingAmount;
      }

      console.log('finalDiscountedAmount 5:>> ', finalDiscountedAmount);

      if (finalDiscountedAmount != cartFinalDiscountedAmount) {
        posLogger.error(
          'quotation',
          'createFinalQuotation',
          'Cart final discounted amount does not match',
        );
        throw new CustomError(
          `Cart final discounted amount ${cartFinalDiscountedAmount} does not match ${finalDiscountedAmount}`,
          400,
        );
      }

      return {
        ...createQuotationInput,
        totalAmount,
        freeProducts: cartFreeProducts,
        quotationProductItems:
          type != 'UPDATE'
            ? quotationProductItems
            : updatedQuotationProductItems,
        bnplItem,
      };
    } catch (error) {
      posLogger.error('quotation', 'createFinalQuotation', { error });
      throw error;
    }
  }

  async createQuotation(
    createQuotationInput: CreateQuotationInput,
  ): Promise<QuotationData> {
    posLogger.info('quotation', 'createQuotation', {
      input: createQuotationInput,
    });

    const { storeId, type } = createQuotationInput;
    const QUOTATION_TABLE = await this.configParameters.getQuotationTableName();

    const getGlobalConfigurationHandler = new GetGlobalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const { value: QUOTATION_EXPIRE_INTERVAL_IN_DAYS } =
      await getGlobalConfigurationHandler.getGlobalConfiguration(
        'QUOTATION_EXPIRE_INTERVAL_IN_DAYS',
      );

    const getStoreHandler = new GetStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const { quotationStartCode, isActive } =
      await getStoreHandler.getStore(storeId);

    if (!isActive) {
      throw new CustomError(`Store is not active`, 400);
    }

    if (!quotationStartCode) {
      throw new CustomError(
        `UNEXPECTED FLOW: quotationStartCode prefix is not present in store`,
        400,
      );
    }

    const { freeProducts, quotationProductItems, bnplItem } =
      await this.createFinalQuotationPayload(createQuotationInput, 'CREATE');

    const codeHandler = new GenerateCode(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const code = await codeHandler.generateCode(
      storeId,
      'QUOTATION_COUNT',
      quotationStartCode,
    );

    // //Expiring all the active quotations of the customer
    // try {
    //   const queryQuotationHandler = new QueryQuotations(
    //     this.configService,
    //     this.docClient,
    //     this.ssmClient,
    //     this.configParameters,
    //   );
    //   const updateQuotationStatusHandler = new UpdateQuotationStatus(
    //     this.configService,
    //     this.docClient,
    //     this.ssmClient,
    //     this.configParameters,
    //   );
    //   const { data: existingActiveQuotations } =
    //     await queryQuotationHandler.queryQuotations({
    //       from: '0',
    //       size: '10',
    //       termSearchFields: { customerId: `${customerId}` },
    //       textSearchFields: { status: QuotationStatus.ACTIVE },
    //     });
    //   const { data: existingOrderCreatedQuotations } =
    //     await queryQuotationHandler.queryQuotations({
    //       from: '0',
    //       size: '10',
    //       termSearchFields: { customerId: `${customerId}` },
    //       textSearchFields: { status: QuotationStatus.ORDER_CREATED },
    //     });

    //   const existingQuotations = [
    //     ...(existingActiveQuotations || []),
    //     ...(existingOrderCreatedQuotations || []),
    //   ];

    //   if (existingQuotations && existingActiveQuotations.length) {
    //     await Promise.all(
    //       existingActiveQuotations.map(async ({ id }) => {
    //         try {
    //           await updateQuotationStatusHandler.updateQuotationStatus(
    //             id,
    //             QuotationStatus.EXPIRED,
    //           );
    //         } catch (error) {
    //           posLogger.error(
    //             'quotation',
    //             'createQuotation existingActiveQuotations expiring',
    //             error,
    //           );
    //         }
    //       }),
    //     );
    //   }
    // } catch (error) {
    //   posLogger.error(
    //     'quotation',
    //     'createQuotation existingActiveQuotations expiring',
    //     error,
    //   );
    // }

    let bnplProduct = null;
    if (type === 'BNPL' && bnplItem) {
      const {
        productId,
        variantId,
        quantity,
        price,
        product,
        variant,
        customData,
      } = bnplItem;

      const itemDiscount = 0;
      const finalItemPrice = price;

      const { gstRate, gstAmount, finalItemPriceWithoutGst, gstPercentage } =
        getGstRateCalculator({
          ...product,
          ...variant,
          finalItemPrice,
        });

      bnplProduct = {
        id: uuid(),
        productId,
        variantId: variantId || null,
        quantity,
        itemDiscount,
        price,
        gstRate,
        gstAmount,
        gstPercentage,
        finalItemPrice,
        finalItemPriceWithoutGst,
        customData: customData || null,
        title: product.title,
        variantTitle: variant?.variantTitle || null,
        mrp: variant?.compare_at_price || null,
        image: variant?.image || product.image,
        productType: product?.product_type || null,
        sku: variant?.sku || null,
        edd: variant?.metafields.find((m) => m.key == 'edd')?.value || null,
        storeId: storeId,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      };
    }

    const { value: BNPL_BOOKING_EXPIRY } =
      await getGlobalConfigurationHandler.getGlobalConfiguration(
        'BNPL_BOOKING_EXPIRY',
      );

    let expiresAt = null;
    if (type === QuotationType.BNPL) {
      expiresAt = moment()
        .add(Number(BNPL_BOOKING_EXPIRY || 1), 'days')
        .toISOString();
    } else {
      expiresAt = moment()
        .add(Number(QUOTATION_EXPIRE_INTERVAL_IN_DAYS), 'days')
        .toISOString();
    }

    // Creating new Quotation
    const data: QuotationData = {
      ...createQuotationInput,
      id: code,
      quotationProducts: quotationProductItems.map(
        ({
          productId,
          variantId,
          quantity,
          price,
          hasColorOptions,
          product,
          variant,
          customData,
        }) => {
          const { itemDiscount, finalItemPrice } = getDiscountAndFinalPrice(
            quantity,
            price,
            createQuotationInput.totalAmount -
              createQuotationInput.finalDiscountedAmount,
            createQuotationInput.totalAmount,
          );

          const {
            gstRate,
            gstAmount,
            finalItemPriceWithoutGst,
            gstPercentage,
          } = getGstRateCalculator({
            ...product,
            ...variant,
            finalItemPrice,
          });

          return {
            id: uuid(),
            productId,
            variantId: variantId || null,
            quantity,
            itemDiscount,
            price,
            gstRate,
            gstAmount,
            hasColorOptions,
            gstPercentage,
            finalItemPrice,
            finalItemPriceWithoutGst,
            customData: customData || null,
            title: product.title,
            variantTitle: variant?.variantTitle || null,
            mrp: variant?.compare_at_price || null,
            image: variant?.image || product.image,
            productType: product?.product_type || null,
            sku: variant?.sku || null,
            edd: variant?.metafields.find((m) => m.key == 'edd')?.value || null,
            storeId: storeId,
            createdAt: moment().toISOString(),
            updatedAt: moment().toISOString(),
          };
        },
      ),
      bnplProduct,
      createdAt: moment().toISOString(),
      updatedAt: moment().toISOString(),
      status: QuotationStatus.ACTIVE,
      freeProducts,
      expiresAt: expiresAt,
    };

    const quotationCommand = new PutCommand({
      TableName: QUOTATION_TABLE,
      Item: data,
      ConditionExpression: 'attribute_not_exists(id)',
    });
    await this.docClient.createItem(quotationCommand);

    posLogger.info('quotation', 'createQuotation', `quotation created ${code}`);

    return data;
  }
  catch(error) {
    throw error;
  }
}
