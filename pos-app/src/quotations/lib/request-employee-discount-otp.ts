import { GetCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

import moment from 'moment';

import { generateOtp } from 'src/common/helper/generate-otp';
import { EmployeeDetailsInput } from '../dto/update-quotation.input';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetQuotation } from './get-quotation';
import { EmployeeDiscountStatus } from '../entities/quotation.entity';
import { OtpService } from 'src/common/helper/send-otp';
export class RequestEmployeeDiscountOTP {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}
  async execute(
    quotationId: string,
    employeeDiscountDetails: EmployeeDetailsInput,
  ) {
    const { phone } = employeeDiscountDetails;
    const EMPLOYEE_MASTER_TABLE =
      await this.configParameters.getMasterEmployeeTableName();

    const employeeMasterCommand = new GetCommand({
      TableName: EMPLOYEE_MASTER_TABLE,
      Key: { phone },
    });

    const employeeData = await this.docClient.getItem(employeeMasterCommand);

    if (!employeeData?.Item) {
      throw new CustomError('Employee not found.', 400);
    }

    if (employeeData.Item?.discountDetails) {
      throw new CustomError('Employee already has a discount.', 400);
    }

    const getQuotationHandler = new GetQuotation(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    console.log('employeeDataa5', quotationId, phone);

    const quotationData = await getQuotationHandler.getQuotation(quotationId);
    if (!quotationData) {
      throw new CustomError('Quotation not found.', 400);
    }
    console.log('employeeDataa6', quotationId, phone);

    const { customDiscountAmount, campaignDiscountAmount } = quotationData;

    if (customDiscountAmount > 0 || campaignDiscountAmount > 0) {
      throw new CustomError('Discount already applied to this quotation.', 400);
    }
    console.log('employeeDataa7', quotationId, phone);
    const otp = generateOtp();
    await this.sendOTP(phone, otp);
    const employeeDiscountDetailsWithId = {
      ...employeeDiscountDetails,
      quotationId,
      otp,
      createdAt: moment().toISOString(),
      updatedAt: moment().toISOString(),
      status: EmployeeDiscountStatus.PENDING,
      expiresAt: moment().add(1, 'hour').toISOString(),
    };
    const QUOTATIONS_TABLE =
      await this.configParameters.getQuotationTableName();
    const updatedQuotation = await this.docClient.updateItem(
      new UpdateCommand({
        TableName: QUOTATIONS_TABLE,
        Key: { id: quotationId },
        UpdateExpression:
          'SET employeeDiscountDetails = :employeeDiscountDetails, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
          ':employeeDiscountDetails': employeeDiscountDetailsWithId,
          ':updatedAt': moment().toISOString(),
        },
        ReturnValues: 'ALL_NEW',
      }),
    );

    return updatedQuotation.Attributes;
  }

  async sendOTP(phone, otp) {
    const otpHandler = new OtpService(this.ssmClient, this.configService);
    const payload = {
      templateName: 'employeeDiscount',
      OTP: otp,
      phoneNo: phone,
    };

    await otpHandler.sendSms(payload);
  }
}
