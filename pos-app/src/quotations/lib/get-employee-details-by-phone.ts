// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { EmployeeMasterDetailsData } from 'src/quotations/entities/quotation.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';

export class GetEmployeeDetails {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getEmployeeDetailsByPhone(
    phone: string,
  ): Promise<EmployeeMasterDetailsData> {
    posLogger.info(
      'GetEmployeeDetailsByPhone',
      'getEmployeeDetailsByPhone',
      phone,
    );
    try {
      const EMPLOYEE_MASTER_TABLE =
        await this.configParameters.getMasterEmployeeTableName();

      const param = new GetCommand({
        TableName: EMPLOYEE_MASTER_TABLE,
        Key: {
          phone,
        },
      });

      const { Item: data }: { Item: EmployeeMasterDetailsData } =
        await this.docClient.getItem(param);

      if (data) {
        return data;
      }

      throw new CustomError(
        `Invalid Quotation ID! No Quotation found for given Quotation ID ${phone}.`,
        404,
      );
    } catch (e) {
      posLogger.error('quotation', 'getQuotation', { error: e });
      throw e;
    }
  }
}
