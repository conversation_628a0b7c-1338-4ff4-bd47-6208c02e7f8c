// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { QuotationData } from '../entities/quotation.entity';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';
import { AppConfigParameters } from 'src/config/config';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { CreateQuotation } from './create-quotation';
import { AppShopify } from 'src/common/shopify/shopify';
import { getGstRateCalculator } from 'src/common/helper/gst-calculator';
import { v4 as uuid } from 'uuid';
import { getDiscountAndFinalPrice } from 'src/orders/helpers/get-item-discount';
import { GetOrderAndStoreAndSendPdf } from 'src/common/helper/get-order-and-store';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { QuotationType } from 'src/common/enum/quotations';
import { PaymentStatus } from 'src/common/enum/payment';

export class updateQuotationProducts {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {}

  async updateQuotationProducts(
    id: string = '',
    { id: {}, ...updateCartInput },
  ): Promise<QuotationData> {
    posLogger.info('quotation', 'updateQuotation', {
      input: { id, updateCartInput },
    });
    try {
      const { storeId, type } = updateCartInput;
      const QUOTATION_TABLE =
        await this.configParameters.getQuotationTableName();

      const getGlobalConfigurationHandler = new GetGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const { value: QUOTATION_EXPIRE_INTERVAL_IN_DAYS } =
        await getGlobalConfigurationHandler.getGlobalConfiguration(
          'QUOTATION_EXPIRE_INTERVAL_IN_DAYS',
        );

      const getStoreHandler = new GetStore(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const { quotationStartCode, isActive } =
        await getStoreHandler.getStore(storeId);

      if (!isActive) {
        throw new CustomError(`Store is not active`, 400);
      }

      if (!quotationStartCode) {
        throw new CustomError(
          `UNEXPECTED FLOW: quotationStartCode prefix is not present in store`,
          400,
        );
      }
      const quotationHandler = new CreateQuotation(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.shopifyClient,
        this.s3Client,
      );

      delete updateCartInput.id;
      const { freeProducts, quotationProductItems, bnplItem } =
        await quotationHandler.createFinalQuotationPayload(
          updateCartInput,
          'UPDATE',
          id,
        );
      const employeeDiscountStatus =
        updateCartInput?.employeeDiscountStatus || null;
      if (updateCartInput?.employeeDiscountStatus) {
        delete updateCartInput.employeeDiscountStatus;
      }
      console.log('freeProducts', freeProducts, quotationProductItems);

      let quotationExpiresAt = moment()
        .add(Number(QUOTATION_EXPIRE_INTERVAL_IN_DAYS), 'days')
        .toISOString();

      if (type === QuotationType.BNPL) {
        if (updateCartInput.bookingAmountStatus === PaymentStatus.PENDING) {
          quotationExpiresAt = moment().endOf('day').toISOString();
        }
      }

      let bnplProduct = undefined;
      if (type === 'BNPL' && bnplItem) {
        const {
          productId,
          variantId,
          quantity,
          price,
          product,
          variant,
          customData,
        } = bnplItem;

        const itemDiscount = 0;
        const finalItemPrice = price;

        const { gstRate, gstAmount, finalItemPriceWithoutGst, gstPercentage } =
          getGstRateCalculator({
            ...product,
            ...variant,
            finalItemPrice,
          });

        bnplProduct = {
          id: uuid(),
          productId,
          variantId: variantId || null,
          quantity,
          itemDiscount,
          price,
          gstRate,
          gstAmount,
          gstPercentage,
          finalItemPrice,
          finalItemPriceWithoutGst,
          customData: customData || null,
          title: product.title,
          variantTitle: variant?.variantTitle || null,
          mrp: variant?.compare_at_price || null,
          image: variant?.image || product.image,
          productType: product?.product_type || null,
          sku: variant?.sku || null,
          edd: variant?.metafields.find((m) => m.key == 'edd')?.value || null,
          storeId: storeId,
          createdAt: moment().toISOString(),
          updatedAt: moment().toISOString(),
        };
      }

      const data: any = {
        ...updateCartInput,
        ...(employeeDiscountStatus ? {} : { employeeDiscountDetails: null }),
        quotationProducts: quotationProductItems.map(
          ({
            productId,
            variantId,
            quantity,
            price,
            // priceType,
            originalPrice = null,
            product,
            variant,
            customData,
            hasColorOptions,
          }) => {
            const { itemDiscount, finalItemPrice } = getDiscountAndFinalPrice(
              quantity,
              price,
              updateCartInput.totalAmount -
                updateCartInput.finalDiscountedAmount +
                (updateCartInput?.deliveryCharge || 0),
              updateCartInput.totalAmount,
            );

            const {
              gstRate,
              gstAmount,
              finalItemPriceWithoutGst,
              gstPercentage,
            } = getGstRateCalculator({
              ...product,
              ...variant,
              finalItemPrice,
            });

            const temp: any = {
              id: uuid(),
              productId,
              variantId: variantId || null,
              quantity,
              itemDiscount,
              price,
              originalPrice,
              gstRate,
              gstAmount,
              gstPercentage,
              finalItemPrice,
              finalItemPriceWithoutGst,
              hasColorOptions,
              customData: customData || null,
              title: product.title,
              variantTitle: variant?.variantTitle || null,
              mrp: variant?.compare_at_price || null,
              image: variant?.image || product.image,
              productType: product?.product_type || null,
              sku: variant?.sku || null,
              edd:
                variant?.metafields.find((m) => m.key == 'edd')?.value || null,
              storeId: storeId,
              createdAt: moment().toISOString(),
              updatedAt: moment().toISOString(),
            };

            // if (priceType) {
            //   temp.priceType = priceType;
            // }
            return temp;
          },
        ),
        freeProducts,
        expiresAt: quotationExpiresAt,
      };

      if (bnplProduct) {
        data.bnplProduct = bnplProduct;
      }

      if (
        updateCartInput.bookingAmountStatus &&
        updateCartInput.bookingAmountStatus === PaymentStatus.PAID
      ) {
        delete data.expiresAt;
      }

      let updateExpressionString: string = 'Set #updatedAt = :updatedAt ,';
      const expressionAttributeNames: Record<string, string> = {
        '#updatedAt': 'updatedAt',
      };
      const expressionAttributesValues: Record<string, any> = {
        ':updatedAt': moment().toISOString(),
      };

      Object.keys(data).map((key) => {
        updateExpressionString += `#${key} = :${key}, `;
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributesValues[`:${key}`] = data[key];
      });

      updateExpressionString = updateExpressionString.substring(
        0,
        updateExpressionString.length - 2,
      );

      // Fire the update command
      const command = new UpdateCommand({
        TableName: QUOTATION_TABLE,
        Key: {
          id,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributesValues,
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: QuotationData } =
        await this.docClient.updateItem(command);
      const updateHandler = new GetOrderAndStoreAndSendPdf(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.s3Client,
        this.configParameters,
      );
      await updateHandler.getQuotationAndStoreAndSendPdf(id, 'DOWNLOAD');
      return Attributes;
    } catch (e) {
      posLogger.error('quotation', 'updateQuotation', { error: e });
      throw e;
    }
  }
}
