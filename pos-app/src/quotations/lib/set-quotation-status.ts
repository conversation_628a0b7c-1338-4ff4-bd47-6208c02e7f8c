// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import moment from 'moment';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { QuotationStatus } from 'src/common/enum/quotations';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { QuotationData } from 'src/quotations/entities/quotation.entity';

export class UpdateQuotationStatus {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateQuotationStatus(
    id: string,
    status: QuotationStatus,
    orderId?: string | null,
    superOrderId?: string | null,
  ): Promise<QuotationData> {
    posLogger.info('quotation', 'updateQuotationStatus', {
      input: { id, status, orderId, superOrderId },
    });
    try {
      const QUOTATION_TABLE =
        await this.configParameters.getQuotationTableName();

      let UpdateExpression = 'set #status = :status, updatedAt = :updatedAt';
      let ExpressionAttributeValues: Record<string, any> = {
        ':status': status,
        ':updatedAt': moment().toISOString(),
      };

      if (superOrderId) {
        UpdateExpression += ', superOrderId = :superOrderId';
        ExpressionAttributeValues = {
          ...ExpressionAttributeValues,
          ':superOrderId': superOrderId || null,
        };
      }

      UpdateExpression += ', orderId = :orderId';
      ExpressionAttributeValues = {
        ...ExpressionAttributeValues,
        ':orderId': orderId || null,
      };

      const param = new UpdateCommand({
        TableName: QUOTATION_TABLE,
        Key: {
          id,
        },
        UpdateExpression,
        ExpressionAttributeValues,
        ConditionExpression: 'attribute_exists(id)',
        ExpressionAttributeNames: {
          '#status': 'status',
        },
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes: data }: { Attributes: QuotationData } =
        await this.docClient.updateItem(param);

      if (data) {
        return data;
      }

      throw new CustomError(
        `updateQuotationStatus Invalid Quotation ID! No Quotation found for given Quotation ID ${id}.`,
        404,
      );
    } catch (e) {
      posLogger.error('quotation', 'updateQuotationStatus', { error: e });
      throw e;
    }
  }
}
