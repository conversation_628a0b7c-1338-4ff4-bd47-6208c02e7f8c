import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { QuotationsService } from './quotations.service';
import {
  EmployeeMasterDetails,
  Quotation,
  Quotations,
} from './entities/quotation.entity';
import { CreateQuotationInput } from './dto/create-quotation.input';
import {
  EmployeeDetailsInput,
  DeliveryChargeApprovalInput,
  UpdateQuotationInput,
} from './dto/update-quotation.input';
import { ListQuotationInput } from './dto/list-quotation.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import {
  CustomError,
  ErrorHandler,
} from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { Response } from 'src/products/entities/product.entity';
import { CRMGuard } from 'src/auth/roles.guard';
import { UpdateQuotationProductsInput } from './dto/update-quotation-products.input';

@Resolver(() => Quotation)
export class QuotationsResolver {
  constructor(
    private readonly quotationsService: QuotationsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => Quotation, { name: 'createQuotation' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async createQuotation(
    @Args('createQuotationInput') createQuotationInput: CreateQuotationInput,
  ) {
    try {
      const data = await this.quotationsService.create(createQuotationInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create quotation',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Quotation)
  async requestEmployeeDiscountOTP(
    @Args('quotationId') quotationId: string,
    @Args('employeeDiscountDetails')
    employeeDiscountDetails: EmployeeDetailsInput,
  ) {
    try {
      const data = await this.quotationsService.requestEmployeeDiscountOTP(
        quotationId,
        employeeDiscountDetails,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to request employee discount OTP',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Quotation)
  async verificationEmployeeDiscountOTP(
    @Args('quotationId') quotationId: string,
    @Args('otp') otp: string,
  ) {
    try {
      const data = await this.quotationsService.verificationEmployeeDiscountOTP(
        quotationId,
        otp,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      return this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to verify employee discount OTP',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  @Query(() => Quotations, { name: 'listQuotations' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async findAll(
    @Args('listQuotationInput', { nullable: true })
    listQuotationInput: ListQuotationInput,
  ) {
    try {
      const { data, count } =
        await this.quotationsService.findAll(listQuotationInput);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list quotations',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Response, { name: 'exportQuotations' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async exportQuotations(
    @Args('email', { type: () => String }) email: string,
    @Args('listQuotationInput', { nullable: true })
    listQuotationInput: ListQuotationInput,
  ) {
    try {
      const { message } = await this.quotationsService.exportQuotations(
        email,
        listQuotationInput,
      );
      return {
        message,
        success: true,
        status: 200,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to export quotations',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Quotation, { name: 'updateQuotation' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async updateQuotation(
    @Args('updateQuotationInput')
    updateQuotationInput: UpdateQuotationInput,
  ) {
    try {
      const data = await this.quotationsService.update(
        updateQuotationInput.id,
        updateQuotationInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update quotation',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Quotation)
  async requestDeliveryChargeApproval(
    @Args('quotationId') quotationId: string,
    @Args('input') input: DeliveryChargeApprovalInput,
    @Args('resend', { type: () => Boolean, defaultValue: false })
    resend: boolean,
  ) {
    try {
      const data = await this.quotationsService.requestDeliveryChargeApproval(
        quotationId,
        input,
        resend,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to request delivery charge approval',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Quotation)
  async verifyDeliveryChargeApproval(
    @Args('quotationId') quotationId: string,
    @Args('otp') otp: string,
  ) {
    try {
      const data = await this.quotationsService.verifyDeliveryChargeApproval(
        quotationId,
        otp,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      return this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to verify delivery charge approval',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  @Query(() => Response, { name: 'quotationPdf' })
  async quotationPdf(
    @Args('quotationId')
    quotationId: string,
    @Args('type')
    type: string,
  ) {
    try {
      if (!['EMAIL', 'DOWNLOAD', 'WHATSAPP'].includes(type)) {
        throw new CustomError(
          `Type should be either EMAIL or DOWNLOAD or WHATSAPP`,
          400,
        );
      }

      const message = await this.quotationsService.sendQuotationPdf(
        quotationId,
        type,
      );

      return {
        message,
        status: 200,
        success: true,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to downloading quotation pdf',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => EmployeeMasterDetails, { name: 'getEmployeeDetails' })
  // @UseGuards(CustomAuthGuard, CRMGuard)
  async findOneEmployee(@Args('phone', { type: () => String }) phone: string) {
    try {
      const data = await this.quotationsService.findOneEmployee(phone);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get quotation',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Quotation, { name: 'getQuotation' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async findOne(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.quotationsService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get quotation',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Mutation(() => Quotation, { name: 'updateQuotationProducts' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async updateQuotationProducts(
    @Args('updateQuotationProductsInput')
    updateQuotationProductsInput: UpdateQuotationProductsInput,
  ) {
    try {
      const data = await this.quotationsService.updateProucts(
        updateQuotationProductsInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create quotation',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
