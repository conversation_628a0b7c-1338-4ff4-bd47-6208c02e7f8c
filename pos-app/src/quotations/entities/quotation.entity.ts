import { ObjectType, Field, Int, registerEnumType } from '@nestjs/graphql';
import {
  CartProduct,
  CustomCode,
  CustomData,
} from 'src/carts/entities/cart.entity';
import { CouponType } from 'src/common/enum/coupons';
import { PaymentStatus } from 'src/common/enum/payment';
import { QuotationStatus, QuotationType } from 'src/common/enum/quotations';
import {
  ApproverDetails,
  CustomDiscountApprovalStatus,
} from 'src/custom-discount-verification/entities/custom-discount-verification.entity';
import { Image } from 'src/products/entities/product.entity';

export enum EmployeeDiscountStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  APPLIED = 'APPLIED',
  EXPIRED = 'EXPIRED',
}

registerEnumType(EmployeeDiscountStatus, {
  name: 'EmployeeDiscountStatus',
  description: 'Status of the employee discount request',
});
export enum DeliveryChargeApprovalStatus {
  CREATED = 'CREATED',
  APPROVED = 'APPROVED',
  EXPIRED = 'EXPIRED',
  APPLIED = 'APPLIED',
}
registerEnumType(DeliveryChargeApprovalStatus, {
  name: 'DeliveryChargeApprovalStatus',
});

registerEnumType(QuotationStatus, { name: 'QuotationStatus' });
registerEnumType(QuotationType, { name: 'QuotationType' });

@ObjectType()
export class CustomerMetadata {
  @Field(() => String, {
    nullable: true,
    description: 'First Name of the customer',
  })
  firstName?: string;

  @Field(() => String, {
    nullable: true,
    description: 'First Name of the customer',
  })
  lastName?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Phone number',
  })
  phone?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Email of the customer',
  })
  email?: string;

  @Field(() => String, {
    nullable: true,
    description: 'date of birth',
  })
  dob?: string;

  @Field(() => String, {
    nullable: true,
    description: 'anniversaryDate',
  })
  anniversaryDate?: string;

  @Field(() => String, {
    nullable: true,
    description: 'serviceLift',
  })
  serviceLift?: string;

  @Field(() => String, {
    nullable: true,
    description: 'accommodationType',
  })
  accommodationType?: string;

  @Field(() => String, {
    nullable: true,
    description: 'landmark',
  })
  landmark?: string;

  @Field(() => String, {
    nullable: true,
    description: 'latitude',
  })
  latitude?: string;

  @Field(() => String, {
    nullable: true,
    description: 'longitude',
  })
  longitude?: string;
}
@ObjectType({
  description: 'Details of the employee discount applied to a quotation',
})
export class DiscountDetails {
  @Field(() => String, {
    description: 'Timestamp when the discount was applied (ISO format)',
  })
  appliedAt: string;

  @Field(() => String, {
    description: 'Unique discount code applied for the employee',
  })
  code: string;

  @Field(() => Number, {
    description: 'Discount value applied to the quotation',
  })
  discountValue: number;

  @Field(() => String, {
    description: 'Quotation ID associated with the discount',
  })
  quotationId: string;
}

@ObjectType()
export class EmployeeMasterDetailsData {
  @Field(() => String, {
    nullable: false,
    description: 'First Name of the customer',
  })
  name: string;

  @Field(() => DiscountDetails, { nullable: true })
  discountDetails?: DiscountDetails;

  @Field(() => String, {
    nullable: false,
    description: 'Phone number',
  })
  phone: string;
}
@ObjectType()
export class EmployeeMasterDetails {
  @Field(() => EmployeeMasterDetailsData, {
    nullable: true,
    description: 'Quotation',
  })
  data: EmployeeMasterDetailsData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class DeliveryChargeApprovalDetails {
  @Field(() => String)
  name: string;

  @Field(() => String)
  phone: string;

  @Field(() => Number)
  requestedCharge: number;

  @Field(() => DeliveryChargeApprovalStatus)
  status: DeliveryChargeApprovalStatus;

  @Field(() => String, { nullable: true })
  otp?: string;

  @Field(() => String)
  expiresAt: string;

  @Field(() => String)
  createdAt: string;

  @Field(() => String)
  updatedAt: string;
}
@ObjectType()
export class CustomDiscountVerificationDetails {
  @Field(() => String, {
    description: 'Quotation ID',
  })
  id: string;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountAmount: number;

  @Field(() => Number, {
    description: 'The initial cart amount before applying the discount.',
  })
  initialCartDiscountedAmount: number;

  @Field(() => Number, {
    description: 'The initial cart amount before applying the discount.',
  })
  cartTotalAmount: number;

  @Field(() => Number, {
    nullable: true,
    description: 'The final cart amount after applying the discount.',
  })
  finalCartDiscountedAmount: number;

  @Field(() => ApproverDetails, {
    description: 'The mobile number of the customer requesting the discount.',
  })
  approverDetails: ApproverDetails;

  @Field(() => CustomDiscountApprovalStatus, {
    description:
      'The status of the discount approval process (REQUESTED, REJECTED, APPROVED, APPLIED, EXPIRED).',
  })
  customDiscountApprovalStatus: CustomDiscountApprovalStatus;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  additionalDiscountPercentage: number;

  @Field(() => CouponType, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountType: CouponType;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountValue: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  promotionalDiscountAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  campaignDiscountAmount: number;

  @Field(() => String, {
    description: 'The timestamp when the discount expired.',
  })
  discountExpiredTimestamp: string;
}
@ObjectType()
export class EmployeeDiscountDetails {
  @Field(() => String, { description: "Employee's phone number" })
  phone: string;

  @Field(() => String, { description: "Employee's phone number" })
  quotationId: string;

  @Field(() => String, { description: "Employee's full name" })
  name: string;

  @Field(() => String, { description: 'OTP sent for discount approval' })
  otp: string;

  @Field(() => String, {
    description: 'Timestamp when the request was created',
  })
  createdAt: string;

  @Field(() => String, {
    description: 'Timestamp when the request was last updated',
  })
  updatedAt: string;

  @Field(() => EmployeeDiscountStatus, {
    description: 'Status of the discount approval request',
  })
  status: EmployeeDiscountStatus;

  @Field(() => String, { description: 'Expiry time for the OTP' })
  expiresAt: string;
}
@ObjectType()
export class QuotationProduct {
  @Field(() => String, { nullable: true })
  id?: string;

  @Field(() => String, { nullable: false })
  productId: string;

  @Field(() => String, { nullable: true })
  variantId?: string;

  @Field(() => Int, { nullable: false })
  quantity: number;

  @Field(() => CustomData, { nullable: true })
  customData?: CustomData;

  @Field(() => Number, { nullable: true })
  price: number;

  @Field(() => String, { nullable: true })
  priceType: string;

  @Field(() => String, { nullable: true })
  mrp?: string;

  @Field(() => String, { nullable: true })
  title?: string;

  @Field(() => String, { nullable: true })
  variantTitle?: string;

  @Field(() => Image, { nullable: true })
  image?: Image;

  @Field(() => String, { nullable: false })
  sku: string;

  @Field(() => Number, { nullable: true })
  edd?: number;

  @Field(() => String, { nullable: true })
  productType?: string;

  @Field(() => Number, { nullable: true })
  itemDiscount?: number;

  @Field(() => Number, { nullable: false })
  finalItemPrice: number;

  @Field(() => Number, { nullable: true })
  originalPrice?: number;

  @Field(() => Boolean, { nullable: true })
  hasColorOptions?: boolean;
}

@ObjectType()
export class InteriorArchitectureDetails {
  @Field(() => String, {
    nullable: false,
    description: 'GST Number of the customer.',
  })
  id: string;

  @Field(() => String, {
    nullable: true,
    description: 'Company Name of the customer.',
  })
  name: string;

  @Field(() => String, {
    nullable: true,
    description: 'Company Name of the customer.',
  })
  source?: string;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Commissioned.',
  })
  commissioned?: boolean;
}

@ObjectType()
export class GSTDetails {
  @Field(() => String, {
    nullable: false,
    description: 'GST Number of the customer.',
  })
  gstNumber: string;

  @Field(() => String, {
    nullable: false,
    description: 'Company Name of the customer.',
  })
  companyName: string;
}

@ObjectType()
export class QuotationAddress {
  @Field(() => String, { nullable: true, description: 'Shopify address Id' })
  shopifyAddressId?: string;

  @Field(() => String, { nullable: false, description: 'Address Line 1' })
  line1: string;

  @Field(() => String, { nullable: true, description: 'Address Line 2' })
  line2?: string;

  @Field(() => String, { nullable: false, description: 'City' })
  city: string;

  @Field(() => String, { nullable: false, description: 'State' })
  state: string;

  @Field(() => String, { nullable: false, description: 'Country' })
  country: string;

  @Field(() => String, { nullable: false, description: 'Pin code' })
  pinCode: string;
}

@ObjectType()
export class AdditionalPromotionalCoupon {
  @Field(() => String, { description: 'Promotional Code' })
  promotionalCode: string;

  @Field(() => Number, { description: 'Promotional Discount Amount' })
  promotionalDiscountAmount: number;
}

@ObjectType()
export class QuotationData {
  @Field(() => String, {
    nullable: false,
    description: 'ID of the customer for the quotation.',
  })
  id: string;

  @Field(() => String, {
    nullable: true,
    description: 'Order ID of the customer for the quotation.',
  })
  orderId?: string;

  @Field(() => String, {
    nullable: false,
    description: 'ID of the customer for the quotation.',
  })
  customerId: string;

  @Field(() => [QuotationProduct], {
    nullable: false,
    description: 'List of products being quoted.',
  })
  quotationProducts: QuotationProduct[];

  @Field(() => String, {
    nullable: false,
    description: 'ID of the store issuing the quotation.',
  })
  storeId: string;

  @Field(() => String, {
    nullable: false,
    description: 'ID of the employee creating the quotation.',
  })
  employeeId: string;

  @Field(() => String, {
    nullable: true,
    description: 'Campaign code associated with the quotation.',
  })
  campaignCode?: string;

  @Field(() => CustomCode, {
    nullable: true,
    description: 'Custom code for the quotation.',
  })
  customCode?: CustomCode;

  @Field(() => String, {
    nullable: true,
    description: 'Promotional code for the quotation.',
  })
  promotionalCode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Notes or comments for the quotation.',
  })
  notes?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Source for the quotation.',
  })
  source?: string;

  @Field(() => CustomerMetadata, {
    nullable: true,
    description: 'Customer metadata for the quotation.',
  })
  customer?: CustomerMetadata;

  @Field(() => QuotationAddress, {
    nullable: true,
    description: 'Shipping Address for the quotation.',
  })
  shippingAddress?: QuotationAddress;

  @Field(() => QuotationAddress, {
    nullable: true,
    description: 'Billing Address for the quotation.',
  })
  billingAddress?: QuotationAddress;

  @Field(() => GSTDetails, { nullable: true })
  gstDetails?: GSTDetails;

  @Field(() => InteriorArchitectureDetails, {
    nullable: true,
    description: 'Interior Architecture',
  })
  interiorArchitecture?: InteriorArchitectureDetails;

  @Field(() => DeliveryChargeApprovalDetails, { nullable: true })
  deliveryChargeApprovalDetails?: DeliveryChargeApprovalDetails;

  @Field(() => Number, {
    nullable: true,
    description: 'delivery amount',
  })
  deliveryCharge?: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Total amount',
  })
  totalAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  finalDiscountedAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  promotionalDiscountAmount: number;

  @Field(() => [AdditionalPromotionalCoupon], {
    nullable: true,
    description: 'Additional Promotional Coupons',
  })
  additionalPromotionalCoupons?: AdditionalPromotionalCoupon[];

  @Field(() => CustomDiscountVerificationDetails, { nullable: true })
  customDiscountVerificationDetails?: CustomDiscountVerificationDetails;

  @Field(() => EmployeeDiscountDetails, { nullable: true, description: '' })
  employeeDiscountDetails?: EmployeeDiscountDetails;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  customDiscountAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  campaignDiscountAmount: number;

  @Field(() => [CartProduct], {
    nullable: true,
    description: 'Final Amount',
  })
  freeProducts?: CartProduct[];

  @Field(() => QuotationStatus, {
    nullable: false,
    description: 'Quotation Status',
  })
  status: QuotationStatus;

  @Field(() => String, { nullable: false, description: 'Created At' })
  createdAt: string;

  @Field(() => String, { nullable: false, description: 'Updated At' })
  updatedAt: string;

  @Field(() => String, { nullable: false, description: 'Expires At' })
  expiresAt: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer acknowledgement for the order.',
  })
  customerAcknowledgement?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer Identity proof for the order.',
  })
  customerIdentityProof?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Quotation Type',
  })
  type: string;

  @Field(() => Number, {
    nullable: true,
    description: 'Price lock booking amount',
  })
  bookingAmount: number;

  @Field(() => String, {
    nullable: true,
    description: 'Price lock booking amount status',
  })
  bookingAmountStatus: PaymentStatus;

  @Field(() => QuotationProduct, {
    nullable: true,
    description: 'BNPL Product data',
  })
  bnplProduct: QuotationProduct;

  @Field(() => String, {
    nullable: true,
    description: 'BNPL Super Order Id',
  })
  superOrderId?: string;
}

@ObjectType()
export class Quotation {
  @Field(() => QuotationData, { nullable: true, description: 'Quotation' })
  data: QuotationData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class Quotations {
  @Field(() => [QuotationData], { nullable: true, description: 'Quotations' })
  data: QuotationData[];

  @Field(() => Number, { nullable: true, description: 'Total Count' })
  count: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
