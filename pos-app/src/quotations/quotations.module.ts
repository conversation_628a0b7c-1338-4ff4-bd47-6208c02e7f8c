import { Module } from '@nestjs/common';
import { QuotationsService } from './quotations.service';
import { QuotationsResolver } from './quotations.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from 'src/common/response/errorHandler/error.handler';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { ShopifyModule } from 'src/common/shopify/shopify.module';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    S3ClientModule,
    ConfigParametersModule,
    ShopifyModule,
  ],
  providers: [
    QuotationsResolver,
    QuotationsService,
    SuccessHandler,
    <PERSON>rror<PERSON><PERSON><PERSON>,
  ],
})
export class QuotationsModule {}
