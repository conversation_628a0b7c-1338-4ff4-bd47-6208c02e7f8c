import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateQuotationInput } from './dto/create-quotation.input';
import { CreateQuotation } from './lib/create-quotation';
import { QueryQuotations } from './lib/list-quotations';
import { ListQuotationInput } from './dto/list-quotation.input';
import {
  EmployeeDetailsInput,
  DeliveryChargeApprovalInput,
  UpdateQuotationInput,
} from './dto/update-quotation.input';
import { UpdateQuotation } from './lib/update-quotation';
import { GetQuotation } from './lib/get-quotation';
import { GetOrderAndStoreAndSendPdf } from 'src/common/helper/get-order-and-store';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';
import { UpdateQuotationProductsInput } from './dto/update-quotation-products.input';
import { updateQuotationProducts } from './lib/update-quotation-products';
import { RequestEmployeeDiscountOTP } from './lib/request-employee-discount-otp';
import { VerificationEmployeeDiscountOTP } from './lib/verification-employee-discount-otp';
import { GetEmployeeDetails } from './lib/get-employee-details-by-phone';
import { DeliveryChargeApprovalService } from './lib/create-delivery-charge-otp-request';
import { PdfService } from '../common/ejs/nodemailer.service';
import moment from 'moment';
import { TemplateType } from '../common/enum/template-type';
import { ExportQuotations } from './lib/export-quotations';

@Injectable()
export class QuotationsService {
  private logger = new Logger(QuotationsService.name);
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}
  async create(createQuotationInput: CreateQuotationInput) {
    const createHandler = new CreateQuotation(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
      this.s3Client,
    );
    return createHandler.createQuotation(createQuotationInput);
  }

  async requestEmployeeDiscountOTP(
    quotationId: string,
    employeeDiscountDetails: EmployeeDetailsInput,
  ) {
    const requestHandler = new RequestEmployeeDiscountOTP(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return requestHandler.execute(quotationId, employeeDiscountDetails);
  }
  async verificationEmployeeDiscountOTP(quotationId: string, otp: string) {
    const requestHandler = new VerificationEmployeeDiscountOTP(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return requestHandler.execute(quotationId, otp);
  }

  async findAll(listQuotationInput: ListQuotationInput) {
    const queryHandler = new QueryQuotations(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return queryHandler.queryQuotations(listQuotationInput);
  }

  async findOne(id: string) {
    const getHandler = new GetQuotation(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.getQuotation(id);
  }
  async findOneEmployee(phone: string) {
    const getHandler = new GetEmployeeDetails(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.getEmployeeDetailsByPhone(phone);
  }

  async update(id: string, updateQuotationInput: UpdateQuotationInput) {
    const updateHandler = new UpdateQuotation(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
      this.shopifyClient,
    );
    return updateHandler.updateQuotation(id, updateQuotationInput);
  }
  async requestDeliveryChargeApproval(
    id: string,
    requestDeliveryChargeApproval: DeliveryChargeApprovalInput,
    resend: boolean,
  ) {
    const requestHandler = new DeliveryChargeApprovalService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return requestHandler.createApprovalRequest(
      id,
      requestDeliveryChargeApproval,
      resend,
    );
  }

  async verifyDeliveryChargeApproval(quotationId: string, otp: string) {
    const handler = new DeliveryChargeApprovalService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return handler.verifyApprovalRequest(quotationId, otp);
  }

  async sendQuotationPdf(id: string, type: string) {
    const updateHandler = new GetOrderAndStoreAndSendPdf(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );
    return updateHandler.getQuotationAndStoreAndSendPdf(id, type);
  }
  async updateProucts(
    updateQuotationProductsInput: UpdateQuotationProductsInput,
  ) {
    const createHandler = new updateQuotationProducts(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
      this.s3Client,
    );
    return createHandler.updateQuotationProducts(
      updateQuotationProductsInput?.id,
      updateQuotationProductsInput,
    );
  }

  async exportQuotations(
    email: string,
    listQuotationInput: ListQuotationInput,
  ) {
    const exportHandler = new ExportQuotations(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );
    const mailService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );
    exportHandler.exportQuotations(email, listQuotationInput).catch((error) => {
      const { fromDate = null, toDate = null } = listQuotationInput || {};
      mailService.sendEmailWithFileAttachment(
        email,
        `SmartServe POS Export | ${moment(fromDate).tz('Asia/Kolkata').format('DD/MM/YYYY')} to ${moment(toDate).tz('Asia/Kolkata').format('DD/MM/YYYY')}The Sleep Company`,
        `Please contact administrator for this data. There is an issue loading the data with to this date range`,
        'text/csv',
        TemplateType.REPORT,
        'csv',
        [],
      );
      this.logger.error(error);
    });
    return { message: 'File will be sent on mail' };
  }
}
