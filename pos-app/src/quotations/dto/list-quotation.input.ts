import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class QuotationTermSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
  })
  updatedAt?: string;

  @Field(() => String, {
    nullable: true,
  })
  expiresAt?: string;

  @Field(() => String, {
    nullable: true,
  })
  customerId?: string;
}

@InputType()
export class QuotationTextSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  id?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  storeId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  employeeId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  status?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Type of quotation',
  })
  type?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer Email from which user wants to retrieve the records',
  })
  'customeremail'?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer Phone from which user wants to retrieve the records',
  })
  'customerphone'?: string;
}

@InputType()
export class QuotationSortingFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class ListQuotationInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size: string;

  @Field(() => QuotationTermSearchFieldsInput, {
    nullable: true,
    description: 'Search Boolean fields to filter the records',
  })
  termSearchFields?: QuotationTermSearchFieldsInput;

  @Field(() => QuotationTextSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  textSearchFields?: QuotationTextSearchFieldsInput;

  @Field(() => QuotationSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { createdAt: 'desc' },
  })
  sortBy?: QuotationSortingFieldsInput;

  @Field(() => String, {
    nullable: true,
    description: 'Date from which user wants to retrieve the records',
  })
  fromDate?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Date until which user wants to retrieve the records',
  })
  toDate?: string;
}
