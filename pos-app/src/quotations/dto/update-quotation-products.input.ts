import { InputType, Field } from '@nestjs/graphql';
import {
  CartProductInput,
  CustomCodeInput,
} from 'src/carts/dto/create-cart.input';
import {
  AdditionalPromotionalCouponInput,
  CustomerMetadataInput,
} from './update-quotation.input';
import { ApproverDetailsInput } from 'src/custom-discount-verification/dto/create-custom-discount-verification.input';
import { CustomDiscountApprovalStatus } from 'src/custom-discount-verification/entities/custom-discount-verification.entity';
import { CouponType } from 'src/common/enum/coupons';
import { EmployeeDiscountStatus } from '../entities/quotation.entity';

@InputType()
export class CustomDiscountVerificationDetailsInput {
  @Field(() => String, {
    description: 'Quotation ID',
  })
  id: string;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountAmount: number;

  @Field(() => ApproverDetailsInput, {
    description: 'The mobile number of the customer requesting the discount.',
  })
  approverDetails: ApproverDetailsInput;

  @Field(() => CustomDiscountApprovalStatus, {
    description:
      'The status of the discount approval process (REQUESTED, REJECTED, APPROVED, APPLIED, EXPIRED).',
  })
  customDiscountApprovalStatus: CustomDiscountApprovalStatus;

  @Field(() => CouponType, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountType: CouponType;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountValue: number;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  additionalDiscountPercentage: number;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  cartTotalAmount: number;

  @Field(() => String, {
    description: 'The timestamp when the discount expired.',
  })
  discountExpiredTimestamp: string;
}
@InputType()
export class UpdateQuotationProductsInput {
  @Field(() => String, {
    nullable: false,
    description: 'ID of the quotation.',
  })
  id: string;

  @Field(() => [AdditionalPromotionalCouponInput], {
    nullable: true,
    description: 'Additional Promotional Coupons',
  })
  additionalPromotionalCoupons?: AdditionalPromotionalCouponInput[];

  @Field(() => EmployeeDiscountStatus, { nullable: true, description: '' })
  employeeDiscountStatus?: EmployeeDiscountStatus;

  @Field(() => String, {
    nullable: false,
    description: 'ID of the customer for the quotation.',
  })
  customerId: string;

  @Field(() => CustomerMetadataInput, {
    nullable: true,
    description: 'Customer metadata for the quotation.',
  })
  customer: CustomerMetadataInput;

  @Field(() => [CartProductInput], {
    nullable: true,
    description: 'List of products being quoted.',
  })
  quotationProducts?: CartProductInput[];

  @Field(() => String, {
    nullable: false,
    description: 'ID of the store issuing the quotation.',
  })
  storeId: string;

  @Field(() => String, {
    nullable: false,
    description: 'ID of the employee creating the quotation.',
  })
  employeeId: string;

  @Field(() => String, {
    nullable: true,
    description: 'Campaign code associated with the quotation.',
  })
  campaignCode?: string;

  @Field(() => CustomCodeInput, {
    nullable: true,
    description: 'Custom code for the quotation.',
  })
  customCode?: CustomCodeInput;

  @Field(() => String, {
    nullable: true,
    description: 'Promotional code for the quotation.',
  })
  promotionalCode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Notes or comments for the quotation.',
  })
  notes?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Source for the quotation.',
  })
  source?: string;

  @Field(() => Number, {
    nullable: true,
    description: 'delivery amount',
  })
  deliveryCharge?: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Total amount',
  })
  totalAmount: number;

  @Field(() => CustomDiscountVerificationDetailsInput, {
    nullable: true,
    description: 'Total amount',
  })
  customDiscountVerificationDetails?: CustomDiscountVerificationDetailsInput;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  finalDiscountedAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Promotional Discount Amount',
  })
  promotionalDiscountAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Custom Discount Amount',
  })
  customDiscountAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Campaign Discount Amount',
  })
  campaignDiscountAmount: number;

  @Field(() => [CartProductInput], {
    nullable: true,
    description: 'Free Products',
  })
  freeProducts?: CartProductInput[];

  @Field(() => String, { nullable: true })
  type?: string;

  @Field(() => String, { nullable: true })
  bookingAmountStatus?: string;

  @Field(() => CartProductInput, { nullable: true })
  bnplProduct?: CartProductInput;

  @Field(() => Number, { nullable: true })
  bookingAmount?: number;
}
