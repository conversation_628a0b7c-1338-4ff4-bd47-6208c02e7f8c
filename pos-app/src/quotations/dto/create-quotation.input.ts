import { InputType, Field } from '@nestjs/graphql';
import {
  CartProductInput,
  CustomCodeInput,
} from 'src/carts/dto/create-cart.input';
import { CustomerMetadataInput } from './update-quotation.input';
import { PaymentStatus } from 'src/common/enum/payment';

@InputType()
export class CreateQuotationInput {
  @Field(() => String, {
    nullable: false,
    description: 'Quotation Type',
  })
  type: string;

  @Field(() => String, {
    nullable: false,
    description: 'ID of the customer for the quotation.',
  })
  customerId: string;

  @Field(() => CustomerMetadataInput, {
    nullable: true,
    description: 'Customer metadata for the quotation.',
  })
  customer: CustomerMetadataInput;

  @Field(() => [CartProductInput], {
    nullable: true,
    description: 'List of products being quoted.',
  })
  quotationProducts?: CartProductInput[];

  @Field(() => String, {
    nullable: false,
    description: 'ID of the store issuing the quotation.',
  })
  storeId: string;

  @Field(() => String, {
    nullable: false,
    description: 'ID of the employee creating the quotation.',
  })
  employeeId: string;

  @Field(() => String, {
    nullable: true,
    description: 'Campaign code associated with the quotation.',
  })
  campaignCode?: string;

  @Field(() => CustomCodeInput, {
    nullable: true,
    description: 'Custom code for the quotation.',
  })
  customCode?: CustomCodeInput;

  @Field(() => String, {
    nullable: true,
    description: 'Promotional code for the quotation.',
  })
  promotionalCode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Notes or comments for the quotation.',
  })
  notes?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Source for the quotation.',
  })
  source?: string;

  @Field(() => Number, {
    nullable: true,
    description: 'Price lock booking amount',
  })
  bookingAmount: number;

  @Field(() => String, {
    nullable: true,
    description: 'Price lock booking amount status',
  })
  bookingAmountStatus: PaymentStatus;

  @Field(() => CartProductInput, {
    nullable: true,
    description: 'BNPL Product data',
  })
  bnplProduct: CartProductInput;

  @Field(() => Number, {
    nullable: false,
    description: 'Total amount',
  })
  totalAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  finalDiscountedAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Promotional Discount Amount',
  })
  promotionalDiscountAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Custom Discount Amount',
  })
  customDiscountAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Campaign Discount Amount',
  })
  campaignDiscountAmount: number;

  @Field(() => [CartProductInput], {
    nullable: true,
    description: 'Free Products',
  })
  freeProducts?: CartProductInput[];
}
