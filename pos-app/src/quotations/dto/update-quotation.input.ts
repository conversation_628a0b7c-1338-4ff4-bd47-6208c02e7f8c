import { InputType, Field } from '@nestjs/graphql';
import { DeliveryChargeApprovalStatus } from '../entities/quotation.entity';

@InputType()
export class CustomerMetadataInput {
  @Field(() => String, {
    nullable: true,
    description: 'First Name of the customer',
  })
  firstName?: string;

  @Field(() => String, {
    nullable: true,
    description: 'First Name of the customer',
  })
  lastName?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Phone number',
  })
  phone?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Email of the customer',
  })
  email?: string;

  @Field(() => String, {
    nullable: true,
    description: 'date of birth',
  })
  dob?: string;

  @Field(() => String, {
    nullable: true,
    description: 'anniversaryDate',
  })
  anniversaryDate?: string;

  @Field(() => String, {
    nullable: true,
    description: 'serviceLift',
  })
  serviceLift?: string;

  @Field(() => String, {
    nullable: true,
    description: 'accommodationType',
  })
  accommodationType?: string;

  @Field(() => String, {
    nullable: true,
    description: 'landmark',
  })
  landmark?: string;

  @Field(() => String, {
    nullable: true,
    description: 'latitude',
  })
  latitude?: string;

  @Field(() => String, {
    nullable: true,
    description: 'longitude',
  })
  longitude?: string;
}

@InputType()
export class DeliveryChargeApprovalInput {
  @Field()
  name: string;

  @Field()
  phone: string;

  @Field()
  requestedCharge: number;
}
@InputType()
export class UpdateDeliveryChargeApprovalInput {
  @Field()
  name: string;

  @Field()
  phone: string;

  @Field()
  requestedCharge: number;

  @Field(() => DeliveryChargeApprovalStatus)
  status: DeliveryChargeApprovalStatus;

  @Field({ nullable: true })
  otp?: string;

  @Field()
  expiresAt: string;

  @Field()
  createdAt: string;

  @Field()
  updatedAt: string;
}

@InputType()
export class GSTDetailsInput {
  @Field(() => String, {
    nullable: true,
    description: 'GST Number of the customer.',
  })
  gstNumber?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Company Name of the customer.',
  })
  companyName?: string;
}

@InputType()
export class AdditionalPromotionalCouponInput {
  @Field(() => String, { description: 'Promotional Code' })
  promotionalCode: string;

  @Field(() => Number, { description: 'Promotional Discount Amount' })
  promotionalDiscountAmount: number;
}

@InputType()
export class QuotationAddressInput {
  @Field(() => String, { nullable: true, description: 'Shopify address Id' })
  shopifyAddressId?: string;

  @Field(() => String, { nullable: false, description: 'Address Line 1' })
  line1: string;

  @Field(() => String, { nullable: true, description: 'Address Line 2' })
  line2?: string;

  @Field(() => String, { nullable: false, description: 'City' })
  city: string;

  @Field(() => String, { nullable: false, description: 'State' })
  state: string;

  @Field(() => String, { nullable: false, description: 'Country' })
  country: string;

  @Field(() => String, { nullable: false, description: 'Pin code' })
  pinCode: string;
}

@InputType()
export class InteriorArchitectureDetailsInput {
  @Field(() => String, {
    nullable: false,
    description: 'GST Number of the customer.',
  })
  id: string;

  @Field(() => String, {
    nullable: true,
    description: 'Company Name of the customer.',
  })
  name?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Company Name of the customer.',
  })
  source?: string;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Commissioned.',
  })
  commissioned?: boolean;
}

@InputType()
export class UpdateQuotationInput {
  @Field(() => String, {
    nullable: false,
    description: 'ID of the customer for the quotation.',
  })
  id: string;

  @Field(() => String, {
    nullable: true,
    description: 'ID of the employee creating the quotation.',
  })
  employeeId?: string;

  @Field(() => CustomerMetadataInput, {
    nullable: true,
    description: 'Customer metadata for the quotation.',
  })
  customer?: CustomerMetadataInput;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  finalDiscountedAmount: number;

  @Field(() => QuotationAddressInput, {
    nullable: true,
    description: 'Shipping Address for the quotation.',
  })
  shippingAddress?: QuotationAddressInput;

  @Field(() => QuotationAddressInput, {
    nullable: true,
    description: 'Billing Address for the quotation.',
  })
  billingAddress?: QuotationAddressInput;

  @Field(() => GSTDetailsInput, { nullable: true })
  gstDetails?: GSTDetailsInput;

  @Field(() => InteriorArchitectureDetailsInput, {
    nullable: true,
    description: 'Interior Architecture.',
  })
  interiorArchitecture?: InteriorArchitectureDetailsInput;

  @Field(() => String, {
    nullable: true,
    description: 'Notes or comments for the order.',
  })
  notes?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Source for the order.',
  })
  source?: string;

  @Field(() => Number, {
    nullable: true,
    description: 'delivery amount',
  })
  deliveryCharge?: number;

  @Field(() => String, {
    nullable: true,
    description: 'Customer acknowledgement for the order.',
  })
  customerAcknowledgement?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer Identity proof for the order.',
  })
  customerIdentityProof?: string;

  @Field(() => [AdditionalPromotionalCouponInput], {
    nullable: true,
    description: 'Additional Promotional Coupons',
  })
  additionalPromotionalCoupons?: AdditionalPromotionalCouponInput[];

  @Field(() => String, {
    nullable: true,
    description: 'Quotation Expire.',
  })
  expiresAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Quotation status',
  })
  status?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Quotation status',
  })
  bookingAmountStatus?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Order Id',
  })
  orderId?: string;

  @Field(() => Number, {
    nullable: true,
    description: 'Booking Amount Discount',
  })
  bookingAmountPaid?: number;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Check Shopify Inventory',
  })
  checkShopifyInventory?: boolean;
}
@InputType()
export class EmployeeDetailsInput {
  @Field(() => String, { description: "Employee's phone number" })
  phone: string;

  @Field(() => String, { description: "Employee's full name" })
  name: string;
}
