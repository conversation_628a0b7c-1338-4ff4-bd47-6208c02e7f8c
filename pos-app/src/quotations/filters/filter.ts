export const searchingFilter = new Map<
  string,
  'string' | 'nonString' | 'arrayString' | 'boolean' | 'date'
>([
  ['customerId', 'string'],
  ['storeId', 'string'],
  ['id', 'string'],
  ['employeeId', 'string'],
  ['status', 'string'],
  ['createdAt', 'date'],
  ['updatedAt', 'date'],
  ['expiresAt', 'string'],
  ['type', 'string'],
  ['customerphone', 'string'],
  ['customeremail', 'string'],
]);

export const sortingFilter = new Map<string, 'string' | 'nonString'>([
  ['createdAt', 'string'],
  ['updatedAt', 'string'],
]);

export const sortingFilterType = {
  createdAt: 'date',
  updatedAt: 'date',
};
