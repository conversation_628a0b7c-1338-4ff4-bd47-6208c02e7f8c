import { Module } from '@nestjs/common';
import { AiMattressService } from './ai-mattress-otp.service';
import { AiMattressResolver } from './ai-mattress-otp.resolver';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';

@Module({
  imports: [
    S3ClientModule,
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
  ],
  providers: [
    AiMattressResolver,
    AiMattressService,
    SuccessHandler,
    ErrorHandler,
  ],
})
export class AiMattressModule {}
