import { InputType, Field } from '@nestjs/graphql';
@InputType()
export class VerifyAiMattressOtpInput {
  @Field(() => String, { description: 'Phone number of the user' })
  phone: string;

  @Field(() => String, { description: 'OTP of the user' })
  otp: string;
}

@InputType()
export class SaveReportInput {
  @Field(() => String, { description: 'Name of the user' })
  name: string;

  // @Field(() => String, { description: 'Email of the user' })
  // email: string;

  @Field(() => String, { description: 'Phone number of the user' })
  phone: string;

  @Field(() => String, { description: 'Questions and answers of the user' })
  questions: string;

  @Field(() => String, { description: 'storeId' })
  storeId: string;

  @Field(() => String, { description: 'storeId' })
  storeState: string;

  @Field(() => String, { description: 'storeId' })
  storeCity: string;

  @Field(() => String, { description: 'storeId' })
  storePinCode: string;
}
