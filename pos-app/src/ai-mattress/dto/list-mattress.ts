import { Field, InputType } from '@nestjs/graphql';
import { QuotationSortingFieldsInput } from 'src/quotations/dto/list-quotation.input';

@InputType()
export class RecommendationTermSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
  })
  phone: string;
}

@InputType()
export class RecommendationTextSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
  })
  phone: string;
}

@InputType()
export class ListRecommendationInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size: string;

  @Field(() => RecommendationTermSearchFieldsInput, {
    nullable: true,
    description: 'Search Boolean fields to filter the records',
  })
  termSearchFields?: RecommendationTermSearchFieldsInput;

  @Field(() => RecommendationTextSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  textSearchFields?: RecommendationTextSearchFieldsInput;

  @Field(() => QuotationSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { createdAt: 'desc' },
  })
  sortBy?: QuotationSortingFieldsInput;
}
