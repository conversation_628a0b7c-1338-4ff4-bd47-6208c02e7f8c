import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class AiMattressOtp {
  @Field(() => String, { description: 'Unique id' })
  id: string;

  @Field(() => String, { description: 'Name of the user' })
  name: string;

  @Field(() => String, { description: 'Email of the user' })
  email: string;

  @Field(() => String, { description: 'Phone number of the user' })
  phone: string;

  @Field(() => String, { description: 'Otp of the user' })
  otp: string;

  @Field(() => String, { description: 'Otp of the user' })
  expires: string;
}

@ObjectType()
export class RecommendationResponse {
  @Field(() => String, { nullable: false })
  message: string;

  @Field(() => String, { nullable: false })
  data: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class Recommendations {
  @Field(() => String, { description: 'Unique id' })
  id: string;

  @Field(() => String, { description: 'Name of the user' })
  name: string;

  // @Field(() => String, { description: 'Email of the user' })
  // email: string;

  @Field(() => String, { description: 'Phone number of the user' })
  phone: string;

  @Field(() => String, { description: 'Questions and answers of the user' })
  questions: string;

  @Field(() => String, { description: 'Recommendation for the user' })
  recommendation: string;

  @Field(() => String, { description: 'Creation timestamp of the user' })
  createdAt: string;
}

@ObjectType()
export class ListRecommendationResponse {
  @Field(() => [Recommendations], { nullable: true })
  data: Recommendations[];

  @Field(() => Number, { nullable: true })
  count: number;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
