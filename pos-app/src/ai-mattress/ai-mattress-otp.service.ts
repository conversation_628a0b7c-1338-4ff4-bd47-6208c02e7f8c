import { Injectable } from '@nestjs/common';
import { CreateAiMattressOtpInput } from './dto/create-ai-mattress-otp.input';
import {
  SaveReportInput,
  VerifyAiMattressOtpInput,
} from './dto/verify-ai-mattress-otp.input';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { ConfigService } from '@nestjs/config';
import { SendOTP } from './lib/send-otp';
import { VerifyOTP } from './lib/verify-otp';
import { Recommender } from './lib/recommender';
// import { SaveReport } from './lib/save-report';
import { ListRecommendations } from './lib/list-recommendations';
import { ListRecommendationInput } from './dto/list-mattress';

@Injectable()
export class AiMattressService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async create(createAiMattressOtpInput: CreateAiMattressOtpInput) {
    const handler = new SendOTP(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return handler.create(createAiMattressOtpInput);
  }

  async validate(verifyAiMattressOtpInput: VerifyAiMattressOtpInput) {
    const handler = new VerifyOTP(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return handler.validate(verifyAiMattressOtpInput);
  }

  async recommendMattress(saveReportInput: SaveReportInput) {
    const handler = new Recommender(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return handler.recommend(saveReportInput);
  }

  // async saveReport(saveReportInput: SaveReportInput) {
  //   const handler = new SaveReport(
  //     this.configService,
  //     this.docClient,
  //     this.ssmClient,
  //     this.configParameters,
  //   );
  //   return handler.save(saveReportInput);
  // }

  async findAll(listRecommendationInput: ListRecommendationInput) {
    const handler = new ListRecommendations(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return handler.listAll(listRecommendationInput);
  }
}
