import { Test, TestingModule } from '@nestjs/testing';
import { AiMattressResolver } from './ai-mattress-otp.resolver';
import { AiMattressService } from './ai-mattress-otp.service';

describe('AiMattressOtpResolver', () => {
  let resolver: AiMattressResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AiMattressResolver, AiMattressService],
    }).compile();

    resolver = module.get<AiMattressResolver>(AiMattressResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
