import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { AiMattressService } from './ai-mattress-otp.service';
import {
  AiMattressOtp,
  ListRecommendationResponse,
  RecommendationResponse,
} from './entities/ai-mattress-otp.entity';
import { CreateAiMattressOtpInput } from './dto/create-ai-mattress-otp.input';
import {
  SaveReportInput,
  VerifyAiMattressOtpInput,
} from './dto/verify-ai-mattress-otp.input';
import { Response } from 'src/products/entities/product.entity';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CRMGuard } from 'src/auth/roles.guard';
import { ListRecommendationInput } from './dto/list-mattress';

@Resolver(() => AiMattressOtp)
export class AiMattressResolver {
  constructor(
    private readonly aiMattressService: AiMattressService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => Response, { name: 'createAiMattressOtp' })
  // @UseGuards(CustomAuthGuard, CRMGuard)
  async createAiMattressOtp(
    @Args('createAiMattressOtpInput')
    createAiMattressOtpInput: CreateAiMattressOtpInput,
  ) {
    return this.aiMattressService.create(createAiMattressOtpInput);
  }

  @Mutation(() => Response, { name: 'validateAiMattressOtp' })
  // @UseGuards(CustomAuthGuard, CRMGuard)
  async validateAiMattressOtp(
    @Args('verifyAiMattressOtpInput')
    verifyAiMattressOtpInput: VerifyAiMattressOtpInput,
  ) {
    return this.aiMattressService.validate(verifyAiMattressOtpInput);
  }

  @Mutation(() => RecommendationResponse, { name: 'recommendMattress' })
  // @UseGuards(CustomAuthGuard, CRMGuard)
  async recommendMattress(
    @Args('saveReportInput')
    saveReportInput: SaveReportInput,
  ) {
    return this.aiMattressService.recommendMattress(saveReportInput);
  }

  // @Mutation(() => Response, { name: 'saveReport' })
  // @UseGuards(CustomAuthGuard, CRMGuard)
  // async saveReport(
  //   @Args('saveReportInput')
  //   saveReportInput: SaveReportInput,
  // ) {
  //   return this.aiMattressService.saveReport(saveReportInput);
  // }

  @Query(() => ListRecommendationResponse, { name: 'listRecommendations' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async listRecommendations(
    @Args('listRecommendationsInput', { nullable: true })
    listRecommendationsInput: ListRecommendationInput,
  ) {
    try {
      const { data, count } = await this.aiMattressService.findAll(
        listRecommendationsInput,
      );
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list quotations',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
