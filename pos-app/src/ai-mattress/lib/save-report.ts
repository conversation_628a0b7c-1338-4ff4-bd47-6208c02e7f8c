import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { ConfigService } from '@nestjs/config';
import { SaveReportInput } from '../dto/verify-ai-mattress-otp.input';
import { v4 as uuid } from 'uuid';
import { PutCommand } from '@aws-sdk/lib-dynamodb';

export class SaveReport {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}
  async save(saveReportInput: SaveReportInput & { recommendation: string }) {
    try {
      const TABLE =
        await this.configParameters.getMattressRecommendationTableName();

      const id = uuid();

      const createCommand = new PutCommand({
        TableName: TABLE,
        Item: {
          pk: saveReportInput.phone,
          sk: id,
          id,
          ...saveReportInput,
          createdAt: new Date().toISOString(),
        },
        ConditionExpression: 'attribute_not_exists(id)',
      });

      await this.docClient.createItem(createCommand);

      return {
        message: 'Report saved successfully',
        status: 201,
        success: true,
      };
    } catch (error) {
      console.error('Error saving report:', error);
      throw error;
    }
  }
}
