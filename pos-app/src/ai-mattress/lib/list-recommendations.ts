import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { ConfigService } from '@nestjs/config';
import { ListRecommendationInput } from '../dto/list-mattress';
import { filterFormatter } from 'src/common/helper/filter-helper';
import { sortingFilter, sortingFilterType } from 'src/employees/filters/filter';
import { ElasticClient } from 'src/utils/elasticsearch.config';

export class ListRecommendations {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}
  async listAll(filter: ListRecommendationInput) {
    try {
      const TABLE =
        await this.configParameters.getMattressRecommendationTableName();

      const { searchArray, sortObject } = await filterFormatter(
        sortingFilter,
        new Map([['phone', 'string']]),
        sortingFilterType,
        filter,
      );

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: TABLE,
        });

        size = bodyRes?.count;
      }

      if (searchArray.length) {
        const { body: bodyRes } = await esHandler.count({
          index: TABLE,
          body: {
            query: {
              bool: {
                must: [...searchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data = response.body.hits.hits.map((hit) => hit._source);

        return { data, count: bodyRes?.count };
      }

      const response = await esHandler.search({
        index: TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data = response.body.hits.hits.map((hit) => hit._source);

      const { body: bodyRes } = await esHandler.count({
        index: TABLE,
      });

      return { data, count: bodyRes?.count };
    } catch (error) {
      console.error('Error saving recommendation:', error);
      throw error;
    }
  }
}
