import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { ConfigService } from '@nestjs/config';
import { SaveReportInput } from '../dto/verify-ai-mattress-otp.input';
import {
  questionAnswersMappings,
  questionAnswersPdfMappings,
} from 'src/utils/ai-mattress-recommender-data';
import { SaveReport } from './save-report';
import { sendSms } from 'src/common/helper/send-sms';

const weightQuestion =
  'What’s the body weight of the person who will sleep on the mattress?';

const answerMapping = {
  'Under 50 Kg': '50',
  '50 Kg to 75 Kg': '75',
  '75 Kg to 100 Kg': '100',
  'Over 100 Kg': '100A',
};

export class Recommender {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  extractProductNames(url: any) {
    const filename = url.split('/').pop().replace('.pdf', '');
    const productName = filename.replace(/^(50_|75_|100_|100A_)/, '');
    const products = productName.split('_');

    const formattedProducts = products.map((name) => {
      const fullName = name.replace(/([a-z])([A-Z])/g, '$1 $2');
      return `Smart ${fullName} Mattress`;
    });

    return formattedProducts;
  }

  async callLeadSquare(payload: any) {
    const { name, customerPhone, storeId, recommendations, questions } =
      payload;

    const requestBody = {
      user_name: name,
      phone: customerPhone,
      source: 'AI Mattress Recommender - POS',
      product_type: 'Mattresses',
      product_name: recommendations.join(', '),
      lead_status: 'Tier 3',
      store_id: storeId,
      activity_source: 'AI Mattress Recommender - POS',
      q2: questions[weightQuestion],
      p1: recommendations[0],
      p2: recommendations[1],
      p3: recommendations[2],
      is_web_engage_event: true,
      web_engage_data: {
        product_category: 'Mattresses',
        product_subcategory: recommendations.join(', '),
      },
    };

    Object.entries(questionAnswersMappings).map(([question, answers]) => {
      requestBody[answers.key] = questions[question];
    });

    console.log('requestBody api calling', JSON.stringify(requestBody));

    const leadResponse = await fetch(
      // 'https://api-in21.leadsquared.com/v2/ProspectActivity.svc/CreateCustom?accessKey=' +
      //   'u%24r3f8f6b578c93f2d348094733191527e8' +
      //   '&secretKey=' +
      //   '524000eef0227ce5479ef4383d98e07ed283923d',
      `https://apis.thesleepcompany.in/lead/datafeed`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      },
    );

    const responseBody = await leadResponse.text();
    console.log('Status Code:', leadResponse.status);
    console.log('Response Body:', responseBody);
  }

  async recommend(recommendMattressInput: SaveReportInput) {
    try {
      const { questions } = recommendMattressInput;
      const userAnswers = JSON.parse(questions);

      let pdfNameKey = '';
      Object.entries(questionAnswersMappings).map(([question, answers]) => {
        pdfNameKey += answers[userAnswers[question]];
      });

      const fileName = `${answerMapping[userAnswers[weightQuestion]]}_${questionAnswersPdfMappings[pdfNameKey]}`;
      const finalFilePath = `https://${this.configService.get('MEDIA_BUCKET')}.thesleepcompany.in/mattress-recommendations/${fileName}.pdf`;

      const handler = new SaveReport(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      await handler.save({
        ...recommendMattressInput,
        recommendation: finalFilePath,
      });

      await this.callLeadSquare({
        ...recommendMattressInput,
        customerPhone: recommendMattressInput.phone,
        recommendations: this.extractProductNames(finalFilePath),
        questions: userAnswers,
      });

      // try {
      //   const payload = {
      //     templateName: 'AiMattressRecommendationsCustomer',
      //     productName: finalFilePath,
      //     phoneNo: recommendMattressInput.phone,
      //   };
      //   const res = await sendSms(payload);
      //   console.log(res, '::::: res');
      // } catch (error) {
      //   console.error('Error sending recommendation to customer:', error);
      // }

      return {
        message: 'Recommendation generated successfully',
        data: finalFilePath,
        status: 200,
        success: true,
      };
    } catch (error) {
      console.error('Error creating and sending otp:', error);
      throw error;
    }
  }
}
