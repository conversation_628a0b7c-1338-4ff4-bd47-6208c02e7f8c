import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { ConfigService } from '@nestjs/config';
import { VerifyAiMattressOtpInput } from '../dto/verify-ai-mattress-otp.input';
import { DeleteCommand, GetCommand } from '@aws-sdk/lib-dynamodb';

export class VerifyOTP {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async deleteOtp(phone: string) {
    const tableName = await this.configParameters.getOTPTableName();
    const command = new DeleteCommand({
      TableName: tableName,
      Key: {
        pk: phone,
      },
    });
    await this.docClient.deleteItem(command);
  }

  async validate(verifyAiMattressOtpInput: VerifyAiMattressOtpInput) {
    try {
      const { phone, otp } = verifyAiMattressOtpInput;
      const tableName = await this.configParameters.getOTPTableName();
      const command = new GetCommand({
        TableName: tableName,
        Key: {
          pk: phone,
        },
      });
      const result = await this.docClient.getItem(command);
      const item = result.Item;
      if (!item) {
        return {
          message: 'OTP not found',
          status: 404,
          success: false,
        };
      }

      const expires = new Date(item.expires);
      if (expires < new Date()) {
        await this.deleteOtp(phone);
        return {
          message: 'OTP expired',
          status: 400,
          success: false,
        };
      }

      if (item.otp !== otp) {
        return {
          message: 'Invalid OTP',
          status: 400,
          success: false,
        };
      }
      await this.deleteOtp(phone);
      return {
        message: 'OTP verified successfully',
        status: 200,
        success: true,
      };
    } catch (error) {
      console.error('Error creating and sending otp:', error);
      throw error;
    }
  }
}
