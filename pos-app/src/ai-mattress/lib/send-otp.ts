import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { ConfigService } from '@nestjs/config';
import { CreateAiMattressOtpInput } from '../dto/create-ai-mattress-otp.input';
import { generateOtp } from 'src/common/helper/generate-otp';
import { v4 as uuid } from 'uuid';
import { PutCommand } from '@aws-sdk/lib-dynamodb';

export class SendOTP {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async create(createAiMattressOtpInput: CreateAiMattressOtpInput) {
    try {
      const { phone, name } = createAiMattressOtpInput;
      const otp = generateOtp();
      const payload = {
        templateName: 'NameaiMattressRecommender',
        OTP: otp,
        customerName: name,
        phoneNo: phone,
      };
      await this.sendOTP(payload);
      const id = uuid();
      const tableName = await this.configParameters.getOTPTableName();
      const item = {
        ...createAiMattressOtpInput,
        pk: phone,
        id,
        otp,
        expires: new Date(Date.now() + 3 * 60 * 1000).toISOString(),
      };

      const command = new PutCommand({
        TableName: tableName,
        Item: item,
      });

      await this.docClient.createItem(command);

      return {
        message: 'OTP sent successfully',
        status: 200,
        success: true,
      };
    } catch (error) {
      console.error('Error creating and sending otp:', error);
      return {
        message: `Fail to send otp : ${error.message}`,
        status: 500,
        success: false,
      };
    }
  }

  async sendOTP(payload: any) {
    const apiEndpoints = [
      {
        url: 'https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/send_sms',
        headers: {
          'Content-Type': 'application/json',
        },
        body: { template_attributes: payload },
      },
      // {
      //   url: 'https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/sendwhatsapp',
      //   headers: {
      //     'x-api-key': 'UBQJgJrPzW67r32ydSn9H8APs1VcZSkN3LKjoukp',
      //     'Content-Type': 'application/json',
      //   },
      //   body: payload,
      // },
    ];

    const requests = apiEndpoints.map((api) =>
      fetch(api.url, {
        method: 'POST',
        headers: api.headers,
        body: JSON.stringify(api.body),
      }).then((res) => res.json()),
    );

    return Promise.all(requests);
  }
}
