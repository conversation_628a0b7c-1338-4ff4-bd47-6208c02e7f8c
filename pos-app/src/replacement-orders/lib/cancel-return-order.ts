import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetReplacementOrder } from 'src/replacement-orders/lib/get-replacement-order';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import {
  CustomError,
  EnhancedError,
} from 'src/common/response/errorHandler/error.handler';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { OrderStatus } from 'src/common/enum/order';
import { RefundDetailsService } from 'src/refund-details/refund-details.service';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import moment from 'moment';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';

@Injectable()
export class CancelReturnService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
  ) {}

  async cancelReturn(
    id: string,
    shopifyOrderId: string,
  ): Promise<{
    success: boolean;
    message: string;
    data?: any;
    errorSource?: string;
  }> {
    posLogger.info('CancelReturnService', 'cancelReturn', {
      message: 'Starting return cancellation process',
      id,
      shopifyOrderId,
    });

    try {
      // 1. Get replacement order details
      const replacementOrder = await this.getReplacementOrderDetails(
        id,
        shopifyOrderId,
      );

      const {
        id: replacementOrderId,
        orderType,
        creditNoteId,
        requestId,
        locationKey,
        status = '',
      } = replacementOrder;

      if (!creditNoteId || !requestId) {
        throw new EnhancedError(
          'Cannot cancel return: No credit note ID or Request Id found in replacement order',
          400,
          'MISSING_DATA',
        );
      }

      // Validate refund status
      const refundService = new RefundDetailsService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.s3Client,
      );

      let refundDetail;

      try {
        refundDetail = await refundService.findOne(
          replacementOrderId,
          shopifyOrderId,
        );
      } catch (error) {
        posLogger.error('CancelReturnService', 'cancelReturn', {
          message: 'Error fetching refund detail',
          error: error.message || error,
        });

        // if refund detail not found its okay but if refund status
      }

      if (!!refundDetail && refundDetail?.status === 'COMPLETED') {
        throw new EnhancedError(
          'Cannot cancel return: Refund has already been completed',
          400,
          'REFUND_COMPLETED',
        );
      }

      // Get order from serverless OMS
      const omsOrder = await this.getOrderfromServerlessOms(shopifyOrderId);

      if (!omsOrder) {
        throw new EnhancedError(
          'Cannot cancel return/replacement: Order not found in system',
          404,
          'SERVERLESS_OMS',
        );
      }

      console.log('omsOrder', JSON.stringify(omsOrder));

      const { awbNumber, awbCancellationDate = '' } = this.getAWBNumber(
        omsOrder,
        requestId,
      );
      console.log('awbNumber', awbNumber);

      if (!!awbCancellationDate || status === OrderStatus.AWB_CANCELLED) {
        console.log('CancelReturnService', 'cancelReturn', {
          message: 'Return was already cancelled',
          id,
          shopifyOrderId,
          awbNumber,
          awbCancellationDate,
        });

        return {
          success: true,
          message: 'Return was already cancelled',
          data: { awbNumber },
        };
      }

      try {
        await this.cancelInitiatedReturnInEasyEcom(locationKey, creditNoteId);
      } catch (error) {
        throw new EnhancedError(
          `EasyEcom API failed: ${error.message}`,
          500,
          'EASYECOM_API',
        );
      }

      try {
        await this.cancelAWBServerlessOMS(shopifyOrderId, awbNumber, orderType);
      } catch (error) {
        throw new EnhancedError(
          `Serverless OMS update failed: ${error.message}`,
          500,
          'SERVERLESS_OMS',
        );
      }

      // Update replacement order status
      const updatePayloadForReplacementOrder = {
        status: OrderStatus.AWB_CANCELLED,
        updatedAt: moment().toISOString(),
      };

      await this.updateReplacementOrder(
        replacementOrderId,
        shopifyOrderId,
        updatePayloadForReplacementOrder,
      );

      posLogger.info('CancelReturnService', 'cancelReturn', {
        message: 'Successfully canceled return',
        id,
        shopifyOrderId,
        awbNumber,
      });

      return {
        success: true,
        message: 'Return cancelled successfully',
        data: { awbNumber },
      };
    } catch (error) {
      posLogger.error('CancelReturnService', 'cancelReturn', {
        message: 'Error cancelling return',
        error: error.message || error,
        source: error.source,
        id,
        shopifyOrderId,
      });

      return {
        success: false,
        message:
          error.message || 'An error occurred while cancelling the return',
        errorSource: error.source || 'INTERNAL_SERVER',
      };
    }
  }

  private async getReplacementOrderDetails(id: string, shopifyOrderId: string) {
    try {
      const getReplacementOrderHandler = new GetReplacementOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const replacementOrder =
        await getReplacementOrderHandler.getReplacementOrder(
          id,
          shopifyOrderId,
        );

      posLogger.info('CancelReturnService', 'getReplacementOrderDetails', {
        message: 'Successfully retrieved replacement order details',
        id,
        shopifyOrderId,
        creditNoteId: replacementOrder.creditNoteId,
        locationKey: replacementOrder.locationKey,
      });

      return replacementOrder;
    } catch (error) {
      posLogger.error('CancelReturnService', 'getReplacementOrderDetails', {
        message: 'Error retrieving replacement order details',
        error: error.message || error,
        id,
        shopifyOrderId,
      });

      throw new CustomError(
        `Failed to retrieve replacement order details: ${error.message}`,
        404,
      );
    }
  }

  private getAWBNumber(omsOrder: any, requestId: string) {
    const { productsData = [] } = omsOrder;
    const product = productsData.find(
      (product) => product.request_id === requestId,
    );

    if (!product) {
      throw new EnhancedError(
        `product not found for request id ${requestId}`,
        404,
      );
    }

    const { return_orders = [], replacement_orders = [] } = product;
    if (!return_orders.length && !replacement_orders.length) {
      throw new EnhancedError(
        `Return order not found for request id ${requestId}`,
        404,
      );
    }

    const returnOrder = return_orders.find(
      (returnOrder) => returnOrder.request_id === requestId,
    );

    const replacementOrder = replacement_orders.find(
      (replacementOrder) => replacementOrder.request_id === requestId,
    );

    if (!returnOrder && !replacementOrder) {
      throw new EnhancedError(
        `Return order or Replacement order not found for request id ${requestId}`,
        404,
      );
    }
    // const { awb_number } = returnOrder;

    const awb_number = returnOrder?.awb_number || replacementOrder?.awb_number;

    const awbCancellationDate =
      returnOrder?.awb_cancelation_date_and_time ||
      replacementOrder?.awb_cancelation_date_and_time ||
      '';

    if (!awb_number) {
      throw new EnhancedError(
        `AWB number not found for request id ${requestId}`,
        404,
      );
    }

    const pickedupRequestDate =
      returnOrder?.pickedup_request_date ||
      replacementOrder?.pickedup_request_date;

    if (!!pickedupRequestDate) {
      throw new EnhancedError(
        `Return order or Replacement order already picked up for request id ${requestId}`,
        400,
      );
    }

    return { awbNumber: awb_number, awbCancellationDate };
  }

  private async updateReplacementOrder(
    id: string,
    shopifyOrderId,
    updatePayloadForReplacementOrder: any,
  ) {
    const REPLACEMENT_ORDER_TABLE =
      await this.configParameters.getReplacementOrderTableName();

    const updateCommand = new UpdateCommand({
      TableName: REPLACEMENT_ORDER_TABLE,
      Key: {
        shopifyOrderId,
        id,
      },
      UpdateExpression: 'SET #status = :status, #updatedAt = :updatedAt',
      ExpressionAttributeNames: {
        '#status': 'status',
        '#updatedAt': 'updatedAt',
      },
      ExpressionAttributeValues: {
        ':status': updatePayloadForReplacementOrder.status,
        ':updatedAt': updatePayloadForReplacementOrder.updatedAt,
      },
    });

    return this.docClient.updateItem(updateCommand);
  }

  private async cancelAWBServerlessOMS(
    orderId: string,
    awbNo: string,
    requestType: string,
  ) {
    const {
      Parameter: { Value: baseURL },
    } = await this.ssmClient.getSSMParamByKey(
      new GetParameterCommand({
        Name: `/${this.configService.get('STACK_NAME')}/replacement/url`,
        WithDecryption: true,
      }),
    );
    const {
      Parameter: { Value: apiKey },
    } = await this.ssmClient.getSSMParamByKey(
      new GetParameterCommand({
        Name: `/${this.configService.get('STACK_NAME')}/replacement/apiKey`,
        WithDecryption: true,
      }),
    );
    const finalURL = `${baseURL}/pos/cancel_awb`;
    await fetch(finalURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
      },
      body: JSON.stringify({
        orderId,
        awbNo,
        requestType,
      }),
    });
  }

  private async getOrderfromServerlessOms(orderId: string) {
    try {
      const {
        Parameter: { Value: baseURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/url`,
          WithDecryption: true,
        }),
      );
      const {
        Parameter: { Value: apiKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/apiKey`,
          WithDecryption: true,
        }),
      );
      const finalURL = `${baseURL}/orderdetail?order_id=${orderId}&order_type=New Order`;
      console.log('finalURL', finalURL);
      console.log('apiKey', apiKey);
      const order = await this.getOrder(finalURL, apiKey);
      if (order && Object.keys(order).length) {
        const { orderDetails = [] } = order;
        return orderDetails?.[0];
      }

      return null;
    } catch (error) {
      posLogger.error(
        'CancelReturnService',
        'getOrderfromServerlessOms',
        error.message,
      );
      throw error;
    }
  }

  private async getOrder(url: string, apiKey: string) {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'x-api-key': apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch order: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      posLogger.error('CancelReturnService', 'getOrder', {
        message: 'Error fetching order data',
        error: error.message || error,
        url,
      });

      throw error;
    }
  }

  private async cancelInitiatedReturnInEasyEcom(
    locationKey: string,
    creditNoteId: string | number,
  ) {
    try {
      // Create EasyEcom service instance
      const easyEcomService = new EasyEcomService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      // Prepare payload
      const easyecomPayload = {
        creditNoteId: Number(creditNoteId),
      };

      // Call EasyEcom API to cancel the return
      const result = await easyEcomService.cancelInitiatedReturn(
        locationKey,
        easyecomPayload,
      );
      console.log('result from delete initiated return>>>', result);
      posLogger.info('CancelReturnService', 'cancelInitiatedReturnInEasyEcom', {
        message: 'Response from EasyEcom API',
        result,
        locationKey,
        creditNoteId,
      });

      // Check if the API call was successful
      if (result.error || result.code === 404) {
        throw new Error(
          result.message.includes('No data not found.')
            ? 'Return is already cancelled'
            : 'Failed to cancel return in EasyEcom',
        );
      }

      return result;
    } catch (error) {
      posLogger.error(
        'CancelReturnService',
        'cancelInitiatedReturnInEasyEcom',
        {
          message: 'Error cancelling return in EasyEcom',
          error: error.message || error,
          locationKey,
          creditNoteId,
        },
      );

      throw error;
    }
  }
}
