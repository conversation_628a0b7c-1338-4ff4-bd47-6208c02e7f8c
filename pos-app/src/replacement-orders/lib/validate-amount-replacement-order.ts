// modules
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { ReplacementOrderData } from '../entities/replacement-order.entity';
import { GetReplacementOrder } from './get-replacement-order';
import { ValidateReplacementOrderInput } from '../dto/validate-amount-replacement-order.input';
import {
  ReplaceableDiscountType,
  ReplacementType,
} from 'src/common/enum/replacement';
import { ValidateProducts } from 'src/common/helper/validate-products';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { CalculateCustomCodeDiscount } from 'src/coupons/lib/calculate-custom-discount';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { OrderType } from 'src/common/enum/order';
export class ValidateReplacementOrder {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validateReplacementOrder(
    validateAmountReplacementOrder: ValidateReplacementOrderInput,
  ): Promise<ReplacementOrderData> {
    posLogger.info('replacementOrder', 'validateReplacementOrder', {
      input: { validateAmountReplacementOrder },
    });
    try {
      const {
        id,
        shopifyOrderId,
        accessories,
        customCode,
        customDiscountAmount: cartCustomDiscountAmount,
        finalDiscountedAmount,
        totalAmount,
        replaceableDiscountType,
        customDiscountAmount,
        deliveryStatus,
        deliveryDate,
        minDeliveryDate,
        isShippingCharged,
        shippingAddress,
        replacementOption,
      } = validateAmountReplacementOrder;

      const REPLACEMENT_ORDER_TABLE =
        await this.configParameters.getReplacementOrderTableName();

      const getReplacementOrderHandler = new GetReplacementOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const replacementOrder: ReplacementOrderData =
        await getReplacementOrderHandler.getReplacementOrder(
          id,
          shopifyOrderId,
        );

      const {
        replacementType,
        replacedProduct,
        orderProducts,
        shippingCost,
        orderType,
      } = replacementOrder;

      // let { finalDiscountedAmount, totalAmount } = replacementOrder;
      let accessoriesData = null;

      if (replacementType === ReplacementType.FULL) {
        if (accessories?.length) {
          const handler = new ValidateProducts(
            this.configService,
            this.ssmClient,
            this.docClient,
            this.configParameters,
          );
          accessoriesData =
            (await handler.validateProducts(accessories))?.map(
              ({
                productId,
                variantId,
                quantity,
                price,
                product,
                variant,
                customData,
              }) => ({
                productId,
                variantId: variantId || null,
                quantity,
                price,
                finalItemPrice: price * quantity,
                customData: customData || null,
                title: product.title,
                variantTitle: variant?.variantTitle || null,
                image: variant?.image || product.image,
                productType: product?.product_type || null,
                sku: variant?.sku || null,
                edd:
                  variant?.metafields.find((m) => m.key == 'edd')?.value ||
                  null,
                storeId: 'CRM',
                createdAt: moment().toISOString(),
                updatedAt: moment().toISOString(),
              }),
            ) || null;

          // const accessoriesPrice = getTotalAmount(accessoriesData);

          // if (
          //   accessoriesData &&
          //   accessoriesData.length &&
          //   finalDiscountedAmount < 1 &&
          //   accessoriesPrice <= 2 * Math.abs(finalDiscountedAmount)
          // ) {
          //   totalAmount += accessoriesPrice;
          // } else {
          //   throw new CustomError('Invalid accessories', 400);
          // }
        }
      }
      console.log(
        'customCode is',
        customCode,
        cartCustomDiscountAmount,
        finalDiscountedAmount,
        orderType,
      );
      if (customCode) {
        const customCodeValidator = new CalculateCustomCodeDiscount(
          this.configService,
          this.ssmClient,
          this.docClient,
          this.configParameters,
        );
        const { discount: customDiscountAmount }: { discount: number } =
          await customCodeValidator.calculateCustomDiscount({
            code: customCode,
            cartTotal: isShippingCharged
              ? Number(totalAmount + shippingCost)
              : totalAmount,
            config: 'REPLACEMENT_CUSTOM_DISCOUNT',
            promotionalDiscountAmount: 0,
            campaignDiscountAmount: 0,
          });

        if (customDiscountAmount != cartCustomDiscountAmount) {
          posLogger.error(
            'replacementOrder',
            'validateReplacementOrder',
            'Cart custom discount amount does not match',
          );
          throw new CustomError(
            `Custom discount amount ${cartCustomDiscountAmount} does not match ${customDiscountAmount}`,
            400,
          );
        }

        const finalDiscountedAmount = Number(
          (isShippingCharged
            ? Number(totalAmount + shippingCost)
            : totalAmount - Number(customDiscountAmount.toFixed(2))
          ).toFixed(2),
        );

        console.log('finalDiscountedAmount', finalDiscountedAmount);
        if (finalDiscountedAmount < -1) {
          posLogger.error(
            'replacementOrder',
            'validateReplacementOrder',
            'Final discounted amount cannot be less than zero during Cart custom discount amount application',
          );
          throw new CustomError(
            'Final discounted amount cannot be less than zero during Cart custom discount amount application',
            400,
          );
        }

        console.log('finalDiscountedAmount 2:>> ', finalDiscountedAmount);
      } else {
        if (
          cartCustomDiscountAmount &&
          cartCustomDiscountAmount != 0 &&
          replaceableDiscountType != ReplaceableDiscountType.WAIVE_OFF
        ) {
          posLogger.error(
            'replacementOrder',
            'validateReplacementOrder',
            'Cart custom discount amount does not match',
          );
          throw new CustomError(
            `Cart custom discount amount does not match`,
            400,
          );
        }
      }

      let refundedPrice = 0;

      if (
        (finalDiscountedAmount < 0 || orderType == OrderType.RETURN) &&
        replacementType == ReplacementType.FULL
      ) {
        const effectivePrice = Number(
          (
            replacedProduct.finalItemPrice -
            replacedProduct.itemDiscount -
            replacedProduct.bankDiscount
          ).toFixed(2),
        );
        const totalPurchasedPrice = [
          ...(orderProducts || []),
          ...(accessoriesData || []),
        ].reduce((acc, curr) => {
          return (acc += curr.finalItemPrice);
        }, 0);
        console.log('effectivePrice', effectivePrice, totalPurchasedPrice);
        if (effectivePrice > totalPurchasedPrice) {
          const refund = effectivePrice - totalPurchasedPrice;
          const refundWithShipping = isShippingCharged
            ? refund - shippingCost
            : refund;
          refundedPrice = refundWithShipping > 0 ? refundWithShipping : 0;
        }
      }
      console.log('now will update', finalDiscountedAmount, refundedPrice);
      if (orderType == OrderType.REPLACEMENT) {
        const command = new UpdateCommand({
          TableName: REPLACEMENT_ORDER_TABLE,
          Key: {
            shopifyOrderId,
            id,
          },
          UpdateExpression:
            'SET #replacementOption = :replacementOption, #replaceableDiscountType = :replaceableDiscountType, #customCode = :customCode, #customDiscountAmount = :customDiscountAmount, #accessories = :accessories, deliveryStatus = :deliveryStatus, deliveryDate = :deliveryDate, minDeliveryDate = :minDeliveryDate, #totalAmount = :totalAmount, isShippingCharged = :isShippingCharged, #finalDiscountedAmount = :finalDiscountedAmount, #shippingAddress = :shippingAddress, refundedPrice = :refundedPrice, updatedAt = :updatedAt',
          ExpressionAttributeNames: {
            '#replaceableDiscountType': 'replaceableDiscountType',
            '#customDiscountAmount': 'customDiscountAmount',
            '#accessories': 'accessories',
            '#totalAmount': 'totalAmount',
            '#finalDiscountedAmount': 'finalDiscountedAmount',
            '#customCode': 'customCode',
            '#shippingAddress': 'shippingAddress',
            '#replacementOption': 'replacementOption',
          },
          ExpressionAttributeValues: {
            ':replaceableDiscountType': replaceableDiscountType,
            ':customCode': customCode,
            ':customDiscountAmount': customDiscountAmount,
            ':accessories': accessoriesData,
            ':deliveryStatus': deliveryStatus,
            ':deliveryDate': deliveryDate,
            ':minDeliveryDate': minDeliveryDate,
            ':totalAmount': totalAmount,
            ':isShippingCharged': isShippingCharged,
            ':finalDiscountedAmount': isShippingCharged
              ? Number(finalDiscountedAmount.toFixed(2)) + Number(shippingCost)
              : Number(finalDiscountedAmount.toFixed(2)) < 1 &&
                  Number(finalDiscountedAmount.toFixed(2)) > -1
                ? 0
                : Number(finalDiscountedAmount.toFixed(2)),
            ':shippingAddress': shippingAddress,
            ':replacementOption': replacementOption,
            ':updatedAt': moment().toISOString(),
            ':refundedPrice': refundedPrice,
          },
          ConditionExpression:
            'attribute_exists(shopifyOrderId) AND attribute_exists(id)',
          ReturnValues: 'ALL_NEW',
        });
        const { Attributes }: { Attributes: ReplacementOrderData } =
          await this.docClient.updateItem(command);

        return Attributes;
      } else {
        const command = new UpdateCommand({
          TableName: REPLACEMENT_ORDER_TABLE,
          Key: {
            shopifyOrderId,
            id,
          },
          UpdateExpression:
            'SET finalDiscountedAmount = :finalDiscountedAmount , totalAmount = :totalAmount,  isShippingCharged = :isShippingCharged, #shippingAddress = :shippingAddress, refundedPrice = :refundedPrice, updatedAt = :updatedAt',
          ExpressionAttributeNames: {
            '#shippingAddress': 'shippingAddress',
          },
          ExpressionAttributeValues: {
            ':isShippingCharged': isShippingCharged,
            ':shippingAddress': shippingAddress,
            ':updatedAt': moment().toISOString(),
            ':refundedPrice': refundedPrice,
            ':totalAmount': totalAmount,
            ':finalDiscountedAmount': isShippingCharged
              ? finalDiscountedAmount + Number(shippingCost)
              : finalDiscountedAmount,
          },
          ConditionExpression:
            'attribute_exists(shopifyOrderId) AND attribute_exists(id)',
          ReturnValues: 'ALL_NEW',
        });
        const { Attributes }: { Attributes: ReplacementOrderData } =
          await this.docClient.updateItem(command);

        return Attributes;
      }
    } catch (e) {
      posLogger.error('replacementOrder', 'validateReplacementOrder', {
        error: e,
      });
      throw e;
    }
  }
}
