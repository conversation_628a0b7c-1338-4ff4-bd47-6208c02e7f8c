import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';
import { ReplacementOrderData } from '../entities/replacement-order.entity';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { UpdateRefundPriceInput } from '../dto/update-refund-price.input';

export class UpdateRefundPrice {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateRefundPrice({
    id,
    shopifyOrderId,
    refundedPrice,
    updatedRefundPrice,
  }: UpdateRefundPriceInput): Promise<ReplacementOrderData> {
    posLogger.info('replacementOrder', 'updateRefundPrice', {
      input: {
        id,
        shopifyOrderId,
        refundedPrice,
        updatedRefundPrice,
      },
    });

    try {
      const REPLACEMENT_ORDER_TABLE =
        await this.configParameters.getReplacementOrderTableName();

      const command = new UpdateCommand({
        TableName: REPLACEMENT_ORDER_TABLE,
        Key: {
          shopifyOrderId,
          id,
        },
        UpdateExpression:
          'SET refundedPrice = :refundedPrice, finalDiscountedAmount = :finalDiscountedAmount , updatedRefundPrice = :updatedRefundPrice, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
          ':refundedPrice': refundedPrice,
          ':finalDiscountedAmount': -Number(refundedPrice),
          ':updatedRefundPrice': updatedRefundPrice,
          ':updatedAt': moment().toISOString(),
        },
        ConditionExpression:
          'attribute_exists(shopifyOrderId) AND attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });
      const { Attributes }: { Attributes: ReplacementOrderData } =
        await this.docClient.updateItem(command);

      if (Attributes) return Attributes;
    } catch (e) {
      posLogger.error('replacementOrder', 'updateRefundPrice', {
        error: e,
      });
      throw e;
    }
  }
}
