// import { ConfigService } from '@nestjs/config';
// import { AppDocumentClient } from 'src/common/document-client/document-client';
// import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
// import { AppConfigParameters } from 'src/config/config';
// import { AppSqsClient } from 'src/common/sqs-client/sqs-client';
// import { ExportReplacementOrdersInput } from '../dto/export-post-order';

// export class ExportReplacementOrders {
//   constructor(
//     private configService: ConfigService,
//     private docClient: AppDocumentClient,
//     private ssmClient: AppSsmClient,
//     private configParameters: AppConfigParameters,
//   ) {}

//   async exportReplacementOrders(
//     email: string,
//     listReplacementInput: ExportReplacementOrdersInput,
//   ): Promise<string> {
//     try {
//       // Initialize SQS client when needed
//       const sqsClient = new AppSqsClient(this.configService);

//       // Prepare the notification input for SQS
//       const notificationInput = {
//         type: 'EXPORT_REPLACEMENT_ORDERS',
//         email,
//         filters: {
//           requestType: listReplacementInput?.requestType,
//           startDate: listReplacementInput?.startDate,
//           endDate: listReplacementInput?.endDate,
//           replacementType: listReplacementInput?.replacementType,
//         },
//         timestamp: new Date().toISOString(),
//       };

//       // Get SQS queue URL from configuration
//       // Adjust this based on your configuration structure

//       //TODO: BRING QUEUE URL FROM SSM PARAMETER.
//       const params = {
//         QueueUrl:
//           'https://sqs.ap-south-1.amazonaws.com/307941960934/pos-prod-export-data-queue',
//         MessageBody: JSON.stringify(notificationInput),
//         DelaySeconds: 0,
//       };

//       const result = await sqsClient.sendMessage(params);
//       console.log('result OF EXPORTING POST ORDER', result);
//       return `Export request has been queued successfully. You will receive the exported data at ${email} once processing is complete.`;
//     } catch (error) {
//       console.error('Error sending export request to SQS:', error);
//       throw new Error(`Failed to queue export request: ${error.message}`);
//     }
//   }
// }

import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { ExportReplacementOrdersInput } from '../dto/export-post-order';
import * as AWS from 'aws-sdk';
import { AppAwsSdkCredentials } from 'src/common/aws-sdk-credentials';

export class ExportReplacementOrders {
  private ecsClient: AWS.ECS;

  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {
    const credentials = new AppAwsSdkCredentials(this.configService);
    this.ecsClient = new AWS.ECS({
      region: this.configService.get('AWS_REGION', 'ap-south-1'),
      credentials: credentials.getCredentials(),
    });
  }

  async exportReplacementOrders(
    email: string,
    listReplacementInput: ExportReplacementOrdersInput,
  ): Promise<string> {
    try {
      const exportConfig = {
        exportType: 'REPLACEMENT_ORDERS',
        email,
        filters: {
          requestType: listReplacementInput?.requestType,
          startDate: listReplacementInput?.startDate,
          endDate: listReplacementInput?.endDate,
          replacementType: listReplacementInput?.replacementType,
        },
        timestamp: new Date().toISOString(),
      };

      //TODO: BRING THESE VALUES FROM SSM

      // Get these values from your CloudFormation stack outputs or config
      const clusterName =
        'pos-prod-ExportECSResources-19G7K6KOPMPYY-export-cluster';
      const taskDefinition =
        'pos-prod-ExportECSResources-19G7K6KOPMPYY-processor';

      // Get these from your actual subnet/security group IDs
      const subnets = [
        'subnet-0e7f7c675c81e6e14',
        'subnet-0a526df13f96880a9',
        'subnet-026f0537da9c95f36',
      ]; // Your actual subnet IDs
      const securityGroups = ['sg-054f9f575daf0ebe6']; // Your actual security group ID

      const runTaskParams: AWS.ECS.RunTaskRequest = {
        cluster: clusterName,
        taskDefinition: taskDefinition,
        launchType: 'FARGATE',
        networkConfiguration: {
          awsvpcConfiguration: {
            subnets: subnets,
            securityGroups: securityGroups,
            assignPublicIp: 'ENABLED',
          },
        },
        overrides: {
          containerOverrides: [
            {
              name: 'export-processor', // Container name from task definition
              environment: [
                {
                  name: 'EXPORT_CONFIG',
                  value: JSON.stringify(exportConfig),
                },
                {
                  name: 'EXECUTION_MODE',
                  value: 'ON_DEMAND',
                },
              ],
            },
          ],
        },
        tags: [
          {
            key: 'ExportType',
            value: 'ReplacementOrders',
          },
          {
            key: 'RequestedBy',
            value: email,
          },
        ],
      };

      const result = await this.ecsClient.runTask(runTaskParams).promise();

      if (!result.tasks || result.tasks.length === 0) {
        throw new Error('Failed to start ECS export task');
      }

      const taskArn = result.tasks[0].taskArn;
      console.log('ECS Export Task started successfully:', taskArn);

      return `Export request started successfully. Task ARN: ${taskArn}. You will receive the exported data at ${email} once processing is complete.`;
    } catch (error) {
      console.error('Error starting ECS export task:', error);
      throw new Error(`Failed to start export task: ${error.message}`);
    }
  }
}
