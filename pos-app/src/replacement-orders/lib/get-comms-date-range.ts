import moment from 'moment-timezone';

// Utility: Get day with suffix (1st, 2nd, 3rd, etc.)
const formatDayWithSuffix = (day: number): string => {
  const suffix =
    day % 10 === 1 && day !== 11
      ? 'st'
      : day % 10 === 2 && day !== 12
        ? 'nd'
        : day % 10 === 3 && day !== 13
          ? 'rd'
          : 'th';
  return `${day}${suffix}`;
};

// Common formatter using moment
const formatDateRange = (start: moment.Moment, end: moment.Moment): string => {
  const startDay = start.date();
  const endDay = end.date();

  const sameMonth = start.month() === end.month() && start.year() === end.year();
  const sameYear = start.year() === end.year();

  if (sameMonth) {
    // Same month and year: "26 June - 29th June"
    return `${startDay} ${start.format('MMMM')} - ${formatDayWithSuffix(endDay)} ${end.format('MMMM')}`;
  } else if (sameYear) {
    // Same year, different months: "26 June - 2nd July"
    return `${startDay} ${start.format('MMMM')} - ${formatDayWithSuffix(endDay)} ${end.format('MMMM')}`;
  } else {
    // Different years: "26 June 2025 - 2nd January 2026"
    return `${startDay} ${start.format('MMMM YYYY')} - ${formatDayWithSuffix(endDay)} ${end.format('MMMM YYYY')}`;
  }
};

// Function for offset days (IST timezone)
export const getHumanReadableDateRange = (
  startOffsetDays: number,
  endOffsetDays: number,
): string => {
  const nowIST = moment().tz('Asia/Kolkata');

  const start = nowIST.clone().add(startOffsetDays, 'days');
  const end = nowIST.clone().add(endOffsetDays, 'days');

  return formatDateRange(start, end);
};

// Function for exact string dates
export const getHumanReadableDateRangeFromDates = (
  startDate: string,
  endDate: string,
): string => {
  const start = moment(startDate, 'YYYY-MM-DD');
  const end = moment(endDate, 'YYYY-MM-DD');

  return formatDateRange(start, end);
};
