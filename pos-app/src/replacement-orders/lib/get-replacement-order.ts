// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { ReplacementOrderData } from '../entities/replacement-order.entity';
import { listMetafields } from 'src/cron/helper/lib';
import { AppShopify } from 'src/common/shopify/shopify';
import { ReplacementType } from 'src/common/enum/replacement';
import { OrderType } from 'src/common/enum/order';
export class GetReplacementOrder {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getReplacementOrder(
    id: string,
    shopifyOrderId: string,
  ): Promise<ReplacementOrderData> {
    posLogger.info('replacementOrder', 'getReplacementOrder', {
      input: { shopifyOrderId, id },
    });
    try {
      const REPLACEMENT_ORDER_TABLE =
        await this.configParameters.getReplacementOrderTableName();

      const replacementOrderCommand = new GetCommand({
        TableName: REPLACEMENT_ORDER_TABLE,
        Key: {
          shopifyOrderId,
          id: id,
        },
      });

      const { Item: data }: { Item: ReplacementOrderData } =
        await this.docClient.getItem(replacementOrderCommand);

      if (!data) {
        throw new CustomError(`Replacement Order ${id} not found`, 404);
      }

      if (
        data.orderType === OrderType.REPLACEMENT &&
        data.replacementType === ReplacementType.PART
      ) {
        const partProductsIds = await this.getReplacablePartProducts(
          data.replacedProduct.productId,
          data.replacedProduct.variantId,
        );

        data.partProductsIds = [...new Set(partProductsIds as string[])];
      }
    
      return data;
    } catch (e) {
      posLogger.error('replacementOrder', 'getReplacementOrder', {
        error: e,
      });
      throw e;
    }
  }

  // async getReplacablePartProducts(productId, variantId) {
  //   const shopifyClient = new AppShopify(this.configService, this.ssmClient);

  //   // get metafield of this variant  ( function  )
  //   const [SHOPIFY_ADMIN_BASE_URL, SHOPIFY_ACCESS_TOKEN] = await Promise.all([
  //     await shopifyClient.getShopifyAdminBaseUrl(),
  //     await shopifyClient.getShopifyAccessToken(),
  //   ]);

  //   const metafields = await listMetafields(
  //     `${SHOPIFY_ADMIN_BASE_URL}/products/${productId}/metafields.json`,
  //     SHOPIFY_ACCESS_TOKEN,
  //   );

  //   const partProducts = JSON.parse(
  //     metafields.find((item: any) => item.key === 'replacement_part_product_id')
  //       ?.value || null,
  //   );

  //   const filteredProducts = partProducts?.[variantId] || [];

  //   return filteredProducts;
  // }

async getReplacablePartProducts(productId, variantId) {
  const shopifyClient = new AppShopify(this.configService, this.ssmClient);

  // Get Shopify credentials
  const [SHOPIFY_ADMIN_BASE_URL, SHOPIFY_ACCESS_TOKEN] = await Promise.all([
    await shopifyClient.getShopifyAdminBaseUrl(),
    await shopifyClient.getShopifyAccessToken(),
  ]);

  console.log("SHOPIFY_ADMIN_BASE_URL", SHOPIFY_ADMIN_BASE_URL);

  // Extract the base URL without any path
  const baseUrl = SHOPIFY_ADMIN_BASE_URL.split('/admin')[0];
  
  // Use the 2024-01 API version that's shown in your logs
  const graphqlEndpoint = `${baseUrl}/admin/api/2024-01/graphql.json`;
  
  // Format product ID if not already in gid format
  const formattedProductId = productId.includes('gid://') 
    ? productId 
    : `gid://shopify/Product/${productId}`;
  
  // Using exactly the query you provided
  const query = `
    query ProductMetafield($namespace: String!, $key: String!, $ownerId: ID!) {
      product(id: $ownerId) {
        metafield(namespace: $namespace, key: $key) {
          value
        }
      }
    }
  `;

  try {
    // Make GraphQL request with exactly the variables structure you provided
    const response = await fetch(graphqlEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN
      },
      body: JSON.stringify({
        query: query,
        variables: {
          namespace: "custom", // Namespace for replacement parts
          key: "replacement_part_product_id", // Key for replacement part product IDs
          ownerId: formattedProductId
        }
      }),
    });

    // Improved error handling
    if (!response.ok) {
      const errorText = await response.text();
      const statusCode = response.status;
      console.error(`Shopify API Error: ${statusCode}`, errorText);
      return {
        data: null,
        message: `HTTP error! Status: ${statusCode}, Response: ${errorText || 'No response body'}`,
        status: statusCode,
        success: false
      };
    }

    const result = await response.json();
    console.log("GraphQL response:", JSON.stringify(result, null, 2));
    
    if (result.errors) {
      console.error('GraphQL errors:', result.errors);
      return {
        data: null,
        message: `GraphQL errors: ${JSON.stringify(result.errors)}`,
        status: 500,
        success: false
      };
    }

    if (!result.data || !result.data.product) {
      console.error('Product not found:', result);
      return []; // Return empty array for missing product
    }

    // Access the metafield value directly using the structure from your query
    const metafield = result.data.product.metafield;
    
    if (!metafield || !metafield.value) {
      console.log('No replacement_part_product_id metafield found');
      return [];
    }

    // Parse the metafield value with error handling
    let partProducts = null;
    try {
      partProducts = JSON.parse(metafield.value || 'null');
    } catch (parseError) {
      console.error('Failed to parse metafield value:', parseError);
      return [];
    }
    
    if (!partProducts) {
      return [];
    }
    
    // Verify partProducts[variantId] is iterable before returning
    if (!partProducts[variantId] || !Array.isArray(partProducts[variantId])) {
      console.log(`No replacement parts found for variant ${variantId} or not an array`);
      return [];
    }
    
    // Get products for the specific variant
    const filteredProducts = partProducts[variantId] || [];

    return filteredProducts;
  } catch (error) {
    console.error('Failed to fetch product metafields:', error);
    // Return a structured error response instead of throwing
    return {
      data: null,
      message: error.message,
      status: 500,
      success: false
    };
  }
}
}
