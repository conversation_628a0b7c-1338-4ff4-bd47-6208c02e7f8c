import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { WhatsAppCommunicationService } from './replacement-comms';
import { posLogger } from 'src/common/logger';
import { getHumanReadableDateRange, getHumanReadableDateRangeFromDates } from './get-comms-date-range';

@Injectable()
export class RequestApprovalService {
  private apiUrl: string;
  private apiKey: string;
  private isInitialized = false;

  constructor(
    private configService: ConfigService,
    private readonly ssmClient: AppSsmClient,
  ) {}

  private async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      const [apiUrlParam, apiKeyParam] = await Promise.all([
        this.ssmClient.getSSMParamByKey(
          new GetParameterCommand({
            Name: `/${this.configService.get('STACK_NAME')}/request-module/api-url`,
            WithDecryption: true,
          }),
        ),
        this.ssmClient.getSSMParamByKey(
          new GetParameterCommand({
            Name: `/${this.configService.get('STACK_NAME')}/request-module/api-key`,
            WithDecryption: true,
          }),
        ),
      ]);

      //TODO: UNCOMMENT THIS BEFORE PROD OR STAGE DEPLOYMENT
      this.apiUrl = apiUrlParam.Parameter.Value;

      // this.apiUrl = 'http://localhost:3001/request-return-replacement'
      this.apiKey = apiKeyParam.Parameter.Value;
      this.isInitialized = true;

      console.log('API URL initialized:', this.apiUrl);
    } catch (error) {
      console.error('Error initializing RequestApprovalService:', error);
      throw error;
    }
  }

  async approveRequest(
    requestId?: string,
    requestType?: string,
    posId?: string,
    replacementType?: string,
    pickupPriority?: string,
    productName?: string,
    carrier?: string,
    replacementOrderId?: string,
    newMinDate?: string,
    newMaxDate?: string,
  ) {
    try {
      console.log('in here to approve request FOR NEW EDD , for edd ',newMinDate, newMaxDate);
      // Initialize if needed
      await this.initialize();

      let payload = {
        requestStatus: 'APPROVED',
        finalRequestType: requestType,
      } as any;

      if (!!posId) {
        payload.posId = posId;
      }

      const response = await fetch(`${this.apiUrl}/${requestId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to approve request: ${response.statusText}`);
      }

      const result = await response.json();

      const { data = {} } = result;
      console.log('data', data);
      const {
        customerName = '',
        customerPhone = '',
        orderId,
        requestType: originalRequestType,
      } = data;

      if (!requestType || requestType === '') {
        requestType = originalRequestType;
      }
      try {
        const commsPayload = {
          orderId,
          customerName,
          customerPhone,
          requestType,
          replacementType,
          pickupPriority,
          productName,
          carrier,
          replacementOrderId,
          newMinDate,
          newMaxDate,
        };
        console.log('commsPayload', commsPayload);

        //DISABLING COMMS ON PROD UNTIL FIXED.
        await this.triggerComms(commsPayload);
      } catch (err) {
        posLogger.error('RequestApprovalService', 'triggerComms', {
          error: err.message || err,
        });
      }

      return result;
    } catch (error) {
      console.log('Error in approving request:', error);
      throw error;
    }
  }

  async getRequestDetailsByOrderName(requestId: string, requestType: string) {
    try {
      // Initialize if needed
      await this.initialize();

      const response = await fetch(`${this.apiUrl}/${requestId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(
          `Failed to get request details: ${response.statusText}`,
        );
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.log('Error in getting request details:', error);
      throw error;
    }
  }

  async updateRequestWithRefundId(requestId: string, refundId: string) {
    try {
      console.log('in here to update request with refund id');
      // Initialize if needed
      await this.initialize();
      console.log('in here to update request with refund id 2');
      const response = await fetch(`${this.apiUrl}/${requestId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
        },
        body: JSON.stringify({
          refundId,
        }),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to update request with refund ID: ${response.statusText}`,
        );
      }
      console.log('in here to update request with refund id 3');
      const result = await response.json();
      return result;
    } catch (error) {
      console.log('Error in updating request with refund ID:', error);
      throw error;
    }
  }

  async triggerComms(data: any) {
    const {
      customerName,
      customerPhone,
      requestType,
      pickupPriority = '',
      productName = '',
      orderId,
      carrier = '',
      replacementOrderId,
      newMinDate,
      newMaxDate,
    } = data;

    const commsHandler = new WhatsAppCommunicationService(
      this.configService,
      this.ssmClient,
    );

    console.log('requestType', requestType);

    switch (requestType) {
      case 'RETURN':
        await commsHandler.sendReturnBookedTemplate(
          customerName,
          productName,
          orderId,
          getHumanReadableDateRange(5, 7),
          carrier || 'Blue Dart',
          customerPhone,
        );
        break;

      case 'FULL_REPLACEMENT':
        if (pickupPriority === 'DELIVERY_BEFORE_PICKUP') {
          await commsHandler.sendReplacementBookedRPCXTemplate(
            customerName,
            productName,
            replacementOrderId,
            getHumanReadableDateRange(5, 7),
            carrier || 'Blue Dart',
            customerPhone,
            getHumanReadableDateRangeFromDates(newMinDate, newMaxDate),
          );
        } else {
          await commsHandler.sendReplacementBookedRPTemplate(
            customerName,
            productName,
            replacementOrderId,
            getHumanReadableDateRange(5, 7),
            carrier || 'Blue Dart',
            customerPhone,
            getHumanReadableDateRangeFromDates(newMinDate, newMaxDate),
          );
        }
        break;

      case 'PART_REPLACEMENT':
        await commsHandler.sendPartBookingTemplate(
          customerName,
          productName,
          orderId,
          customerPhone,
          getHumanReadableDateRangeFromDates(newMinDate, newMaxDate),
        );
        break;

      default:
        break;
    }
  }
}
