import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';
import { OrderStatus, OrderType } from 'src/common/enum/order';
import moment from 'moment';
import { OrderData, RaisedIssueData } from 'src/orders/entities/order.entity';
import { CreateIssueTicketInput } from 'src/orders/dto/create-issue-ticket.input';
import { CreateIssueTicket } from 'src/orders/lib/create-issue-ticket';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { GetOrder } from 'src/orders/lib/get-order';
import { QueryCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { GetInventoryCredential } from 'src/inventory-credentials/lib/get-inventory-credential';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import { PickupPriority, ReplacementType } from 'src/common/enum/replacement';
import { GetReplacementOrder } from './get-replacement-order';
import { ReplacementOrderData } from '../entities/replacement-order.entity';
import { CreateFinalOrder } from './create-final-order';
import { PaymentStatus } from 'src/common/enum/payment';
import { getPickupPriorityLabel } from '../helper/get-pickup-priority';
import { RequestApprovalService } from './update-replacement-request';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { freshDeskTicketFailMailTemplate } from '../helper/mail-templates';

export class ConfirmReplacementOrder {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
  ) {}

  async createEEOrder(order) {
    try {
      const {
        id,
        shippingAddress,
        billingAddress,
        orderProducts,
        replacementType,
        pickupPriority,
        customer,
        shippingCost,
        transactionId = null,
        transactionAmount,
        amountPaid,
      } = order;
      const getInventoryCredHandler = new GetInventoryCredential(
        this.docClient,
        this.configParameters,
      );
      const { locationKey, state: ecomState } =
        await getInventoryCredHandler.getInventoryCredential('WH-FG');
      let eePayload = {
        orderDate: moment().toISOString(),
        orderNumber: id,
        orderType: 'retailorder',
        marketplace_id: 987,
        remarks1: 'Replacement Order',
        shippingCost: shippingCost,
        promoCodeDiscount: Number(transactionAmount) - Number(amountPaid) || 0,
        customer: [
          {
            shipping: {
              addressLine1: shippingAddress.line1,
              addressLine2: shippingAddress?.line2,
              postalCode: shippingAddress.pinCode,
              city: shippingAddress.city,
              state: shippingAddress.state,
              country: shippingAddress.country,
              contact: customer?.phone,
              name: customer?.firstName + customer?.lastName,
              email: customer?.email,
            },
            billing: {
              addressLine1: billingAddress.line1,
              addressLine2: billingAddress?.line2,
              postalCode: billingAddress.pinCode,
              city: billingAddress.city,
              state: billingAddress.state,
              country: billingAddress.country,
              contact: customer?.phone,
              name: customer?.firstName + customer?.lastName,
              email: customer?.email,
            },
          },
        ],
        items: null,
        ...(transactionId && { remarks2: `transactionId:${transactionId}` }),
      };

      const eeServiceHandler = new EasyEcomService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      //eslint-disable-next-line
      const [ecomProducts, _, adjustedShippingCost] =
        await eeServiceHandler.createProducts(
          orderProducts,
          shippingAddress.state === ecomState,
          OrderType.REPLACEMENT,
          order,
        );

      eePayload = {
        ...eePayload,
        items: ecomProducts,
        shippingCost: adjustedShippingCost,
      };

      const orderData = await eeServiceHandler.createOrder(
        locationKey,
        eePayload,
      );
      const { code } = orderData;

      if (code !== 200) {
        throw new CustomError(
          orderData?.data[0]?.Message || 'Error creating order',
          400,
        );
      }

      posLogger.info('EasyEcomService', 'createOrder', {
        code,
        orderData,
      });

      if (
        replacementType === ReplacementType.FULL &&
        pickupPriority === PickupPriority.PICKUP_BEFORE_DELIVERY
      ) {
        const {
          data: { InvoiceID },
        } = orderData;
        await eeServiceHandler.holdOrder(InvoiceID, locationKey);
      }

      return orderData;
    } catch (error) {
      posLogger.error('EasyEcomService', 'createOrder', {
        error: error.message || error,
      });
      throw new CustomError(
        `Error while creating order : ${error.message} `,
        400,
      );
    }
  }

  async createReturnTicket(
    createReturnTicketInput: CreateIssueTicketInput,
  ): Promise<RaisedIssueData> {
    posLogger.info('replacement-orders', 'createReturnTicket', {
      createReturnTicketInput,
    });

    const {
      phone,
      subject,
      description,
      name,
      email,
      orderId,
      status,
      priority,
    } = createReturnTicketInput;
    const createdAt = moment().toISOString();

    //CHANGED GROUP ID HERE
    const apiData = {
      description,
      subject,
      email,
      name,
      phone,
      priority,
      status,
      group_id: ***********,
      internal_group_id: ***********,
      internal_agent_id: 84007339185,
      type: 'Query',
      custom_fields: {
        cf_order_id: orderId,
      },
    };

    try {
      const createTicketHandler = new CreateIssueTicket(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const responseData = await createTicketHandler.createTicket(apiData);
      posLogger.info('replacement-orders', 'createReturnTicket', responseData);
      let raisedTicket: RaisedIssueData = null;

      if (responseData?.id) {
        const { id: ticketId } = responseData;
        raisedTicket = {
          subject,
          description,
          createdAt,
          ticketId,
        };

        return raisedTicket;
      } else {
        throw new CustomError(`Failed to raise return ticket`, 400);
      }
    } catch (error) {
      posLogger.error('replacement-orders', 'createReturnTicket', error);
      throw error;
    }
  }

  async confirmReplacementOrder(
    id: string,
    AWBNumber?: string,
    AWBApprovedBy?: string,
    newMinDate?: string,
    newMaxDate?: string,
  ): Promise<OrderData> {
    posLogger.info('replacementOrder', 'createConfirmOrder', {
      input: {
        id,
      },
    });

    console.log('newMinDate', newMinDate, 'newMaxDate', newMaxDate);

    const eeServiceHandler = new EasyEcomService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    let initiatedCancelData: any = undefined;
    let newOrderData: any = undefined;

    try {
      const [ORDER_TABLE, REPLACEMENT_ORDER_TABLE, PAYMENT_TABLE] =
        await Promise.all([
          await this.configParameters.getOrderTableName(),
          await this.configParameters.getReplacementOrderTableName(),
          await this.configParameters.getPaymentTableName(),
        ]);

      const getOrderHandler = new GetOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const order: OrderData = await getOrderHandler.getOrder(id);
      const { status, quotationId } = order;

      if (status == OrderStatus.SENT_TO_EE) return order;

      const {
        finalDiscountedAmount,
        // customer: { email, firstName, lastName, phone },
        transactions,

        // quotationId,
      } = order;
      const { totalPaidAmount } = transactions;

      if (Math.ceil(totalPaidAmount) != Math.ceil(finalDiscountedAmount)) {
        throw new CustomError(
          `Order amount ${finalDiscountedAmount} and paid amount ${totalPaidAmount} does not match!`,
          400,
        );
      }

      const getReplacementOrderHandler = new GetReplacementOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const replacementOrder: ReplacementOrderData =
        await getReplacementOrderHandler.getReplacementOrder(id, quotationId);

      const {
        pickupPriority,
        replacedProduct,
        orderType,
        orderProducts,
        accessories,
        customer,
        refundedPrice = 0,
        minDeliveryDate,
        deliveryDate,
        eeRefNo,
        originalPaymentMode = [],
        shopifyOrderId,
        locationKey,
        transactionIDs = [],
        requestId: omsRequestId = '',
        replacementType = '',
        primaryReason: reason,
      } = replacementOrder;

      let awbNumber = AWBNumber;
      let carrier, creditNoteId;

      const queryCommand = new QueryCommand({
        TableName: PAYMENT_TABLE,
        KeyConditionExpression: 'orderId = :orderId',
        FilterExpression: '#status = :status',
        ExpressionAttributeNames: {
          '#status': 'status',
        },
        ExpressionAttributeValues: {
          ':orderId': id,
          ':status': PaymentStatus.COMPLETED,
        },
      });

      let transactionId;
      let amountPaid;
      let transactionAmount;
      try {
        const result = await this.docClient.queryItems(queryCommand);
        console.log('result iss payment', result);
        if (result && result.Items && result.Items.length) {
          const item = result.Items[0];
          if (item && item.paymentID) {
            transactionId = Number(item.paymentID);
            amountPaid = item.amountPaid;
            transactionAmount = item.transactionAmount;
          }
        }
      } catch (error) {
        console.error('Error querying transaction ID:', error);
      }
      const createFinalOrderHandler = new CreateFinalOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.s3Client,
      );

      if (
        !awbNumber &&
        !(
          pickupPriority === PickupPriority.NO_PICKUP ||
          order.replacementType == ReplacementType.PART
        )
      ) {
        const payload = {
          reference_code: replacementOrder.referenceCode,
          return_reason: reason,
          items: [
            {
              parent_sku: `${replacedProduct?.sku}`,
              child_sku: `${replacedProduct?.sku}`,
              return_quantity: `${replacedProduct?.quantity}`,
            },
          ],
        };
        console.log('payload', payload);

        const returnResponse = await eeServiceHandler.initiateReturn(
          replacementOrder.locationKey,
          payload,
        );
        console.log('initiateReturn response ', returnResponse);

        if (!returnResponse.awbNumber) {
          throw new CustomError(returnResponse?.message, 400);
        }

        const {
          awbNumber: updatedAWB,
          carrier: creditNoteIdPartner,
          creditNoteId: creditId,
        } = returnResponse;
        console.log('new updatedAWB', updatedAWB);
        awbNumber = updatedAWB;
        carrier = creditNoteIdPartner || '';
        creditNoteId = creditId || '';

        // For Rollback if anything fails
        initiatedCancelData = {
          creditNoteId: creditId || '',
          locationKey: locationKey,
        };
      }

      const { OrderID, SuborderID, InvoiceID } = await this.createEEOrder({
        ...order,
        transactionId,
        id: order.eeRefNo,
        transactionAmount,
        amountPaid,
      });

      // For Rollback
      newOrderData = {
        orderId: eeRefNo,
        locationKey: locationKey,
      };

      //APPROVE THE REQUEST NOW.

      const baseReplacedSku = replacedProduct?.sku?.includes('_')
        ? replacedProduct?.sku?.split('_')[0]
        : replacedProduct?.sku;
      console.log('baseReplacedSku', baseReplacedSku);

      const combinedProducts = [
        ...(orderProducts || []),
        ...(accessories || []),
      ];
      const newSku = combinedProducts?.map((product) => product?.sku);
    
      console.log('after ecom order creation', OrderID, SuborderID, InvoiceID);
      const middlewarPaylod = {
        id: quotationId,
        orderType: orderType,
        sku: baseReplacedSku,
        newSku,
        quantity: replacedProduct?.quantity,
        newOrderId: eeRefNo,
        awbNumber: awbNumber?.toString() || '',
        createdAt: moment().toISOString(),
        refundedAmount: finalDiscountedAmount,
        refund_from_status: false,
        request_id: omsRequestId,
        dateRange: {
          start: moment(minDeliveryDate).format('DD-MM-YYYY'),
          end: moment(deliveryDate).format('DD-MM-YYYY'),
        },
      };
      console.log('middlewarPaylod', middlewarPaylod);

      const response =
        await createFinalOrderHandler.middleWareCall(middlewarPaylod);
      console.log('middleware response', response);

      const newItems = combinedProducts
        .map((item) => {
          const name =
            item.customData && item.customData.length
              ? `${item.title} - Custom - ${item.customData.length} x ${item.customData.breadth} x ${item.customData.height}`
              : `${item.title} - ${item.variantTitle}`;

          return `New item: ${name}\nQuantity - ${item.quantity}\nNew Item SKU ID: ${item.sku}\nPricing for the new items: ${item.finalItemPrice * item.quantity}`;
        })
        .join('\n\n');

      // const replacementBills = combinedPorducts
      //   .map((product) => product.price || 0)
      //   .join(', ');
      let ticketResponse;

      if (order?.replacementType !== ReplacementType.PART) {
        console.log('part ticking getting created here still');
        try {
          ticketResponse = await createFinalOrderHandler.createTicket({
            orderId: shopifyOrderId,
            email: customer.email,
            name: customer.firstName,
            phone: customer.phone,
            description: `<span>
                    Original Order ID: ${shopifyOrderId || '-'}<br/>
                    Replacement Order ID: ${eeRefNo || '-'}<br/>
                    Replacement Order Date:${moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss') || '-'}<br/>
                    Items being replaced: ${replacedProduct.title || '-'} <br/>
                    Quantity - ${replacedProduct.quantity || 0} <br/>
                    Original Item SKU ID: - ${replacedProduct.sku || 0}<br/>
                    Original Item Value: ${replacedProduct?.finalItemPrice - replacedProduct?.itemDiscount - replacedProduct?.bankDiscount || 0}<br/>
                    New items: ${newItems}<br/>
                    Extra Amount Paid: ${finalDiscountedAmount > 0 ? finalDiscountedAmount : 'NA'}<br/>
                    Amount to be refunded: ${refundedPrice || 0}<br/>
                    Replacement Type: ${getPickupPriorityLabel(pickupPriority) || ''}<br/>
                    Return AWB Number: ${awbNumber || ''}<br/>
                    Courier Partner Name: ${carrier || ''}<br/>
                    Credit Note Id: ${creditNoteId || ''}<br/>
                    Warehouse: ${locationKey || ''}<br/>
                    Original Payment Mode: ${originalPaymentMode.join(', ') || ''}<br/><br/>
                    <br/> 
                  </span>
                  `,

            priority: 1,
            status: 13,
            subject:
              'Booked Return/Replacement - Arrange Pick-up & Process Refund',
            group_id: ***********,
            voc1: 'Booked Replacement Refund',
          });
        } catch (error) {
          // TODO: Mail logic to send mail to fresh desk
          const handler = new PdfService(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.s3Client,
            this.configParameters,
          );
          await handler.sendEmailWithoutAttachment(
            '<EMAIL>',
            'Error while creating ticket on fresh desk for full type replacement order.',
            freshDeskTicketFailMailTemplate({
              shopifyOrderId,
              eeRefNo,
              replacedProduct,
              newItems,
              finalDiscountedAmount,
              refundedPrice,
              pickupPriority,
              awbNumber,
              carrier,
              creditNoteId,
              locationKey,
              originalPaymentMode,
            }),
          );
        }
        console.log('ticket res', ticketResponse);
      }

      // const command = new UpdateCommand({
      //   TableName: ORDER_TABLE,
      //   Key: { id },
      //   UpdateExpression:
      //     'SET #status = :status, #updatedAt = :updatedAt, #awbNumber = :awbNumber, #RRTicketId = :RRTicketId, #ecomInvoiceId = :ecomInvoiceId, #ecomOrderId = :ecomOrderId, #ecomSubOrderId = :ecomSubOrderId , #AWBApprovedBy = :AWBApprovedBy , #creditNoteId = :creditNoteId, #carrier = :carrier',
      //   ExpressionAttributeNames: {
      //     '#status': 'status',
      //     '#awbNumber': 'awbNumber',
      //     '#RRTicketId': 'RRTicketId',
      //     '#ecomInvoiceId': 'ecomInvoiceId',
      //     '#ecomOrderId': 'ecomOrderId',
      //     '#ecomSubOrderId': 'ecomSubOrderId',
      //     '#AWBApprovedBy': 'AWBApprovedBy',
      //     '#updatedAt': 'updatedAt',
      //     '#creditNoteId': 'creditNoteId',
      //     '#carrier': 'carrier',
      //   },
      //   ExpressionAttributeValues: {
      //     ':updatedAt': moment().toISOString(),
      //     ':status': OrderStatus.SENT_TO_EE,
      //     ':awbNumber': awbNumber || '',
      //     ':RRTicketId': ticketResponse?.ticketId || '',
      //     ':ecomInvoiceId': InvoiceID,
      //     ':ecomOrderId': OrderID,
      //     ':ecomSubOrderId': SuborderID,
      //     ':AWBApprovedBy': AWBApprovedBy || null,
      //     ':creditNoteId': creditNoteId || '',
      //     ':carrier': carrier || '',
      //   },
      //   ConditionExpression: 'attribute_exists(id)',
      //   ReturnValues: 'ALL_NEW',
      // });
      const expressionValues = {
        ':updatedAt': moment().toISOString(),
        ':status': OrderStatus.SENT_TO_EE,
      };

      if (awbNumber) expressionValues[':awbNumber'] = awbNumber;
      if (ticketResponse?.ticketId)
        expressionValues[':RRTicketId'] = ticketResponse.ticketId;
      if (InvoiceID) expressionValues[':ecomInvoiceId'] = InvoiceID;
      if (OrderID) expressionValues[':ecomOrderId'] = OrderID;
      if (SuborderID) expressionValues[':ecomSubOrderId'] = SuborderID;
      if (AWBApprovedBy) expressionValues[':AWBApprovedBy'] = AWBApprovedBy;
      if (creditNoteId) expressionValues[':creditNoteId'] = creditNoteId;
      if (carrier) expressionValues[':carrier'] = carrier;

      const expressionAttributeNames = Object.keys(expressionValues).reduce(
        (acc, key) => {
          const attributeName = key.replace(':', '');
          acc[`#${attributeName}`] = attributeName;
          return acc;
        },
        {},
      );

      // Fix: Properly format UpdateExpression
      const updateExpression = Object.keys(expressionValues)
        .map((key) => `#${key.replace(':', '')} = ${key}`)
        .join(', ');

      const command = new UpdateCommand({
        TableName: ORDER_TABLE,
        Key: { id },
        UpdateExpression: `SET ${updateExpression}`,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionValues,
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: OrderData } =
        await this.docClient.updateItem(command);

      const updateCommand = new UpdateCommand({
        TableName: REPLACEMENT_ORDER_TABLE,
        Key: {
          shopifyOrderId: quotationId,
          id: id,
        },
        UpdateExpression:
          'SET #status = :status, #updatedAt = :updatedAt, #awbNumber = :awbNumber, #RRTicketId = :RRTicketId , #creditNoteId = :creditNoteId, #carrier = :carrier',
        ExpressionAttributeNames: {
          '#status': 'status',
          '#updatedAt': 'updatedAt',
          '#awbNumber': 'awbNumber',
          '#RRTicketId': 'RRTicketId',
          '#creditNoteId': 'creditNoteId',
          '#carrier': 'carrier',
        },
        ExpressionAttributeValues: {
          ':status': OrderStatus.SENT_TO_EE,
          ':updatedAt': moment().toISOString(),
          ':awbNumber': awbNumber || null,
          ':RRTicketId': ticketResponse?.ticketId || '',
          ':creditNoteId': creditNoteId || '',
          ':carrier': carrier || '',
        },
        ConditionExpression:
          'attribute_exists(shopifyOrderId) AND attribute_exists(id)',
      });

      await this.docClient.updateItem(updateCommand);

      console.log(
        'in here to approve the request',
        id,
        omsRequestId,
        `${replacementType}_REPLACEMENT`,
      );
      try {
        const requestApprovalService = new RequestApprovalService(
          this.configService,
          this.ssmClient,
        );
        console.log(
          'replacementType accepting the request in confirm replacement order',
          omsRequestId,
          `${replacementType}_REPLACEMENT`,
        );

        const requestDetailsAfterApproval =
          await requestApprovalService.approveRequest(
            omsRequestId,
            `${replacementType}_REPLACEMENT`,
            id,
            replacementType,
            pickupPriority,
            replacedProduct?.title,
            carrier,
            eeRefNo,
            newMinDate,
            newMaxDate,
          );
        posLogger.info('RequestApprovalService', 'approveRequest', {
          message: `Successfully approved request ${id}`,
        });
      } catch (approvalError) {
        posLogger.error('RequestApprovalService', 'approveRequest', {
          error: approvalError.message || approvalError,
          id,
        });
        // We continue with order creation even if approval fails
      }

      const newmiddlewarPaylod = {
        replacement_attribute: {
          order_id: eeRefNo,
          original_order_qty: replacedProduct?.quantity,
          original_product_name:
            replacedProduct?.title + ' - ' + replacedProduct?.variantTitle,
          refund_required_or_extra_payment: 'Extra Payment',
          refund_amount_or_extra_paid: finalDiscountedAmount,
          return_awb_number: awbNumber?.toString() || '',
          return_awb_status: 'Pending',
          original_item_amount_paid:
            (replacedProduct?.price || 0) * (replacedProduct?.quantity || 0) -
            (replacedProduct?.itemDiscount || 0),
          original_item_transaction_id: `${transactionIDs?.join(', ') || ''}`,
          original_item_payment_mode: `${originalPaymentMode.join(', ') || ''}`,
          refundedAmount: finalDiscountedAmount < 0 ? finalDiscountedAmount : 0,
          new_item_transaction_id: transactionId || '',
          refund_from_status: false,
          refund_details_from_customer: '',
          replacement_remark: 'replacement order',
          orderType:
            orderType == OrderType.REPLACEMENT
              ? 'Replaced Order'
              : 'Return Order',
          dateRange: {
            start: moment(minDeliveryDate).format('DD-MM-YYYY'),
            end: moment(deliveryDate).format('DD-MM-YYYY'),
          },
        },
      };
      console.log('newmiddlewarPaylod', newmiddlewarPaylod);
      const newresponse =
        await createFinalOrderHandler.newMiddleWareCall(newmiddlewarPaylod);
      console.log('newmiddleware response', newresponse);

      console.log('everything went well for part replacement tickets');
      return Attributes;
    } catch (e) {
      posLogger.error('replacementOrder', 'createConfirmOrder', {
        error: e,
      });
      // For Rollback if anything fails
      if (initiatedCancelData) {
        console.log('Cancelling initiated return');
        const res = await eeServiceHandler.cancelInitiatedReturn(
          initiatedCancelData.locationKey,
          {
            creditNoteId: initiatedCancelData?.creditNoteId,
          },
        );
        console.log('Cancelling initiated return res', res);
      }
      if (newOrderData) {
        console.log('Cancelling new order');
        const res = await eeServiceHandler.cancelOrder(
          newOrderData.orderId,
          newOrderData.locationKey,
        );
        console.log('Cancelling new order res', res);
      }
      throw e;
    }
  }
}
