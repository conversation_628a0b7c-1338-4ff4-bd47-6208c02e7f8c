import { QueryCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { OrderStatus } from 'src/common/enum/order';
import { PaymentStatus } from 'src/common/enum/payment';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
// import { ReplacementOrderData } from '../entities/replacement-order.entity';
import { OrderData } from 'src/orders/entities/order.entity';

export class ListPaymentPendingOrders {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async scanOrders(orderTable, nextToken): Promise<any> {
    const params = {
      TableName: orderTable,
      //  ProjectionExpression: 'id, orderType, finalDiscountedAmount',
      FilterExpression:
        '#orderType = :orderType AND #finalDiscountedAmount > :finalDiscountedAmount AND #status = :status',
      ExpressionAttributeNames: {
        '#orderType': 'orderType',
        '#finalDiscountedAmount': 'finalDiscountedAmount',
        '#status': 'status',
      },
      ExpressionAttributeValues: {
        ':orderType': 'REPLACEMENT',
        ':finalDiscountedAmount': 0,
        ':status': OrderStatus.ORDER_CREATED,
      },
      ...(nextToken && { ExclusiveStartKey: nextToken }),
    };

    try {
      const result = await this.docClient.scanItems(new ScanCommand(params));
      return {
        Items: result.Items || [],
        LastEvaluatedKey: result.LastEvaluatedKey || null,
      };
    } catch (error) {
      console.error('Error scanning orders:', error);
      throw error;
    }
  }
  async getTotalCount(orderTable): Promise<{ count: number }> {
    try {
      let allOrders = [];
      let nextToken = null;

      do {
        const { Items, LastEvaluatedKey } = await this.scanOrders(
          orderTable,
          nextToken,
        );

        allOrders = allOrders.concat(Items);
        nextToken = LastEvaluatedKey;
      } while (nextToken);

      return { count: allOrders?.length || 0 };
    } catch (error) {
      console.error('Error processing orders:', error);
    }
  }
  async scanPaymentStatus(orderData): Promise<any> {
    const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

    const results = await Promise.all(
      orderData.map(async (order: any) => {
        const params = {
          TableName: PAYMENT_TABLE,
          KeyConditionExpression: 'orderId = :orderId',
          ExpressionAttributeValues: {
            ':orderId': order?.id,
          },
        };

        try {
          const result = await this.docClient.queryItems(
            new QueryCommand(params),
          );
          const items = result.Items || [];
          return {
            ...order,
            transcations: items,
          };
        } catch (error) {
          console.error('Error scanning payments:', error);
          throw error;
        }
      }),
    );

    return results.flat();
  }
  async processPaymentStatus(scanPaymentStatus) {
    const currentTime = new Date();

    return scanPaymentStatus.map((item) => {
      const transactions = item.transcations || [];
      if (transactions?.length === 0) {
        return { ...item, linkStatus: 'LINK_NOT_SENT' };
      }

      const completedTransaction = transactions.find(
        (txn) => txn.status === PaymentStatus.COMPLETED,
      );
      if (completedTransaction) {
        return { ...item, linkStatus: 'PAYMENT_COMPLETED' };
      }

      const latestTransaction = transactions.reduce((latest, txn) =>
        new Date(txn.createdAt) > new Date(latest.createdAt) ? txn : latest,
      );

      const latestTransactionTime = new Date(
        latestTransaction.createdAt,
      ).getTime();
      const currentTimeInMs = currentTime.getTime();
      const hoursDiff =
        Math.abs(currentTimeInMs - latestTransactionTime) / 36e5;

      if (hoursDiff < 15) {
        const remainingTimeInMs =
          15 * 36e5 - (currentTimeInMs - latestTransactionTime);
        const remainingHours = Math.floor(remainingTimeInMs / 36e5);
        const remainingMinutes = Math.floor((remainingTimeInMs % 36e5) / 60000);

        return {
          ...item,
          linkStatus: 'LINK_CREATED',
          linkTimeToExpire: `${remainingHours} hours ${remainingMinutes} minutes`,
        };
      }

      return { ...item, linkStatus: 'LINK_EXPIRED' };
    });
  }

  async listFilteredOrders(listPaymentPendingOrdersInput): Promise<{
    data: OrderData[];
    count: number;
    lastEvaluatedKey?: Record<string, any>;
  }> {
    posLogger.info('replacementOrder', 'listFilteredOrders', {
      listPaymentPendingOrdersInput,
    });

    console.log('listPaymentPendingOrdersInput', listPaymentPendingOrdersInput);
    try {
      const REPLACEMENT_ORDER_TABLE =
        await this.configParameters.getReplacementOrderTableName();
      //  const size = Number(listPaymentPendingOrdersInput.size);
      if (listPaymentPendingOrdersInput?.orderId) {
        const params = {
          TableName: REPLACEMENT_ORDER_TABLE,
          KeyConditionExpression: '#shopifyOrderId = :shopifyOrderId',
          FilterExpression:
            '#orderType = :orderType AND #finalDiscountedAmount > :finalDiscountedAmount AND #status = :status',
          ExpressionAttributeNames: {
            '#shopifyOrderId': 'shopifyOrderId',
            '#orderType': 'orderType',
            '#finalDiscountedAmount': 'finalDiscountedAmount',
            '#status': 'status',
          },
          ExpressionAttributeValues: {
            ':shopifyOrderId': listPaymentPendingOrdersInput?.orderId,
            ':orderType': 'REPLACEMENT',
            ':finalDiscountedAmount': 0,
            ':status': OrderStatus.ORDER_CREATED,
          },
        };

        try {
          const result = await this.docClient.queryItems(
            new QueryCommand(params),
          );
          const scanPaymentStatus = await this.scanPaymentStatus(result?.Items);
          const processedData =
            await this.processPaymentStatus(scanPaymentStatus);

          return {
            data: processedData,
            count: scanPaymentStatus?.length,
            lastEvaluatedKey: null,
          };
        } catch (error) {
          console.error('Error scanning orders:', error);
          throw error;
        }
      }

      const totalCount = await this.getTotalCount(REPLACEMENT_ORDER_TABLE);
      const result = await this.scanOrders(
        REPLACEMENT_ORDER_TABLE,
        listPaymentPendingOrdersInput?.lastEvaluatedKey,
      );

      let dataLength = result.Items.length;
      let allItems = result.Items;

      while (
        dataLength < Number(totalCount?.count) &&
        result.LastEvaluatedKey
      ) {
        const additionalResult = await this.scanOrders(
          REPLACEMENT_ORDER_TABLE,
          result.LastEvaluatedKey,
        );

        allItems = [...allItems, ...additionalResult.Items];
        result.LastEvaluatedKey = additionalResult.LastEvaluatedKey;
        dataLength = allItems.length;
      }
      const scanPaymentStatus = await this.scanPaymentStatus(allItems);
      const processedData = await this.processPaymentStatus(scanPaymentStatus);
      return {
        data: processedData,
        count: Number(totalCount?.count) || 0,
        lastEvaluatedKey: result.LastEvaluatedKey || null,
      };
    } catch (e) {
      posLogger.error('replacementOrder', 'listFilteredOrders', {
        error: e,
      });
      throw e;
    }
  }
}
