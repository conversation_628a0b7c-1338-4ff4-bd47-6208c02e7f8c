import { Injectable } from '@nestjs/common';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class WhatsAppCommunicationService {
  private baseUrl: string;
  private apiKey: string;
  private initialized: boolean = false;

  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {}

  private async init(): Promise<void> {
    if (this.initialized) return;

    try {
      const stackName = this.configService.get('STACK_NAME');

      const {
        Parameter: { Value: apiUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${stackName}/whatsapp/apiUrl`,
          WithDecryption: true,
        }),
      );

      const {
        Parameter: { Value: apiKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${stackName}/whatsapp/apiKey`,
          WithDecryption: true,
        }),
      );

      this.baseUrl =
        apiUrl ||
        'https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/sendwhatsapp';
      this.apiKey = apiKey || 'UBQJgJrPzW67r32ydSn9H8APs1VcZSkN3LKjoukp';
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize WhatsApp service:', error);
      this.baseUrl =
        'https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/sendwhatsapp';
      this.apiKey = 'UBQJgJrPzW67r32ydSn9H8APs1VcZSkN3LKjoukp';
      this.initialized = true; // Prevent further initialization attempts
    }
  }

  private async sendWhatsAppTemplate(templateAttributes: any): Promise<any> {
    await this.init();
    console.log('templateAttributes', templateAttributes);
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'x-api-key': this.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ template_attributes: templateAttributes }),
      });

      if (!response.ok) {
        throw new Error(
          `WhatsApp API responded with status: ${response.status}`,
        );
      }
      console.log('response', response);
      return await response.json();
    } catch (error) {
      console.error('Error sending WhatsApp template:', error);
      throw error;
    }
  }

  async sendReturnBookedTemplate(
    customerName: string,
    productName: string,
    orderId: string,
    pickupDate: string,
    courierPartner: string,
    phoneNo: string,
  ): Promise<any> {
    const templateAttributes = {
      templateName: 'returnBooked',
      customerName,
      productName,
      order_id: orderId,
      pickupDate,
      courierPartner,
      phoneNo,
    };

    return this.sendWhatsAppTemplate(templateAttributes);
  }

  async sendReplacementBookedRPTemplate(
    customerName: string,
    productName: string,
    orderId: string,
    pickupDate: string,
    courierPartner: string,
    phoneNo: string,
    deliveryDate: string,
  ): Promise<any> {
    const templateAttributes = {
      templateName: 'replacementBookedRP',
      customerName,
      productName,
      order_id: orderId,
      pickupDate,
      courierPartner,
      phoneNo,
      deliveryDate,
    };

    return this.sendWhatsAppTemplate(templateAttributes);
  }

  async sendReplacementBookedRPCXTemplate(
    customerName: string,
    productName: string,
    orderId: string,
    pickupDate: string,
    courierPartner: string,
    phoneNo: string,
    deliveryDate: string,
  ): Promise<any> {
    const templateAttributes = {
      templateName: 'replacementBookedRPCX',
      customerName,
      productName,
      order_id: orderId,
      pickupDate,
      courierPartner,
      phoneNo,
      deliveryDate,
    };

    return this.sendWhatsAppTemplate(templateAttributes);
  }

  async sendPartBookingTemplate(
    customerName: string,
    productName: string,
    orderId: string,
    phoneNo: string,
    deliveryDate: string,
  ): Promise<any> {
    const templateAttributes = {
      templateName: 'partBooking',
      customerName,
      productName,
      order_id: orderId,
      phoneNo,
      deliveryDate,
    };

    return this.sendWhatsAppTemplate(templateAttributes);
  }
}
