import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';
import { ReplacementOrderData } from '../entities/replacement-order.entity';
import { GetReplacementOrder } from './get-replacement-order';
import { OrderStatus, OrderType } from 'src/common/enum/order';
import moment from 'moment';
import { PutCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { OrderData, RaisedIssueData } from 'src/orders/entities/order.entity';
//import { ConfirmReplacementOrder } from './confirm-replacement-order';
import { generateEECode } from '../helper/generate-id';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { CreateIssueTicket } from 'src/orders/lib/create-issue-ticket';
import { PickupPriority, ReplacementType } from 'src/common/enum/replacement';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { ConfirmReplacementOrder } from './confirm-replacement-order';
import { getPickupPriorityLabel } from '../helper/get-pickup-priority';
import { RequestApprovalService } from './update-replacement-request';
import { RequestRefundService } from 'src/refund-details/lib/refund-request-handler';
import { CreateRefundDetail } from 'src/refund-details/lib/create-refund-detail';
import { determineIsBackToSource } from 'src/refund-details/lib/refund-option';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import {
  freshDeskTicketFailMailTemplate,
  returnTicketFailMailTemplate,
} from '../helper/mail-templates';

// THIS ARE BEING USED AT THE TIMES OF
// WHEN A RETURN HAS BEEN INITIATED
// WHEN A FULL REPLACEMENT HAS BEEN INITIATED AND THERE IS NO MONEY TO BE TAKEN FROM THE CUSTOMER WE WILL CALL CONFIRM REPLACEMENT ORDER FROM HERE.
// WHEN A PART REPLACEMENT HAS BEEN INITIATED AND THERE IS NO MONEY TO BE TAKEN FROM THE CUSTOMER WE WILL CALL CONFIRM REPLACEMENT ORDER FROM HERE.
// THIS FILES CREATES EASYECOM RETURN + FRESHDESK TICKETS + ROHITS DB UPDATES
export class CreateFinalOrder {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
  ) {}

  async createTicket(createRefundTicketInput: any): Promise<RaisedIssueData> {
    posLogger.info('refund-detail', 'createRefundTicket', {
      createRefundTicketInput,
    });

    const {
      phone,
      subject,
      description,
      name,
      email,
      orderId,
      status,
      priority,
      voc1,
      //  tags,
    } = createRefundTicketInput;
    const createdAt = moment().toISOString();

    //CHANGED GROUP ID HERE
    const apiData = {
      description,
      subject,
      email,
      name,
      phone,
      priority,
      status,
      type: 'Query',
      // group_id: group_id,
      group_id: ***********,
      internal_group_id: 84000192282,
      internal_agent_id: 84007339185,
      //  tags: tags,
      custom_fields: {
        cf_order_id: orderId,
        cf_voc_level_1: voc1,
      },
    };
    console.log('create ticket ', apiData);
    try {
      const createTicketHandler = new CreateIssueTicket(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const responseData = await createTicketHandler.createTicket(apiData);
      posLogger.info('refund-detail', 'createRefundTicket', responseData);
      let raisedTicket: RaisedIssueData = null;

      if (responseData?.id) {
        const { id: ticketId } = responseData;
        raisedTicket = {
          subject,
          description,
          createdAt,
          ticketId,
        };

        return raisedTicket;
      } else {
        throw new CustomError(`Failed to raise refund ticket`, 400);
      }
    } catch (error) {
      posLogger.error('refund-detail', 'createRefundTicket', error);
      throw error;
    }
  }
  async middleWareCall(middlewarPaylod) {
    try {
      const {
        Parameter: { Value: apiKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/apiKey`,
          WithDecryption: true,
        }),
      );
      const {
        Parameter: { Value: baseURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/url`,
          WithDecryption: true,
        }),
      );
      const url = `${baseURL}/create/pos/return_replacement`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
        },
        body: JSON.stringify({ ...middlewarPaylod }),
      });

      return await response.json();
    } catch (error) {
      throw new CustomError(`Error Updating middleWare`, 400);
    }
  }

  async newMiddleWareCall(middlewarPaylod) {
    try {
      const {
        Parameter: { Value: apiKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/apiKey`,
          WithDecryption: true,
        }),
      );
      const {
        Parameter: { Value: baseURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/url`,
          WithDecryption: true,
        }),
      );
      const url = `${baseURL}/create/pos/replacement_order`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
        },
        body: JSON.stringify({ ...middlewarPaylod }),
      });

      return await response.json();
    } catch (error) {
      throw new CustomError(`Error Updating new middleWare`, 400);
    }
  }

  async sendSMSNotification(payload) {
    try {
      const url = `https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/send_sms`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to send SMS: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      throw new CustomError(
        `Error sending SMS notification: ${error.message}`,
        400,
      );
    }
  }

  async createFinalOrder(
    id: string,
    shopifyOrderId: string,
    AWBNumber?: string,
    AWBApprovedBy?: string,
    newMinDate?: string,
    newMaxDate?: string,
  ): Promise<OrderData> {
    posLogger.info('replacementOrder', 'createFinalOrder', {
      input: {
        id,
        shopifyOrderId,
      },
    });
    let awbNumber = AWBNumber;
    let newItems;
    let newSku = [];
    let initiatedCancelData: any = undefined;
    let newOrderData: any = undefined;

    const eeServiceHandler = new EasyEcomService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    try {
      const [ORDER_TABLE, REPLACEMENT_ORDER_TABLE] = await Promise.all([
        await this.configParameters.getOrderTableName(),
        await this.configParameters.getReplacementOrderTableName(),
      ]);
      const getReplacementOrderHandler = new GetReplacementOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      let requestType = '';
      let isBankDetailsAvailableInRequest = false;
      const replacementOrder: ReplacementOrderData =
        await getReplacementOrderHandler.getReplacementOrder(
          id,
          shopifyOrderId,
        );

      const {
        orderProducts,
        accessories,
        customer,
        customCode,
        customDiscountAmount,
        totalAmount,
        finalDiscountedAmount,
        primaryReason: reason,
        shippingAddress,
        billingAddress,
        deliveryStatus,
        deliveryDate,
        minDeliveryDate,
        customerId,
        replacedProduct,
        isShippingCharged,
        shippingCost,
        replacementType,
        pickupPriority,
        orderType,
        isRazorpay = false,
        originalPaymentMode = [],
        creditNoteId,
        locationKey,
        transactionIDs = [],
        // originalRequestType = '',
        requestId: omsRequestId = '',
      } = replacementOrder;

      let orderPayload: OrderData = {
        ...replacementOrder,
        quotationId: `${shopifyOrderId}`,
        id,
        status: OrderStatus.PENDING,
        orderProducts: [...(orderProducts || []), ...(accessories || [])],
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
        deliveryDate,
        deliveryStatus,
        minDeliveryDate,
        replacedProduct,
        customer,
        customCode,
        customDiscountAmount,
        totalAmount,
        finalDiscountedAmount,
        notes: reason,
        shippingAddress,
        billingAddress,
        employeeId: 'CRM',
        storeId: 'CRM',
        promotionalDiscountAmount: 0,
        campaignDiscountAmount: 0,
        orderType: orderType,
        customerId,
        isRazorpay,
        eeRefNo: generateEECode({
          id: shopifyOrderId,
          replacementType,
          pickupPriority,
        }),
        shippingCost: isShippingCharged ? shippingCost : 0,
        isShippingCharged,
        originalPaymentMode,
        transactionIDs,
        awbNumber,
      };

      const combinedProducts = [
        ...(orderProducts || []),
        ...(accessories || []),
      ];

      if (orderType !== OrderType.RETURN) {
        newSku = combinedProducts?.map((product) => product?.sku);

        console.log('in getting newSku for order id', newSku, id);
      }

      

      //EASYECOM FLOW. FOR THE CASE OF FULL REPLACEMENT AND RETURN
      //  if (finalDiscountedAmount <= 0) {
      if (
        !(
          pickupPriority === PickupPriority.NO_PICKUP ||
          replacementType == ReplacementType.PART
        )
      ) {
        if (!replacementOrder.locationKey || !replacementOrder.referenceCode) {
          posLogger.error('replacementReturnOrder', 'createReplacementOrder', {
            error:
              'Easy Ecom locationKey,eeRefNo is missing for this order to initiate a return',
          });
          throw new CustomError(
            `Easy Ecom locationKey,eeRefNo is missing for this order to initiate a return`,
            400,
          );
        }
        try {
          if (!AWBNumber && !AWBApprovedBy && finalDiscountedAmount <= 0) {
            const payload = {
              reference_code: replacementOrder.referenceCode,
              return_reason: reason,
              items: [
                {
                  parent_sku: `${replacedProduct?.sku}`,
                  child_sku: `${replacedProduct?.sku}`,
                  return_quantity: `${replacedProduct?.quantity}`,
                },
              ],
            };
            console.log('payload', payload);
            const response = await eeServiceHandler.initiateReturn(
              replacementOrder.locationKey,
              payload,
            );
            console.log('initiateReturn response ', response);
            if (!response.awbNumber) {
              throw new CustomError(response?.message, 400);
            }
            const {
              awbNumber: updatedAWB,
              carrier: creditNoteIdPartner,
              creditNoteId: creditId,
            } = response;
            awbNumber = updatedAWB;

            // For Rollback if anything fails
            initiatedCancelData = {
              creditNoteId: creditId || '',
              locationKey: locationKey,
            };

            orderPayload = {
              ...orderPayload,
              awbNumber: updatedAWB,
              carrier: creditNoteIdPartner || '',
              creditNoteId: creditId || '',
            };
          } else {
            orderPayload = {
              ...orderPayload,
              awbNumber: awbNumber,
              AWBApprovedBy: AWBApprovedBy || null,
            };
          }

          newItems = combinedProducts
            .map((item) => {
              const name =
                item.customData && item.customData.length
                  ? `${item.title} - Custom - ${item.customData.length} x ${item.customData.breadth} x ${item.customData.height}`
                  : `${item.title} - ${item.variantTitle}`;

              return `New item: ${name}\nQuantity - ${item.quantity}\nNew Item SKU ID: ${item.sku}\nPricing for the new items: ${item.finalItemPrice * item.quantity}`;
            })
            .join('\n\n');

          console.log(newItems);

          //APPROVE THE REQUEST NOW.
          console.log('booking returnn>> and approving the request');

          // const replacementBills = combinedProducts
          //   .map((product) => product.price || 0)
          //   .join(', ');
        } catch (error) {
          posLogger.error(
            'replacementReturnOrder',
            'createFinalOrder',
            error.message,
          );
          throw new CustomError(`${error.message}`, 400);
        }
      }

      const confirmReplacementOrderHandler = new ConfirmReplacementOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.s3Client,
      );

      //TODO: IF finalDiscountedAmount<=0 AND ORDER TYPE IS NOT RETURN I.E the order has gotten waive off or 100%custom discount.
      // IN THIS CASE THEN THERE IS NO REFUND.
      // AGAIN EE FLOW FOR THE CASE OF REPLACEMENT WHEN THERE IS NO AMOUNT TO BE PAID.
      console.log('finalDiscountedAmount', finalDiscountedAmount);
      if (orderType != OrderType.RETURN && finalDiscountedAmount <= 0) {
        console.log('in here creating easyecom order using confirm order');
        try {
          const {
            data: { OrderID, SuborderID, InvoiceID },
          } = await confirmReplacementOrderHandler.createEEOrder({
            ...orderPayload,
            id: orderPayload.eeRefNo,
          });

          // For Rollback
          newOrderData = {
            orderId: orderPayload.eeRefNo,
            locationKey: locationKey,
          };

          orderPayload = {
            ...orderPayload,
            ecomInvoiceId: InvoiceID,
            ecomOrderId: OrderID,
            ecomSubOrderId: SuborderID,
            status: OrderStatus.SENT_TO_EE,
          };
          if (
            replacementType === ReplacementType.FULL &&
            pickupPriority === PickupPriority.PICKUP_BEFORE_DELIVERY
          ) {
            await eeServiceHandler.holdOrder(
              InvoiceID,
              replacementOrder.locationKey,
            );
          }
        } catch (error) {
          // await eeServiceHandler.cancelInitiatedReturn(locationKey, {
          //   creditNoteId: orderPayload?.creditNoteId,
          // });
          throw error;
        }
      }

      const handler = new PdfService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.s3Client,
        this.configParameters,
      );

      //CREATING FRESHDESK TICKETS FOR THE CASE OF REPLACEMENT WHERE NO AMOUNT HAS TO BE PAID BY THE CUSTOMER.
      if (
        finalDiscountedAmount <= 0 &&
        orderType === OrderType.REPLACEMENT &&
        replacementType !== ReplacementType.PART
      ) {
        let ticketResponse;
        console.log('part ticket getting created here>> 2');
        try {
          ticketResponse = await this.createTicket({
            orderId: shopifyOrderId,
            email: orderPayload.customer.email,
            name: orderPayload.customer.firstName,
            phone: orderPayload.customer.phone,
            description: `<span>
        Original Order ID: ${shopifyOrderId || '-'}<br/>
        Replacement Order ID: ${orderPayload.eeRefNo || '-'}<br/>
        Replacement Order Date:${moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss') || '-'}<br/>
        Items being replaced: ${replacedProduct.title || '-'} <br/>
        Quantity - ${replacedProduct.quantity || 0} <br/>
        Original Item SKU ID: - ${replacedProduct.sku || 0}<br/>
        Original Item Value: ${replacedProduct?.finalItemPrice - replacedProduct?.itemDiscount - replacedProduct?.bankDiscount || 0}<br/>
        New items: ${newItems}<br/>
        Extra Amount Paid: ${finalDiscountedAmount > 0 ? finalDiscountedAmount : 'NA'}<br/>
        Amount to be refunded: ${orderPayload.refundedPrice || 0}<br/>
      Replacement Type: ${getPickupPriorityLabel(pickupPriority) || ''}<br/>
                    
        Return AWB Number: ${awbNumber || ''}<br/>
        Courier Partner Name: ${orderPayload?.carrier || ''}<br/>
        CreditNoteId: ${creditNoteId || ''}<br/>
        Warehouse: ${locationKey || ''}<br/>
          Original Payment Mode: ${originalPaymentMode.join(', ') || ''}<br/><br/>
        <br/> 
      </span>
      `,

            priority: 1,
            status: 13,
            subject: 'Booked Replacement - Arrange Pick-up & Process Refund',
            group_id: ***********,
            voc1: 'Booked Replacement Refund',
            //  voc2: 'Refund Details',
            //  tags: ['Replacement Request'],
          });
          console.log('ticket res', ticketResponse);
        } catch (error) {
          // TODO: Mail logic to send mail to fresh desk
          await handler.sendEmailWithoutAttachment(
            '<EMAIL>',
            'Error while creating ticket on fresh desk for full type replacement order.',
            freshDeskTicketFailMailTemplate({
              shopifyOrderId,
              eeRefNo: orderPayload?.eeRefNo,
              replacedProduct,
              newItems,
              finalDiscountedAmount,
              refundedPrice: orderPayload?.refundedPrice,
              pickupPriority,
              awbNumber,
              carrier: orderPayload?.carrier,
              creditNoteId,
              locationKey,
              originalPaymentMode,
            }),
          );
        }
        orderPayload = {
          ...orderPayload,
          RRTicketId: ticketResponse.ticketId,
        };
      }

      //CREATING FRESHDESK TICKETS FOR THE CASE OF RETURN & REFUND
      if (orderType == OrderType.RETURN) {
        let ticketResponse;
        console.log('return ticket getting created here>> 2');
        try {
          ticketResponse = await this.createTicket({
            orderId: shopifyOrderId,
            email: orderPayload.customer.email,
            name: orderPayload.customer.firstName,
            phone: orderPayload.customer.phone,
            description: `<span>
  Original Order ID: ${shopifyOrderId || '-'}<br/>
  Return Order Date:${moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss') || '-'}<br/>
  Items being returned: ${replacedProduct.title || '-'} <br/>
  Return Quantity - ${replacedProduct.quantity || 0} <br/>
      Original Item SKU ID: - ${replacedProduct.sku || 0}<br/>
        Original Item Value: ${replacedProduct?.finalItemPrice - replacedProduct?.itemDiscount - replacedProduct?.bankDiscount || 0}<br/>
    
  Amount to be refunded: ${orderPayload.refundedPrice || 0}<br/>
  Return AWB Number: ${awbNumber || ''}<br/>
Courier Partner Name: ${orderPayload?.carrier || ''}<br/>
        CreditNoteId: ${creditNoteId || ''}<br/>
        Warehouse: ${locationKey || ''}<br/>
          Original Payment Mode: ${originalPaymentMode.join(', ') || ''}<br/><br/> 
</span>
`,

            priority: 1,
            status: 13,
            subject: 'Booked Return - Arrange Pick-up & Process Refund',
            group_id: ***********,
            voc1: 'Booked Return Refund',
            //  voc2: 'Refund Details',
            //  tags: ['Return Request'],
          });
          console.log('ticket res', ticketResponse);
        } catch (error) {
          await handler.sendEmailWithoutAttachment(
            '<EMAIL>',
            'Error while creating ticket on fresh desk for return order.',
            returnTicketFailMailTemplate({
              shopifyOrderId,
              awbNumber,
              carrier: orderPayload?.carrier,
              creditNoteId: orderPayload?.creditNoteId,
              locationKey,
              originalPaymentMode,
              refundedPrice: orderPayload?.refundedPrice,
              replacedProduct,
            }),
          );
        }
        orderPayload = {
          ...orderPayload,
          RRTicketId: ticketResponse?.ticketId,
        };
      }

      const baseReplacedSku = replacedProduct?.sku?.includes('_')
        ? replacedProduct?.sku?.split('_')[0]
        : replacedProduct?.sku;
      console.log('baseReplacedSku', baseReplacedSku);

      //CALLING ROHIT APIS
      if (finalDiscountedAmount <= 0) {
        const middlewarPaylod = {
          id: shopifyOrderId,
          orderType: orderType,
          newSku,
          sku: baseReplacedSku,
          quantity: replacedProduct?.quantity,
          newOrderId:
            orderType == OrderType.REPLACEMENT
              ? orderPayload?.eeRefNo
              : orderPayload?.eeRefNo,
          awbNumber: awbNumber?.toString() || '',
          request_id: omsRequestId,
          createdAt: moment().toISOString(),
          refundedAmount: finalDiscountedAmount,
          refund_from_status: false,
          dateRange: {
            start: moment(minDeliveryDate).format('DD-MM-YYYY'),
            end: moment(deliveryDate).format('DD-MM-YYYY'),
          },
        };
        console.log('middlewarPaylod', middlewarPaylod);
        const response = await this.middleWareCall(middlewarPaylod);
        console.log('middleware response', response);
        const newmiddlewarPaylod = {
          replacement_attribute: {
            order_id:
              orderType == OrderType.REPLACEMENT
                ? orderPayload?.eeRefNo
                : orderPayload?.eeRefNo,
            original_order_qty: replacedProduct?.quantity || '',
            original_product_name:
              replacedProduct?.title + ' - ' + replacedProduct?.variantTitle ||
              '',
            refund_required_or_extra_payment: 'Refund',
            refund_amount_or_extra_paid: finalDiscountedAmount,
            return_awb_number: awbNumber?.toString() || '',
            return_awb_status: 'Pending',
            original_item_amount_paid:
              (replacedProduct?.price || 0) * (replacedProduct?.quantity || 0) -
              (replacedProduct?.itemDiscount || 0),
            original_item_transaction_id: `${transactionIDs?.join(', ') || ''}`,
            original_item_payment_mode: `${originalPaymentMode.join(', ') || ''}`,
            refundedAmount:
              finalDiscountedAmount < 0 ? finalDiscountedAmount : 0,
            new_item_transaction_id: '',
            refund_from_status: false,
            refund_details_from_customer: '',
            replacement_remark: 'replacement order',
            orderType:
              orderType == OrderType.REPLACEMENT
                ? 'Replaced Order'
                : 'Return Order',
            dateRange:
              orderType == OrderType.REPLACEMENT
                ? {
                    start: moment(minDeliveryDate).format('DD-MM-YYYY'),
                    end: moment(deliveryDate).format('DD-MM-YYYY'),
                  }
                : {},
          },
        };
        const newresponse = await this.newMiddleWareCall(newmiddlewarPaylod);
        console.log('newmiddleware response', newresponse);

        //TODO: GET THE REQUEST AND IF THE REQUEST HAS BANK OR UPI DETAILS, DO NOT SEND THE LINK TO THE CUSTOMER.
      }

      console.log(
        'replacement order getting created on order table>',
        orderPayload,
      );

      const orderCommand = new PutCommand({
        TableName: ORDER_TABLE,
        Item: orderPayload,
        ConditionExpression: 'attribute_not_exists(id)',
      });

      const res = await this.docClient.createItem(orderCommand);
      console.log('resss>>>', res);

      //FINALLY CALLING THE REQUEST APPROVAL SERVICE
      //TODO: WILL CALL THIS ONLY IF A REQUEST ID IS PRESENT
      console.log(
        'booking returnn>> and approving the request in create final order',
      );
      console.log('refunded price>>', orderPayload.refundedPrice);
      if (!!omsRequestId && orderPayload.refundedPrice > 0) {
        try {
          console.log('approving the request in create final order');
          const requestApprovalService = new RequestApprovalService(
            this.configService,
            this.ssmClient,
          );

          console.log('shopifyOrderId', shopifyOrderId);

          console.log(
            'replacementType in final replacement order',
            omsRequestId,
            `${replacementType}_REPLACEMENT`,
          );

          const requestDetailsAfterApproval =
            await requestApprovalService.approveRequest(
              omsRequestId,
              requestType,
              id,
              replacementType,
              pickupPriority,
              replacedProduct?.title,
              orderPayload?.carrier,
              orderPayload?.eeRefNo,
              newMinDate,
              newMaxDate,
            );

          const { data: requestData = {} } = requestDetailsAfterApproval;

          if (!requestData || Object.keys(requestData).length <= 0) {
            throw new Error('No refund details found');
          }

          const {
            _id: requestId,
            bank_details: bankDetails,
            upi_details: upiDetails,
            isBackToSource,
          } = requestData;

          if (
            (bankDetails && Object.keys(bankDetails).length) ||
            (upiDetails && Object.keys(upiDetails).length)
          ) {
            isBankDetailsAvailableInRequest = true;
          }
          posLogger.info('RequestApprovalService', 'approveRequest', {
            message: `Successfully approved request ${shopifyOrderId}`,
          });
        } catch (approvalError) {
          posLogger.error('RequestApprovalService', 'approveRequest', {
            error: approvalError.message || approvalError,
            id,
          });
          // We continue with order creation even if approval fails
        }

        //TODO: ADD THE CONDITION IF REQUEST ID THERE THEN DO THIS
        //UPDATING ORDER PAYLOAD FOR THE CASE WHEN IF BANK DETAILS ARE AVAILABLE IN THE REQUEST
        if (
          orderPayload?.refundedPrice > 0 &&
          isBankDetailsAvailableInRequest
        ) {
          orderPayload = {
            ...orderPayload,
            refundDetailAlreadyAdded: true,
          };
        }
      }

      const updateCommand = new UpdateCommand({
        TableName: REPLACEMENT_ORDER_TABLE,
        Key: {
          shopifyOrderId,
          id,
        },
        UpdateExpression:
          'SET #orderId = :orderId, #status = :status, #awbNumber = :awbNumber, #updatedAt = :updatedAt, #eeRefNo = :eeRefNo, #RRTicketId = :RRTicketId, #creditNoteId = :creditNoteId, #carrier = :carrier,#refundDetailAlreadyAdded = :refundDetailAlreadyAdded',
        ExpressionAttributeNames: {
          '#orderId': 'orderId',
          '#status': 'status',
          '#awbNumber': 'awbNumber',
          '#RRTicketId': 'RRTicketId',
          '#creditNoteId': 'creditNoteId',
          '#carrier': 'carrier',
          '#updatedAt': 'updatedAt',
          '#eeRefNo': 'eeRefNo',
          '#refundDetailAlreadyAdded': 'refundDetailAlreadyAdded',
        },
        ExpressionAttributeValues: {
          ':orderId': id,
          ':status':
            finalDiscountedAmount <= 0
              ? OrderStatus.SENT_TO_EE
              : OrderStatus.ORDER_CREATED,
          ':updatedAt': moment().toISOString(),
          ':eeRefNo': orderPayload.eeRefNo,
          ':awbNumber': awbNumber || null,
          ':RRTicketId': orderPayload.RRTicketId || '',
          ':creditNoteId': orderPayload.creditNoteId || '',
          ':carrier': orderPayload.carrier || '',
          ':refundDetailAlreadyAdded': isBankDetailsAvailableInRequest,
        },
        ConditionExpression:
          'attribute_exists(shopifyOrderId) AND attribute_exists(id)',
      });

      await this.docClient.updateItem(updateCommand);

      //TODO: ADD THE PART IF FOR SOME REASON PROCESS REFUND FAILS THEN WE NEED TO UPDATE THE refundDetailAlreadyAdded AS FALSE.
      //TODO: IF THE REQUEST TYPE IS REPLACEMENT AND THEN CHECK IF WE EVEN NEED TO DO REFUND IF YES THEN ONLY PROCEED WITH THE FOLLOWING.
      //
      console.log(
        'booking returnn>> and refunding the request',
        requestType,
        isBankDetailsAvailableInRequest,
      );

      // IF THE REQUEST ID IS AVAILABLE & BANK DETAILS ARE ALSO THERE IN THE REQUEST THEN PROCESS THAT REFUND
      // OR ELSE JUST PUT AN ENTRY OF THE REFUND.
      console.log(
        'processing refund',
        finalDiscountedAmount,
        isBankDetailsAvailableInRequest,
        omsRequestId,
      );

      //ONLY AT THE TIME OF REFUND WE NEED TO PROCESS OR ADD REFUND DETAILS
      if (orderPayload?.refundedPrice > 0) {
        const isBackToSource = determineIsBackToSource(originalPaymentMode);
        if (
          !!omsRequestId &&
          (isBankDetailsAvailableInRequest || isBackToSource)
        ) {
          console.log('when refund details are there in the request');
          try {
            const refundRequestHandler = new RequestRefundService(
              this.configService,
              this.docClient,
              this.ssmClient,
              this.configParameters,
              this.s3Client,
            );
            const res = await refundRequestHandler.processRefundAgainstARequest(
              id,
              shopifyOrderId,
              orderType.toUpperCase(),
              omsRequestId,
              isBackToSource,
            );
          } catch (err) {
            posLogger.error(
              'RequestRefundService',
              'processRefundAgainstARequest',
              {
                error: err.message || err,
                id,
              },
            );
            // We continue with order creation even if approval fails
          }
        } else {
          console.log(
            'in here creating refund in case we need details from user procesing request in create final order',
          );
          try {
            const createRefundHandler = new CreateRefundDetail(
              this.configService,
              this.docClient,
              this.ssmClient,
              this.configParameters,
              this.s3Client,
            );
            console.log('originalPaymentMode', originalPaymentMode);
            const isBackToSource = determineIsBackToSource(originalPaymentMode);
            console.log('isBackToSource', isBackToSource);
            const refundDetail = await createRefundHandler.createRefundDetail({
              id,
              shopifyOrderId,
              isBackToSource,
              isFormSubmitted: false,
              requestId: omsRequestId,
            });

            console.log('refundDetail', refundDetail);
          } catch (err) {
            console.error('err in creating refund details', err);
          }

          //CREATE REFUND DETAILS HERE
        }

        // IF BANK DETAILS ARE NOT IN REQUEST THEN WE ALSO NEED TO SEND AN SMS.
        if (!isBankDetailsAvailableInRequest) {
          try {
            const SMSpayload = {
              template_attributes: {
                templateName: 'ReplacementRefund',
                phoneNo: customer?.phone,
                customerName:
                  (customer?.firstName || '') + (customer?.lastName || ''),
                refund_link: `https://refund-form-${process.env.NODE_ENV}.thesleepcompany.in/?shopifyOrderId=${shopifyOrderId}%26id=${id}`,
              },
            };
            console.log('SMSpayload', SMSpayload);
            const smsResponse = await this.sendSMSNotification(SMSpayload);
            console.log('SMS sent successfully:', smsResponse);
          } catch (err) {
            posLogger.error('replacement', 'refund sms', err);
          }
        }
      }

      return orderPayload;
    } catch (e) {
      posLogger.error('replacementOrder', 'createFinalOrder', e);
      // For Rollback if anything fails
      if (initiatedCancelData) {
        console.log('Cancelling initiated return');
        const res = await eeServiceHandler.cancelInitiatedReturn(
          initiatedCancelData.locationKey,
          {
            creditNoteId: initiatedCancelData?.creditNoteId,
          },
        );
        console.log('Cancelling initiated return res', res);
      }
      if (newOrderData) {
        console.log('Cancelling new order');
        const res = await eeServiceHandler.cancelOrder(
          newOrderData.orderId,
          newOrderData.locationKey,
        );
        console.log('Cancelling new order res', res);
      }
      throw e;
    }
  }
}
