import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { ReplacementType } from 'src/common/enum/replacement';
import { ReplacementOrderData } from '../entities/replacement-order.entity';
import { UpdateReplacementOrderProductInput } from '../dto/update-replacement-order-product.input';
import { GetReplacementOrder } from './get-replacement-order';
import { ValidateProducts } from 'src/common/helper/validate-products';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { getCartCount } from 'src/common/helper/get-cart-count';

export class UpdateReplacementOrderProduct {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateReplacementOrderProduct({
    id,
    shopifyOrderId,
    orderProducts,
    pickupPriority,
  }: UpdateReplacementOrderProductInput): Promise<ReplacementOrderData> {
    
    posLogger.info('replacementOrder', 'updateReplacementOrderProduct', {
      input: {
        id,
        shopifyOrderId,
        orderProducts,
        pickupPriority,
      },
    });

    try {
      const [REPLACEMENT_ORDER_TABLE] = await Promise.all([
        await this.configParameters.getReplacementOrderTableName(),
      ]);
      const getReplacementOrderHandler = new GetReplacementOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const replacementOrder: ReplacementOrderData =
        await getReplacementOrderHandler.getReplacementOrder(
          id,
          shopifyOrderId,
        );

      const { replacedProduct, replacementType } = replacementOrder;
      let invalidProducts = null,
        finalProducts = null;

      if (replacementType === ReplacementType.PART) {
        const { metafields } = replacedProduct;
        const partProducts = JSON.parse(
          metafields?.find(
            (item: any) => item.key === 'replacement_part_product_id',
          )?.value || null,
        );
        if (
          !partProducts ||
          !partProducts[replacedProduct.variantId] ||
          !partProducts[replacedProduct.variantId].length
        ) {
          throw new CustomError(
            'Part Products not available for this product',
            400,
          );
        }

        const validateProducthandler = new ValidateProducts(
          this.configService,
          this.ssmClient,
          this.docClient,
          this.configParameters,
        );

        const res =
          await validateProducthandler.validateProducts(orderProducts);

        finalProducts = res.map(
          ({ productId, variantId, quantity, price, product, variant }) => ({
            productId,
            variantId,
            quantity,
            price: Number(price),
            customData: null,
            title: product.title,
            variantTitle: variant?.variantTitle || null,
            mrp: variant?.compare_at_price || null,
            image: variant?.image || product.image,
            productType: 'Parts',
            sku: variant?.sku || null,
            finalItemPrice: quantity * Number(price),
            edd:
              variant?.metafields?.find((m) => m.key == 'edd')?.value || null,
            createdAt: moment().toISOString(),
            updatedAt: moment().toISOString(),
          }),
        );
      } else {
        if (getCartCount([replacedProduct]) < getCartCount(orderProducts))
          throw new CustomError(
            'Ordered quantity is more than Replaceable quantity',
            400,
          );

        const { productType } = replacedProduct;
        const handler = new ValidateProducts(
          this.configService,
          this.ssmClient,
          this.docClient,
          this.configParameters,
        );
        const res = await handler.validateProducts(orderProducts);
        invalidProducts = res.filter((p) => p.product_type == productType);
        if (invalidProducts && invalidProducts.length)
          throw new CustomError('Selected products are invalid', 400);

        finalProducts = res.map(
          ({
            productId,
            variantId,
            quantity,
            price,
            product,
            variant,
            customData,
          }) => {
            return {
              productId,
              variantId: variantId || null,
              quantity,
              price: Number(price),
              customData: customData || null,
              title: product.title,
              variantTitle: variant?.variantTitle || null,
              mrp: variant?.compare_at_price || null,
              image: variant?.image || product.image,
              productType: product?.product_type || null,
              sku: variant?.sku || null,
              edd:
                variant?.metafields?.find((m) => m.key == 'edd')?.value || null,
              finalItemPrice: Number(price) * quantity,
              createdAt: moment().toISOString(),
              updatedAt: moment().toISOString(),
            };
          },
        );
      }

      const amount =
        replacementType === ReplacementType.FULL
          ? finalProducts.reduce((acc, p) => {
              if (p.variantId !== replacedProduct.variantId)
                return (acc += Number(p.price));
              return acc;
            }, 0) -
            Number(replacedProduct.price) * replacedProduct.quantity
          : finalProducts.reduce((acc, p) => {
              if (p.variantId !== replacedProduct.variantId)
                return (acc += Number(p.price));
              return acc;
            }, 0);

      const command = new UpdateCommand({
        TableName: REPLACEMENT_ORDER_TABLE,
        Key: {
          shopifyOrderId,
          id,
        },
        UpdateExpression: `
          SET 
            #pickupPriority = :pickupPriority, 
            #orderProducts = :orderProducts, 
            #totalAmount = :totalAmount, 
            #finalDiscountedAmount = :finalDiscountedAmount, 
            updatedAt = :updatedAt,
            #customDiscountAmount  = :customDiscountAmount, 
            #customCode   = :customCode,
            #replaceableDiscountType   = :replaceableDiscountType,
            #refundedPrice   = :refundedPrice,
            #replacementOption = :replacementOption
            
        `,
        ExpressionAttributeNames: {
          '#pickupPriority': 'pickupPriority',
          '#orderProducts': 'orderProducts',
          '#totalAmount': 'totalAmount',
          '#finalDiscountedAmount': 'finalDiscountedAmount',
          '#customDiscountAmount': 'customDiscountAmount',
          '#customCode': 'customCode',
          '#replaceableDiscountType': 'replaceableDiscountType',
          '#replacementOption': 'replacementOption',
          '#refundedPrice': 'refundedPrice',
        },
        ExpressionAttributeValues: {
          ':pickupPriority': pickupPriority,
          ':orderProducts': finalProducts,
          ':totalAmount': amount,
          ':finalDiscountedAmount': amount,
          ':updatedAt': moment().toISOString(),
          ':customDiscountAmount': null,
          ':customCode': null,
          ':replaceableDiscountType': null,
          ':refundedPrice': null,
          ':replacementOption': null,
        },
        ConditionExpression:
          'attribute_exists(shopifyOrderId) AND attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });
      const { Attributes }: { Attributes: ReplacementOrderData } =
        await this.docClient.updateItem(command);

      if (Attributes) return Attributes;
    } catch (e) {
      console.error('Error updating replacement order product', e);
      posLogger.error('replacementOrder', 'updateReplacementOrderProduct', {
        error: e,
      });
      throw e;
    }
  }
}
