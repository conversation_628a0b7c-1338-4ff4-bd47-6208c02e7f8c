// import { GetCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
// import { ConfigService } from '@nestjs/config';
// import { AppDocumentClient } from 'src/common/document-client/document-client';
// import { posLogger } from 'src/common/logger';
// import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
// import { CustomError } from 'src/common/response/errorHandler/error.handler';
// import { AppConfigParameters } from 'src/config/config';
// import { ReplacementOrderData } from '../entities/replacement-order.entity';
// import { AppShopify } from 'src/common/shopify/shopify';
// import { ReplacementType } from 'src/common/enum/replacement';
// import { OrderType } from 'src/common/enum/order';
// import { json2csv } from 'json-2-csv';
// import * as fs from 'fs';
// import { ElasticClient } from 'src/utils/elasticsearch.config';
// import { RequestApprovalService } from './update-replacement-request';
// import { RefundDetailsService } from 'src/refund-details/refund-details.service';
// import { AppS3Client } from 'src/common/s3-client/s3-client';
// import { PaymentsService } from 'src/payments/payments.service';
// import { PaymentStatus } from 'src/common/enum/payment';

// interface ReplacementOrderCSVData {
//   orderId: string;
//   shopifyOrderId: string;
//   oldProductName: string;
//   oldProductSize: string;
//   oldSkuId: string;
//   oldProductPriceAfterDiscount: number;
//   requestExists: string;
//   requestType: string;
//   requestTicketId: string;
//   dateOfRequest: string;
//   status: string;
//   replacementPriority: string;
//   rejectedReason: string;
//   dateOfReplacementBooking: string;
//   acceptedPrimaryReason: string;
//   acceptedSubReason: string;
//   acceptedDepartment: string;
//   agentName: string;
//   finalRequestType: string;
//   requestStatus: string;
//   reasonAdditionalComments: string;
//   replacementOrderId: string;
//   replacementOrderIdStatus: string;
//   replacementProductName: string;
//   replacementProductSkuId: string;
//   replacementProductSize: string;
//   replacementProductPrice: number;
//   customDiscountApplied: number;
//   approverName: string;
//   refundOrAdditionalPayment: string;
//   refundAmount: number;
//   shippingChargesApplied: string;
//   shippingChargesAmount: number;
//   additionalPaymentAmount: number;
//   bookingTicketId: string;
//   pickupEdd: string;
//   newOrderEdd: string;
//   paymentTransactionId: string;
//   refundTrackerId: string;
//   refundMode: string;
//   refundStatus: string;
//   refundTransactionId: string;
//   refundInitiatedDate: string;
//   daysFromRequestToReturn: number;
// }

// export class ListReplacementOrders {
//   private esHandler: ElasticClient;
//   private requestService: any;
//   private refundDetailsService: any;
//   private paymentsService: any;
//   constructor(
//     private configService: ConfigService,
//     private docClient: AppDocumentClient,
//     private ssmClient: AppSsmClient,
//     private configParameters: AppConfigParameters,
//     private s3Client: AppS3Client,
//   ) {
//     this.requestService = new RequestApprovalService(
//       this.configService,
//       this.ssmClient,
//     );
//     this.esHandler = new ElasticClient(this.configService, this.ssmClient);

//     this.refundDetailsService = new RefundDetailsService(
//       this.configService,
//       this.docClient,
//       this.ssmClient,
//       this.configParameters,
//       this.s3Client,
//     );

//     this.paymentsService = new PaymentsService(
//       this.configService,
//       this.docClient,
//       this.ssmClient,
//       this.configParameters,
//       this.shopifyClient,
//       this.s3Client,
//     );
//   }

//   async listReplacementOrders(): Promise<void> {
//     posLogger.info('replacementOrder', 'listReplacementOrders', {});

//     try {
//       const REPLACEMENT_ORDER_TABLE =
//         await this.configParameters.getReplacementOrderTableName();

//       // Get all replacement orders from OpenSearch
//       const replacementOrders = await this.getAllReplacementOrdersFromOS();
//       console.log('replacementOrders', replacementOrders.length);
//       // Filter for orders with status ORDER_CREATED or SENT_TO_EE
//       const filteredOrders = replacementOrders.filter(
//         (order) =>
//           order.status === 'ORDER_CREATED' || order.status === 'SENT_TO_EE',
//       );
//       console.log('filteredOrders', filteredOrders.length);
//       const csvData: ReplacementOrderCSVData[] = [];

//       for (const replacementOrder of filteredOrders) {
//         try {
//           // Get request details
//           const requestData = await this.getRequestDetails(replacementOrder);

//           // Get refund details
//           const refundData = await this.getRefundDetails(replacementOrder);

//           // Get payment details if needed
//           const paymentData = await this.getPaymentDetails(replacementOrder);

//           // Create CSV row
//           const csvRow = await this.createCSVRow(
//             replacementOrder,
//             requestData,
//             refundData,
//             paymentData,
//           );
//           csvData.push(csvRow);
//         } catch (error) {
//           posLogger.error('replacementOrder', 'processingSingleOrder', {
//             orderId: replacementOrder.id,
//             error: error.message,
//           });
//           continue; // Continue with next order if one fails
//         }
//       }

//       // Convert to CSV and save
//       await this.saveToCSV(csvData);

//       posLogger.info('replacementOrder', 'listReplacementOrders', {
//         totalOrders: csvData.length,
//         message: 'CSV file generated successfully',
//       });
//     } catch (error) {
//       posLogger.error('replacementOrder', 'listReplacementOrders', {
//         error: error.message,
//       });
//       throw error;
//     }
//   }

//   private async getAllReplacementOrdersFromOS(): Promise<any[]> {
//     // Implement OpenSearch query to get all replacement orders
//     // This is a placeholder - implement according to your OpenSearch setup
//     const searchArray = [
//       {
//         terms: {
//           'status.keyword': ['ORDER_CREATED', 'SENT_TO_EE'],
//         },
//       },
//     ];

//     const query = {
//       bool: {
//         must: [...searchArray],
//       },
//     };

//     const response = await this.esHandler.search({
//       index: 'pos-prod-replacement-table', // Use your actual index name
//       body: {
//         from: 0,
//         size: 10000, // Adjust as needed
//         query: query,
//       },
//     });
//     console.log('response', response);
//     return response.body.hits.hits.map((hit) => hit._source);
//   }

//   private async getRequestDetails(replacementOrder: any): Promise<any> {
//     try {
//       if (replacementOrder.requestId) {
//         const { data } = await this.requestService.getRequestDetailsByOrderName(
//           replacementOrder.requestId,
//         );
//         console.log('request data', data?._id);
//         return data;
//       }
//       return null;
//     } catch (error) {
//       posLogger.error('replacementOrder', 'getRequestDetails', {
//         orderId: replacementOrder.id,
//         error: error.message,
//       });
//       return null;
//     }
//   }

//   private async getRefundDetails(replacementOrder: any): Promise<any> {
//     try {
//       return await this.refundDetailsService.findOne(
//         replacementOrder.id,
//         replacementOrder.shopifyOrderId,
//       );
//     } catch (error) {
//       posLogger.error('replacementOrder', 'getRefundDetails', {
//         orderId: replacementOrder.id,
//         error: error.message,
//       });
//       return null;
//     }
//   }

//   private async getPaymentDetails(replacementOrder: any): Promise<any> {
//     try {
//       return await this.paymentsService.findAllByOrderID(replacementOrder.id);
//     } catch (error) {
//       posLogger.error('replacementOrder', 'getPaymentDetails', {
//         orderId: replacementOrder.id,
//         error: error.message,
//       });
//       return null;
//     }
//   }

//   private async createCSVRow(
//     replacementOrder: any,
//     requestData: any,
//     refundData: any,
//     paymentData: any,
//   ): Promise<ReplacementOrderCSVData> {
//     const oldProduct = replacementOrder.replacedProduct || {};
//     const newProduct = replacementOrder.orderProducts?.[0] || {};
//     // console.log('payment amount', paymentData);
//     // Calculate days from request to return
//     const daysFromRequest = this.calculateDaysFromRequest(
//       requestData,
//       replacementOrder,
//     );

//     // Determine refund or additional payment
//     const refundOrAdditionalPayment =
//       this.determineRefundOrPayment(replacementOrder);
//     const { amountPaid, transactionAmount, transactionId } =
//       await this.calculateAdditionalPayment(replacementOrder);
//     return {
//       // 1. Order ID, shopify order id
//       orderId: replacementOrder.orderId || '',
//       shopifyOrderId: replacementOrder.shopifyOrderId || '',

//       // 2-5. OLD Product details
//       oldProductName: oldProduct.title || '',
//       oldProductSize: oldProduct.variantTitle || '',
//       oldSkuId: oldProduct.sku || '',
//       oldProductPriceAfterDiscount: oldProduct.finalItemPrice || 0,

//       // 6-12. Request details
//       requestExists: requestData ? 'Yes' : 'No',
//       requestType:
//         requestData?.finalRequestType || requestData?.requestType || '',
//       requestTicketId: requestData?.freshdeskId || '',
//       dateOfRequest: requestData?.createdAt || '',
//       status: replacementOrder?.status || '',
//       requestStatus: requestData?.requestStatus || 'NONE',
//       finalRequestType: requestData?.finalRequestType || '',
//       replacementPriority: replacementOrder.pickupPriority || '',
//       rejectedReason: requestData?.rejectedReason || '',

//       // 13-19. Replacement booking details
//       dateOfReplacementBooking: replacementOrder.createdAt || '',
//       acceptedPrimaryReason:
//         replacementOrder.primaryReason || replacementOrder.reason || '',
//       acceptedSubReason: replacementOrder.secondaryReason || '',
//       acceptedDepartment: replacementOrder.department || '',
//       agentName: replacementOrder.agentName || '',
//       reasonAdditionalComments: replacementOrder.additionalComments || '',

//       // 20-25. Replacement order and product details
//       replacementOrderId: replacementOrder.id || '',
//       replacementOrderIdStatus: replacementOrder.status || '',
//       replacementProductName: newProduct.title || '',
//       replacementProductSkuId: newProduct.sku || '',
//       replacementProductSize: newProduct.variantTitle || '',
//       replacementProductPrice: newProduct.finalItemPrice || 0,
//       customDiscountApplied: replacementOrder.customDiscountAmount || 0,

//       // 26-32. Payment and shipping details
//       approverName: replacementOrder.approverName || '',
//       refundOrAdditionalPayment: refundOrAdditionalPayment,
//       // refundAmount: this.calculateRefundAmount(replacementOrder, refundData),
//       refundAmount: replacementOrder?.refundedPrice,
//       shippingChargesApplied: replacementOrder.isShippingCharged ? 'Yes' : 'No',
//       shippingChargesAmount: replacementOrder.shippingCost || 0,
//       // additionalPaymentAmount:
//       //   this.calculateAdditionalPayment(replacementOrder),
//       additionalPaymentAmount: amountPaid || 0,
//       paymentTransactionId: transactionId || '',
//       // 33-35. Ticket and EDD details
//       bookingTicketId: replacementOrder.RRTicketId?.toString() || '',
//       pickupEdd: this.formatDate(replacementOrder.pickupEdd),
//       newOrderEdd: this.formatDate(replacementOrder.deliveryDate),

//       // 36-40. Refund details
//       refundTrackerId: refundData?.id || '',
//       refundMode: this.getRefundMode(refundData, replacementOrder),
//       refundStatus: refundData?.status || 'PENDING',
//       refundTransactionId: refundData?.transactionId || '',
//       refundInitiatedDate: refundData?.createdAt || '',

//       // 41. Days calculation
//       daysFromRequestToReturn: daysFromRequest,
//     };
//   }

//   private mapRequestStatus(status: string): string {
//     const statusMap = {
//       APPROVED: 'Full Replacement Booked',
//       REJECTED: 'Rejected',
//       PENDING: 'None',
//       CANCELLED: 'Order Cancelled',
//     };
//     return statusMap[status] || status || 'None';
//   }

//   private determineRefundOrPayment(replacementOrder: any): string {
//     const finalAmount = replacementOrder.finalDiscountedAmount || 0;
//     const orderTotal = replacementOrder.orderTotal || 0;
//     const replacedPrice = replacementOrder.replacedProduct?.finalItemPrice || 0;

//     if (replacementOrder.refundedPrice > 0) {
//       return 'Refund';
//     }
//     if (finalAmount > 0) {
//       return 'Additional Payment';
//     }
//     return 'No Change';
//   }

//   private calculateRefundAmount(
//     replacementOrder: any,
//     refundData: any,
//   ): number {
//     if (refundData?.refundAmount) {
//       return refundData.refundAmount;
//     }

//     const replacedPrice = replacementOrder.replacedProduct?.finalItemPrice || 0;
//     const finalAmount = replacementOrder.finalDiscountedAmount || 0;

//     return Math.max(0, replacedPrice - finalAmount);
//   }

//   private async calculateAdditionalPayment(
//     replacementOrder: any,
//   ): Promise<any> {
//     // const replacedPrice = replacementOrder.replacedProduct?.finalItemPrice || 0;
//     // const finalAmount = replacementOrder.finalDiscountedAmount || 0;

//     // return Math.max(0, finalAmount - replacedPrice);
//     const queryCommand = new QueryCommand({
//       TableName: 'pos-prod-payment-table',
//       KeyConditionExpression: 'orderId = :orderId',
//       FilterExpression: '#status = :status',
//       ExpressionAttributeNames: {
//         '#status': 'status',
//       },
//       ExpressionAttributeValues: {
//         ':orderId': replacementOrder.id,
//         ':status': PaymentStatus.COMPLETED,
//       },
//     });

//     let transactionId;
//     let amountPaid;
//     let transactionAmount;
//     try {
//       const result = await this.docClient.queryItems(queryCommand);
//       console.log('result iss payment', result);
//       if (result && result.Items && result.Items.length) {
//         const item = result.Items[0];
//         if (item && item.paymentID) {
//           transactionId = Number(item.paymentID);
//           amountPaid = item.amountPaid;
//           transactionAmount = item.transactionAmount;
//         }
//       }
//       return { amountPaid, transactionId, transactionAmount };
//     } catch (error) {
//       console.error('Error querying transaction ID:', error);
//     }
//   }

//   private getRefundMode(refundData: any, replacementOrder: any): string {
//     if (refundData?.refundMode) {
//       return refundData.refundMode;
//     }

//     if (refundData?.isBackToSource) {
//       return 'Back to Source';
//     }

//     // Check original payment mode
//     const originalPaymentMode = replacementOrder.originalPaymentMode?.[0];
//     if (originalPaymentMode) {
//       return originalPaymentMode;
//     }

//     return '';
//   }

//   private calculateDaysFromRequest(
//     requestData: any,
//     replacementOrder: any,
//   ): number {
//     if (!requestData?.createdAt) return 0;

//     const requestDate = new Date(requestData.createdAt);
//     const returnDate = new Date(replacementOrder.createdAt);

//     const diffTime = Math.abs(returnDate.getTime() - requestDate.getTime());
//     return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
//   }

//   private formatDate(dateString: string): string {
//     if (!dateString) return '';

//     try {
//       return new Date(dateString).toLocaleDateString('en-GB');
//     } catch (error) {
//       return dateString;
//     }
//   }

//   private async saveToCSV(data: ReplacementOrderCSVData[]): Promise<void> {
//     try {
//       const csv = await json2csv(data);
//       const fileName = `replacement_orders_${new Date().toISOString().split('T')[0]}.csv`;
//       const filePath = `./exports/${fileName}`;

//       // Ensure exports directory exists
//       if (!fs.existsSync('./exports')) {
//         fs.mkdirSync('./exports', { recursive: true });
//       }

//       fs.writeFileSync(filePath, csv);

//       posLogger.info('replacementOrder', 'saveToCSV', {
//         fileName,
//         recordCount: data.length,
//         message: 'CSV file saved successfully',
//       });
//     } catch (error) {
//       posLogger.error('replacementOrder', 'saveToCSV', {
//         error: error.message,
//       });
//       throw new CustomError('Failed to save CSV file', 500);
//     }
//   }
// }
