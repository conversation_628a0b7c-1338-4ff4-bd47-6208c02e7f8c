import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetReplacementOrder } from 'src/replacement-orders/lib/get-replacement-order';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import {
  CustomError,
  EnhancedError,
} from 'src/common/response/errorHandler/error.handler';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { OrderStatus, OrderType } from 'src/common/enum/order';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import moment from 'moment';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { CancelReturnService } from './cancel-return-order';
import { ReplacementType } from 'src/common/enum/replacement';

@Injectable()
export class RegenerateReturnService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
  ) {}

  async regenerateReturn(
    id: string,
    shopifyOrderId: string,
  ): Promise<{
    success: boolean;
    message: string;
    newAwbNumber?: string;
    previousAwbNumber?: string;
    errorSource?: string;
  }> {
    posLogger.info('RegenerateReturnService', 'regenerateReturn', {
      message: 'Starting return regeneration process',
      id,
      shopifyOrderId,
    });

    const cancelReturnService = new CancelReturnService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
    );

    let previousAwbNumber: string;

    try {
      // 1. First cancel the existing return
      const cancelResult = await cancelReturnService.cancelReturn(
        id,
        shopifyOrderId,
      );

      if (!cancelResult.success) {
        throw new EnhancedError(
          `Failed to cancel existing return: ${cancelResult.message}`,
          400,
          cancelResult.errorSource || 'CANCEL_OPERATION',
        );
      }

      previousAwbNumber = cancelResult.data?.awbNumber;

      // 2. Get replacement order details to re-initiate the return
      const replacementOrder = await this.getReplacementOrderDetails(
        id,
        shopifyOrderId,
      );

      // 3. Prepare and initiate the new return
      let newReturnResult;

      try {
        newReturnResult = await this.initiateNewReturn(replacementOrder);
      } catch (error) {
        throw new EnhancedError(
          `EasyEcom API failed during regeneration: ${error.message}`,
          500,
          'EASYECOM_API', // ✅ NEW - Specific error source
        );
      }

      // 4. Update the replacement order status
      await this.updateReplacementOrderStatus(
        id,
        shopifyOrderId,
        newReturnResult.awbNumber,
        newReturnResult.creditNoteId,
      );

      // 5. Call the serverless OMS to inform about the new AWB

      try {
        await this.updateServerlessOMS(
          shopifyOrderId,
          replacementOrder.requestId,
          previousAwbNumber,
          newReturnResult.awbNumber,
          replacementOrder.orderType,
        );
      } catch (error) {
        throw new EnhancedError(
          `Serverless OMS update failed: ${error.message}`,
          500,
          'SERVERLESS_OMS',
        );
      }

      return {
        success: true,
        message: 'Return successfully regenerated',
        newAwbNumber: newReturnResult.awbNumber,
        previousAwbNumber,
      };
    } catch (error) {
      posLogger.error('RegenerateReturnService', 'regenerateReturn', {
        message: 'Error regenerating return',
        error: error.message || error,
        source: error.source,
        id,
        shopifyOrderId,
      });

      return {
        success: false,
        message:
          error.message || 'An error occurred while regenerating the return',
        errorSource: error.source || 'INTERNAL_SERVER',
        previousAwbNumber,
      };
    }
  }

  private async getReplacementOrderDetails(id: string, shopifyOrderId: string) {
    try {
      const getReplacementOrderHandler = new GetReplacementOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const replacementOrder =
        await getReplacementOrderHandler.getReplacementOrder(
          id,
          shopifyOrderId,
        );

      if (replacementOrder.replacementType === ReplacementType.PART) {
        throw new CustomError(
          'Cannot regenerate return for part replacement order types',
          400,
        );
      }

      if (!replacementOrder.creditNoteId || !replacementOrder.requestId) {
        throw new CustomError(
          'Missing creditNoteId or requestId in replacement order',
          400,
        );
      }

      posLogger.info('RegenerateReturnService', 'getReplacementOrderDetails', {
        message: 'Successfully retrieved replacement order details',
        id,
        shopifyOrderId,
        creditNoteId: replacementOrder.creditNoteId,
        locationKey: replacementOrder.locationKey,
      });

      return replacementOrder;
    } catch (error) {
      posLogger.error('RegenerateReturnService', 'getReplacementOrderDetails', {
        message: 'Error retrieving replacement order details',
        error: error.message || error,
        id,
        shopifyOrderId,
      });

      throw error;
    }
  }

  private async initiateNewReturn(replacementOrder: any) {
    try {
      // Create EasyEcom service instance
      const easyEcomService = new EasyEcomService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      // Extract relevant info from replacementOrder
      const {
        locationKey,
        referenceCode,
        replacedProduct = {},
        primaryReason: reason,
      } = replacementOrder;

      if (!locationKey || !referenceCode || !replacedProduct || !reason) {
        throw new CustomError(
          'Missing required fields in replacement order for regenrating AWB',
          400,
        );
      }

      const { sku, quantity } = replacedProduct;

      // Prepare return payload
      const payload = {
        reference_code: referenceCode,
        return_reason: reason,
        items: [
          {
            parent_sku: `${sku}`,
            child_sku: `${sku}`,
            return_quantity: `${quantity}`,
          },
        ],
      };

      posLogger.info('RegenerateReturnService', 'initiateNewReturn', {
        message: 'Initiating new return with EasyEcom',
        payload,
        locationKey,
      });

      // Call EasyEcom API to initiate the return
      const result = await easyEcomService.initiateReturn(locationKey, payload);
      console.log('result from initiate return>>>', result);
      if (!result || !result.awbNumber) {
        throw new CustomError(
          result.message || 'Failed to initiate new return in EasyEcom',
          400,
        );
      }

      // Extract AWB number from the response

      posLogger.info('RegenerateReturnService', 'initiateNewReturn', {
        message: 'Successfully initiated new return',
        awbNumber: result.awbNumber,
      });

      return {
        success: true,
        awbNumber: result.awbNumber,
        creditNoteId: result.creditNoteId,
      };
    } catch (error) {
      console.log('error in initiate return>>>', error);
      posLogger.error('RegenerateReturnService', 'initiateNewReturn', {
        message: 'Error initiating new return',
        error: error.message || error,
      });

      throw error;
    }
  }

  private async updateReplacementOrderStatus(
    id: string,
    shopifyOrderId: string,
    newAwbNumber: string,
    creditNoteId: string,
  ) {
    try {
      const REPLACEMENT_ORDER_TABLE =
        await this.configParameters.getReplacementOrderTableName();

      const updatePayload = {
        status: OrderStatus.AWB_REINITIALIZED,
        updatedAt: moment().toISOString(),
        awbNumber: newAwbNumber,
      };

      const updateCommand = new UpdateCommand({
        TableName: REPLACEMENT_ORDER_TABLE,
        Key: {
          shopifyOrderId,
          id,
        },
        UpdateExpression:
          'SET #status = :status, #updatedAt = :updatedAt, #awbNumber = :awbNumber , #creditNoteId = :creditNoteId',
        ExpressionAttributeNames: {
          '#status': 'status',
          '#updatedAt': 'updatedAt',
          '#awbNumber': 'awbNumber',
          '#creditNoteId': 'creditNoteId',
        },
        ExpressionAttributeValues: {
          ':status': updatePayload.status,
          ':updatedAt': updatePayload.updatedAt,
          ':awbNumber': updatePayload.awbNumber,
          ':creditNoteId': creditNoteId,
        },
      });

      await this.docClient.updateItem(updateCommand);

      posLogger.info(
        'RegenerateReturnService',
        'updateReplacementOrderStatus',
        {
          message: 'Successfully updated replacement order status',
          id,
          shopifyOrderId,
          newStatus: updatePayload.status,
        },
      );

      return true;
    } catch (error) {
      posLogger.error(
        'RegenerateReturnService',
        'updateReplacementOrderStatus',
        {
          message: 'Error updating replacement order status',
          error: error.message || error,
          id,
          shopifyOrderId,
        },
      );

      throw error;
    }
  }

  private async updateServerlessOMS(
    orderId: string,
    requestId: string,
    oldAwbNumber: string,
    newAwbNumber: string,
    orderType: string,
  ) {
    try {
      const {
        Parameter: { Value: baseURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/url`,
          WithDecryption: true,
        }),
      );

      const {
        Parameter: { Value: apiKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/apiKey`,
          WithDecryption: true,
        }),
      );

      const finalURL = `${baseURL}/pos/cancel_awb`;

      let payloadForRegenerateAWB = {
        orderId,
        cancel_awbNo: oldAwbNumber,
        awbNo: newAwbNumber,
        requestType: orderType,
      };

      console.log('payloadForRegenerateAWB', payloadForRegenerateAWB);
      const response = await fetch(finalURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
        },
        body: JSON.stringify(payloadForRegenerateAWB),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to update serverless OMS: ${response.statusText}`,
        );
      }

      posLogger.info('RegenerateReturnService', 'updateServerlessOMS', {
        message: 'Successfully updated serverless OMS with new AWB',
        orderId,
        requestId,
        newAwbNumber,
      });

      return true;
    } catch (error) {
      posLogger.error('RegenerateReturnService', 'updateServerlessOMS', {
        message: 'Error updating serverless OMS',
        error: error.message || error,
        orderId,
        requestId,
      });

      throw error;
    }
  }
}
