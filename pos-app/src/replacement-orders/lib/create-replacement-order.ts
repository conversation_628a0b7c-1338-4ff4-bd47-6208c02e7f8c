import moment from 'moment';
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { ValidateProducts } from 'src/common/helper/validate-products';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { v4 as uuid } from 'uuid';
import { AppShopify } from 'src/common/shopify/shopify';
import { CreateReplacementOrderInput } from '../dto/create-replacement-order.input';
import { ReplacementOrderData } from '../entities/replacement-order.entity';
import { OrderStatus } from 'src/common/enum/order';
import { GetSkuPrice } from 'src/sku-price-master/lib/get-sku-price';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { extractProductAndVariantId } from '../helper/getProductIds';
// import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';

export class CreateReplacementOrder {
  // private locationKey: string | null = null;
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}

  async getOrder(url: string, apiKey: string) {
    try {
      const createPriceRuleShopify = await fetch(url, {
        method: 'GET',
        headers: {
          'x-api-key': apiKey,
          'Content-Type': 'application/json',
        },
      });
      const response = await createPriceRuleShopify.json();
      return response?.data;
    } catch (error) {
      posLogger.error('CreateReplacementOrder', 'getOrder', error.message);
      return error?.response;
    }
  }

  // async initLocationKey(): Promise<void> {
  //   if (this.locationKey) return; // already initialized

  //   try {
  //     const stackName = this.configService.get('STACK_NAME');
  //     const parameterName = `/${stackName}/easyecom/locationKey`;

  //     const {
  //       Parameter: { Value },
  //     } = await this.ssmClient.getSSMParamByKey(
  //       new GetParameterCommand({
  //         Name: parameterName,
  //         WithDecryption: true,
  //       }),
  //     );

  //     // this.locationKey = Value;
  //     this.locationKey = 'ne10776308481';
  //     posLogger.info('CreateReplacement', 'initLocationKey', {
  //       locationKey: this.locationKey,
  //     });
  //   } catch (error) {
  //     posLogger.error('CreateReplacement', 'initLocationKey', {
  //       error: error.message || error,
  //     });
  //     this.locationKey = 'ne10776308481';
  //   }
  // }
  async createReplacementOrderForShopify(
    createReplacementOrderInput: CreateReplacementOrderInput,
  ) {
    try {
      const { shopifyOrderId } = createReplacementOrderInput;
      const {
        Parameter: { Value: baseURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/url`,
          WithDecryption: true,
        }),
      );
      const {
        Parameter: { Value: apiKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/apiKey`,
          WithDecryption: true,
        }),
      );
      const finalURL = `${baseURL}/order/orderInfo?orderId=${shopifyOrderId}`;
      console.log('finalURL', finalURL);
      console.log('apiKey', apiKey);
      const order = await this.getOrder(finalURL, apiKey);

      if (Array.isArray(order)) {
        const [shopifyOrderData] = order;
        return shopifyOrderData;
      }

      return null;
    } catch (error) {
      throw error;
    }
  }

  // async createReplacementOrderForEE(
  //   createReplacementOrderInput: CreateReplacementOrderInput,
  // ) {
  //   try {
  //     const { shopifyOrderId } = createReplacementOrderInput;

  //     const order = await this.getOrder(
  //       `https://gybqro5fdj.execute-api.ap-south-1.amazonaws.com/dev/replacement/orderInfo?orderId=${shopifyOrderId}`,
  //       'PpuHE5OWtR3xYmZTZWRBwaa3ogMvAumX8kWc4Alm',
  //     );

  //     if (Array.isArray(order)) {
  //       const [shopifyOrderData] = order;
  //       if (shopifyOrderData) {
  //         const {
  //           order_items,
  //           billing_address_2,
  //           billing_address_1,
  //           pin_code,
  //           state,
  //           city,
  //           billing_state,
  //           order_date,
  //           email,
  //           address_line_1,
  //           address_line_2,
  //           billing_pin_code,
  //           billing_country,
  //           customer_name,
  //           contact_num,
  //           country,
  //           billing_city,
  //         } = shopifyOrderData;
  //         return {
  //           created_at: order_date,
  //           billing_address: {
  //             address1: billing_address_1,
  //             address2: billing_address_2,
  //             city: billing_city,
  //             province: billing_state,
  //             country: billing_country,
  //             zip: billing_pin_code,
  //           },
  //           shipping_address: {
  //             address1: address_line_1,
  //             address2: address_line_2,
  //             city: city,
  //             province: state,
  //             country: country,
  //             zip: pin_code,
  //           },
  //           customerphone: contact_num,
  //           customer: {
  //             first_name: customer_name,
  //             last_name: '',
  //             email,
  //             id: '',
  //           },
  //           productsData: order_items.map(
  //             ({ productName, item_quantity, selling_price, ean }) => ({
  //               product_id: '',
  //               variant_id: '',
  //               ProductName: productName,
  //               ProductPrice: selling_price,
  //               image: null,
  //               sku: ean,
  //               quantity: item_quantity,
  //             }),
  //           ),
  //         };
  //       }
  //     }

  //     return null;
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async createReplacementOrder(
    createReplacementOrderInput: CreateReplacementOrderInput,
  ): Promise<ReplacementOrderData> {
    posLogger.info('replacementOrder', 'createReplacementOrder', {
      input: createReplacementOrderInput,
    });
    console.log('in creating replacmeent order');
    try {
      const [REPLACEMENT_ORDER_TABLE] = await Promise.all([
        await this.configParameters.getReplacementOrderTableName(),
      ]);

      const { shopifyOrderId, replacedProduct } = createReplacementOrderInput;
      console.log(
        'createReplacementOrderInput',
        JSON.stringify(replacedProduct),
      );
      let shopifyOrderData = null;

      shopifyOrderData = await this.createReplacementOrderForShopify(
        createReplacementOrderInput,
      );

      if (!shopifyOrderData) throw new CustomError('Order not found', 400);
      //ecom api changes
      const {
        productsData,
        created_at,
        billing_address: {
          address1: line1,
          address2: line2,
          city,
          province: state,
          country,
          zip: pinCode,
        },
        price_before_discount,
        BankDiscount: bankDiscount = 0,
        shipping_address: {
          address1: shippingLine1,
          address2: shippingLine2,
          city: shippingCity,
          province: shippingState,
          country: shippingCountry,
          zip: shippingPinCode,
        },
        customerphone,
        easyecom,
        customer: { first_name, last_name, email, id: customerId },
      } = shopifyOrderData || {};

      console.log('shopifyOrderData', shopifyOrderData);
      if (!easyecom) {
        posLogger.error('replacementOrder', 'createReplacementOrder', {
          error: 'Suborder not found in the order',
        });
        throw new CustomError(
          'Item not delivered yet cannot book return/replacement for this item',
          400,
        );
      }
      if (!productsData || !productsData.length)
        throw new CustomError('Order Products not found', 400);

      let replacedItemData = replacedProduct?.variantId
        ? productsData.find((e) => e.variant_id == replacedProduct.variantId)
        : null;
      console.log('replacedItemData?.sku', replacedItemData);
      let product, variant;
      const handler = new ValidateProducts(
        this.configService,
        this.ssmClient,
        this.docClient,
        this.configParameters,
      );

      if (!replacedItemData) {
        let replacedItemSKU = null;
        for (const e of productsData || []) {
          if (e.sku === replacedProduct.sku && e.custom_fields?.length) {
            replacedItemData = e;
            console.log('replacedItemData?.sku', replacedItemData);
            console.log('innnside matched', e, e.custom_fields);
            const customField = e.custom_fields.find(
              (field) => field.field_name === 'Notes',
            );
            console.log('notes field', customField);
            if (customField) {
              const { productId, variantId } =
                extractProductAndVariantId(customField);
              console.log('parssedddd ids', productId, variantId);
              replacedItemSKU = customField;
              [{ product, variant }] = await handler.validateProducts(
                [
                  {
                    productId: productId.toString(),
                    variantId: variantId.toString(),
                  },
                ],
                false,
                false,
              );
            }
          }
        }

        if (!replacedItemSKU) {
          posLogger.error('replacementOrder', 'createReplacementOrder', {
            error: 'Replaced item not found in the order',
          });
          throw new CustomError('Replaced product not found', 400);
        }
      }

      const result = easyecom.find((record) =>
        record?.suborders.some(
          (sub) =>
            sub?.marketplace_sku === replacedProduct?.sku ||
            sub?.AccountingSku === replacedProduct?.sku ||
            sub?.model_no === replacedProduct?.sku ||
            sub?.ean === replacedProduct?.sku,
        ),
      );

      console.log('result', JSON.stringify(result, null, 2));

      if (!result || result?.invoice_status !== 'delivered') {
        posLogger.error('replacementOrder', 'createReplacementOrder', {
          error: 'Suborder not found in the order',
        });
        throw new CustomError(
          'Item not delivered yet cannot book return/replacement for this item',
          400,
        );
      }

      const suborder = result?.suborders.find(
        (sub) =>
          sub?.marketplace_sku === replacedProduct?.sku ||
          sub?.AccountingSku === replacedProduct?.sku ||
          sub?.model_no === replacedProduct?.sku ||
          sub?.ean === replacedProduct?.sku,
      );

      console.log('suborder', JSON.stringify(suborder, null, 2));
      console.log('suborder', suborder?.sku_type);

      const skuType = suborder?.sku_type?.toUpperCase() || '';
      console.log('skuType', skuType);
      // if (skuType === 'COMBO') {
      //   await this.initLocationKey();
      //   console.log('in combo');
      //   const easyEcomHandler = new EasyEcomService(
      //     this.configService,
      //     this.docClient,
      //     this.ssmClient,
      //     this.configParameters,
      //   );

      //   const orderDetails = await easyEcomHandler.getOrderDetails(
      //     shopifyOrderId,
      //     this.locationKey,
      //     true,
      //   );

      //   console.log(
      //     'orderDetails in creation of replacement order',
      //     JSON.stringify(orderDetails, null, 2),
      //   );

      // }

      if (replacedItemData && replacedProduct.variantId) {
        [{ product, variant }] = await handler.validateProducts(
          [
            {
              productId: replacedItemData.product_id.toString(),
              variantId: replacedProduct.variantId.toString(),
            },
          ],
          false,
          false,
        );
      }

      const { product_type: productType, metafields } = product || {
        product_type: '',
        metafields: [],
      };

      const { variantTitle } = variant;

      const skuPriceHandler = new GetSkuPrice(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      console.log('in create replacement order');

      const { replacementWindow = 100, replacementShippingCost = 1000 } =
        (await skuPriceHandler.getSkuPrice(replacedProduct?.sku, false)) || {};

      const isRazorpay = shopifyOrderData?.note_attributes?.length
        ? shopifyOrderData?.note_attributes.some(
            (note) =>
              note.name.startsWith('Transaction Mode') &&
              note.value === 'RAZORPAY',
          )
        : false;
      const transactionModes = shopifyOrderData?.note_attributes
        ?.filter((attr) => attr.name.includes('Transaction Mode'))
        ?.map((attr) => attr.value);

      const transactionIDs = shopifyOrderData?.note_attributes
        ?.filter((attr) => attr.name.includes('TransactionIDs'))
        ?.map((attr) => attr.value);

      // Creating new ReplacementOrder
      console.log('replacedItemData?.properties', replacedItemData?.properties);
      const data: ReplacementOrderData = {
        ...createReplacementOrderInput,
        id: uuid(),
        customerId: customerId || '',
        shopifyOrderId,
        billingAddress: {
          line1,
          line2,
          city,
          state,
          country,
          pinCode,
        },
        shippingAddress: {
          line1: shippingLine1,
          line2: shippingLine2,
          city: shippingCity,
          state: shippingState,
          country: shippingCountry,
          pinCode: shippingPinCode,
        },
        customer: {
          firstName: first_name || '',
          lastName: last_name || '',
          email,
          phone: customerphone,
        },
        replacedProduct: {
          ...replacedProduct,
          productType,
          metafields,
          properties: replacedItemData?.properties,
          price: Number(replacedItemData?.ProductPrice),
          title: replacedItemData?.ProductName,
          variantTitle,
          image: replacedItemData?.image,
          sku: suborder?.sku,

          finalItemPrice:
            Number(replacedItemData?.ProductPrice) *
            Number(replacedProduct.quantity),
          itemDiscount: (replacedItemData?.discount_allocations || []).reduce(
            (acc, item) => {
              const totalAmount = Number(item?.amount) || 0;
              const totalQuantity = Number(replacedItemData?.quantity) || 1;
              const perUnitDiscount =
                (totalAmount / totalQuantity) * replacedProduct?.quantity;
              return acc + Math.round(perUnitDiscount);
            },
            0,
          ),

          bankDiscount: bankDiscount
            ? Math.round(
                ((Number(replacedItemData?.ProductPrice) *
                  Number(replacedProduct?.quantity)) /
                  Number(price_before_discount)) *
                  bankDiscount,
              )
            : 0,
        },
        inReplaceableWindow:
          moment().diff(created_at, 'days') <= Number(replacementWindow),
        shippingCost: Number(replacementShippingCost || 0),
        status: OrderStatus.PENDING,
        orderCreatedAt: moment(created_at).toISOString(),
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
        orderTotal: Number(price_before_discount),
        bankDiscount: Number(bankDiscount || 0),
        referenceCode: suborder?.reference_code || null,
        locationKey: result?.location_key || null,
        isRazorpay,
        originalPaymentMode: transactionModes || [],
        transactionIDs: transactionIDs || [],
      };
      console.log(
        'API E ECOM',
        suborder,
        result,
        suborder?.reference_code,
        result?.location_key,
      );
      console.log('Table update payload', data);
      const replacementOrderCommand = new PutCommand({
        TableName: REPLACEMENT_ORDER_TABLE,
        Item: data,
        ConditionExpression: 'attribute_not_exists(id)',
      });
      await this.docClient.createItem(replacementOrderCommand);

      posLogger.info(
        'replacementOrder',
        'createReplacementOrder',
        `replacementOrder created ${data.id}`,
      );

      return data;
    } catch (error) {
      throw error;
    }
  }
}
