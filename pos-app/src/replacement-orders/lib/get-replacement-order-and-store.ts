import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { TemplateType } from 'src/common/enum/template-type';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
import { GetReplacementOrder } from './get-replacement-order';
import { ReplacementOrderData } from '../entities/replacement-order.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

export class GetReplacementOrderAndStoreAndSendPdf {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
  ) {}

  async getReplacementOrderAndStoreAndSendPdf(
    id: string,
    shopifyOrderId: string,
    type: string,
  ) {
    const getReplacementOrderHandler = new GetReplacementOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const order: ReplacementOrderData =
      await getReplacementOrderHandler.getReplacementOrder(id, shopifyOrderId);

    const { customer } = order;

    if (!customer?.email && type == 'EMAIL') {
      throw new CustomError('Cannot find customer email', 404);
    }

    if (!customer?.phone && type == 'WHATSAPP') {
      throw new CustomError('Cannot find customer phone number', 404);
    }

    const pdfService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
      order,
    );
    const message = await pdfService.generateAndSendFile(
      TemplateType.REPLACEMENT_ORDER,
      'application/pdf',
      'pdf',
      type,
    );
    return message;
  }
}
