import { Test, TestingModule } from '@nestjs/testing';
import { ReplacementOrdersResolver } from './replacement-orders.resolver';
import { ReplacementOrdersService } from './replacement-orders.service';

describe('ReplacementOrdersResolver', () => {
  let resolver: ReplacementOrdersResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ReplacementOrdersResolver, ReplacementOrdersService],
    }).compile();

    resolver = module.get<ReplacementOrdersResolver>(ReplacementOrdersResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
