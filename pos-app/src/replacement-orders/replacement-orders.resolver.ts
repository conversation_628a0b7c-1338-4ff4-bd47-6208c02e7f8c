import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { ReplacementOrdersService } from './replacement-orders.service';
import {
  CancelInitiatedReturn,
  PaymentPendingReplacementOrders,
  RegenerateReturn,
  ReplacementOrder,
} from './entities/replacement-order.entity';
import { CreateReplacementOrderInput } from './dto/create-replacement-order.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import {
  CustomError,
  ErrorHandler,
} from 'src/common/response/errorHandler/error.handler';
import { HttpStatus } from '@nestjs/common';
import { UpdateReplacementOrderProductInput } from './dto/update-replacement-order-product.input';
import { ValidateReplacementOrderInput } from './dto/validate-amount-replacement-order.input';
import { Response } from 'src/products/entities/product.entity';
import { UpdateRefundPriceInput } from './dto/update-refund-price.input';
import { ListPaymentPendingOrdersInput } from './dto/list-payment-pending-orders-input';
import { ExportReplacementOrdersInput } from './dto/export-post-order';

@Resolver(() => ReplacementOrder)
export class ReplacementOrdersResolver {
  constructor(
    private readonly replacementOrdersService: ReplacementOrdersService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => ReplacementOrder, { name: 'createReplacementOrder' })
  async createReplacementOrder(
    @Args('createReplacementOrderInput')
    createReplacementOrderInput: CreateReplacementOrderInput,
  ) {
    try {
      const data = await this.replacementOrdersService.create(
        createReplacementOrderInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create replacement order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => ReplacementOrder, { name: 'updateReplacementOrderProduct' })
  async updateReplacementOrderProduct(
    @Args('updateReplacementOrderProductInput')
    updateReplacementOrderProductInput: UpdateReplacementOrderProductInput,
  ) {
    try {
      const data =
        await this.replacementOrdersService.updateReplacementOrderProduct(
          updateReplacementOrderProductInput,
        );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update replacement order product',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Response, { name: 'replacementOrderPdf' })
  async replacementOrderPdf(
    @Args('id', { type: () => String })
    id: string,
    @Args('shopifyOrderId', { type: () => String })
    shopifyOrderId: string,
    @Args('type', { type: () => String })
    type: string,
  ) {
    try {
      if (!['EMAIL', 'DOWNLOAD', 'WHATSAPP'].includes(type)) {
        throw new CustomError(
          `Type should be either EMAIL or DOWNLOAD or WHATSAPP`,
          400,
        );
      }

      const message =
        await this.replacementOrdersService.sendReplacementOrderPdf(
          id,
          shopifyOrderId,
          type,
        );

      return {
        message,
        status: 200,
        success: true,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to downloading order pdf',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => ReplacementOrder, { name: 'getReplacementOrder' })
  async findOne(
    @Args('id', { type: () => String }) id: string,
    @Args('shopifyOrderId', { type: () => String }) shopifyOrderId: string,
  ) {
    try {
      const data = await this.replacementOrdersService.findOne(
        id,
        shopifyOrderId,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get replacement order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => ReplacementOrder, { name: 'validateAmountReplacementOrder' })
  async validateAmountReplacementOrder(
    @Args('validateAmountReplacementOrderInput')
    validateAmountReplacementOrder: ValidateReplacementOrderInput,
  ) {
    try {
      const data =
        await this.replacementOrdersService.validateAmountReplacementOrder(
          validateAmountReplacementOrder,
        );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to validate amount replacement order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => ReplacementOrder, { name: 'createFinalOrder' })
  async createFinalOrder(
    @Args('id', { type: () => String }) id: string,
    @Args('shopifyOrderId', { type: () => String }) shopifyOrderId: string,
    @Args('AWBNumber', { type: () => String, nullable: true })
    AWBNumber?: string,
    @Args('AWBApprovedBy', { type: () => String, nullable: true })
    AWBApprovedBy?: string,
    @Args('newMinDate', { type: () => String, nullable: true })
    newMinDate?: string,
    @Args('newMaxDate', { type: () => String, nullable: true })
    newMaxDate?: string,
  ) {
    console.log('newMinDate', newMinDate, 'newMaxDate', newMaxDate);
    try {
      const data = await this.replacementOrdersService.createFinalOrder(
        id,
        shopifyOrderId,
        AWBNumber,
        AWBApprovedBy,
        newMinDate,
        newMaxDate,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to validate amount replacement order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => ReplacementOrder, { name: 'confirmReplacementOrder' })
  async confirmReplacementOrder(
    @Args('id', { type: () => String })
    id: string,
    @Args('AWBNumber', { type: () => String, nullable: true })
    AWBNumber?: string,
    @Args('AWBApprovedBy', { type: () => String, nullable: true })
    AWBApprovedBy?: string,
    @Args('newMinDate', { type: () => String, nullable: true })
    newMinDate?: string,
    @Args('newMaxDate', { type: () => String, nullable: true })
    newMaxDate?: string,
  ) {
      console.log('newMinDate', newMinDate, 'newMaxDate', newMaxDate);
    try {
      const data = await this.replacementOrdersService.confirmReplacementOrder(
        id,
        AWBNumber,
        AWBApprovedBy,
        newMinDate,
        newMaxDate,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create replacement order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => ReplacementOrder, { name: 'updateRefundPrice' })
  async updateRefundPrice(
    @Args('updateRefundPriceInput')
    updateRefundPriceInput: UpdateRefundPriceInput,
  ) {
    try {
      const data = await this.replacementOrdersService.updateRefundPrice(
        updateRefundPriceInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update refund price',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Query(() => PaymentPendingReplacementOrders, { name: 'replacementOrders' })
  async findAll(
    @Args('listPaymentPendingOrdersInput', { nullable: true })
    listPaymentPendingOrdersInput: ListPaymentPendingOrdersInput,
  ) {
    try {
      const { data, count } = await this.replacementOrdersService.findAll(
        listPaymentPendingOrdersInput,
      );
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list quotations',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => CancelInitiatedReturn, { name: 'cancelInitiatedReturn' })
  async cancelInitiatedreturn(
    @Args('id', { type: () => String }) id: string,
    @Args('shopifyOrderId', { type: () => String }) shopifyOrderId: string,
  ) {
    try {
      const result = await this.replacementOrdersService.cancelInitiatedReturn(
        id,
        shopifyOrderId,
      );

      return {
        success: result.success,
        message: result.message,
        status: result.success ? 200 : 400,
        cancelledAwbNumber: result.data?.awbNumber,
        errorSource: result.errorSource,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to cancel initiated return',
        status: error.statusCode || 500,
        errorSource: error.source || 'INTERNAL_SERVER',
      };
    }
  }

  @Mutation(() => RegenerateReturn, { name: 'regenerateReturn' })
  async regenerateReturn(
    @Args('id', { type: () => String }) id: string,
    @Args('shopifyOrderId', { type: () => String }) shopifyOrderId: string,
  ) {
    try {
      const result = await this.replacementOrdersService.regenerateReturn(
        id,
        shopifyOrderId,
      );

      return {
        success: result.success,
        message: result.message,
        status: result.success ? 200 : 400,
        newAwbNumber: result.newAwbNumber,
        errorSource: result.errorSource,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to regenerate return',
        status: error.statusCode || 500,
        errorSource: error.source || 'INTERNAL_SERVER',
      };
    }
  }

  @Query(() => Response, { name: 'exportReplacementOrders' })
  async exportReplacementOrders(
    @Args('email', { type: () => String })
    email: string,
    @Args('listReplacementInput', { nullable: true })
    listReplacementInput: ExportReplacementOrdersInput,
  ) {
    try {
      const message =
        await this.replacementOrdersService.exportReplacementOrders(
          email,
          listReplacementInput,
        );

      return {
        message,
        status: 200,
        success: true,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to export replacement orders',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  // @Query(() => [ReplacementOrder], { name: 'replacementOrders' })
  // async findAll() {
  //   try {
  //     const { data, count } =
  //       await this.replacementOrdersService.findAll(listQuotationInput);
  //     const res = this.successHandler.getSuccessResponse({
  //       data,
  //       code: 200,
  //     });
  //     return { ...res, count };
  //   } catch (error) {
  //     const errorResponse = this.errorHandler.getErrorResponse({
  //       message: error.message || 'Failed to list quotations',
  //       code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
  //     });
  //     return errorResponse;
  //   }
  // }

  // @Query(() => ReplacementOrder, { name: 'replacementOrder' })
  // findOne(@Args('id', { type: () => Int }) id: number) {
  //   return this.replacementOrdersService.findOne(id);
  // }

  // @Mutation(() => ReplacementOrder)
  // async updateReplacementOrder(
  //   @Args('updateReplacementOrderInput')
  //   updateReplacementOrderInput: UpdateReplacementOrderInput,
  // ) {
  //   try {
  //     const data = await this.replacementOrdersService.update(
  //       updateReplacementOrderInput,
  //     );
  //     return this.successHandler.getSuccessResponse({
  //       data,
  //       code: 201,
  //     });
  //   } catch (error) {
  //     const errorResponse = this.errorHandler.getErrorResponse({
  //       message: error.message || 'Failed to update replacement order',
  //       code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
  //     });
  //     return errorResponse;
  //   }
  // }

  // @Mutation(() => ReplacementOrder)
  // removeReplacementOrder(@Args('id', { type: () => Int }) id: number) {
  //   return this.replacementOrdersService.remove(id);
  // }
}
