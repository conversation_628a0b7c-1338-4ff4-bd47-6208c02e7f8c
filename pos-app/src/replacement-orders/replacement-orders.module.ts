import { Module } from '@nestjs/common';
import { ReplacementOrdersService } from './replacement-orders.service';
import { ReplacementOrdersResolver } from './replacement-orders.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { ShopifyModule } from 'src/common/shopify/shopify.module';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';
import { RequestRefundService } from 'src/refund-details/lib/refund-request-handler';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
    ShopifyModule,
    S3ClientModule,
  ],
  providers: [
    ReplacementOrdersResolver,
    ReplacementOrdersService,
    SuccessHandler,
    ErrorHandler,

  ],
})
export class ReplacementOrdersModule {}
