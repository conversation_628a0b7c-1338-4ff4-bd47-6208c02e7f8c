import { InputType, Field, Int } from '@nestjs/graphql';
import { CustomDataInput } from 'src/carts/dto/create-cart.input';
import { OrderType } from 'src/common/enum/order';
import { ReplacementType } from 'src/common/enum/replacement';

@InputType()
export class ReplacedProductInput {
  @Field(() => String, { nullable: false })
  productId: string;

  @Field(() => String, { nullable: true })
  variantId?: string;

  @Field(() => String, { nullable: true })
  sku?: string;

  @Field(() => Int, { nullable: false })
  quantity: number;

  @Field(() => CustomDataInput, { nullable: true })
  customData?: CustomDataInput;
}

@InputType()
export class CreateReplacementOrderInput {
  @Field(() => String)
  shopifyOrderId: string;

  @Field(() => String)
  ticketId: string;

  @Field(() => OrderType, { defaultValue: OrderType.POS })
  orderType: OrderType;

  @Field(() => ReplacedProductInput)
  replacedProduct: ReplacedProductInput;

  @Field(() => ReplacementType, { nullable: true })
  replacementType?: ReplacementType;

  // @Field(() => String, { 
  //   nullable: true,
  //   description: 'Complete reason text for the replacement request.'
  // })
  // reason?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Primary reason for the replacement',
  })
  primaryReason?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Secondary reason or sub-reason for the replacement',
  })
  secondaryReason?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Department related to the replacement request',
  })
  department?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Agent name, required when department is AGENT',
  })
  agentName?: string;

  @Field(() => String, { 
    nullable: true,
    description: 'Additional comments or notes about the replacement' 
  })
  additionalComments?: string;

  @Field(() => String, { nullable: true })
  requestId?: string;
}