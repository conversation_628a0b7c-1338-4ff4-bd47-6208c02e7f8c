import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class ExportReplacementOrdersInput {
  @Field(() => String, { nullable: true, description: 'Request type filter' })
  requestType?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Start date for filtering (YYYY-MM-DD)',
  })
  startDate?: string;

  @Field(() => String, {
    nullable: true,
    description: 'End date for filtering (YYYY-MM-DD)',
  })
  endDate?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Replacement type filter',
  })
  replacementType?: string;
}
