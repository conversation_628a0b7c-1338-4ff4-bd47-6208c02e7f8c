import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class LastEvaluatedKey {
  @Field(() => String)
  id: string;

  @Field(() => String)
  shopifyOrderId: string;
}

@InputType()
export class ListPaymentPendingOrdersInput {
  @Field(() => LastEvaluatedKey, { nullable: true })
  lastEvaluatedKey?: LastEvaluatedKey;

  @Field(() => String)
  size: string;

  @Field(() => String, { nullable: true })
  orderId?: string;

  @Field(() => String, { nullable: true })
  phoneNumber?: string;
}
