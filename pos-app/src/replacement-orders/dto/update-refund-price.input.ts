import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class UpdatedRefundInput {
  @Field(() => String, { nullable: false })
  approver: string;

  @Field(() => String, { nullable: false })
  reason: string;
}

@InputType()
export class UpdateRefundPriceInput {
  @Field(() => String)
  id: string;

  @Field(() => String)
  shopifyOrderId: string;

  @Field(() => UpdatedRefundInput, {
    description: 'Manually modified refund price reason',
    nullable: false,
  })
  updatedRefundPrice: UpdatedRefundInput;

  @Field(() => Number, {
    nullable: false,
    description: 'Refunded Price',
  })
  refundedPrice: number;
}
