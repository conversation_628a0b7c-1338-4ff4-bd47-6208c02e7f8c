import { InputType, Field } from '@nestjs/graphql';
import { CartProductInput } from 'src/carts/dto/create-cart.input';
import { PickupPriority, ReplacementType } from 'src/common/enum/replacement';
import { ReplacedProductInput } from './create-replacement-order.input';
import { OrderType } from 'src/common/enum/order';

@InputType()
export class UpdateReplacementOrderProductInput {
  @Field(() => String)
  id: string;

  @Field(() => String)
  shopifyOrderId: string;

  @Field(() => String, { nullable: true })
  ticketId?: string;

  @Field(() => OrderType, { nullable: true })
  orderType?: OrderType;

  @Field(() => ReplacedProductInput, { nullable: true })
  replacedProduct?: ReplacedProductInput;

  @Field(() => PickupPriority)
  pickupPriority: PickupPriority;

  @Field(() => ReplacementType, { nullable: true })
  replacementType?: ReplacementType;

  @Field(() => String, {
    nullable: true,
  })
  reason?: string;

  @Field(() => [CartProductInput])
  orderProducts: CartProductInput[];
}
