import { InputType, Field } from '@nestjs/graphql';
import {
  CartProductInput,
  CustomCodeInput,
} from 'src/carts/dto/create-cart.input';
import {
  PickupPriority,
  ReplaceableDiscountType,
  ReplacementOptions,
  ReplacementType,
} from 'src/common/enum/replacement';
import { ReplacedProductInput } from './create-replacement-order.input';
import { DeliveryType } from 'src/common/enum/delivery';
import { QuotationAddressInput } from 'src/quotations/dto/update-quotation.input';
import { OrderType } from 'src/common/enum/order';

@InputType()
export class ValidateReplacementOrderInput {
  @Field(() => String)
  id: string;

  @Field(() => String)
  shopifyOrderId: string;

  @Field(() => ReplacementOptions, { nullable: true })
  replacementOption?: ReplacementOptions;

  @Field(() => ReplacedProductInput, { nullable: true })
  replacedProduct?: ReplacedProductInput;

  @Field(() => PickupPriority, { nullable: true })
  pickupPriority?: PickupPriority;

  @Field(() => ReplacementType, { nullable: true })
  replacementType?: ReplacementType;

  @Field(() => String, {
    nullable: true,
  })
  reason?: string;

  @Field(() => [CartProductInput], { nullable: true })
  orderProducts?: CartProductInput[];

  @Field(() => ReplaceableDiscountType, { nullable: true })
  replaceableDiscountType?: ReplaceableDiscountType;

  @Field(() => CustomCodeInput, {
    nullable: true,
    description: 'Custom code for the quotation.',
  })
  customCode?: CustomCodeInput;

  @Field(() => Number, {
    nullable: true,
    description: 'Custom Discount Amount',
  })
  customDiscountAmount?: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Total amount',
  })
  totalAmount?: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Final Amount',
  })
  finalDiscountedAmount?: number;

  @Field(() => [CartProductInput], {
    nullable: true,
    description: 'List of products being quoted.',
  })
  accessories?: CartProductInput[];

  @Field(() => DeliveryType, {
    nullable: true,
    description: 'Delivery Type of the order product',
  })
  deliveryStatus?: DeliveryType;

  @Field(() => String, {
    nullable: true,
    description: 'Max Delivery date of the order product',
  })
  deliveryDate: string;

  @Field(() => String, {
    description: 'Min Delivery date of the order product',
    nullable: true,
  })
  minDeliveryDate: string;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Is Shipping charges.',
  })
  isShippingCharged: boolean;

  @Field(() => QuotationAddressInput, {
    nullable: true,
    description: 'Shipping Address.',
  })
  shippingAddress: QuotationAddressInput;

  @Field(() => QuotationAddressInput, {
    nullable: true,
    description: 'Billing Address.',
  })
  billingAddress: QuotationAddressInput;

  @Field(() => String, { nullable: true })
  ticketId?: string;

  @Field(() => OrderType, { nullable: true })
  orderType?: OrderType;
}
