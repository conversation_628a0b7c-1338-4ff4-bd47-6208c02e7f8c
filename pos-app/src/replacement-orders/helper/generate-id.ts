import { PickupPriority, ReplacementType } from 'src/common/enum/replacement';

// export const generateEECode = ({
//   id,
//   replacementType,
//   pickupPriority,
// }: {
//   id: string;
//   replacementType: string;
//   pickupPriority: string;
// }) =>
//   replacementType === ReplacementType.PART
//     ? `${id}_part_${Math.floor(Math.random() * 10000)}`
//     : pickupPriority === PickupPriority.DELIVERY_BEFORE_PICKUP
//       ? `${id}_RPCX_${Math.floor(Math.random() * 10000)}`
//       : `${id}_RP_${Math.floor(Math.random() * 10000)}`;

export const generateEECode = ({
  id,
  replacementType,
  pickupPriority,
}: {
  id: string;
  replacementType: string;
  pickupPriority: string;
}) => {
  // Generate unique suffix using timestamp - last 4 digits
  const timestamp = Date.now().toString();
  const uniqueSuffix = timestamp.substring(timestamp.length - 4);
  console.log("uniqueSuffix",uniqueSuffix)
  // Check if the order ID contains _RP or _RPCX, indicating it's already a replacement order
  const isReplacingReplacement = id.includes('_RP') || id.includes('_RPCX');
  
  if (replacementType === ReplacementType.PART) {
    console.log("part replacement id",`${id}_PRT${uniqueSuffix}`)
    return `${id}_PRT${uniqueSuffix}`;
  } else {
    if (pickupPriority === PickupPriority.DELIVERY_BEFORE_PICKUP) {
      console.log("delivery before pickup id",`${id}_RPCX${uniqueSuffix}`)
      return `${id}_RPCX${uniqueSuffix}`;
    } else {
      console.log("regular replacement id",isReplacingReplacement ? `${id}_RRP${uniqueSuffix}` : `${id}_RP${uniqueSuffix}`)
      return isReplacingReplacement ? `${id}_RRP${uniqueSuffix}` : `${id}_RP${uniqueSuffix}`;
    }
  }
};