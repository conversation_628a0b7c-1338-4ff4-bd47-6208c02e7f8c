import moment from 'moment';
import { getPickupPriorityLabel } from './get-pickup-priority';

export const freshDeskTicketFailMailTemplate = ({
  shopifyOrderId,
  eeRefNo,
  replacedProduct,
  newItems,
  finalDiscountedAmount,
  refundedPrice,
  pickupPriority,
  awbNumber,
  carrier,
  creditNoteId,
  locationKey,
  originalPaymentMode,
}) => {
  return `<table style="width:100%; border-collapse: collapse; font-family: Arial, sans-serif; font-size: 14px;">
    <tr style="background-color: #f2f2f2;">
        <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Field</th>
        <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Value</th>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Original Order ID</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${shopifyOrderId || '-'}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Replacement Order ID</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${eeRefNo || '-'}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Replacement Order Date</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss') || '-'}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Items being replaced</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${replacedProduct.title || '-'}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Quantity</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${replacedProduct.quantity || 0}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Original Item SKU ID</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${replacedProduct.sku || 0}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Original Item Value</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${replacedProduct?.finalItemPrice - replacedProduct?.itemDiscount - replacedProduct?.bankDiscount || 0}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">New Items</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${newItems}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Extra Amount Paid</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${finalDiscountedAmount > 0 ? finalDiscountedAmount : 'NA'}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Amount to be refunded</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${refundedPrice || 0}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Replacement Type</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${getPickupPriorityLabel(pickupPriority) || ''}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Return AWB Number</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${awbNumber || ''}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Courier Partner Name</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${carrier || ''}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Credit Note Id</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${creditNoteId || ''}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Warehouse</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${locationKey || ''}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Original Payment Mode</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${originalPaymentMode.join(', ') || ''}</td>
    </tr>
</table>
`;
};

export const returnTicketFailMailTemplate = ({
  shopifyOrderId,
  awbNumber,
  carrier,
  creditNoteId,
  locationKey,
  originalPaymentMode,
  refundedPrice,
  replacedProduct,
}) => {
  return `<table style="width:100%; border-collapse: collapse; font-family: Arial, sans-serif; font-size: 14px;">
    <tr style="background-color: #f2f2f2;">
        <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Field</th>
        <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Value</th>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Original Order ID</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${shopifyOrderId || '-'}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Return Order Date</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss') || '-'}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Items being returned</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${replacedProduct.title || '-'}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Return Quantity</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${replacedProduct.quantity || 0}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Original Item SKU ID</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${replacedProduct.sku || 0}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Original Item Value</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${replacedProduct?.finalItemPrice - replacedProduct?.itemDiscount - replacedProduct?.bankDiscount || 0}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Amount to be refunded</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${refundedPrice || 0}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Return AWB Number</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${awbNumber || ''}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Courier Partner Name</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${carrier || ''}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Credit Note Id</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${creditNoteId || ''}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Warehouse</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${locationKey || ''}</td>
    </tr>
    <tr>
        <td style="border: 1px solid #ddd; padding: 10px;">Original Payment Mode</td>
        <td style="border: 1px solid #ddd; padding: 10px;">${originalPaymentMode.join(', ') || ''}</td>
    </tr>
</table>
`;
};
