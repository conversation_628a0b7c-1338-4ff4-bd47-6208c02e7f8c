import { Injectable } from '@nestjs/common';
import { CreateReplacementOrderInput } from './dto/create-replacement-order.input';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';
import { CreateReplacementOrder } from './lib/create-replacement-order';
import { UpdateReplacementOrderProduct } from './lib/update-replacement-order-product';
import { UpdateReplacementOrderProductInput } from './dto/update-replacement-order-product.input';
import { GetReplacementOrder } from './lib/get-replacement-order';
import { ValidateReplacementOrderInput } from './dto/validate-amount-replacement-order.input';
import { ValidateReplacementOrder } from './lib/validate-amount-replacement-order';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { GetReplacementOrderAndStoreAndSendPdf } from './lib/get-replacement-order-and-store';
import { CreateFinalOrder } from './lib/create-final-order';
import { ConfirmReplacementOrder } from './lib/confirm-replacement-order';
import { UpdateRefundPriceInput } from './dto/update-refund-price.input';
import { UpdateRefundPrice } from './lib/update-refund-price';
import { ListPaymentPendingOrders } from './lib/payment-pending-orders';
import { ListPaymentPendingOrdersInput } from './dto/list-payment-pending-orders-input';
import { CancelReturnService } from './lib/cancel-return-order';
import { RegenerateReturnService } from './lib/regenerate-return-order';
import { ExportReplacementOrdersInput } from './dto/export-post-order';
import { ExportReplacementOrders } from './lib/export-replacement-orders';
// import { ListReplacementOrders } from './lib/list-replacement-orders';

@Injectable()
export class ReplacementOrdersService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {}

  async create(createReplacementOrderInput: CreateReplacementOrderInput) {
    const createHandler = new CreateReplacementOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
    );
    return await createHandler.createReplacementOrder(
      createReplacementOrderInput,
    );
  }

  async updateReplacementOrderProduct(
    updateReplacementOrderProductInput: UpdateReplacementOrderProductInput,
  ) {
    const updateReplacementOrderProductHandler =
      new UpdateReplacementOrderProduct(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
    return await updateReplacementOrderProductHandler.updateReplacementOrderProduct(
      updateReplacementOrderProductInput,
    );
  }

  async sendReplacementOrderPdf(
    id: string,
    shopifyOrderId: string,
    type: string,
  ) {
    const getHandler = new GetReplacementOrderAndStoreAndSendPdf(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );
    return await getHandler.getReplacementOrderAndStoreAndSendPdf(
      id,
      shopifyOrderId,
      type,
    );
  }

  async findOne(id: string, shopifyOrderId: string) {
    const getHandler = new GetReplacementOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await getHandler.getReplacementOrder(id, shopifyOrderId);
  }

  // async listReplacementOrders() {
  //   const getHandler = new ListReplacementOrders(
  //     this.configService,
  //     this.docClient,
  //     this.ssmClient,
  //     this.configParameters,
  //     this.s3Client
  //   );
  //   return await getHandler.listReplacementOrders();
  // }

  async createFinalOrder(
    id: string,
    shopifyOrderId: string,
    AWBNumber?: string,
    AWBApprovedBy?: string,
    newMinDate?: string,
    newMaxDate?: string,
  ) {
    const createHandler = new CreateFinalOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
    );
    return await createHandler.createFinalOrder(
      id,
      shopifyOrderId,
      AWBNumber,
      AWBApprovedBy,
      newMinDate,
      newMaxDate,
    );
  }

  async confirmReplacementOrder(
    id: string,
    AWBNumber?: string,
    AWBApprovedBy?: string,
    newMinDate?: string,
    newMaxDate?: string,
  ) {
    const confirmReplacementOrderHandler = new ConfirmReplacementOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
    );
    return await confirmReplacementOrderHandler.confirmReplacementOrder(
      id,
      AWBNumber,
      AWBApprovedBy,
      newMinDate,
      newMaxDate,
    );
  }

  async validateAmountReplacementOrder(
    validateAmountReplacementOrder: ValidateReplacementOrderInput,
  ) {
    const validateHandler = new ValidateReplacementOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await validateHandler.validateReplacementOrder(
      validateAmountReplacementOrder,
    );
  }

  async updateRefundPrice(updateRefundPriceInput: UpdateRefundPriceInput) {
    const updateHandler = new UpdateRefundPrice(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await updateHandler.updateRefundPrice(updateRefundPriceInput);
  }

  async findAll(listPaymentPendingOrdersInput: ListPaymentPendingOrdersInput) {
    const listPaymentPendingOrders = new ListPaymentPendingOrders(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await listPaymentPendingOrders.listFilteredOrders(
      listPaymentPendingOrdersInput,
    );
  }

  async cancelInitiatedReturn(
    id: string,
    shopifyOrderId: string,
  ): Promise<{
    success: boolean;
    message: string;
    data?: any;
    errorSource?: string;
  }> {
    const cancelReturnService = new CancelReturnService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
    );

    return await cancelReturnService.cancelReturn(id, shopifyOrderId);
  }

  async regenerateReturn(
    id: string,
    shopifyOrderId: string,
  ): Promise<{
    success: boolean;
    message: string;
    newAwbNumber?: string;
    previousAwbNumber?: string;
    errorSource?: string;
  }> {
    const regenerateReturnService = new RegenerateReturnService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
    );

    return await regenerateReturnService.regenerateReturn(id, shopifyOrderId);
  }

  async exportReplacementOrders(
    email: string,
    listReplacementInput: ExportReplacementOrdersInput,
  ) {
    const exportHandler = new ExportReplacementOrders(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await exportHandler.exportReplacementOrders(
      email,
      listReplacementInput,
    );
  }
}
