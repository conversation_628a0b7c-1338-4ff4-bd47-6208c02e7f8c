import { ObjectType, Field, registerEnumType, Int } from '@nestjs/graphql';
import { CustomCode, CustomData } from 'src/carts/entities/cart.entity';
import { DeliveryType } from 'src/common/enum/delivery';
import { OrderStatus, OrderType } from 'src/common/enum/order';
import {
  PickupPriority,
  ReplaceableDiscountType,
  ReplacementOptions,
  ReplacementType,
} from 'src/common/enum/replacement';
import { OrderCustomerMetadata } from 'src/orders/entities/order.entity';
import { Image, Metafield } from 'src/products/entities/product.entity';
import { QuotationAddress } from 'src/quotations/entities/quotation.entity';

registerEnumType(ReplacementType, { name: 'ReplacementType' });
registerEnumType(PickupPriority, { name: 'PickupPriority' });
registerEnumType(ReplaceableDiscountType, { name: 'ReplaceableDiscountType' });
registerEnumType(ReplacementOptions, { name: 'ReplacementOptions' });

@ObjectType()
export class Property {
  @Field(() => String)
  name: string;

  @Field(() => String)
  value: string;
}
@ObjectType()
export class ReplacedProduct {
  @Field(() => String, { nullable: false })
  productId: string;

  @Field(() => String, { nullable: true })
  variantId?: string;

  @Field(() => Int, { nullable: false })
  quantity: number;

  @Field(() => Number, { nullable: true })
  price: number;

  @Field(() => String, { nullable: true })
  title?: string;

  @Field(() => CustomData, { nullable: true })
  customData?: CustomData;

  @Field(() => String, {
    nullable: true,
    description: 'VariantTitle of the order product',
  })
  variantTitle?: string;

  @Field(() => String, { nullable: false })
  sku: string;

  @Field(() => String, { nullable: true })
  productType?: string;

  @Field(() => [Metafield], { nullable: true })
  metafields?: Metafield[];

  @Field(() => Image, {
    nullable: true,
    description: 'Image of the order product',
  })
  image?: Image;

  @Field(() => Number, { nullable: false })
  finalItemPrice: number;

  @Field(() => Number, { nullable: true })
  itemDiscount?: number;

  @Field(() => Number, { nullable: true })
  bankDiscount?: number;

  @Field(() => Number, { nullable: true })
  edd?: number;

  @Field(() => [Property], { nullable: true })
  properties?: Property[];
}

@ObjectType()
export class UpdatedRefundPrice {
  @Field(() => String, { nullable: false })
  approver: string;

  @Field(() => String, { nullable: false })
  reason: string;
}

@ObjectType()
export class ReplacementOrderData {
  @Field(() => String)
  id: string;

  @Field(() => String, {
    nullable: true,
    description: 'RR order payment link status',
  })
  linkStatus?: string;

  @Field(() => String, {
    nullable: true,
    description: 'RR order payment link time to expire',
  })
  linkTimeToExpire?: string;

  @Field(() => String, {
    nullable: true,
    description: 'carrier',
  })
  carrier?: string;

  @Field(() => String, {
    nullable: true,
    description: 'creditNoteId',
  })
  creditNoteId?: string;

  @Field(() => [String], { nullable: true })
  originalPaymentMode?: string[];

  @Field(() => [String], { nullable: true })
  transactionIDs?: string[];

  @Field(() => String, { nullable: true })
  RRTicketId?: string;

  @Field(() => Boolean, { nullable: true })
  isRazorpay: boolean;

  @Field(() => String)
  shopifyOrderId: string;

  @Field(() => String, { nullable: true })
  orderId?: string;

  @Field(() => String)
  customerId: string;

  @Field(() => ReplacedProduct)
  replacedProduct: ReplacedProduct;

  @Field(() => PickupPriority, { nullable: true })
  pickupPriority?: PickupPriority;

  @Field(() => ReplacementType, { nullable: true })
  replacementType?: ReplacementType;

  @Field(() => String)
  ticketId: string;

  @Field(() => OrderType, { defaultValue: OrderType.POS })
  orderType: OrderType;

  @Field(() => ReplacementOptions, { nullable: true })
  replacementOption?: ReplacementOptions;

  @Field(() => ReplaceableDiscountType, { nullable: true })
  replaceableDiscountType?: ReplaceableDiscountType;

  @Field(() => [ReplacedProduct], {
    nullable: true,
    description: 'List of products being quoted.',
  })
  orderProducts?: ReplacedProduct[];

  @Field(() => [ReplacedProduct], {
    nullable: true,
    description: 'List of products being quoted.',
  })
  accessories?: ReplacedProduct[];

  @Field(() => OrderStatus, {
    nullable: false,
    description: 'Order status.',
  })
  status: OrderStatus;

  @Field(() => OrderCustomerMetadata, {
    nullable: true,
    description: 'Customer metadata for the quotation.',
  })
  customer: OrderCustomerMetadata;

  @Field(() => CustomCode, {
    nullable: true,
    description: 'Custom code for the quotation.',
  })
  customCode?: CustomCode;

  @Field(() => Number, {
    nullable: true,
    description: 'Custom Discount Amount',
  })
  customDiscountAmount?: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Total amount',
  })
  totalAmount?: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Final Amount',
  })
  finalDiscountedAmount?: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Refunded Price',
  })
  refundedPrice?: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Order Total',
  })
  orderTotal?: number;

  @Field(() => Number, {
    nullable: true,
    description: "Bank's Discount",
  })
  bankDiscount?: number;

  // @Field(() => String, {
  //   nullable: true,
  //   description: 'Notes or comments for the order.',
  // })
  // reason?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Primary reason for the replacement',
  })
  primaryReason?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Secondary reason or sub-reason for the replacement',
  })
  secondaryReason?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Department related to the replacement request',
  })
  department?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Agent name, required when department is AGENT',
  })
  agentName?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Additional comments or notes about the replacement',
  })
  additionalComments?: string;

  @Field(() => QuotationAddress, {
    nullable: true,
    description: 'Shipping Address for the quotation.',
  })
  shippingAddress?: QuotationAddress;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Shipping charges.',
  })
  isShippingCharged?: boolean;

  @Field(() => Number, {
    nullable: true,
    description: 'Shipping Charges.',
  })
  shippingCost?: number;

  @Field(() => QuotationAddress, {
    nullable: true,
    description: 'Billing Address for the quotation.',
  })
  billingAddress?: QuotationAddress;

  @Field(() => String, { nullable: false, description: 'Created At' })
  orderCreatedAt: string;

  @Field(() => String, { nullable: false, description: 'Created At' })
  createdAt: string;

  @Field(() => String, { nullable: false, description: 'Updated At' })
  updatedAt: string;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Is in replaceable window',
  })
  inReplaceableWindow?: boolean;

  @Field(() => DeliveryType, {
    nullable: true,
    description: 'Delivery Type of the order product',
  })
  deliveryStatus?: DeliveryType;

  @Field(() => String, {
    nullable: true,
    description: 'Max Delivery date of the order product',
  })
  deliveryDate?: string;

  @Field(() => String, {
    description: 'Min Delivery date of the order product',
    nullable: true,
  })
  minDeliveryDate?: string;

  @Field(() => String, {
    description: 'Easy ecom Reference Number',
    nullable: true,
  })
  eeRefNo?: string;

  @Field(() => String, {
    description: 'Easy ecom Reference Number',
    nullable: true,
  })
  referenceCode?: string;

  @Field(() => UpdatedRefundPrice, {
    description: 'Manually modified refund price reason',
    nullable: true,
  })
  updatedRefundPrice?: UpdatedRefundPrice;

  @Field(() => String, {
    description: 'Easy ecom locationKey',
    nullable: true,
  })
  locationKey?: string;

  @Field(() => String, {
    description: 'Easy ecom awbNumber',
    nullable: true,
  })
  awbNumber?: string;

  @Field(() => [String], {
    description: 'Replaceable part product ids',
    nullable: true,
  })
  partProductsIds?: string[];

  @Field(() => String, {
    description: 'refundDetailAlreadyAdded',
    nullable: true,
  })
  refundDetailAlreadyAdded?: boolean;

  @Field(() => String, {
    description: 'request id',
    nullable: true,
  })
  requestId?: string;
}

@ObjectType()
export class LastEvaluatedKeyObj {
  @Field(() => String)
  id: string;

  @Field(() => String)
  shopifyOrderId: string;
}

@ObjectType()
export class ReplacementOrder {
  @Field(() => ReplacementOrderData, {
    nullable: true,
    description: 'Replacement order.',
  })
  data: ReplacementOrderData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
@ObjectType()
export class PaymentPendingReplacementOrders {
  @Field(() => [ReplacementOrderData], {
    nullable: true,
    description: 'Replacement order.',
  })
  data: ReplacementOrderData[];

  @Field(() => Number, { nullable: true })
  count: number;

  @Field(() => LastEvaluatedKeyObj, { nullable: true })
  lastEvaluatedKey: LastEvaluatedKeyObj;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class CancelInitiatedReturn {
  @Field(() => Boolean, {
    nullable: true,
    description: 'Operation success status',
  })
  success: boolean;

  @Field(() => String, {
    nullable: true,
    description: 'Response message',
  })
  message?: string;

  @Field(() => Number, {
    nullable: true,
    description: 'HTTP status code',
  })
  status?: number;

  @Field(() => String, {
    nullable: true,
    description: 'Cancelled AWB number',
  })
  cancelledAwbNumber?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Error source if operation fails',
  })
  errorSource?: string;
}

@ObjectType()
export class RegenerateReturn {
  @Field(() => Boolean, {
    nullable: true,
    description: 'Operation success status',
  })
  success: boolean;

  @Field(() => String, {
    nullable: true,
    description: 'Response message',
  })
  message?: string;

  @Field(() => Number, {
    nullable: true,
    description: 'HTTP status code',
  })
  status?: number;

  @Field(() => String, {
    nullable: true,
    description: 'New AWB number generated',
  })
  newAwbNumber?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Previous AWB number generated',
  })
  previousAwbNumber?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Error source if operation fails',
  })
  errorSource?: string;
}
