import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class VariantTermSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  id?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  product_id?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  updatedAt?: string;
}

@InputType()
export class VariantTextSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  title?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  type?: string;
}

@InputType()
export class VariantSortingFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class ListVariantInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size?: string;

  @Field(() => VariantTermSearchFieldsInput, {
    nullable: true,
    description: 'Search Boolean fields to filter the records',
  })
  termSearchFields?: VariantTermSearchFieldsInput;

  @Field(() => VariantTextSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  textSearchFields?: VariantTextSearchFieldsInput;

  @Field(() => VariantSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { createdAt: 'desc' },
  })
  sortBy?: VariantSortingFieldsInput;
}
