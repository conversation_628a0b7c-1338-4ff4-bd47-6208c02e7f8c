import { Field, ID, InputType } from '@nestjs/graphql';

@InputType()
export class ImageInput {
  @Field(() => String, { nullable: true })
  admin_graphql_api_id: string;

  @Field(() => String, { nullable: true })
  alt?: string;

  @Field(() => String, { nullable: true })
  created_at?: string;

  @Field(() => Number, { nullable: true })
  height: number;

  @Field(() => ID, { nullable: true })
  id: string;

  @Field(() => Number, { nullable: true })
  position: number;

  @Field(() => ID, { nullable: true })
  product_id: string;

  @Field(() => String, { nullable: true })
  src: string;

  @Field(() => String, { nullable: true })
  updated_at?: string;

  @Field(() => [ID], { nullable: true })
  variant_ids?: string[];

  @Field(() => Number, { nullable: true })
  width: number;
}
