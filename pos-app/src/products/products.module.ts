import { Module } from '@nestjs/common';
import { ProductsService } from './products.service';
import { ProductsResolver } from './products.resolver';
import { CronService } from 'src/cron/cron.service';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ConfigParametersModule } from 'src/config/config.module';
import { ShopifyModule } from 'src/common/shopify/shopify.module';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';

@Module({
  imports: [
    S3ClientModule,
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
    ShopifyModule,
  ],
  providers: [
    ProductsResolver,
    ProductsService,
    CronService,
    SuccessHandler,
    ErrorHandler,
  ],
})
export class ProductsModule {}
