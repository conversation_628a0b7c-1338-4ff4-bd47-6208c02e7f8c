// modules
import { BatchGetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ProductData } from '../entities/product.entity';
import { AppConfigParameters } from 'src/config/config';
import { ProductStatus } from 'src/common/enum/products';
export class QueryProductsByIds {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryProductsByIds(
    productIds: string[],
    activeProducts: boolean = false,
  ): Promise<ProductData[]> {
    posLogger.info('product', 'queryProductsByIds', {
      input: { productIds },
    });

    if (!productIds.length) return [];

    try {
      const PRODUCT_TABLE = await this.configParameters.getProductTableName();

      const batchGetItemsCommand = new BatchGetCommand({
        RequestItems: {
          [PRODUCT_TABLE]: {
            Keys: productIds.map((sk) => ({
              pk: 'PRODUCT',
              sk,
            })),
          },
        },
      });

      const { Responses = {} } =
        await this.docClient.batchGetItems(batchGetItemsCommand);
      const products = Responses[PRODUCT_TABLE];

      if (activeProducts) {
        
        return products.filter(
          (product) => product.status === ProductStatus.ACTIVE,
        );
      }

      return products;
    } catch (e) {
      posLogger.error('product', 'queryProductsByIds', { error: e });
      throw e;
    }
  }

  async queryProductsByVariantIds(ids): Promise<ProductData[]> {
    posLogger.info('product', 'queryProductsByVariantIds', {
      input: { ids },
    });

    if (!ids.length) return [];

    try {
      const PRODUCT_TABLE = await this.configParameters.getProductTableName();

      const batchGetItemsCommand = new BatchGetCommand({
        RequestItems: {
          [PRODUCT_TABLE]: {
            Keys: ids.map(({ productId, variantId }) => ({
              pk: 'VARIANT',
              sk: `${productId}#${variantId}`,
            })),
          },
        },
      });

      const { Responses = {} } =
        await this.docClient.batchGetItems(batchGetItemsCommand);
      const products = Responses[PRODUCT_TABLE];

      return products;
    } catch (e) {
      posLogger.error('product', 'queryProductsByVariantIds', { error: e });
      throw e;
    }
  }
}
