import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { Products } from '../entities/product.entity';
import { AppConfigParameters } from 'src/config/config';
import { ProductStatus } from 'src/common/enum/products';

export class QueryAccessories {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryAccessories(query: string = ''): Promise<Products> {
    posLogger.info('product', 'queryAccessories', { input: { query } });
    try {
      const PRODUCT_TABLE = await this.configParameters.getProductTableName();
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (query) {
        const esQuery = {
          bool: {
            filter: {
              terms: {
                'product_type.keyword': [
                  'Pillows',
                  'Bedding',
                  'Cushions',
                  'Custom-Bedding',
                ],
              },
            },
          },
        };

        // const { body: bodyRes } = await esHandler.count({
        //   index: PRODUCT_TABLE,
        //   body: {
        //     query: esQuery,
        //   },
        // });

        const response = await esHandler.search({
          index: PRODUCT_TABLE,
          body: {
            size: 300,
            from: 0,
            query: esQuery,
          },
        });
        const data = response.body.hits.hits
          .map((hit) => hit._source)
          .filter(({ status, type }) =>
            !!status
              ? status === ProductStatus.ACTIVE
              : type === 'VARIANT' && !status,
          );

        return { data, count: 1 };
      }

      const response = await esHandler.search({
        index: PRODUCT_TABLE,

        body: {
          size: 300,
          from: 0,
          query: {
            term: {
              status: ProductStatus.ACTIVE,
            },
          },
        },
      });

      const data = response.body.hits.hits
        .map((hit) => hit._source)
        .filter(({ status, type }) =>
          !!status
            ? status === ProductStatus.ACTIVE
            : type === 'VARIANT' && !status,
        );

      // const { body: bodyRes } = await esHandler.count({
      //   index: PRODUCT_TABLE,
      //   body: {
      //     query: {
      //       term: {
      //         status: ProductStatus.ACTIVE,
      //       },
      //     },
      //   },
      // });

      return { data, count: 1 };
    } catch (e) {
      posLogger.error('product', 'queryProducts', { error: e });
      throw e;
    }
  }
}
