// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GetProduct } from './get-product';
import { ProductData } from '../entities/product.entity';
import { VariantData } from '../entities/variant.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
export class GetVariant {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getVariantById(
    variantId: string,
    product_id: string,
  ): Promise<ProductData> {
    posLogger.info('product', 'getVariantById', {
      input: { variantId, product_id },
    });
    try {
      const PRODUCT_TABLE = await this.configParameters.getProductTableName();

      const getProductHandler = new GetProduct(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const product: ProductData =
        await getProductHandler.getProductById(product_id);

      const variantCommand = new GetCommand({
        TableName: PRODUCT_TABLE,
        Key: {
          pk: 'VARIANT',
          sk: `${product_id}#${variantId}`,
        },
      });

      const { Item: data }: { Item: VariantData } =
        await this.docClient.getItem(variantCommand);
      posLogger.info('product', '💰getVariantById', {
        variantPrice: data.price,
      });

      if (!data) {
        throw new CustomError(`Variant ${variantId} not found`, 404);
      }

      return { ...product, variant: { data } };
    } catch (e) {
      posLogger.error('product', 'getVariantById', { error: e });
      throw e;
    }
  }
}
