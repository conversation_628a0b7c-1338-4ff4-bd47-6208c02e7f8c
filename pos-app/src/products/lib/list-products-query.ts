import { filterFormatter } from 'src/common/helper/filter-helper';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { ListProductInput } from '../dto/list-products.input';
import { Products } from '../entities/product.entity';
import { AppConfigParameters } from 'src/config/config';

export class QueryProductsByFilter {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryProductsByFilter(filter: ListProductInput): Promise<Products> {
    posLogger.info('product', 'queryProductsByFilter', { input: filter });
    try {
      const PRODUCT_TABLE = await this.configParameters.getProductTableName();

      const { searchArray, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );
      searchArray.push({ match_phrase_prefix: { type: 'PRODUCT' } });

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: PRODUCT_TABLE,
        });

        size = bodyRes?.count;
      }

      if (searchArray.length) {
        const { body: bodyRes } = await esHandler.count({
          index: PRODUCT_TABLE,
          body: {
            query: {
              bool: {
                must: [...searchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: PRODUCT_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data = response.body.hits.hits.map((hit) => hit._source);

        return { data, count: bodyRes?.count };
      }

      const response = await esHandler.search({
        index: PRODUCT_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data = response.body.hits.hits.map((hit) => hit._source);
      const { body: bodyRes } = await esHandler.count({
        index: PRODUCT_TABLE,
      });

      return { data, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('product', 'queryProductsByFilter', { error: e });
      throw e;
    }
  }
}
