import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { Products } from '../entities/product.entity';
import { AppConfigParameters } from 'src/config/config';
import { ProductStatus } from 'src/common/enum/products';

export class QueryProducts {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryProducts(query: string = ''): Promise<Products> {
    posLogger.info('product', 'queryProducts', { input: { query } });
    try {
      const PRODUCT_TABLE = await this.configParameters.getProductTableName();
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (query) {
        const queries = query.split(',').map((q) => q.trim());
        const fields = ['id', 'title', 'handle', 'sku', 'product_type'];
        const should = [];

        queries.forEach((q) => {
          should.push({
            multi_match: {
              type: 'phrase_prefix',
              query: q,
              fields,
            },
          });
        });

        const esQuery = {
          bool: {
            should,
          },
        };

        // const { body: bodyRes } = await esHandler.count({
        //   index: PRODUCT_TABLE,
        //   body: {
        //     query: esQuery,
        //   },
        // });

        const response = await esHandler.search({
          index: PRODUCT_TABLE,
          body: {
            size: 300,
            from: 0,
            query: esQuery,
          },
        });
        const data = response.body.hits.hits
          .map((hit) => hit._source)
          .filter(({ status, type }) =>
            !!status
              ? status === ProductStatus.ACTIVE
              : type === 'VARIANT' && !status,
          );

        return { data, count: 1 };
      }

      const response = await esHandler.search({
        index: PRODUCT_TABLE,

        body: {
          size: 300,
          from: 0,
          query: {
            term: {
              status: ProductStatus.ACTIVE,
            },
          },
        },
      });

      const data = response.body.hits.hits
        .map((hit) => hit._source)
        .filter(({ status, type }) =>
          !!status
            ? status === ProductStatus.ACTIVE
            : type === 'VARIANT' && !status,
        );

      // const { body: bodyRes } = await esHandler.count({
      //   index: PRODUCT_TABLE,
      //   body: {
      //     query: {
      //       term: {
      //         status: ProductStatus.ACTIVE,
      //       },
      //     },
      //   },
      // });

      return { data, count: 1 };
    } catch (e) {
      posLogger.error('product', 'queryProducts', { error: e });
      throw e;
    }
  }
}
