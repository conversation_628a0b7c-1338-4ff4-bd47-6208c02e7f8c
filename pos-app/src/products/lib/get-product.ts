// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ProductData } from '../entities/product.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
export class GetProduct {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getProductById(id: string): Promise<ProductData> {
    posLogger.info('product', 'getProductById', { input: { id } });
    try {
      const PRODUCT_TABLE = await this.configParameters.getProductTableName();

      const command = new GetCommand({
        TableName: PRODUCT_TABLE,
        Key: {
          pk: 'PRODUCT',
          sk: id,
        },
      });

      const { Item: data } = await this.docClient.getItem(command);

      if (!data) {
        throw new CustomError(`Product ${id} not found`, 404);
      }

      return data;
    } catch (e) {
      posLogger.error('product', 'getProductById', { error: e });
      throw new CustomError(`Product ${id} not found`, 404);
    }
  }
}
