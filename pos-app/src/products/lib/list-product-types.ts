import { ElasticClient } from 'src/utils/elasticsearch.config';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { ProductTypeData } from '../entities/product-type.entity';
import { AppConfigParameters } from 'src/config/config';
export class QueryProductTypes {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryProductTypes(): Promise<ProductTypeData[]> {
    posLogger.info('product', 'queryProductTypes', { input: {} });
    try {
      const PRODUCT_TABLE = await this.configParameters.getProductTableName();

      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      const response = await esHandler.search({
        index: PRODUCT_TABLE,
        body: {
          size: 0,
          aggs: {
            unique_product_types: {
              terms: {
                field: 'product_type.keyword',
                size: 10000,
              },
            },
          },
        },
      });

      const data = response.body.aggregations.unique_product_types.buckets;
      return data;
    } catch (e) {
      posLogger.error('product', 'queryProductTypes', { error: e });
      throw e;
    }
  }
}
