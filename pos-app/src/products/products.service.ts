import { Injectable } from '@nestjs/common';
import { GetProduct } from './lib/get-product';
import { GetVariant } from './lib/get-variant';
import { QueryProducts } from './lib/list-products';
import { ListVariantInput } from './dto/list-variants.input';
import { QueryVariants } from './lib/list-variants';
import { CronService } from 'src/cron/cron.service';
import { posLogger } from 'src/common/logger';
import { QueryProductTypes } from './lib/list-product-types';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { QueryProductsByIds } from './lib/list-products-by-ids';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { ProductData } from './entities/product.entity';
import { AppConfigParameters } from 'src/config/config';
import { ListProductInput } from './dto/list-products.input';
import { QueryProductsByFilter } from './lib/list-products-query';
import { QueryAccessories } from './lib/list-accessories';

@Injectable()
export class ProductsService {
  constructor(
    private readonly cronService: CronService,
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async fetchProducts() {
    this.cronService.fetchShopifyProducts().catch((error) => {
      posLogger.error('PRODUCTS', 'fetchProducts', error);
    });
    return { message: 'Fetching products task has been initiated' };
  }

  async findAll(listProductInput: ListProductInput): Promise<ProductData[]> {
    try {
      const { data: products } =
        await this.findAllProductsByQuery(listProductInput);
      const items = await Promise.all(
        products.map(async (item) => {
          const { data: variants } = await this.findAllVariants({
            from: '0',
            size: '100',
            termSearchFields: { product_id: item.id.toString() },
          });
          return {
            ...item,
            variants: { data: variants || [], count: variants?.length || 0 },
          };
        }),
      );

      if (!items.length) throw new CustomError('No products found', 404);
      return items;
    } catch (error) {
      throw error;
    }
  }

  async findAllProducts(query: string) {
    const queryProductsHandler = new QueryProducts(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return queryProductsHandler.queryProducts(query);
  }

  async findAllAccessories(query: string) {
    const queryProductsHandler = new QueryAccessories(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return queryProductsHandler.queryAccessories(query);
  }

  async findAllProductsByQuery(listProductInput: ListProductInput) {
    const queryProductsHandler = new QueryProductsByFilter(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return queryProductsHandler.queryProductsByFilter(listProductInput);
  }

  async findAllVariants(listVariantInput: ListVariantInput) {
    const queryVairiantsHandler = new QueryVariants(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return queryVairiantsHandler.queryVariants(listVariantInput);
  }

  async findOneProductById(id: string) {
    const getProductHandler = new GetProduct(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getProductHandler.getProductById(id);
  }

  async findOneVariantById(variantId: string, product_id: string) {
    const getVariantHandler = new GetVariant(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getVariantHandler.getVariantById(variantId, product_id);
  }

  async findAllProductTypes() {
    const queryProductTypesHandler = new QueryProductTypes(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    return queryProductTypesHandler.queryProductTypes();
  }

  async findAllProductsByIds(
    productIds: string[],
    activeProducts: boolean = false,
  ) {
    const queryProductsByIdsHandler = new QueryProductsByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    return queryProductsByIdsHandler.queryProductsByIds(productIds,activeProducts);
  }
}
