export const searchingFilter = new Map<
  string,
  'string' | 'nonString' | 'arrayString' | 'boolean' | 'date'
>([
  ['title', 'string'],
  ['type', 'string'],
  ['handle', 'string'],
  ['id', 'nonString'],
  ['product_id', 'nonString'],
  ['product_type', 'string'],
  ['status', 'string'],
  ['createdAt', 'date'],
  ['updatedAt', 'date'],
]);

export const sortingFilter = new Map<string, 'string' | 'nonString'>([
  ['createdAt', 'string'],
  ['updatedAt', 'string'],
]);

export const sortingFilterType = {
  createdAt: 'date',
  updatedAt: 'date',
};
