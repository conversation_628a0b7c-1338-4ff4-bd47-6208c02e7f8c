import { Resolver, Query, Args } from '@nestjs/graphql';
import { ProductsService } from './products.service';
import { Product, Products, Response } from './entities/product.entity';
import { Variants } from './entities/variant.entity';
import { ListVariantInput } from './dto/list-variants.input';
import { ProductType } from './entities/product-type.entity';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { AdminGuard, CRMGuard } from 'src/auth/roles.guard';
import { ListProductInput } from './dto/list-products.input';

@Resolver(() => Product)
@UseGuards(CustomAuthGuard, CRMGuard)
export class ProductsResolver {
  constructor(
    private readonly productsService: ProductsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: <PERSON><PERSON>r<PERSON>and<PERSON>,
  ) {}

  @Query(() => Products, { name: 'listProductsWithVariants' })
  async findAll(
    @Args('listProductInput', { nullable: true })
    listProductInput: ListProductInput,
  ) {
    try {
      const data = await this.productsService.findAll(listProductInput);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count: data.length };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list products with variants',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @UseGuards(AdminGuard)
  @Query(() => Response, { name: 'fetchProducts' })
  async fetchProducts() {
    return await this.productsService.fetchProducts();
  }

  @Query(() => Product, { name: 'getProductById' })
  async findOneProductById(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.productsService.findOneProductById(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get product by id',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Product, { name: 'getVariantById' })
  async findOneVariantById(
    @Args('product_id', { type: () => String }) product_id: string,
    @Args('id', { type: () => String }) id: string,
  ) {
    try {
      const data = await this.productsService.findOneVariantById(
        id,
        product_id,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get variant by id',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Products, { name: 'listProducts' })
  async findAllProducts(
    @Args('query', { nullable: true, type: () => String })
    query: string,
  ) {
    try {
      const { data, count } = await this.productsService.findAllProducts(query);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list products',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Query(() => Products, { name: 'listAccessories' })
  async findAllAccessories(
    @Args('query', { nullable: true, type: () => String })
    query: string,
  ) {
    try {
      const { data, count } =
        await this.productsService.findAllAccessories(query);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list products',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Query(() => Variants, { name: 'listVariants' })
  async findAllVariants(
    @Args('listVariantInput', { type: () => ListVariantInput, nullable: true })
    listVariantInput: ListVariantInput,
  ) {
    try {
      const { data, count } =
        await this.productsService.findAllVariants(listVariantInput);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list variants',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => ProductType, { name: 'listProductTypes' })
  async findAllProductTypes() {
    try {
      const data = await this.productsService.findAllProductTypes();
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count: data.length };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list product types',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Products, { name: 'listProductsByIds' })
  async findAllProductsByIds(
    @Args('productIds', { type: () => [String], nullable: true })
    productIds: string[],
    @Args('activeProducts', {
      type: () => Boolean,
      nullable: true,
      defaultValue: false,
    })
    activeProducts?: boolean,
  ) {
    try {
      const data = await this.productsService.findAllProductsByIds(productIds,activeProducts);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count: data.length };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list products by ids',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
