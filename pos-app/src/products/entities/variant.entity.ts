import { Field, ID, ObjectType } from '@nestjs/graphql';
import { Image, Metafield } from './product.entity';
import { ProductStatus } from 'src/common/enum/products';

@ObjectType()
export class VariantData {
  @Field(() => ID)
  id: string;

  @Field(() => ID)
  product_id: string;

  @Field(() => String)
  type: string;

  @Field(() => ID, { nullable: true })
  admin_graphql_api_id: string;

  @Field(() => String, { nullable: true })
  barcode?: string;

  @Field(() => String, { nullable: true })
  product_type?: string;

  @Field(() => String, { nullable: true })
  compare_at_price?: string;

  @Field(() => String, { nullable: true })
  createdAt?: string;

  @Field(() => String, { nullable: true })
  created_at?: string;

  @Field(() => String, { nullable: true })
  fulfillment_service: string;

  @Field(() => Number, { nullable: true })
  grams: number;

  @Field(() => String, { nullable: true })
  image_id?: string;

  @Field(() => Image, { nullable: true })
  image?: Image;

  @Field(() => ID, { nullable: true })
  inventory_item_id: string;

  @Field(() => String, { nullable: true })
  inventory_management?: string;

  @Field(() => String, { nullable: true })
  inventory_policy: string;

  @Field(() => String, { nullable: true })
  productTitle: string;

  @Field(() => Number, { nullable: true })
  inventory_quantity: number;

  @Field(() => [Metafield], { nullable: true })
  metafields?: Metafield[];

  @Field(() => Number, { nullable: true })
  old_inventory_quantity: number;

  @Field(() => String, { nullable: true })
  option1?: string;

  @Field(() => String, { nullable: true })
  option2?: string;

  @Field(() => String, { nullable: true })
  option3?: string;

  @Field(() => String, { nullable: true })
  option4?: string;

  @Field(() => String, { nullable: true })
  option5?: string;

  @Field(() => String, { nullable: true })
  option6?: string;

  @Field(() => String, { nullable: true })
  option7?: string;

  @Field(() => String, { nullable: true })
  option8?: string;

  @Field(() => String, { nullable: true })
  option9?: string;

  @Field(() => String, { nullable: true })
  option10?: string;

  @Field(() => Number, { nullable: true })
  position: number;

  @Field(() => String, { nullable: true })
  price: string;

  @Field(() => Boolean, { nullable: true })
  requires_shipping: boolean;

  @Field(() => String, { nullable: true })
  sku: string;

  @Field(() => ProductStatus, { nullable: true })
  status: ProductStatus;

  @Field(() => Boolean, { nullable: true })
  taxable: boolean;

  @Field(() => String, { nullable: true })
  title: string;

  @Field(() => String, { nullable: true })
  updatedAt?: string;

  @Field(() => String, { nullable: true })
  updated_at?: string;

  @Field(() => Number, { nullable: true })
  weight: number;

  @Field(() => String, { nullable: true })
  weight_unit: string;

  @Field(() => String, { nullable: true })
  variantTitle: string;
}

@ObjectType()
export class Variants {
  @Field(() => [VariantData], { nullable: true })
  data: VariantData[];

  @Field(() => Number, { description: 'Count of Total data', nullable: true })
  count?: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class Variant {
  @Field(() => VariantData, { nullable: true })
  data: VariantData;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
