import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class ProductTypeData {
  @Field(() => String)
  key: string;

  @Field(() => Number)
  doc_count: number;
}

@ObjectType()
export class ProductType {
  @Field(() => [ProductTypeData], { nullable: true })
  data: ProductTypeData[];

  @Field(() => Number, { nullable: true })
  count: number;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
