import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql';
import { Variant, Variants } from './variant.entity';
import { ProductStatus } from 'src/common/enum/products';

// Register the enum with GraphQL
registerEnumType(ProductStatus, { name: 'ProductStatus' });

@ObjectType()
export class Image {
  @Field(() => ID, { nullable: true })
  admin_graphql_api_id?: string;

  @Field(() => String, { nullable: true })
  alt?: string;

  @Field(() => String, { nullable: true })
  created_at?: string;

  @Field(() => Number, { nullable: true })
  height?: number;

  @Field(() => ID, { nullable: true })
  id?: string;

  @Field(() => Number, { nullable: true })
  position?: number;

  @Field(() => ID, { nullable: true })
  product_id?: string;

  @Field(() => String, { nullable: true })
  src?: string;

  @Field(() => String, { nullable: true })
  updated_at?: string;

  @Field(() => [ID], { nullable: true })
  variant_ids?: string[];

  @Field(() => Number, { nullable: true })
  width?: number;
}

@ObjectType()
export class Metafield {
  @Field(() => ID, { nullable: true })
  admin_graphql_api_id: string;

  @Field(() => String, { nullable: true })
  created_at?: string;

  @Field(() => String, { nullable: true })
  description?: string;

  @Field(() => ID, { nullable: true })
  id: string;

  @Field(() => String, { nullable: true })
  key: string;

  @Field(() => String, { nullable: true })
  namespace: string;

  @Field(() => ID, { nullable: true })
  owner_id: string;

  @Field(() => String, { nullable: true })
  owner_resource: string;

  @Field(() => String, { nullable: true })
  type: string;

  @Field(() => String, { nullable: true })
  updated_at?: string;

  @Field(() => String, { nullable: true })
  value: string;
}

@ObjectType()
export class Option {
  @Field(() => ID, { nullable: true })
  id: string;

  @Field(() => String, { nullable: true })
  name: string;

  @Field(() => Number, { nullable: true })
  position: number;

  @Field(() => ID, { nullable: true })
  product_id: string;

  @Field(() => [String], { nullable: true })
  values: string[];
}

@ObjectType()
export class ProductData {
  @Field(() => ID)
  id: string;

  @Field(() => String)
  type: string;

  @Field(() => ID, { nullable: true })
  admin_graphql_api_id: string;

  @Field(() => String, { nullable: true })
  body_html?: string;

  @Field(() => String, { nullable: true })
  createdAt: string;

  @Field(() => String, { nullable: true })
  created_at?: string;

  @Field(() => String)
  handle: string;

  @Field(() => String, { nullable: true })
  price?: string;

  @Field(() => Image, { nullable: true })
  image?: Image;

  @Field(() => [Image], { nullable: true })
  images: Image[];

  @Field(() => [Metafield], { nullable: true })
  metafields?: Metafield[];

  @Field(() => [Option], { nullable: true })
  options: Option[];

  @Field(() => String, { nullable: true })
  product_type?: string;

  @Field(() => String, { nullable: true })
  published_at?: string;

  @Field(() => String, { nullable: true })
  published_scope?: string;

  @Field(() => ProductStatus, { nullable: true })
  status: ProductStatus;

  @Field(() => [String], { nullable: true })
  tags?: string[];

  @Field(() => String, { nullable: true })
  template_suffix?: string;

  @Field(() => String, { nullable: true })
  title: string;

  @Field(() => String, { nullable: true })
  updatedAt: string;

  @Field(() => String, { nullable: true })
  updated_at?: string;

  @Field(() => String, { nullable: true })
  vendor?: string;

  @Field(() => Variants, { nullable: true })
  variants?: Variants;

  @Field(() => Variant, { nullable: true })
  variant?: Variant;

  @Field(() => ID, { nullable: true })
  product_id?: string;

  @Field(() => String, { nullable: true })
  sku?: string;

  @Field(() => Number, { nullable: true })
  inventory_quantity: number;

  @Field(() => String, { nullable: true })
  inventory_management: string;

  @Field(() => String, { nullable: true })
  productTitle: string;

  @Field(() => String, { nullable: true })
  variantTitle: string;

  @Field(() => String, { nullable: true })
  minimumEmi?: string;
}

@ObjectType()
export class Products {
  @Field(() => [ProductData], { nullable: true })
  data: ProductData[];

  @Field(() => Number, { description: 'Count of Total data', nullable: true })
  count: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class Product {
  @Field(() => ProductData, { nullable: true })
  data: ProductData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class Response {
  @Field(() => String, { nullable: false })
  message: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
