// modules
// custom modules
import { ListStoreInput } from '../dto/list-store.input';
import { filterFormatter } from 'src/common/helper/filter-helper';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { Stores } from '../entities/store.entity';
import { AppConfigParameters } from 'src/config/config';

export class QueryStores {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryStores({ storeIds, ...filter }: ListStoreInput): Promise<Stores> {
    posLogger.info('store', 'queryStores', { input: filter });
    try {
      const { searchArray, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      const STORE_TABLE = await this.configParameters.getStoreTableName();

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: STORE_TABLE,
        });

        size = bodyRes?.count;
      }

      const storeFilter = storeIds?.length
        ? [{ terms: { 'id.keyword': storeIds } }]
        : [];

      if (searchArray.length) {
        const { body: bodyRes } = await esHandler.count({
          index: STORE_TABLE,
          body: {
            query: {
              bool: {
                must: [...storeFilter, ...searchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: STORE_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...storeFilter, ...searchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data = response.body.hits.hits.map((hit) => hit._source);

        return { data, count: bodyRes?.count };
      }

      const response = await esHandler.search({
        index: STORE_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data = response.body.hits.hits.map((hit) => hit._source);
      const { body: bodyRes } = await esHandler.count({
        index: STORE_TABLE,
      });

      return { data, count: bodyRes?.count };
    } catch (error) {
      posLogger.error('store', 'queryStores', { error });
      throw error;
    }
  }
}
