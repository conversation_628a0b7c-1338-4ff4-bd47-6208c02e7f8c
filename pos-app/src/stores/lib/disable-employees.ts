import { ElasticClient } from 'src/utils/elasticsearch.config';
import { UpdateEmployee } from 'src/employees/lib/update-employee';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppCognitoClient } from 'src/common/cognito-identity-client/cognito-identity-client';
import { AppConfigParameters } from 'src/config/config';

export class DisableEmployee {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private cognitoClient: AppCognitoClient,
    private configParameters: AppConfigParameters,
  ) {}

  async disableEmployee(storeId: string) {
    posLogger.info('store', 'disableEmployee', { input: storeId });
    try {
      const STORE_TABLE = await this.configParameters.getStoreTableName();
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      const response = await esHandler.search({
        index: STORE_TABLE,
        body: {
          query: {
            match: {
              stores: `${storeId}`,
            },
          },
        },
      });
      const data = response.body.hits.hits.map((hit) => hit._source);

      if (data.length) {
        await Promise.all(
          data.map(async ({ id: employeeId, ...item }) => {
            if (JSON.stringify(item.stores) == JSON.stringify([storeId])) {
              const updateHandler = new UpdateEmployee(
                this.configService,
                this.docClient,
                this.ssmClient,
                this.cognitoClient,
                this.configParameters,
              );
              await updateHandler.updateEmployee(employeeId, {
                id: employeeId,
                isActive: false,
              });
            } else {
              const stores = item.stores;
              stores.splice(stores.indexOf(storeId), 1);
              const updateHandler = new UpdateEmployee(
                this.configService,
                this.docClient,
                this.ssmClient,
                this.cognitoClient,
                this.configParameters,
              );
              await updateHandler.updateEmployee(employeeId, {
                id: employeeId,
                stores,
              });
            }
          }),
        );
      }
    } catch (e) {
      posLogger.error('store', 'disableEmployee', { error: e });
      throw new Error(e);
    }
  }
}
