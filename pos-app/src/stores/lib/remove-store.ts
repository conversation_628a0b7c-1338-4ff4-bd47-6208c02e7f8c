// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { posLogger } from 'src/common/logger';
import { DisableEmployee } from './disable-employees';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppCognitoClient } from 'src/common/cognito-identity-client/cognito-identity-client';
import { StoreData } from '../entities/store.entity';
import moment from 'moment';
import { AppConfigParameters } from 'src/config/config';

export class RemoveStore {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private cognitoClient: AppCognitoClient,
    private configParameters: AppConfigParameters,
  ) {}

  async removeStore(id: string): Promise<StoreData> {
    posLogger.info('store', 'removeStore', { input: id });
    try {
      const STORE_TABLE = await this.configParameters.getStoreTableName();

      // Fire the update command
      const command = new UpdateCommand({
        TableName: STORE_TABLE,
        Key: {
          id,
        },
        UpdateExpression: 'SET #isDeleted = :isDeleted, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
          '#isDeleted': 'isDeleted',
        },
        ExpressionAttributeValues: {
          ':isDeleted': true,
          ':updatedAt': moment().toISOString(),
        },
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes } = await this.docClient.updateItem(command);

      const disableEmployeeHandler = new DisableEmployee(
        this.configService,
        this.ssmClient,
        this.docClient,
        this.cognitoClient,
        this.configParameters,
      );
      await disableEmployeeHandler.disableEmployee(id);

      if (Attributes) return Attributes;
    } catch (e) {
      posLogger.error('store', 'removeHandler', e);
      return e;
    }
  }
}
