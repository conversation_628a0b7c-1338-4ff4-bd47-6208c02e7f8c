// modules
import { BatchGetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { StoreData } from '../entities/store.entity';
import { AppConfigParameters } from 'src/config/config';

export class QueryStoresByIds {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryStoresByIds(storeIds: string[]): Promise<StoreData[]> {
    posLogger.info('store', 'queryStoresByIds', {
      input: { storeIds },
    });

    if (!storeIds.length) return [];

    const STORE_TABLE = await this.configParameters.getStoreTableName();
    const BATCH_SIZE = 100;
    const allResults: StoreData[] = [];

    // Helper to chunk the array
    const chunkArray = <T>(arr: T[], size: number): T[][] => {
      const result: T[][] = [];
      for (let i = 0; i < arr.length; i += size) {
        result.push(arr.slice(i, i + size));
      }
      return result;
    };

    try {
      const chunks = chunkArray(storeIds, BATCH_SIZE);

      for (const chunk of chunks) {
        const batchGetItemsCommand = new BatchGetCommand({
          RequestItems: {
            [STORE_TABLE]: {
              Keys: chunk.map((id) => ({ id })),
            },
          },
        });

        const { Responses = {}, UnprocessedKeys } =
          await this.docClient.batchGetItems(batchGetItemsCommand);

        const stores = Responses[STORE_TABLE] ?? [];
        allResults.push(...stores);

        // Optional: retry unprocessed keys (for extra reliability)
        if (UnprocessedKeys && UnprocessedKeys[STORE_TABLE]) {
          posLogger.info('store', 'queryStoresByIds', {
            message: 'Retrying unprocessed keys',
            count: UnprocessedKeys[STORE_TABLE].Keys?.length || 0,
          });

          const retryCommand = new BatchGetCommand({
            RequestItems: UnprocessedKeys,
          });

          const retryResponse =
            await this.docClient.batchGetItems(retryCommand);
          const retryStores = retryResponse.Responses?.[STORE_TABLE] ?? [];
          allResults.push(...retryStores);
        }
      }

      return allResults;
    } catch (e) {
      posLogger.error('store', 'queryStoresByIds', { error: e });
      throw e;
    }
  }
}
