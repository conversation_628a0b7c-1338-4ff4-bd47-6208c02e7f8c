// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { StoreData } from '../entities/store.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';

export class GetStore {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getStore(id: string = ''): Promise<StoreData> {
    posLogger.info('store', 'getStore', { input: { id } });
    try {
      if (!id) throw new CustomError('Bad Request', 400);

      const STORE_TABLE = await this.configParameters.getStoreTableName();

      const param = new GetCommand({
        TableName: STORE_TABLE,
        Key: {
          id,
        },
      });

      const { Item } = await this.docClient.getItem(param);

      if (!Item || Item?.isDeleted) {
        throw new CustomError(
          `Invalid ID! No store found for given ID ${id}.`,
          404,
        );
      }
      return Item;
    } catch (e) {
      posLogger.error('store', 'getStore', e);
      return e;
    }
  }
}
