// modules
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
// custom modules
import { posLogger } from 'src/common/logger';
import { StoreData } from '../entities/store.entity';
import { CreateStoreInput } from '../dto/create-store.input';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { CreateEmployee } from 'src/employees/lib/create-employee';
import { AppCognitoClient } from 'src/common/cognito-identity-client/cognito-identity-client';
import { Roles } from 'src/common/enum/roles';
import { GenerateCode } from 'src/common/helper/generate-code';
import { CreateEmployeeInput } from 'src/employees/dto/create-employee.input';

export class CreateStore {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private cognitoClient: AppCognitoClient,
  ) {}

  async createStore(createStoreInput: CreateStoreInput): Promise<StoreData> {
    posLogger.info('store', 'createStore', { input: createStoreInput });
    try {
      const employeeHandler = new CreateEmployee(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.cognitoClient,
        this.configParameters,
      );

      const codeHandler = new GenerateCode(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const code = await codeHandler.generateCode(
        'GLOBAL',
        'EMPLOYEE_COUNT',
        'TSCEMP-',
      );

      const storeUserInput = {
        email: createStoreInput.email,
        role: Roles.STORE_LOGIN,
        id: code,
      };
      const res = await employeeHandler.createCognitoUser(storeUserInput);

      const cognitoStoreId = res.find((e) => e.Name === 'sub').Value;

      const employeeInput: CreateEmployeeInput = {
        id: code,
        cognitoId: cognitoStoreId,
        firstName: createStoreInput.name,
        lastName: 'STORE',
        email: createStoreInput.email,
        phone: createStoreInput.phone,
        role: Roles.STORE_LOGIN,
        stores: [createStoreInput.id],
        designation: Roles.STORE_LOGIN,
      };
      await employeeHandler.createEmployee(employeeInput);

      const Item: StoreData = {
        ...createStoreInput,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
        isActive: true,
      };

      const STORE_TABLE = await this.configParameters.getStoreTableName();

      const params = new PutCommand({
        TableName: STORE_TABLE,
        Item,
        ConditionExpression: 'attribute_not_exists(id)',
      });
      await this.docClient.createItem(params);

      return Item;
    } catch (e) {
      posLogger.error('store', 'createStore', e);
      return e;
    }
  }
}
