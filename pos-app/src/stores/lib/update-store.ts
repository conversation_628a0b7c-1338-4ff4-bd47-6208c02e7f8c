// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { DisableEmployee } from './disable-employees';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppCognitoClient } from 'src/common/cognito-identity-client/cognito-identity-client';
import { StoreData } from '../entities/store.entity';
import { AppConfigParameters } from 'src/config/config';

export class UpdateStore {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private cognitoClient: AppCognitoClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateStore(
    id: string = '',
    updatePayload: any = {},
  ): Promise<StoreData> {
    posLogger.info('store', 'updateStore', { input: { id, updatePayload } });
    try {
      const STORE_TABLE = await this.configParameters.getStoreTableName();

      let updateExpressionString = 'Set ';
      const expressionAttributesValues = {};
      const expressionAttributeNames = {};

      delete updatePayload.id;
      updatePayload.updatedAt = moment().toISOString();

      if (updatePayload.isActive === false) {
        const disableEmployeeHandler = new DisableEmployee(
          this.configService,
          this.ssmClient,
          this.docClient,
          this.cognitoClient,
          this.configParameters,
        );
        await disableEmployeeHandler.disableEmployee(id);
      }

      Object.keys(updatePayload).map((key) => {
        updateExpressionString += `#${key} = :${key}, `;
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributesValues[`:${key}`] = updatePayload[key];
      });
      updateExpressionString = updateExpressionString.substring(
        0,
        updateExpressionString.length - 2,
      );

      const command = new UpdateCommand({
        TableName: STORE_TABLE,
        Key: {
          id,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributesValues,
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });
      const { Attributes } = await this.docClient.updateItem(command);

      if (Attributes) return Attributes;
    } catch (e) {
      posLogger.error('store', 'updateHandler', e);
      return e;
    }
  }
}
