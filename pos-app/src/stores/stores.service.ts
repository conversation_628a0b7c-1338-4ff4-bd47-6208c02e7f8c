import { Injectable } from '@nestjs/common';
import { CreateStoreInput } from './dto/create-store.input';
import { UpdateStoreInput } from './dto/update-store.input';
import { CreateStore } from './lib/create-store';
import { QueryStores } from './lib/get-all-stores';
import { GetStore } from './lib/get-store-by-id';
import { UpdateStore } from './lib/update-store';
import { RemoveStore } from './lib/remove-store';
import { ListStoreInput } from './dto/list-store.input';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppCognitoClient } from 'src/common/cognito-identity-client/cognito-identity-client';
import { QueryStoresByIds } from './lib/list-stores-by-ids';
import { AppConfigParameters } from 'src/config/config';

@Injectable()
export class StoresService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private cognitoClient: AppCognitoClient,
    private configParameters: AppConfigParameters,
  ) {}

  async create(createStoreInput: CreateStoreInput) {
    const createHandler = new CreateStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.cognitoClient,
    );
    return createHandler.createStore(createStoreInput);
  }

  async findAll(listStoreInput: ListStoreInput) {
    const queryHandler = new QueryStores(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return queryHandler.queryStores(listStoreInput);
  }

  async findAllStoresByIds(storeIds: string[]) {
    const queryProductsByIdsHandler = new QueryStoresByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    return queryProductsByIdsHandler.queryStoresByIds(storeIds);
  }

  async findOne(id: string) {
    const getHandler = new GetStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.getStore(id);
  }

  async update(id: string, updateStoreInput: UpdateStoreInput) {
    const updateHandler = new UpdateStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.cognitoClient,
      this.configParameters,
    );
    return updateHandler.updateStore(id, updateStoreInput);
  }

  async remove(id: string) {
    const removeHandler = new RemoveStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.cognitoClient,
      this.configParameters,
    );
    return removeHandler.removeStore(id);
  }
}
