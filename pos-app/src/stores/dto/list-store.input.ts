import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class StoreTermSearchFieldsInput {
  @Field(() => Boolean, {
    nullable: true,
  })
  isActive?: boolean;

  @Field(() => String, {
    nullable: true,
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
  })
  updatedAt?: string;
}

@InputType()
export class StoreTextSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
  })
  name?: string;

  @Field(() => String, {
    nullable: true,
  })
  id?: string;

  @Field(() => String, {
    nullable: true,
  })
  phone?: string;

  @Field(() => String, {
    nullable: true,
  })
  email?: string;

  @Field(() => String, {
    nullable: true,
  })
  posIds?: string;
}

@InputType()
export class StoreSortingFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class ListStoreInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size?: string;

  @Field(() => StoreTermSearchFieldsInput, {
    nullable: true,
    description: 'Search Boolean fields to filter the records',
  })
  termSearchFields?: StoreTermSearchFieldsInput;

  @Field(() => StoreTextSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  textSearchFields?: StoreTextSearchFieldsInput;

  @Field(() => StoreSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { createdAt: 'desc' },
  })
  sortBy?: StoreSortingFieldsInput;

  @Field(() => [String], {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  storeIds?: string[];
}
