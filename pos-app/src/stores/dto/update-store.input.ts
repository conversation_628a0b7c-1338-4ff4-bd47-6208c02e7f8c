import { InputType, Field } from '@nestjs/graphql';
import {
  AddressInput,
  BillingInfoInput,
  GstDetailsInput,
  PinelabsInput,
  SnapmintInput,
} from '../dto/create-store.input';
import { Address, BillingInfo, GstDetails } from '../entities/store.entity';

@InputType()
export class UpdateStoreInput {
  @Field(() => String, { nullable: false, description: 'Item pointer' })
  id: string;

  @Field(() => String, { nullable: true, description: 'Name of the store' })
  name?: string;

  @Field(() => [PinelabsInput], {
    nullable: true,
    description: 'Billing Information with Name and Address info',
  })
  pinelabsInfo: PinelabsInput[];

  @Field(() => String, {
    nullable: true,
    description: 'Description of the store',
  })
  description?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Contact number of the store',
  })
  phone?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Billing Information with Name and Address info',
  })
  reviewsBrochure?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Email address of the store',
  })
  email?: string;

  @Field(() => GstDetailsInput, {
    nullable: true,
    description: 'GST Details',
  })
  gstDetails?: GstDetails;

  @Field(() => BillingInfoInput, {
    nullable: true,
    description: 'Billing Information with Name and Address info',
  })
  billingInfo?: BillingInfo;

  @Field(() => AddressInput, { nullable: true, description: 'Address info' })
  address?: Address;

  @Field(() => String, {
    nullable: true,
    description: 'Quotation Start Code',
  })
  quotationStartCode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Invoice Start Code',
  })
  invoiceStartCode?: string;

  @Field(() => String, { nullable: true, description: 'store location key' })
  getStoreLocationKey?: string;

  @Field(() => String, { nullable: true, description: 'map link' })
  mapLink?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Store short code',
  })
  storeShortCode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Store Timings',
  })
  storeTimings?: string;

  @Field(() => [String], {
    nullable: true,
    description: 'Store Image',
  })
  storeImage?: string[];

  @Field(() => String, {
    nullable: true,
    description: 'SAP Location Code',
  })
  sapLocationCode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Warehouse mapping ID',
  })
  warehouseMappingId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'SAP Account Code',
  })
  accountCode: string;

  @Field(() => String, {
    nullable: true,
    description: 'SAP Card Code',
  })
  cardCode: string;

  @Field(() => [String], {
    nullable: true,
    description: 'POS ID',
  })
  posIds?: string[];

  @Field(() => [String], {
    nullable: true,
    description: 'MSwipe POS ID',
  })
  mswipePosIds?: string[];

  @Field(() => String, {
    nullable: true,
    description: 'Area Sales Manager',
  })
  asm?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Source Warehouse mapping ID',
  })
  sourceWarehouseMappingId: string;

  @Field(() => String, {
    nullable: true,
    description: 'DID Number',
  })
  didNumber?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Zonal Head',
  })
  zonalHead?: string;

  @Field(() => String, {
    nullable: true,
    description: 'EasyEcom Location ID',
  })
  easyEcomWarehouseLocationId?: string;

  @Field(() => Boolean, {
    nullable: false,
    description: 'Active/Inactive status',
  })
  isActive: boolean;

  @Field(() => SnapmintInput, {
    nullable: true,
    description: 'Billing Information with Name and Address info',
  })
  snapmintInfo: SnapmintInput;
}
