import { InputType, Field } from '@nestjs/graphql';
import { Address, BillingInfo, GstDetails } from '../entities/store.entity';

@InputType()
export class AddressInput {
  @Field(() => String, { nullable: false, description: 'Address Line 1' })
  line1: string;

  @Field(() => String, { nullable: true, description: 'Address Line 2' })
  line2?: string;

  @Field(() => String, { nullable: false, description: 'City' })
  city: string;

  @Field(() => String, { nullable: false, description: 'State' })
  state: string;

  @Field(() => String, { nullable: true, description: 'State Code' })
  stateCode?: string;

  @Field(() => String, { nullable: true, description: 'State Code' })
  stateNo?: string;

  @Field(() => String, { nullable: false, description: 'Country' })
  country: string;

  @Field(() => String, { nullable: false, description: 'Pin code' })
  pinCode: string;

  @Field(() => String, { nullable: true, description: 'Latitude' })
  latitude?: string;

  @Field(() => String, { nullable: true, description: 'Longitude' })
  longitude?: string;
}

@InputType()
export class BillingInfoInput {
  @Field(() => String, { nullable: false, description: 'Name of the Entity' })
  name: string;

  @Field(() => AddressInput, {
    nullable: false,
    description: 'Address of the Entity',
  })
  address: AddressInput;
}

@InputType()
export class GstDetailsInput {
  @Field(() => String, {
    nullable: false,
    description: 'GST Number',
  })
  gstNumber: string;

  @Field(() => String, {
    nullable: false,
    description: 'GST Owner Name',
  })
  companyName: string;
}

@InputType()
export class SnapmintInput {
  @Field(() => String, {
    nullable: false,
    description: 'snapmint Merchant ID',
  })
  merchantId: string;

  @Field(() => String, {
    nullable: false,
    description: 'snapmint token',
  })
  accessToken: string;

  @Field(() => String, {
    nullable: false,
    description: 'snapmint token',
  })
  merchantKey: string;
}
@InputType()
export class PinelabsInput {
  @Field(() => String, {
    nullable: false,
    description: 'Pinelabs POS ID',
  })
  posId: string;

  @Field(() => String, {
    nullable: false,
    description: 'pine labs storeid',
  })
  storeId: string;
}

@InputType()
export class CreateStoreInput {
  @Field(() => String, { nullable: false, description: 'Name of the store' })
  name: string;

  @Field(() => String, {
    nullable: true,
    description: 'Description of the store',
  })
  description?: string;

  @Field(() => String, {
    nullable: false,
    description: 'Contact number of the store',
  })
  phone: string;

  @Field(() => String, {
    nullable: false,
    description: 'Email address of the store',
  })
  email: string;

  @Field(() => String, {
    nullable: false,
    description: 'Source Warehouse mapping ID',
  })
  sourceWarehouseMappingId: string;

  @Field(() => String, {
    nullable: false,
    description: 'Store code',
  })
  id: string;

  @Field(() => GstDetailsInput, {
    nullable: true,
    description: 'GST Details',
  })
  gstDetails?: GstDetails;

  @Field(() => BillingInfoInput, {
    nullable: false,
    description: 'Billing Information with Name and Address info',
  })
  billingInfo: BillingInfo;

  @Field(() => String, {
    nullable: true,
    description: 'Billing Information with Name and Address info',
  })
  reviewsBrochure?: string;

  @Field(() => AddressInput, { nullable: false, description: 'Address info' })
  address: Address;

  @Field(() => String, {
    nullable: false,
    description: 'Quotation Start Code',
  })
  quotationStartCode: string;

  @Field(() => String, { nullable: false, description: 'store location key' })
  getStoreLocationKey: string;

  @Field(() => String, { nullable: false, description: 'map link' })
  mapLink: string;

  @Field(() => String, {
    nullable: false,
    description: 'Store short code',
  })
  storeShortCode: string;

  @Field(() => String, {
    nullable: false,
    description: 'Store Timings',
  })
  storeTimings: string;

  @Field(() => [String], {
    nullable: false,
    description: 'Store Image',
  })
  storeImage: string[];

  @Field(() => String, {
    nullable: false,
    description: 'Invoice Start Code',
  })
  invoiceStartCode: string;

  @Field(() => String, {
    nullable: false,
    description: 'SAP Location Code',
  })
  sapLocationCode: string;

  @Field(() => String, {
    nullable: false,
    description: 'Warehouse mapping ID',
  })
  warehouseMappingId: string;

  @Field(() => String, {
    nullable: true,
    description: 'EasyEcom Location ID',
  })
  easyEcomWarehouseLocationId?: string;

  @Field(() => String, {
    nullable: false,
    description: 'SAP Account Code',
  })
  accountCode: string;

  @Field(() => String, {
    nullable: false,
    description: 'SAP Card Code',
  })
  cardCode: string;

  @Field(() => [String], {
    nullable: true,
    description: 'POS ID',
  })
  posIds: string[];

  @Field(() => String, {
    nullable: true,
    description: 'DID Number',
  })
  didNumber?: string;

  @Field(() => [String], {
    nullable: true,
    description: 'Mswipe POS ID',
  })
  mswipePosIds?: string[];

  @Field(() => String, {
    nullable: false,
    description: 'Area Sales Manager',
  })
  asm: string;

  @Field(() => String, {
    nullable: false,
    description: 'Zonal Head',
  })
  zonalHead: string;

  @Field(() => SnapmintInput, {
    nullable: true,
    description: 'Billing Information with Name and Address info',
  })
  snapmintInfo: SnapmintInput;

  @Field(() => [PinelabsInput], {
    nullable: true,
    description: 'Billing Information with Name and Address info',
  })
  pinelabsInfo: PinelabsInput[];
}
