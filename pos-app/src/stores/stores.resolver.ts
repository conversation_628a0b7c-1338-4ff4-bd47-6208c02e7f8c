import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { StoresService } from './stores.service';
import { Store, Stores } from './entities/store.entity';
import { CreateStoreInput } from './dto/create-store.input';
import { UpdateStoreInput } from './dto/update-store.input';
import { ListStoreInput } from './dto/list-store.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { AdminGuard, CRMGuard, StaffGuard } from 'src/auth/roles.guard';

@Resolver(() => Stores)
@UseGuards(CustomAuthGuard, CRMGuard)
export class StoresResolver {
  constructor(
    private readonly storesService: StoresService,
    private readonly successHandler: SuccessHand<PERSON>,
    private readonly errorHandler: <PERSON>rror<PERSON><PERSON><PERSON>,
  ) {}

  @Mutation(() => Store)
  @UseGuards(AdminGuard)
  async createStore(
    @Args('createStoreInput') createStoreInput: CreateStoreInput,
  ) {
    try {
      const data = await this.storesService.create(createStoreInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create store',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Stores, { name: 'listStores' })
  @UseGuards(StaffGuard)
  async findAll(
    @Args('listStoreInput', { nullable: true })
    listStoreInput: ListStoreInput,
  ) {
    try {
      const { data, count } = await this.storesService.findAll(listStoreInput);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list stores',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Stores, { name: 'listStoresByIds' })
  @UseGuards(StaffGuard)
  async findAllStoresByIds(
    @Args('storeIds', { type: () => [String], nullable: true })
    storeIds: string[],
  ) {
    try {
      const data = await this.storesService.findAllStoresByIds(storeIds);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count: data.length };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list stores by ids',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Store, { name: 'getStore' })
  @UseGuards(StaffGuard)
  async findOne(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.storesService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get store',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Store)
  @UseGuards(AdminGuard)
  async updateStore(
    @Args('updateStoreInput') updateStoreInput: UpdateStoreInput,
  ) {
    try {
      const data = await this.storesService.update(
        updateStoreInput.id,
        updateStoreInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update store',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Store, { name: 'deleteStore' })
  @UseGuards(AdminGuard)
  async removeStore(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.storesService.remove(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to delete store',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
