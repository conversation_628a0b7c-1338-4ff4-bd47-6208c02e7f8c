import { Module } from '@nestjs/common';
import { StoresService } from './stores.service';
import { StoresResolver } from './stores.resolver';
import { EmployeesService } from 'src/employees/employees.service';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { CognitoIdentityClientModule } from 'src/common/cognito-identity-client/cognito-identity-client.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { ConfigParametersModule } from 'src/config/config.module';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    CognitoIdentityClientModule,
    ConfigParametersModule,
  ],
  providers: [
    StoresResolver,
    StoresService,
    EmployeesService,
    SuccessHandler,
    ErrorHandler,
  ],
})
export class StoresModule {}
