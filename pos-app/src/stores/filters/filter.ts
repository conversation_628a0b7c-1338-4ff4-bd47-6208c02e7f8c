export const searchingFilter = new Map<
  string,
  'string' | 'nonString' | 'arrayString' | 'boolean' | 'date'
>([
  ['name', 'string'],
  ['id', 'string'],
  ['isActive', 'boolean'],
  ['posIds', 'arrayString'],
  ['phone', 'string'],
  ['email', 'string'],
  ['createdAt', 'date'],
  ['updatedAt', 'date'],
]);

export const sortingFilter = new Map<string, 'string' | 'nonString'>([
  ['createdAt', 'string'],
  ['updatedAt', 'string'],
]);

export const sortingFilterType = {
  createdAt: 'date',
  updatedAt: 'date',
};
