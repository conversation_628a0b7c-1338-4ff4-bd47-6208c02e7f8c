import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType()
export class Address {
  @Field(() => String, { nullable: false, description: 'Address Line 1' })
  line1: string;

  @Field(() => String, { nullable: true, description: 'Address Line 2' })
  line2?: string;

  @Field(() => String, { nullable: false, description: 'City' })
  city: string;

  @Field(() => String, { nullable: false, description: 'State' })
  state: string;

  @Field(() => String, { nullable: true, description: 'State Code' })
  stateCode?: string;

  @Field(() => String, { nullable: true, description: 'State Code' })
  stateNo?: string;

  @Field(() => String, { nullable: false, description: 'Country' })
  country: string;

  @Field(() => String, { nullable: false, description: 'Pin code' })
  pinCode: string;

  @Field(() => String, { nullable: true, description: 'Latitude' })
  latitude?: string;

  @Field(() => String, { nullable: true, description: 'Longitude' })
  longitude?: string;
}

@ObjectType()
export class BillingInfo {
  @Field(() => String, { nullable: false, description: 'Name of the Entity' })
  name: string;

  @Field(() => Address, {
    nullable: false,
    description: 'Address of the Entity',
  })
  address: Address;
}

@ObjectType()
export class GstDetails {
  @Field(() => String, {
    nullable: false,
    description: 'GST Number',
  })
  gstNumber: string;

  @Field(() => String, {
    nullable: false,
    description: 'GST Owner Name',
  })
  companyName: string;
}

@ObjectType()
export class Snapmint {
  @Field(() => String, {
    nullable: false,
    description: 'snapmint Merchant ID',
  })
  merchantId: string;

  @Field(() => String, {
    nullable: false,
    description: 'snapmint token',
  })
  accessToken: string;

  @Field(() => String, {
    nullable: false,
    description: 'snapmint token',
  })
  merchantKey: string;
}

@ObjectType()
export class Pinelabs {
  @Field(() => String, {
    nullable: false,
    description: 'Pinelabs POS ID',
  })
  posId: string;

  @Field(() => String, {
    nullable: false,
    description: 'pine labs storeid',
  })
  storeId: string;
}

@ObjectType()
export class StoreData {
  @Field(() => ID, { nullable: false, description: 'Store ID' })
  id: string;

  @Field(() => String, { nullable: false, description: 'Name of the store' })
  name: string;

  @Field(() => String, {
    nullable: true,
    description: 'Description of the store',
  })
  description?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Billing Information with Name and Address info',
  })
  reviewsBrochure?: string;
  @Field(() => String, { nullable: true, description: 'store location key' })
  getStoreLocationKey?: string;

  @Field(() => String, { nullable: true, description: 'map link' })
  mapLink?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Store short code',
  })
  storeShortCode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Store Timings',
  })
  storeTimings?: string;

  @Field(() => [String], {
    nullable: true,
    description: 'Store Image',
  })
  storeImage?: string[];

  @Field(() => Address, {
    nullable: false,
    description: 'Address of the store',
  })
  address: Address;

  @Field(() => String, {
    nullable: false,
    description: 'Contact number of the store',
  })
  phone: string;

  @Field(() => String, {
    nullable: false,
    description: 'Email address of the store',
  })
  email: string;

  @Field(() => BillingInfo, {
    nullable: false,
    description: 'Billing Information with Name and Address info',
  })
  billingInfo: BillingInfo;

  @Field(() => GstDetails, {
    nullable: true,
    description: 'GST Details',
  })
  gstDetails?: GstDetails;

  @Field(() => String, {
    nullable: false,
    description: 'Time of store creation',
  })
  createdAt: string;

  @Field(() => String, { nullable: false, description: 'Time of store update' })
  updatedAt: string;

  @Field(() => Boolean, {
    nullable: false,
    description: 'Active/Inactive status',
  })
  isActive: boolean;

  @Field(() => String, {
    nullable: false,
    description: 'Quotation Start Code',
  })
  quotationStartCode: string;

  @Field(() => String, {
    nullable: false,
    description: 'Invoice Start Code',
  })
  invoiceStartCode: string;

  @Field(() => String, {
    nullable: false,
    description: 'SAP Location Code',
  })
  sapLocationCode: string;

  @Field(() => String, {
    nullable: false,
    description: 'Warehouse mapping ID',
  })
  warehouseMappingId: string;

  @Field(() => String, {
    nullable: true,
    description: 'EasyEcom Location ID',
  })
  easyEcomWarehouseLocationId?: string;

  @Field(() => String, {
    nullable: false,
    description: 'Source Warehouse mapping ID',
  })
  sourceWarehouseMappingId: string;

  @Field(() => String, {
    nullable: false,
    description: 'SAP Account Code',
  })
  accountCode: string;

  @Field(() => String, {
    nullable: false,
    description: 'SAP Card Code',
  })
  cardCode: string;

  @Field(() => [String], {
    nullable: true,
    description: 'POS ID',
  })
  posIds: string[];

  @Field(() => [String], {
    nullable: true,
    description: 'MSwipe POS ID',
  })
  mswipePosIds?: string[];

  @Field(() => String, {
    nullable: false,
    description: 'Area Sales Manager',
  })
  asm: string;

  @Field(() => String, {
    nullable: true,
    description: 'DID Number',
  })
  didNumber?: string;

  @Field(() => String, {
    nullable: false,
    description: 'Zonal Head',
  })
  zonalHead: string;

  @Field(() => Snapmint, {
    nullable: true,
    description: 'Billing Information with Name and Address info',
  })
  snapmintInfo: Snapmint;

  @Field(() => [Pinelabs], {
    nullable: true,
    description: 'Billing Information with Name and Address info',
  })
  pinelabsInfo: Pinelabs[];
}

@ObjectType()
export class Stores {
  @Field(() => [StoreData], { nullable: true })
  data: StoreData[];

  @Field(() => Number, { description: 'Count of Total data', nullable: true })
  count: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class Store {
  @Field(() => StoreData, { nullable: true })
  data: StoreData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
