import { ObjectType, Field, Float } from '@nestjs/graphql';

@ObjectType()
export class ShopifyAddressData {
  @Field(() => Number, { nullable: true })
  id?: number;

  @Field(() => String, { nullable: true })
  customerId?: string;

  @Field(() => String, { nullable: true })
  firstName?: string;

  @Field(() => String, { nullable: true })
  lastName?: string;

  @Field(() => String, { nullable: true })
  company?: string;

  @Field(() => String, { nullable: true })
  line1?: string;

  @Field(() => String, { nullable: true })
  line2?: string;

  @Field(() => String, { nullable: true })
  city?: string;

  @Field(() => String, { nullable: true })
  state?: string;

  @Field(() => String, { nullable: true })
  country?: string;

  @Field(() => String, { nullable: true })
  pinCode?: string;

  @Field(() => String, { nullable: true })
  name?: string;

  @Field(() => String, { nullable: true })
  province_code?: string;

  @Field(() => String, { nullable: true })
  country_code?: string;

  @Field(() => String, { nullable: true })
  country_name?: string;

  @Field(() => Boolean, { nullable: true })
  default?: boolean;

  @Field(() => String, { nullable: true })
  serviceLift?: string;

  @Field(() => String, { nullable: true })
  accommodationType?: string;

  @Field(() => String, { nullable: true })
  landmark?: string;

  @Field(() => Float, { nullable: true })
  latitude?: number;

  @Field(() => Float, { nullable: true })
  longitude?: number;
}

@ObjectType()
export class EmailMarketingConsent {
  @Field(() => String, { nullable: true })
  state?: string;

  @Field(() => String, { nullable: true })
  opt_in_level?: string;

  @Field(() => String, { nullable: true })
  consent_updated_at?: string;
}

@ObjectType()
export class SmsMarketingConsent {
  @Field(() => String, { nullable: true })
  state?: string;

  @Field(() => String, { nullable: true })
  opt_in_level?: string;

  @Field(() => String, { nullable: true })
  consent_updated_at?: string;

  @Field(() => String, { nullable: true })
  consent_collected_from?: string;
}

@ObjectType()
export class CustomerData {
  @Field(() => String, { nullable: true })
  id?: string;

  @Field(() => String, { nullable: true })
  email?: string;

  @Field(() => String, { nullable: true })
  created_at?: string;

  @Field(() => String, { nullable: true })
  updated_at?: string;

  @Field(() => String, { nullable: true })
  firstName?: string;

  @Field(() => String, { nullable: true })
  lastName?: string;

  @Field(() => String, { nullable: true })
  dob?: string;

  @Field(() => String, { nullable: false })
  anniversaryDate?: string;

  @Field(() => Number, { nullable: true })
  orders_count?: number;

  @Field(() => String, { nullable: true })
  state?: string;

  @Field(() => String, { nullable: true })
  total_spent?: string;

  @Field(() => Number, { nullable: true })
  last_order_id?: number;

  @Field(() => String, { nullable: true })
  note?: string;

  @Field(() => Boolean, { nullable: true })
  verified_email?: boolean;

  @Field(() => String, { nullable: true })
  multipass_identifier?: string;

  @Field(() => Boolean, { nullable: true })
  tax_exempt?: boolean;

  @Field(() => String, { nullable: true })
  tags?: string;

  @Field(() => String, { nullable: true })
  last_order_name?: string;

  @Field(() => String, { nullable: true })
  currency?: string;

  @Field(() => String, { nullable: true })
  phone?: string;

  @Field(() => [ShopifyAddressData], { nullable: true })
  addresses?: ShopifyAddressData[];

  @Field(() => [String], { nullable: true })
  tax_exemptions?: string[];

  @Field(() => EmailMarketingConsent, { nullable: true })
  email_marketing_consent?: EmailMarketingConsent;

  @Field(() => SmsMarketingConsent, { nullable: true })
  sms_marketing_consent?: SmsMarketingConsent;

  @Field(() => String, { nullable: true })
  admin_graphql_api_id?: string;

  @Field(() => ShopifyAddressData, { nullable: true })
  default_address?: ShopifyAddressData;
}

@ObjectType()
export class Customer {
  @Field(() => CustomerData, { nullable: true })
  data: CustomerData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class ShopifyAddress {
  @Field(() => ShopifyAddressData, { nullable: true })
  data: ShopifyAddressData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
