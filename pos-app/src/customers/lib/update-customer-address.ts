import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { UpdateCustomerAddressInput } from '../dto/update-customer-address.input';
import { ShopifyAddressData } from '../entities/customer.entity';
import {
  mapCustomerAddressData,
  transformAddressFromGraphQL,
} from '../helper/map-customer-from-shopify';
import { AppShopify } from 'src/common/shopify/shopify';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { getCustomerQuery, updateCustomerMutation } from './customerGQL';

export class UpdateCustomerAddress {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private shopifyClient: AppShopify,
    private configParameters: AppConfigParameters,
    private docClient: AppDocumentClient,
  ) {}

  async updateOrAddExtraAddressData(address: any) {
    try {
      const CUSTOMER_ADDRESS_TABLE =
        await this.configParameters.getCustomerAddressTableName();

      const updateFields = Object.keys(address).filter(
        (key) => key !== 'pk' && key !== 'sk',
      );

      if (updateFields.length === 0) {
        throw new Error('No fields to update.');
      }

      const updateExpression = `SET ${updateFields
        .map((field) => `${field} = :${field}`)
        .join(', ')}`;

      const expressionAttributeValues = updateFields.reduce(
        (acc, field) => {
          acc[`:${field}`] = address[field];
          return acc;
        },
        {} as Record<string, any>,
      );

      const updateCommand = new UpdateCommand({
        TableName: CUSTOMER_ADDRESS_TABLE,
        Key: {
          pk: address.pk,
          sk: address.sk,
        },
        UpdateExpression: updateExpression,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'ALL_NEW',
      });

      return await this.docClient.updateItem(updateCommand);
    } catch (error) {
      posLogger.error('customer', 'updateOrAddExtraAddressData', { error });
      throw error;
    }
  }

  private async executeShopifyGraphQL(
    query: string,
    variables: any,
    accessToken: string,
    adminUrl: string,
  ) {
    const response = await fetch(`${adminUrl}/graphql.json`, {
      method: 'POST',
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    return await response.json();
  }

  private async fetchCustomerData(
    customerId: string,
    accessToken: string,
    adminUrl: string,
  ) {
    const customerData = await this.executeShopifyGraphQL(
      getCustomerQuery,
      { customerId },
      accessToken,
      adminUrl,
    );

    if (customerData.errors) {
      throw new Error(JSON.stringify(customerData.errors));
    }

    return customerData;
  }

  private async updateCustomerData(
    formattedCustomerId: string,
    updatedAddresses: any[],
    accessToken: string,
    adminUrl: string,
  ) {
    const updateData = await this.executeShopifyGraphQL(
      updateCustomerMutation,
      {
        input: {
          id: formattedCustomerId,
          addresses: updatedAddresses,
        },
      },
      accessToken,
      adminUrl,
    );

    return updateData;
  }

  async updateCustomerAddress(
    updateCustomerAddressInput: UpdateCustomerAddressInput,
  ): Promise<ShopifyAddressData> {
    posLogger.info('customer', 'updateCustomerAddressInput', {
      input: { updateCustomerAddressInput },
    });
    const { address, customerId, phone, firstName, lastName, addressId } =
      updateCustomerAddressInput;
    const {
      line1,
      line2,
      city,
      country,
      state,
      pinCode,
      latitude,
      longitude,
      serviceLift,
      accommodationType,
      landmark,
    } = address;

    try {
      const [SHOPIFY_ACCESS_TOKEN, SHOPIFY_ADMIN_BASE_URL] = await Promise.all([
        await this.shopifyClient.getShopifyAccessToken(),
        await this.shopifyClient.getShopifyAdminBaseUrl(),
      ]);

      const formattedCustomerId = `gid://shopify/Customer/${customerId}`;
      const formattedAddressId = `gid://shopify/MailingAddress/${addressId}?model_name=CustomerAddress`;

      const customerData = await this.fetchCustomerData(
        formattedCustomerId,
        SHOPIFY_ACCESS_TOKEN,
        SHOPIFY_ADMIN_BASE_URL,
      );

      const existingAddresses = customerData.data?.customer?.addresses || [];

      const updatedAddresses = existingAddresses.map((addr) => {
        if (addr.id === formattedAddressId) {
          return {
            id: formattedAddressId,
            address1: line1,
            address2: line2,
            city,
            country,
            province: state,
            zip: pinCode,
            phone,
            firstName,
            lastName,
          };
        }
        return addr;
      });

      const updateData = await this.updateCustomerData(
        formattedCustomerId,
        updatedAddresses,
        SHOPIFY_ACCESS_TOKEN,
        SHOPIFY_ADMIN_BASE_URL,
      );

      let customer_address;
      let customerErrors;

      if (updateData.errors) {
        customerErrors = updateData.errors;
      } else if (updateData.data?.customerUpdate?.userErrors?.length > 0) {
        customerErrors = updateData.data.customerUpdate.userErrors;
      } else {
        const addresses =
          updateData.data?.customerUpdate?.customer?.addresses || [];
        customer_address = addresses.find(
          (addr) => addr.id === formattedAddressId,
        );
      }

      if (customerErrors) {
        const errorMessage = JSON.stringify(customerErrors);
        if (
          errorMessage.includes('"message":"Address has already been taken"') ||
          (errorMessage.includes('addresses') &&
            errorMessage.includes('has already been taken'))
        ) {
          console.log('Only landmark got changed...', errorMessage);
          customer_address = {
            customer_id: customerId,
            first_name: firstName,
            last_name: lastName,
            phone,
            address1: line1,
            address2: line2,
            city,
            country: country,
            province: state,
            zip: pinCode,
          };
        } else {
          throw new Error(errorMessage);
        }
      }

      if (customer_address && !customer_address.customer_id) {
        customer_address = transformAddressFromGraphQL(
          customer_address,
          customerId,
        );
      }

      const extraDataPayload = {
        pk: phone.replace('+91', ''),
        sk: addressId.toString(),
        id: Number(addressId),
        customer_id: customerId,
        serviceLift,
        accommodationType,
        landmark,
        latitude,
        longitude,
      };

      await this.updateOrAddExtraAddressData(extraDataPayload);

      const addressData: ShopifyAddressData =
        mapCustomerAddressData(customer_address);

      return { ...addressData, ...extraDataPayload };
    } catch (e) {
      posLogger.error('customer', 'updateCustomerAddress', e);
      throw e;
    }
  }
}
