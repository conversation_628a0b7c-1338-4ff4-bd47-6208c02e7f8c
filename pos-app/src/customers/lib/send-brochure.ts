import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { AppConfigParameters } from 'src/config/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { GetCustomer } from './get-customer';
import { AppShopify } from 'src/common/shopify/shopify';

export class SendBrochure {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}

  async sendBrochure(
    phone: string,
    category: string,
    storeId?: string,
  ): Promise<any> {
    posLogger.info('brochure', 'SendBrochure', {
      phone,
      category,
      storeId,
    });

    const pdfHandler = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );
    const getHandler = new GetCustomer(
      this.configService,
      this.ssmClient,
      this.shopifyClient,
      this.configParameters,
      this.docClient,
    );
    const customer = await getHandler.getCustomer(phone, 'BROCHURE', false);
    if (!Array.isArray(customer)) {
      const { firstName = '' } = customer || {};

      const { link: brochureLink } = await pdfHandler.sendBrochureByWhatsapp(
        firstName || 'Customer',
        phone,
        category,
        storeId,
      );

      return brochureLink;
    }
  }
}
