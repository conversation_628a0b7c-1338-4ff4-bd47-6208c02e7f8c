import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateCustomerAddressInput } from '../dto/create-customer-address.input';
import { ShopifyAddressData } from '../entities/customer.entity';
import {
  extractNumericId,
  mapCustomerAddressData,
} from '../helper/map-customer-from-shopify';
import { AppShopify } from 'src/common/shopify/shopify';
import { AppConfigParameters } from 'src/config/config';
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { getCustomerQuery, updateCustomerMutation } from './customerGQL';

export class CreateCustomerAddress {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private shopifyClient: AppShopify,
    private configParameters: AppConfigParameters,
    private docClient: AppDocumentClient,
  ) {}

  async saveExtraData(address: any) {
    try {
      const CUSTOMER_ADDRESS_TABLE =
        await this.configParameters.getCustomerAddressTableName();

      const orderCommand = new PutCommand({
        TableName: CUSTOMER_ADDRESS_TABLE,
        Item: address,
        ConditionExpression: 'attribute_not_exists(id)',
      });

      return await this.docClient.createItem(orderCommand);
    } catch (error) {
      posLogger.error('customer', 'createCustomerAddressWithExtraData', {
        error,
      });
      throw error;
    }
  }
  async createCustomerAddress(
    createCustomerAddressInput: CreateCustomerAddressInput,
  ): Promise<ShopifyAddressData> {
    posLogger.info('customer', 'createCustomerAddress', {
      input: { createCustomerAddressInput },
    });
    const { address, customerId, phone } = createCustomerAddressInput;
    const {
      line1,
      line2,
      city,
      country,
      state,
      pinCode,
      serviceLift,
      accommodationType,
      landmark,
      latitude,
      longitude,
    } = address;

    try {
      const [SHOPIFY_ACCESS_TOKEN, SHOPIFY_ADMIN_BASE_URL] = await Promise.all([
        await this.shopifyClient.getShopifyAccessToken(),
        await this.shopifyClient.getShopifyAdminBaseUrl(),
      ]);

      const getCustomerResponse = await fetch(
        `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          },
          body: JSON.stringify({
            query: getCustomerQuery,
            variables: {
              customerId: `gid://shopify/Customer/${customerId}`,
            },
          }),
        },
      );

      const customerData = await getCustomerResponse.json();

      if (customerData.errors) {
        throw new Error(JSON.stringify(customerData.errors));
      }

      const existingAddresses = customerData.data.customer.addresses || [];

      const addressInput = {
        phone,
        address1: line1,
        address2: line2,
        city,
        country,
        province: state,
        zip: pinCode,
      };

      const existingAddressesInput = existingAddresses.map((addr) => ({
        id: addr.id,
        firstName: addr.firstName,
        lastName: addr.lastName,
        address1: addr.address1,
        address2: addr.address2,
        city: addr.city,
        country: addr.country,
        province: addr.province,
        zip: addr.zip,
        phone: addr.phone,
      }));

      const updateResponse = await fetch(
        `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          },
          body: JSON.stringify({
            query: updateCustomerMutation,
            variables: {
              input: {
                id: `gid://shopify/Customer/${customerId}`,
                addresses: [...existingAddressesInput, addressInput],
              },
            },
          }),
        },
      );

      const updateData = await updateResponse.json();
      console.log('updateData', JSON.stringify(updateData));

      if (updateData.errors) {
        throw new Error(JSON.stringify(updateData.errors));
      }

      if (
        updateData.data.customerUpdate.userErrors &&
        updateData.data.customerUpdate.userErrors.length > 0
      ) {
        throw new Error(
          JSON.stringify(updateData.data.customerUpdate.userErrors),
        );
      }

      const allAddresses = updateData.data.customerUpdate.customer.addresses;
      const newAddress = allAddresses[allAddresses.length - 1];
      const addressId = extractNumericId(newAddress.id);

      const numericAddressId = parseInt(addressId, 10);

      const extraDataPayload = {
        pk: phone.replace('+91', ''),
        sk: addressId,
        id: numericAddressId,
        customer_id: customerId,
        serviceLift,
        accommodationType,
        landmark,
        latitude,
        longitude,
      };

      await this.saveExtraData(extraDataPayload);

      const rawAddressData = {
        id: numericAddressId,
        customer_id: customerId,
        first_name: newAddress.firstName || '',
        last_name: newAddress.lastName || '',
        company: null,
        address1: line1,
        address2: line2,
        city,
        province: state,
        country,
        zip: pinCode,
        phone,
        name: `${newAddress.firstName || ''} ${newAddress.lastName || ''}`.trim(),
        province_code: null,
        country_code: null,
        country_name: country,
        default: true,
      };

      posLogger.info('customer', 'beforeMapping', { rawAddressData });

      const mappedAddress = mapCustomerAddressData(rawAddressData);

      posLogger.info('customer', 'afterMapping', { mappedAddress });

      const result = {
        ...mappedAddress,
        serviceLift,
        accommodationType,
        landmark,
        latitude,
        longitude,
        country_name: country,
        default: true,
      };

      if (!result.firstName && rawAddressData.first_name) {
        result.firstName = rawAddressData.first_name;
      }

      if (!result.lastName && rawAddressData.last_name) {
        result.lastName = rawAddressData.last_name;
      }

      posLogger.info('customer', 'finalResult', { result });

      return result;
    } catch (e) {
      posLogger.error('customer', 'createCustomerAddress', { error: e });
      throw e;
    }
  }
}
