// GraphQL queries and mutations
export const QUERIES = {
  CUSTOMERS_BY_PHONE: `
    query GetCustomersByPhone($query: String!) {
      customers(first: 10, query: $query) {
        edges {
          node {
            id
            firstName
            lastName
            email
            phone
            createdAt
            updatedAt
            orders(first: 1) {
              edges {
                node {
                  id
                  name
                  totalPriceSet {
                    shopMoney {
                      amount
                      currencyCode
                    }
                  }
                }
              }
            }
            state
            note
            verifiedEmail
            taxExempt
            tags
            taxExemptions
            emailMarketingConsent {
              consentUpdatedAt
              marketingState
              marketingOptInLevel
            }
            smsMarketingConsent {
              consentUpdatedAt
              marketingState
              marketingOptInLevel
            }
            defaultAddress {
              id
              address1
              address2
              city
              provinceCode
              province
              country
              countryCodeV2
              zip
              phone
              firstName
              lastName
              company
              name
            }
            addresses {
              id
              address1
              address2
              city
              provinceCode
              province
              country
              countryCodeV2
              zip
              phone
              firstName
              lastName
              company
              name
            }
          }
        }
      }
    }
  `,

  CUSTOMER_METAFIELDS: `
    query GetCustomerMetafields($customerId: ID!) {
      customer(id: $customerId) {
        metafields(first: 20) {
          edges {
            node {
              id
              namespace
              key
              value
            }
          }
        }
      }
    }
  `,
};

export const MUTATIONS = {
  CREATE_CUSTOMER: `
    mutation CustomerCreate($input: CustomerInput!) {
      customerCreate(input: $input) {
        customer {
          id
          firstName
          lastName
          email
          phone
          createdAt
          updatedAt
          orders(first: 1) {
            edges {
              node {
                id
                name
                totalPriceSet {
                  shopMoney {
                    amount
                    currencyCode
                  }
                }
              }
            }
          }
          state
          note
          verifiedEmail
          taxExempt
          tags
          taxExemptions
          emailMarketingConsent {
            consentUpdatedAt
            marketingState
            marketingOptInLevel
          }
          smsMarketingConsent {
            consentUpdatedAt
            marketingState
            marketingOptInLevel
          }
          defaultAddress {
            id
            address1
            address2
            city
            provinceCode
            province
            country
            countryCodeV2
            zip
            phone
            firstName
            lastName
            company
            name
          }
          addresses {
            id
            address1
            address2
            city
            provinceCode
            province
            country
            countryCodeV2
            zip
            phone
            firstName
            lastName
            company
            name
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `,
};

export const getCustomerQuery = `
query getCustomer($customerId: ID!) {
  customer(id: $customerId) {
    addresses {
      id
      address1
      address2
      city
      country
      province
      zip
      phone
      firstName
      lastName
    }
  }
}
`;
export const updateCustomerMutation = `
mutation customerUpdate($input: CustomerInput!) {
  customerUpdate(input: $input) {
    customer {
      id
      addresses {
        id
      address1
      address2
      city
      provinceCode
      province
      country
      countryCodeV2
      zip
      phone
      firstName
      lastName
      company
      name
      }
    }
    userErrors {
      field
      message
    }
  }
}
`;

export const updateCustomerNameGraphqlQuery = `
mutation updateCustomer($input: CustomerInput!) {
  customerUpdate(input: $input) {
    customer {
      id
      email
      createdAt
      updatedAt
      firstName
      lastName
      phone
      note
      verifiedEmail
      taxExempt
      tags
      state
      orders(first: 1) {
        edges {
          node {
            id
            name
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
      }
      addresses {
        id
        firstName
        lastName
        company
        address1
        address2
        city
        province
        country
        zip
        phone
        name
        provinceCode
        countryCodeV2
      }
      defaultAddress {
        id
        firstName
        lastName
        company
        address1
        address2
        city
        province
        country
        zip
        phone
        name
        provinceCode
        countryCodeV2
      }
      emailMarketingConsent {
        marketingState
        marketingOptInLevel
        consentUpdatedAt
      }
      smsMarketingConsent {
        marketingState
        marketingOptInLevel
        consentUpdatedAt
        consentCollectedFrom
      }
      taxExemptions
      metafields(first: 10) {
        edges {
          node {
            id
            namespace
            key
            value
          }
        }
      }
    }
    userErrors {
      field
      message
    }
  }
}
`;
