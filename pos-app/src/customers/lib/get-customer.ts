import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import {
  constructCustomerResponse,
  mapCustomerData,
  transformCustomerFromGraphQL,
} from '../helper/map-customer-from-shopify';
import { AppShopify } from 'src/common/shopify/shopify';
import { CustomerData, ShopifyAddressData } from '../entities/customer.entity';
import { optInCustomer } from 'src/common/helper/opt-in-customer';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { QUERIES, MUTATIONS } from './customerGQL';

export class GetCustomer {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private shopifyClient: AppShopify,
    private configParameters: AppConfigParameters,
    private docClient: AppDocumentClient,
  ) {}

  async fetchExtraAddressData(addresses: ShopifyAddressData[], phone: string) {
    try {
      const CUSTOMER_ADDRESS_TABLE =
        await this.configParameters.getCustomerAddressTableName();
      const data = await Promise.all(
        addresses.map(async (address) => {
          const command = new GetCommand({
            TableName: CUSTOMER_ADDRESS_TABLE,
            Key: {
              pk: phone,
              sk: address.id.toString(),
            },
          });
          const { Item: data } = await this.docClient.getItem(command);
          return { ...address, ...data };
        }),
      );
      return data;
    } catch (error) {
      console.log(
        error,
        '::::: Error getting extra data for addresses from DB.',
      );
    }
  }

  async executeShopifyGraphQL(
    endpoint: string,
    accessToken: string,
    query: string,
    variables: Record<string, any>,
  ): Promise<any> {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'X-Shopify-Access-Token': accessToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    const result = await response.json();

    return result;
  }

  async getCustomer<T extends boolean>(
    phone: string,
    type = 'API',
    fetchAllIds?: T,
  ): Promise<CustomerData[] | CustomerData> {
    posLogger.info('customer', 'getCustomer', { input: { phone } });

    try {
      // Get Shopify credentials
      const [SHOPIFY_ACCESS_TOKEN, SHOPIFY_ADMIN_BASE_URL] = await Promise.all([
        await this.shopifyClient.getShopifyAccessToken(),
        await this.shopifyClient.getShopifyAdminBaseUrl(),
      ]);

      const graphqlEndpoint = `${SHOPIFY_ADMIN_BASE_URL.replace(
        /\/admin\/api\/\d{4}-\d{2}/,
        '/admin/api/2023-10',
      )}/graphql.json`;

      const customersData = await this.executeShopifyGraphQL(
        graphqlEndpoint,
        SHOPIFY_ACCESS_TOKEN,
        QUERIES.CUSTOMERS_BY_PHONE,
        { query: `phone:${phone}` },
      );

      const customers =
        customersData?.data?.customers?.edges?.map((edge: any) =>
          transformCustomerFromGraphQL(edge.node),
        ) || [];

      if (customers?.length) {
        await optInCustomer(phone);

        const customerIds = customers.map((customer) => customer?.id);
        if (fetchAllIds) {
          return customerIds;
        }

        const singleID = customers[0].id;

        const addresses = await this.fetchExtraAddressData(
          customers[0].addresses,
          phone.replace('+91', ''),
        );

        const metafieldsData = await this.executeShopifyGraphQL(
          graphqlEndpoint,
          SHOPIFY_ACCESS_TOKEN,
          QUERIES.CUSTOMER_METAFIELDS,
          { customerId: `gid://shopify/Customer/${singleID}` },
        );
        console.log('metafieldsData', metafieldsData);

        const metafields =
          metafieldsData?.data?.customer?.metafields?.edges?.map(
            (edge: any) => edge.node,
          ) || [];

        return constructCustomerResponse(
          customers[0],
          addresses,
          phone,
          metafields,
        );
      }

      if (type === 'API') {
        const createCustomerData = await this.executeShopifyGraphQL(
          graphqlEndpoint,
          SHOPIFY_ACCESS_TOKEN,
          MUTATIONS.CREATE_CUSTOMER,
          { input: { phone } },
        );

        const userErrors = createCustomerData?.data?.customerCreate?.userErrors;
        if (userErrors && userErrors.length > 0) {
          throw new Error(JSON.stringify(userErrors));
        }

        const customer = transformCustomerFromGraphQL(
          createCustomerData?.data?.customerCreate?.customer,
        );

        const addresses = await this.fetchExtraAddressData(
          customer.addresses,
          phone.replace('+91', ''),
        );

        await optInCustomer(phone);
        return { ...mapCustomerData({ ...customer, addresses }), phone };
      }

      return undefined;
    } catch (e) {
      posLogger.error('customer', 'getCustomer', { error: e });
      throw e;
    }
  }
}
