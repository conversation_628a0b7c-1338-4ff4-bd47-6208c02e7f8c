import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CustomerData } from '../entities/customer.entity';
import {
  mapCustomerData,
  processCustomerMetafieldsAndData,
} from '../helper/map-customer-from-shopify';
import { SetCustomerNameInput } from '../dto/set-customer-name.input';
import { AppShopify } from 'src/common/shopify/shopify';
import moment from 'moment';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { transformCustomerFromGraphQL } from '../helper/map-customer-from-shopify';
import { updateCustomerNameGraphqlQuery } from './customerGQL';

export class SetCustomerName {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private shopifyClient: AppShopify,
  ) {}

  async setCustomerName(
    setCustomerNameInput: SetCustomerNameInput,
  ): Promise<CustomerData> {
    console.log('======setCustomerNameInput======', setCustomerNameInput);
    posLogger.info('customer', 'setCustomerName', {
      input: { setCustomerNameInput },
    });
    const { customerId, firstName, lastName, dob, ...restCustomer } =
      setCustomerNameInput;

    try {
      // Validate customer age if DOB is provided
      if (dob) {
        const isAgeValid =
          moment().diff(moment(dob, 'YYYY-MM-DD'), 'years') >= 18;
        if (!isAgeValid) {
          throw new CustomError('Customer is less than 18 years old', 400);
        }
      }

      const [SHOPIFY_ACCESS_TOKEN, SHOPIFY_ADMIN_BASE_URL] = await Promise.all([
        await this.shopifyClient.getShopifyAccessToken(),
        await this.shopifyClient.getShopifyAdminBaseUrl(),
      ]);

      const graphqlVariables = {
        input: {
          id: `gid://shopify/Customer/${customerId}`,
          firstName: firstName,
          lastName: lastName,
          metafields: [
            {
              namespace: 'custom',
              key: 'date_of_birth_string',
              type: 'single_line_text_field',
              value: dob || '',
            },
            {
              namespace: 'custom',
              key: 'anniversary_date',
              type: 'single_line_text_field',
              value: restCustomer.anniversaryDate || '',
            },
          ],
        },
      };

      const response = await fetch(`${SHOPIFY_ADMIN_BASE_URL}/graphql.json`, {
        method: 'POST',
        headers: {
          'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: updateCustomerNameGraphqlQuery,
          variables: graphqlVariables,
        }),
      });

      const result = await response.json();

      if (result.errors) {
        throw new Error(JSON.stringify(result.errors));
      }

      const userErrors = result.data?.customerUpdate?.userErrors;
      if (userErrors && userErrors.length > 0) {
        throw new Error(JSON.stringify(userErrors));
      }

      const customer = result.data?.customerUpdate?.customer;
      if (!customer) {
        throw new Error('Customer update failed');
      }

      let customerData = transformCustomerFromGraphQL(customer);

      customerData = processCustomerMetafieldsAndData(customer, customerData);
      return mapCustomerData(customerData);
    } catch (error) {
      posLogger.error('customer', 'setCustomerName', { error });
      throw error;
    }
  }
}
