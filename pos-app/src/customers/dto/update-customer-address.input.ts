import { InputType, Field } from '@nestjs/graphql';
import { CustomerAddressInput } from './create-customer-address.input';

@InputType()
export class UpdateCustomerAddressInput {
  @Field(() => CustomerAddressInput, {
    nullable: false,
    description: 'Address',
  })
  address: CustomerAddressInput;

  @Field(() => String, { nullable: true, description: 'Phone number' })
  phone?: string;

  @Field(() => String, { nullable: true, description: 'First Name' })
  firstName?: string;

  @Field(() => String, { nullable: true, description: 'Last Name' })
  lastName?: string;

  @Field(() => String, { nullable: false, description: 'Customer ID' })
  customerId: string;

  @Field(() => String, { nullable: false, description: 'Address ID' })
  addressId: string;
}
