import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class SetCustomerNameInput {
  @Field(() => String, { nullable: true, description: 'First Name' })
  firstName: string;

  @Field(() => String, { nullable: true, description: 'Last Name' })
  lastName: string;

  @Field(() => String, { nullable: true, description: 'Last Name' })
  email: string;

  @Field(() => String, { nullable: false, description: 'Last Name' })
  customerId: string;

  @Field(() => String, { nullable: true, description: 'Last Name' })
  dob?: string;

  @Field(() => String, { nullable: true, description: 'Anniversary Date' })
  anniversaryDate: string;
}
