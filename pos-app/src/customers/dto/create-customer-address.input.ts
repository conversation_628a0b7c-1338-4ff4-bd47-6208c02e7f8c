import { InputType, Field, Float } from '@nestjs/graphql';

@InputType()
export class CustomerAddressInput {
  @Field(() => String, { nullable: false, description: 'Address Line 1' })
  line1: string;

  @Field(() => String, { nullable: true, description: 'Address Line 2' })
  line2?: string;

  @Field(() => String, { nullable: false, description: 'City' })
  city: string;

  @Field(() => String, { nullable: false, description: 'State' })
  state: string;

  @Field(() => String, { nullable: false, description: 'Country' })
  country: string;

  @Field(() => String, { nullable: false, description: 'Pin code' })
  pinCode: string;

  @Field(() => String, { nullable: true, description: 'latitude' })
  serviceLift?: string;

  @Field(() => String, { nullable: true, description: 'longitude' })
  accommodationType?: string;

  @Field(() => String, { nullable: true, description: 'longitude' })
  landmark?: string;

  @Field(() => Float, { nullable: true, description: 'latitude' })
  latitude?: number;

  @Field(() => Float, { nullable: true, description: 'longitude' })
  longitude: number;
}

@InputType()
export class CreateCustomerAddressInput {
  @Field(() => CustomerAddressInput, {
    nullable: false,
    description: 'Address Line 1',
  })
  address: CustomerAddressInput;

  @Field(() => String, { nullable: true, description: 'Phone number' })
  phone?: string;

  @Field(() => String, { nullable: true, description: 'First Name' })
  firstName?: string;

  @Field(() => String, { nullable: true, description: 'Last Name' })
  lastName?: string;

  @Field(() => String, { nullable: false, description: 'Customer ID' })
  customerId: string;
}
