import { Module } from '@nestjs/common';
import { CustomersService } from './customers.service';
import { CustomersResolver } from './customers.resolver';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { ConfigParametersModule } from 'src/config/config.module';
import { ShopifyModule } from 'src/common/shopify/shopify.module';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';

@Module({
  imports: [
    SsmClientModule,
    ShopifyModule,
    ConfigParametersModule,
    DocumentClientModule,
    SsmClientModule,
    S3ClientModule,
    ConfigParametersModule,
    ShopifyModule,
  ],
  providers: [
    CustomersResolver,
    CustomersService,
    SuccessHandler,
    ErrorHandler,
  ],
})
export class CustomersModule {}
