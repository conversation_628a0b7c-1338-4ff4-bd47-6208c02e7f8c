import { Resolver, Query, Args, Mutation } from '@nestjs/graphql';
import { CustomersService } from './customers.service';
import { Customer, ShopifyAddress } from './entities/customer.entity';
import { CreateCustomerAddressInput } from './dto/create-customer-address.input';
import { UpdateCustomerAddressInput } from './dto/update-customer-address.input';
import { SetCustomerNameInput } from './dto/set-customer-name.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import {
  CustomError,
  ErrorHandler,
} from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CRMGuard } from 'src/auth/roles.guard';
import { Response } from 'src/products/entities/product.entity';

@Resolver(() => Customer)
@UseGuards(CustomAuthGuard, CRMGuard)
export class CustomersResolver {
  constructor(
    private readonly customersService: CustomersService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Query(() => Customer, { name: 'getCustomer' })
  async findOne(@Args('phone', { type: () => String }) phone: string) {
    try {
      const data = await this.customersService.findOne(phone);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get shopify customer',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => ShopifyAddress, { name: 'createCustomerAddress' })
  async createAddress(
    @Args('createCustomerAddressInput')
    createCustomerAddressInput: CreateCustomerAddressInput,
  ) {
    try {
      const data = await this.customersService.createAddress(
        createCustomerAddressInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create shopify address',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Response, { name: 'sendBrochure' })
  async sendBrochure(
    @Args('phone')
    phone: string,
    @Args('type')
    category: string,
    @Args('storeId', { nullable: true }) storeId?: string,
  ) {
    try {
      if (
        ![
          'PILLOW',
          'ADJUSTABLE_DESK',
          'CHAIRS',
          'MATTRESS',
          'RECLINER_BED',
          'RECLINER_SOFA',
          'ELITE_RECLINER_SOFA',
          'SENSAI',
          'STORE_REVIEWS',
        ].includes(category)
      ) {
        throw new CustomError(`Type should be either PILLOW`, 400);
      }
      if (category === 'STORE_REVIEWS' && !storeId) {
        throw new CustomError('Store ID is required for Store Reviews', 400);
      }

      const message = await this.customersService.sendBrochure(
        phone,
        category,
        storeId,
      );

      return {
        message,
        status: 200,
        success: true,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to downloading quotation pdf',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => ShopifyAddress, { name: 'updateCustomerAddress' })
  async updateAddress(
    @Args('updateCustomerAddressInput')
    updateCustomerAddressInput: UpdateCustomerAddressInput,
  ) {
    try {
      const data = await this.customersService.updateAddress(
        updateCustomerAddressInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update shopify address',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Customer, { name: 'setCustomerName' })
  async setName(
    @Args('setCustomerNameInput')
    setCustomerNameInput: SetCustomerNameInput,
  ) {
    try {
      const data = await this.customersService.setName(setCustomerNameInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to set shopify customer name',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
