import { Injectable } from '@nestjs/common';
import { GetCustomer } from './lib/get-customer';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateCustomerAddressInput } from './dto/create-customer-address.input';
import { UpdateCustomerAddressInput } from './dto/update-customer-address.input';
import { CreateCustomerAddress } from './lib/create-customer-address';
import { UpdateCustomerAddress } from './lib/update-customer-address';
import { SetCustomerNameInput } from './dto/set-customer-name.input';
import { SetCustomerName } from './lib/set-customer-name';
import { AppShopify } from 'src/common/shopify/shopify';
import { SendBrochure } from './lib/send-brochure';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';

@Injectable()
export class CustomersService {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private shopifyClient: AppShopify,
    private docClient: AppDocumentClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
  ) {}

  createAddress(createCustomerAddressInput: CreateCustomerAddressInput) {
    const getHandler = new CreateCustomerAddress(
      this.configService,
      this.ssmClient,
      this.shopifyClient,
      this.configParameters,
      this.docClient,
    );
    return getHandler.createCustomerAddress(createCustomerAddressInput);
  }

  updateAddress(updateCustomerAddressInput: UpdateCustomerAddressInput) {
    const getHandler = new UpdateCustomerAddress(
      this.configService,
      this.ssmClient,
      this.shopifyClient,
      this.configParameters,
      this.docClient,
    );
    return getHandler.updateCustomerAddress(updateCustomerAddressInput);
  }
  sendBrochure(phone: string, category: string, storeId?: string) {
    const sendBrochureHandler = new SendBrochure(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.s3Client,
      this.configParameters,
      this.shopifyClient,
    );
    return sendBrochureHandler.sendBrochure(phone, category, storeId);
  }

  setName(setCustomerNameInput: SetCustomerNameInput) {
    const getHandler = new SetCustomerName(
      this.configService,
      this.ssmClient,
      this.shopifyClient,
    );
    return getHandler.setCustomerName(setCustomerNameInput);
  }

  findOne(phone: string) {
    const getHandler = new GetCustomer(
      this.configService,
      this.ssmClient,
      this.shopifyClient,
      this.configParameters,
      this.docClient,
    );
    return getHandler.getCustomer(phone);
  }
}
