import { CustomerData, ShopifyAddressData } from '../entities/customer.entity';

export const mapCustomerAddressData = ({
  customer_id,
  first_name,
  last_name,
  address1,
  address2,
  province,
  zip,
  ...restAddress
}): ShopifyAddressData => {
  return {
    ...restAddress,
    firstName: first_name,
    lastName: last_name,
    customerId: customer_id.toString(),
    line1: address1,
    line2: address2,
    state: province,
    pinCode: zip,
  };
};

export const mapCustomerData = ({
  id,
  first_name,
  last_name,
  default_address,
  addresses,
  ...restCustomer
}): CustomerData => {
  return {
    ...restCustomer,
    id: id.toString(),
    firstName: first_name,
    lastName: last_name,
    state: restCustomer?.state ? restCustomer?.state.toLowerCase() : null,
    default_address: default_address
      ? mapCustomerAddressData({ ...default_address, default: true })
      : null,
    addresses:
      addresses && addresses.length
        ? addresses.map((addr) =>
            mapCustomerAddressData({
              ...addr,
              default: default_address
                ? addr.id.toString() === default_address.id.toString()
                : false,
            }),
          )
        : null,
  };
};

export function extractNumericId(globalId: string): string {
  if (!globalId) return '';

  const parts = globalId.split('/');
  const lastPart = parts[parts.length - 1] || '';

  const numericPart = lastPart.split('?')[0];

  return numericPart.replace(/\D/g, '');
}

export function transformAddressFromGraphQL(
  addressNode: any,
  customerId: string,
): any {
  return {
    id: extractNumericId(addressNode.id),
    customer_id: customerId,
    first_name: addressNode.firstName,
    last_name: addressNode.lastName,
    address1: addressNode.address1,
    address2: addressNode.address2,
    city: addressNode.city,
    province: addressNode.province,
    country: addressNode.country,
    country_name: addressNode.country,
    country_code: addressNode.countryCodeV2,
    province_code: addressNode.provinceCode,
    zip: addressNode.zip,
    phone: addressNode.phone,
    company: addressNode.company,
    name: addressNode.name,
  };
}

export function transformCustomerFromGraphQL(customerNode: any): any {
  const id = extractNumericId(customerNode.id);

  const addresses =
    customerNode.addresses?.map((address: any) =>
      transformAddressFromGraphQL(address, id),
    ) || [];

  const default_address = customerNode.defaultAddress
    ? transformAddressFromGraphQL(customerNode.defaultAddress, id)
    : undefined;

  const orders = customerNode.orders?.edges || [];
  const orders_count = orders.length;

  let last_order_id;
  let last_order_name;
  let total_spent;
  let currency;

  if (orders.length > 0) {
    const lastOrder = orders[0].node;
    last_order_id = extractNumericId(lastOrder.id);
    last_order_name = lastOrder.name;

    if (lastOrder.totalPriceSet?.shopMoney) {
      total_spent = lastOrder.totalPriceSet.shopMoney.amount;
      currency = lastOrder.totalPriceSet.shopMoney.currencyCode;
    }
  }

  return {
    id,
    first_name: customerNode.firstName,
    last_name: customerNode.lastName,
    default_address,
    addresses,

    email: customerNode.email,
    phone: customerNode.phone,
    created_at: customerNode.createdAt,
    updated_at: customerNode.updatedAt,
    orders_count,
    state: customerNode.state,
    total_spent,
    last_order_id,
    note: customerNode.note,
    verified_email: customerNode.verifiedEmail,
    tax_exempt: customerNode.taxExempt,
    tags: customerNode.tags,
    last_order_name,
    currency,
    tax_exemptions: customerNode.taxExemptions,
    email_marketing_consent: customerNode.emailMarketingConsent,
    sms_marketing_consent: customerNode.smsMarketingConsent,
    admin_graphql_api_id: customerNode.id,
  };
}
export function constructCustomerResponse(
  customer: any,
  addresses: any,
  phone: string,
  metafields: any[],
): CustomerData {
  const dobMetafield = metafields.find(
    (metafield) => metafield.key === 'date_of_birth_string',
  );

  const anniversaryMetafield = metafields.find(
    (metafield) => metafield.key === 'anniversary_date',
  );

  const mappedCustomer = mapCustomerData({
    ...customer,
    addresses,
  });

  const result = {
    ...mappedCustomer,
    phone,
  };

  if (dobMetafield) {
    result.dob = dobMetafield.value;
  }

  if (anniversaryMetafield) {
    result.anniversaryDate = anniversaryMetafield.value;
  }

  return result;
}

function getMetafieldValue(
  metafields: any[],
  namespace: string,
  key: string,
): string | null {
  const metafield = metafields.find(
    (meta) => meta.namespace === namespace && meta.key === key,
  );
  return metafield ? metafield.value : null;
}

function normalizeCustomerTags(tags: any): string {
  if (tags === undefined || tags === null) {
    return '';
  } else if (Array.isArray(tags)) {
    return tags.join(', ');
  }
  return tags;
}

function mapMarketingConsent(consentData: any): any {
  if (!consentData) return null;

  return {
    state: consentData.marketingState || null,
    opt_in_level: consentData.marketingOptInLevel || null,
    consent_updated_at: consentData.consentUpdatedAt || null,
    ...(consentData.consentCollectedFrom
      ? { consent_collected_from: consentData.consentCollectedFrom }
      : {}),
  };
}

export function processCustomerMetafieldsAndData(
  customer: any,
  customerData: CustomerData,
): CustomerData {
  const metafields = customer.metafields?.edges?.map((edge) => edge.node) || [];

  customerData.dob =
    getMetafieldValue(metafields, 'custom', 'date_of_birth_string') ||
    customerData.dob;
  customerData.anniversaryDate =
    getMetafieldValue(metafields, 'custom', 'anniversary_date') ||
    customerData.anniversaryDate;

  customerData.tags = normalizeCustomerTags(customerData.tags);

  if (customer.emailMarketingConsent) {
    customerData.email_marketing_consent = mapMarketingConsent(
      customer.emailMarketingConsent,
    );
  }

  if (customer.smsMarketingConsent) {
    customerData.sms_marketing_consent = mapMarketingConsent(
      customer.smsMarketingConsent,
    );
  }

  return customerData;
}
