import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class InteriorArchitectureData {
  @Field(() => String, {
    nullable: true,
    description: 'Unique identifier for the user',
  })
  id: string;

  @Field(() => String, { nullable: true, description: 'City of the user' })
  city?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Creation timestamp of the user',
  })
  createdAt?: string;

  @Field(() => String, { nullable: true, description: 'Date of the user' })
  date?: string;

  @Field(() => String, { nullable: true, description: 'Email of the user' })
  email?: string;

  @Field(() => String, {
    nullable: true,
    description: 'File upload of the user',
  })
  fileupload?: string;

  @Field(() => String, { nullable: true, description: 'GST of the user' })
  gst?: string;

  @Field(() => String, { nullable: true, description: 'Name of the user' })
  name?: string;

  @Field(() => String, { nullable: true, description: 'Last Name of the user' })
  lname?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Organisation of the user',
  })
  organisation?: string;

  @Field(() => String, { nullable: true, description: 'PAN of the user' })
  pan?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Phone number of the user',
  })
  phone?: string;

  @Field(() => String, { nullable: true, description: 'Source of the user' })
  source?: string;

  @Field(() => String, { nullable: true, description: 'Store of the user' })
  store?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Last update timestamp of the user',
  })
  updatedAt?: string;

  @Field(() => String, { nullable: true, description: 'Store of the user' })
  vendorcode?: string;
}

@ObjectType()
export class InteriorArchitecture {
  @Field(() => InteriorArchitectureData, { nullable: true })
  data: InteriorArchitectureData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class InteriorArchitectures {
  @Field(() => [InteriorArchitectureData], { nullable: true })
  data: InteriorArchitectureData[];

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
