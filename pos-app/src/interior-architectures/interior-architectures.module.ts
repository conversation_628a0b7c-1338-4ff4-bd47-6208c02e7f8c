import { Module } from '@nestjs/common';
import { InteriorArchitecturesService } from './interior-architectures.service';
import { InteriorArchitecturesResolver } from './interior-architectures.resolver';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { <PERSON>rrorHandler } from 'src/common/response/errorHandler/error.handler';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { CronService } from 'src/cron/cron.service';
import { ShopifyModule } from 'src/common/shopify/shopify.module';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';

@Module({
  imports: [
    S3ClientModule,
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
    ShopifyModule,
  ],
  providers: [
    InteriorArchitecturesResolver,
    InteriorArchitecturesService,
    SuccessHandler,
    ErrorHandler,
    CronService,
  ],
})
export class InteriorArchitecturesModule {}
