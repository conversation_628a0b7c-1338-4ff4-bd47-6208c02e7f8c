import { Test, TestingModule } from '@nestjs/testing';
import { InteriorArchitecturesResolver } from './interior-architectures.resolver';
import { InteriorArchitecturesService } from './interior-architectures.service';

describe('InteriorArchitecturesResolver', () => {
  let resolver: InteriorArchitecturesResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [InteriorArchitecturesResolver, InteriorArchitecturesService],
    }).compile();

    resolver = module.get<InteriorArchitecturesResolver>(
      InteriorArchitecturesResolver,
    );
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
