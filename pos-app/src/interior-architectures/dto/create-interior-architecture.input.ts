import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CreateInteriorArchitectureInput {
  @Field(() => String, {
    nullable: false,
    description: 'Unique identifier for the user',
  })
  id: string;

  @Field(() => String, { nullable: true, description: 'City of the user' })
  city: string;

  @Field(() => String, { nullable: true, description: 'Date of the user' })
  date: string;

  @Field(() => String, { nullable: true, description: 'Email of the user' })
  email: string;

  @Field(() => String, {
    nullable: true,
    description: 'File upload of the user',
  })
  fileupload?: string;

  @Field(() => String, { nullable: true, description: 'GST of the user' })
  gst?: string;

  @Field(() => String, { nullable: true, description: 'Name of the user' })
  name: string;

  @Field(() => String, {
    nullable: true,
    description: 'Last Name of the user',
  })
  lname: string;

  @Field(() => String, {
    nullable: true,
    description: 'Organisation of the user',
  })
  organisation?: string;

  @Field(() => String, { nullable: true, description: 'PAN of the user' })
  pan?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Phone number of the user',
  })
  phone: string;

  @Field(() => String, { nullable: false, description: 'Source of the user' })
  source: string;

  @Field(() => String, { nullable: true, description: 'Store of the user' })
  store?: string;

  @Field(() => String, { nullable: true, description: 'Store of the user' })
  vendorcode?: string;
}
