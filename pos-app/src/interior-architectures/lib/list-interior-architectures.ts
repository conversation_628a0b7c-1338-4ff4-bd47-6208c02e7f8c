// modules
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { InteriorArchitectureData } from '../entities/interior-architecture.entity';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { AppConfigParameters } from 'src/config/config';
import { filterFormatter } from 'src/common/helper/filter-helper';
import { searchingFilter } from '../filters/filter';

export class QueryInteriorArchitectures {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryInteriorArchitectures(
    query: string,
  ): Promise<InteriorArchitectureData[]> {
    const INTERIOR_ARCHITECTURE_TABLE =
      await this.configParameters.getInteriorArchitectureTableName();

    const esHandler = new ElasticClient(this.configService, this.ssmClient);

    const response = await esHandler.search({
      index: INTERIOR_ARCHITECTURE_TABLE,
      body: {
        query: {
          multi_match: {
            query,
            fields: ['name', 'id', 'lname'],
            type: 'phrase_prefix',
          },
        },
      },
    });
    const data = response.body.hits.hits.map((hit) => hit._source);

    return data;
  }

  async getIACount(filter) {
    const INTERIOR_ARCHITECTURE_TABLE =
      await this.configParameters.getInteriorArchitectureTableName();

    const esHandler = new ElasticClient(this.configService, this.ssmClient);

    const { searchArray } = await filterFormatter(
      null,
      searchingFilter,
      null,
      filter,
    );
    const { body: bodyRes } = await esHandler.count({
      index: INTERIOR_ARCHITECTURE_TABLE,
      body: {
        query: {
          bool: {
            must: [...searchArray],
          },
        },
      },
    });
    return bodyRes;
  }
}
