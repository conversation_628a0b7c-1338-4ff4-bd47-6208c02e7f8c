// modules
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { InteriorArchitectureData } from '../entities/interior-architecture.entity';
import { CreateInteriorArchitectureInput } from '../dto/create-interior-architecture.input';
import { AppConfigParameters } from 'src/config/config';
import { QueryInteriorArchitectures } from './list-interior-architectures';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

export class CreateInteriorArchitecture {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createInteriorArchitecture(
    configuration: CreateInteriorArchitectureInput,
  ): Promise<InteriorArchitectureData> {
    posLogger.info('InteriorArchitecture', 'createInteriorArchitecture', {
      input: configuration,
    });
    const INTERIOR_ARCHITECTURE_TABLE =
      await this.configParameters.getInteriorArchitectureTableName();

    try {
      if (configuration?.phone?.length) {
        const filter = {
          termSearchFields: {
            phone: configuration?.phone,
          },
        };

        const queryHandler = new QueryInteriorArchitectures(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const architecture = await queryHandler.getIACount(filter);

        if (architecture && architecture.count > 0) {
          throw new CustomError(
            `Interior architect already exists in the system with the given phone number entered.`,
            400,
          );
        }
      }

      const Item: InteriorArchitectureData = {
        ...configuration,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      };

      const command = new PutCommand({
        TableName: INTERIOR_ARCHITECTURE_TABLE,
        Item,
      });

      await this.docClient.createItem(command);
      return Item;
    } catch (e) {
      posLogger.error('InteriorArchitecture', 'createInteriorArchitecture', {
        error: e,
      });
      throw e;
    }
  }
}
