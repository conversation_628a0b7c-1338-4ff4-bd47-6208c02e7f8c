// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { InteriorArchitectureData } from '../entities/interior-architecture.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
export class GetInteriorArchitecture {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getInteriorArchitecture(id: string): Promise<InteriorArchitectureData> {
    posLogger.info('interior-architecture', 'getInteriorArchitecture', {
      input: id,
    });

    try {
      const INTERIOR_ARCHITECTURE_TABLE =
        await this.configParameters.getInteriorArchitectureTableName();

      const command = new GetCommand({
        TableName: INTERIOR_ARCHITECTURE_TABLE,
        Key: { id },
      });

      const { Item: architecture } = await this.docClient.getItem(command);

      if (!architecture) {
        throw new CustomError(`Interior Architecture ${id} not found`, 404);
      }

      return architecture;
    } catch (e) {
      posLogger.error('interior-architecture', 'getInteriorArchitecture', {
        error: e,
      });
      throw e;
    }
  }
}
