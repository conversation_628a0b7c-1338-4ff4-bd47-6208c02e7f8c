import { Resolver, Query, Args, Mutation } from '@nestjs/graphql';
import { InteriorArchitecturesService } from './interior-architectures.service';
import {
  InteriorArchitecture,
  InteriorArchitectures,
} from './entities/interior-architecture.entity';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CreateInteriorArchitectureInput } from './dto/create-interior-architecture.input';
import { SAPCustomAuthGuard } from 'src/auth/sap.guard';
import { AdminGuard } from 'src/auth/roles.guard';
import { Response } from 'src/products/entities/product.entity';

@Resolver(() => InteriorArchitecture)
export class InteriorArchitecturesResolver {
  constructor(
    private readonly interiorArchitecturesService: InteriorArchitecturesService,
    private readonly successHandler: SuccessH<PERSON>ler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @UseGuards(SAPCustomAuthGuard)
  @Mutation(() => InteriorArchitecture)
  async createInteriorArchitecture(
    @Args('createInteriorArchitectureInput')
    createInteriorArchitectureInput: CreateInteriorArchitectureInput,
  ) {
    try {
      const data = await this.interiorArchitecturesService.create(
        createInteriorArchitectureInput,
      );

      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get interior architecture',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  // @Query(() => [InteriorArchitecture], { name: 'interiorArchitectures' })
  // findAll() {
  //   return this.interiorArchitecturesService.findAll();
  // }

  @UseGuards(CustomAuthGuard)
  @Query(() => InteriorArchitecture, { name: 'getInteriorArchitecture' })
  async findOne(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.interiorArchitecturesService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get interior architecture',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @UseGuards(CustomAuthGuard)
  @Query(() => InteriorArchitectures, { name: 'listInteriorArchitectures' })
  async findAll(@Args('query', { type: () => String }) query: string) {
    try {
      const data = await this.interiorArchitecturesService.findAll(query);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list interior architectures',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @UseGuards(AdminGuard)
  @Query(() => Response, { name: 'fetchInteriorArchitecture' })
  async fetchInteriorArchitecture() {
    return await this.interiorArchitecturesService.fetchInteriorArchitecture();
  }

  // @Mutation(() => InteriorArchitecture)
  // updateInteriorArchitecture(
  //   @Args('updateInteriorArchitectureInput')
  //   updateInteriorArchitectureInput: UpdateInteriorArchitectureInput,
  // ) {
  //   return this.interiorArchitecturesService.update(
  //     updateInteriorArchitectureInput.id,
  //     updateInteriorArchitectureInput,
  //   );
  // }

  // @Mutation(() => InteriorArchitecture)
  // removeInteriorArchitecture(@Args('id', { type: () => Int }) id: number) {
  //   return this.interiorArchitecturesService.remove(id);
  // }
}
