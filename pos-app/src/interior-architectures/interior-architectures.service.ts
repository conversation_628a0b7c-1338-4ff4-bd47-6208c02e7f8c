import { Injectable } from '@nestjs/common';
import { GetInteriorArchitecture } from './lib/get-interior-architecture';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { QueryInteriorArchitectures } from './lib/list-interior-architectures';
import { AppConfigParameters } from 'src/config/config';
import { CreateInteriorArchitectureInput } from './dto/create-interior-architecture.input';
import { CreateInteriorArchitecture } from './lib/create-interior-architecture';
import { CronService } from 'src/cron/cron.service';
import { posLogger } from 'src/common/logger';

@Injectable()
export class InteriorArchitecturesService {
  constructor(
    private readonly cronService: CronService,
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}
  async create(
    createInteriorArchitectureInput: CreateInteriorArchitectureInput,
  ) {
    const createHandler = new CreateInteriorArchitecture(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await createHandler.createInteriorArchitecture(
      createInteriorArchitectureInput,
    );
  }

  // findAll() {
  //   return `This action returns all interiorArchitectures`;
  // }

  async findOne(id: string) {
    const getHandler = new GetInteriorArchitecture(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await getHandler.getInteriorArchitecture(id);
  }

  async findAll(query: string) {
    const queryHandler = new QueryInteriorArchitectures(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await queryHandler.queryInteriorArchitectures(query);
  }

  async fetchInteriorArchitecture() {
    this.cronService.fetchSheetInteriorArchitectures().catch((error) => {
      posLogger.error(
        'Interior Architecture',
        'fetchInteriorArchitecture',
        error,
      );
    });
    return {
      message: 'Fetching Interior Architectures task has been initiated',
    };
  }

  // update(
  //   id: number,
  //   updateInteriorArchitectureInput: UpdateInteriorArchitectureInput,
  // ) {
  //   return `This action updates a #${id} interiorArchitecture`;
  // }

  // remove(id: number) {
  //   return `This action removes a #${id} interiorArchitecture`;
  // }
}
