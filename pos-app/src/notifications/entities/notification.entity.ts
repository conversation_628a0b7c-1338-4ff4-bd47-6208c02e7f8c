import { ObjectType, Field, registerEnumType, Int } from '@nestjs/graphql';
import {
  NotificationStatus,
  NotificationType,
} from 'src/common/enum/notifications';
import { StoreAllocationType } from 'src/common/enum/store-allocation';

registerEnumType(NotificationStatus, { name: 'NotificationStatus' });
registerEnumType(NotificationType, { name: 'NotificationType' });
registerEnumType(StoreAllocationType, { name: 'StoreAllocationType' });

@ObjectType()
export class FirebaseToken {
  @Field(() => String, { description: 'id' })
  id: string;

  @Field(() => String, { description: 'storeId' })
  storeId: string;

  @Field(() => String, { description: 'cognitoId' })
  cognitoId: string;

  @Field(() => String, { description: 'CreatedAt', nullable: true })
  createdAt: string;

  @Field(() => String, { description: 'UpdatedAt' })
  updatedAt: string;
}

@ObjectType()
export class Notification {
  @Field(() => String, { description: 'id' })
  id: string;

  @Field(() => String, { description: 'createdBy', nullable: true })
  createdBy?: string;

  @Field(() => String, { description: 'header' })
  header: string;

  @Field(() => String, { description: 'description' })
  description: string;

  @Field(() => NotificationType, { description: 'notificationtype' })
  notificationtype: NotificationType;

  @Field(() => String, { description: 'triggerTime' })
  triggerTime: string;

  @Field(() => String, { description: 'link', nullable: true })
  link?: string;

  @Field(() => Boolean, { description: 'HighAlert' })
  highAlert: boolean;

  @Field(() => Boolean, { description: 'pinned' })
  pinned: boolean;

  @Field(() => String, { description: 'pinnedContent', nullable: true })
  pinnedContent?: string;

  @Field(() => String, { description: 'pinnedStartTime', nullable: true })
  pinnedStartTime?: string;

  @Field(() => String, { description: 'pinnedEndTime', nullable: true })
  pinnedEndTime?: string;

  @Field(() => StoreAllocationType, { description: 'allocationType' })
  allocationType: StoreAllocationType;

  @Field(() => [String], { description: 'stores' })
  stores: string[];

  @Field(() => NotificationStatus, { description: 'status' })
  notificationStatus: NotificationStatus;

  @Field(() => String, { description: 'CreatedAt', nullable: true })
  createdAt: string;

  @Field(() => String, { description: 'UpdatedAt' })
  updatedAt: string;

  @Field(() => Boolean, { description: 'isDisabled' })
  isDisabled: boolean;

  @Field(() => String, { description: 'sqsMessageId', nullable: true })
  sqsMessageId?: string;

  @Field(() => Boolean, { description: 'sendImmediately' })
  sendImmediately: boolean;
}

@ObjectType()
export class NotificationData {
  @Field(() => Notification, {
    nullable: true,
    description: 'Notification Data',
  })
  data: Notification;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class NotificationsData {
  @Field(() => [Notification], {
    nullable: true,
    description: 'Notifications Data',
  })
  data: Notification[];

  @Field(() => Int, { nullable: true, description: 'count' })
  count: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
@ObjectType()
export class TokenData {
  @Field(() => FirebaseToken, {
    nullable: true,
    description: 'Token Data',
  })
  data: FirebaseToken;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class StoreNotification {
  @Field(() => String, { description: 'notificationIdd' })
  notificationId: string;

  @Field(() => String, { description: 'header' })
  header: string;

  @Field(() => String, { description: 'description' })
  description: string;

  @Field(() => NotificationType, { description: 'notificationtype' })
  notificationtype: NotificationType;

  @Field(() => String, { description: 'triggerTime' })
  triggerTime: string;

  @Field(() => String, { description: 'link', nullable: true })
  link?: string;

  @Field(() => Boolean, { description: 'HighAlert' })
  highAlert: boolean;

  @Field(() => Boolean, { description: 'pinned' })
  pinned: boolean;

  @Field(() => String, { description: 'pinnedContent', nullable: true })
  pinnedContent?: string;

  @Field(() => String, { description: 'pinnedStartTime', nullable: true })
  pinnedStartTime?: string;

  @Field(() => String, { description: 'pinnedEndTime', nullable: true })
  pinnedEndTime?: string;

  @Field(() => Number, { description: 'clickCounts', nullable: true })
  clickCounts: number;

  @Field(() => Boolean, { description: 'read' })
  read: boolean;

  @Field(() => String, { description: 'CreatedAt', nullable: true })
  createdAt: string;

  @Field(() => String, { description: 'UpdatedAt' })
  updatedAt: string;
}

@ObjectType()
export class StoreNotificationData {
  @Field(() => [StoreNotification], {
    nullable: true,
    description: 'StoreNotification Data',
  })
  data: StoreNotification[];

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
