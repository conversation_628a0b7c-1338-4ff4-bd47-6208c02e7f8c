import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { NotificationsService } from './notifications.service';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { AdminGuard, CRMGuard, StaffGuard } from 'src/auth/roles.guard';
import {
  NotificationsData,
  NotificationData,
  TokenData,
  StoreNotificationData,
} from './entities/notification.entity';
import { CreateNotificationsInput } from './dto/create-notifications.input';
import { UpdateNotificationsInput } from './dto/update-notification';
import { ListNotificationInput } from './dto/list-notification';
import { UpdateFirebaseTokenInput } from './dto/update-firebase-token';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { UpdateNotificationActionInput } from './dto/update-notification-action';

@Resolver(() => NotificationsService)
@UseGuards(CustomAuthGuard, CRMGuard)
export class NotificationsResolver {
  constructor(
    private readonly notificationService: NotificationsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => NotificationData)
  @UseGuards(AdminGuard)
  async createNotification(
    @Args('createNotificationInput')
    createNotificationInput: CreateNotificationsInput,
  ) {
    try {
      const data = await this.notificationService.create(
        createNotificationInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create notification',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Query(() => NotificationData, { name: 'getNotification' })
  @UseGuards(StaffGuard)
  async findOne(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.notificationService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get notification',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => NotificationsData, { name: 'listNotifications' })
  @UseGuards(AdminGuard)
  async findAll(
    @Args('listNotificationInput', { nullable: true })
    listNotificationInput?: ListNotificationInput,
  ) {
    try {
      const { data, count } = await this.notificationService.findAll(
        listNotificationInput,
      );
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list notifications',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => NotificationData, { name: 'updateNotification' })
  @UseGuards(AdminGuard)
  async updateNotification(
    @Args('updateNotificationsInput')
    updateNotificationsInput: UpdateNotificationsInput,
  ) {
    try {
      const data = await this.notificationService.update(
        updateNotificationsInput.id,
        updateNotificationsInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update notification',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => NotificationData, { name: 'deleteNotification' })
  @UseGuards(AdminGuard)
  async removeNotification(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.notificationService.remove(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to delete notification',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Mutation(() => TokenData)
  @UseGuards(StaffGuard)
  async updateFirebaseToken(
    @Args('updateFirebaseToken')
    updateFirebaseToken: UpdateFirebaseTokenInput,
  ) {
    try {
      const data =
        await this.notificationService.updateToken(updateFirebaseToken);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create notification',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Query(() => StoreNotificationData, { name: 'listStoreNotification' })
  @UseGuards(StaffGuard)
  async findAllStore(@Args('storeId', { type: () => String }) storeId: string) {
    try {
      const data =
        await this.notificationService.findAllStoreNotifications(storeId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get notification',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => StoreNotificationData, {
    name: 'updateStoreNotificationAction',
  })
  @UseGuards(StaffGuard)
  async updateNotificationAction(
    @Args('updateNotificationActionInput')
    updateNotificationActionInput: UpdateNotificationActionInput,
  ) {
    try {
      const data = await this.notificationService.updateNotificationAction(
        updateNotificationActionInput,
      );

      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update notification',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
