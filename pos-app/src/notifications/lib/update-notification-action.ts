// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { StoreNotification } from '../entities/notification.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { UpdateNotificationActionInput } from '../dto/update-notification-action';
import moment from 'moment';

export class UpdateNotificationAction {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async UpdateStoreNotificationAction(
    notificationInput: UpdateNotificationActionInput,
  ): Promise<StoreNotification[]> {
    posLogger.info('notification', 'updateStoreNotificationAction', {
      input: { notificationInput },
    });
    try {
      const NOTIFICATION_LOGS_TABLE =
        await this.configParameters.getNotificationLogsTableName();

      let updateExpressionString = 'SET #updatedAt = :updatedAt, ';
      const updateExpressionAttributeNames: Record<string, string> = {
        '#updatedAt': 'updatedAt',
      };

      const expressionAttributesValues: Record<string, any> = {
        ':updatedAt': moment().toISOString(),
      };
      // Check if isRead is passed and update it
      if (notificationInput.isRead === true) {
        updateExpressionString += '#read = :read, ';
        updateExpressionAttributeNames['#read'] = 'read';
        expressionAttributesValues[':read'] = true;
      }

      // Auto increment clickCount if clicked is passed
      if (notificationInput.increaseCount === true) {
        updateExpressionString +=
          '#clickCounts = if_not_exists(#clickCounts, :zero) + :increment, ';
        updateExpressionAttributeNames['#clickCounts'] = 'clickCounts';
        expressionAttributesValues[':increment'] = 1; // Increment by 1
        expressionAttributesValues[':zero'] = 0; // Initialize clickCounts to 0 if it doesn't exist
      }

      // Remove trailing comma and space
      updateExpressionString = updateExpressionString.slice(0, -2);

      // Execute the update command
      const command = new UpdateCommand({
        TableName: NOTIFICATION_LOGS_TABLE,
        Key: {
          storeId: notificationInput.storeId,
          notificationId: notificationInput.notificationId,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: updateExpressionAttributeNames,
        ExpressionAttributeValues: expressionAttributesValues,
        ConditionExpression:
          'attribute_exists(storeId) AND attribute_exists(notificationId)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: StoreNotification } =
        await this.docClient.updateItem(command);
      console.log('Attributes', Attributes);
      if (!Attributes) {
        throw new CustomError(
          'Invalid ID! No notifications found for given input.',
          404,
        );
      }

      return [Attributes];
    } catch (e) {
      posLogger.error('notification', 'updateStoreNotificationAction', {
        error: e,
      });
      throw e;
    }
  }
}
