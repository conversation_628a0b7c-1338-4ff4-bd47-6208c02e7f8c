// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { Notification } from '../entities/notification.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';

export class GetNotification {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getNotificationByID(id: string): Promise<Notification> {
    posLogger.info('notification', 'getNotificationByID', { input: { id } });
    try {
      const NOTIFICATION_TABLE =
        await this.configParameters.getNotificationTableName();

      const param = new GetCommand({
        TableName: NOTIFICATION_TABLE,
        Key: {
          id: `${id}`,
        },
      });

      const { Item }: { Item: Notification } =
        await this.docClient.getItem(param);
      console.log('Item', Item);
      if (!Item) {
        throw new CustomError(
          'Invalid ID! No notification found for given  ID.',
          404,
        );
      }

      posLogger.info('notification', 'getNotificationByID', {
        res: { Item },
      });
      return Item;
    } catch (e) {
      posLogger.error('notification', 'getNotificationByID', {
        error: e,
      });
      throw e;
    }
  }
}
