import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { Notification } from '../entities/notification.entity';
import { AppConfigParameters } from 'src/config/config';
import { AppSqsClient } from 'src/common/sqs-client/sqs-client';
import { NotificationStatus } from 'src/common/enum/notifications';

export class UpdateNotification {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private sqsClient: AppSqsClient,
  ) {}

  async updateNotification(
    id: string = '',
    { ...updateNotificationInput }: Partial<Notification>,
  ): Promise<Notification> {
    posLogger.info('notification', 'updateNotification', {
      input: { id, updateNotificationInput },
    });

    try {
      const NOTIFICATION_TABLE =
        await this.configParameters.getNotificationTableName();

      let updateExpressionString = 'SET #updatedAt = :updatedAt, ';
      const updateExpressionAttributeNames: Record<string, string> = {
        '#updatedAt': 'updatedAt',
      };

      const expressionAttributesValues: Record<string, any> = {
        ':updatedAt': moment().toISOString(),
      };

      // Use the attribute names in the update expression
      Object.keys(updateNotificationInput).forEach((key) => {
        if (key !== 'id') {
          updateExpressionString += `#${key} = :${key}, `;
          updateExpressionAttributeNames[`#${key}`] = key;
          expressionAttributesValues[`:${key}`] = updateNotificationInput[key];
        }
      });

      // Remove trailing comma and space
      updateExpressionString = updateExpressionString.slice(0, -2);

      // Execute the update command
      const command = new UpdateCommand({
        TableName: NOTIFICATION_TABLE,
        Key: { id },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: updateExpressionAttributeNames,
        ExpressionAttributeValues: expressionAttributesValues,
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: Notification } =
        await this.docClient.updateItem(command);

      if (updateNotificationInput?.sendImmediately) {
        const sqsMessageId = await this.addNotificationToQueue(
          updateNotificationInput,
        );
        console.log('sqsMessageId', sqsMessageId);
      }

      if (
        updateNotificationInput?.notificationStatus == NotificationStatus.SENT
      ) {
        const NOTIFICATION_LOGS_TABLE =
          await this.configParameters.getNotificationLogsTableName();

        let updateExpressionString = 'SET #updatedAt = :updatedAt, ';
        const updateExpressionAttributeNames: Record<string, string> = {
          '#updatedAt': 'updatedAt',
        };

        const expressionAttributesValues: Record<string, any> = {
          ':updatedAt': moment().toISOString(),
        };
        for (const storeId of updateNotificationInput.stores) {
          Object.keys(updateNotificationInput).forEach((key) => {
            if (key !== 'id') {
              updateExpressionString += `#${key} = :${key}, `;
              updateExpressionAttributeNames[`#${key}`] = key;
              expressionAttributesValues[`:${key}`] =
                updateNotificationInput[key];
            }
          });

          updateExpressionString = updateExpressionString.slice(0, -2);

          const command = new UpdateCommand({
            TableName: NOTIFICATION_LOGS_TABLE,
            Key: { notificationId: id, storeId },
            UpdateExpression: updateExpressionString,
            ExpressionAttributeNames: updateExpressionAttributeNames,
            ExpressionAttributeValues: expressionAttributesValues,
            ConditionExpression:
              'attribute_exists(notificationId) AND attribute_exists(storeId) ',
            ReturnValues: 'ALL_NEW',
          });
          const { Attributes }: { Attributes: Notification } =
            await this.docClient.updateItem(command);
          console.log('Attributes', Attributes);
        }
      }

      return Attributes; // Return updated notification
    } catch (e) {
      posLogger.error('notification', 'updateNotification', { error: e });
      throw e;
    }
  }

  private async addNotificationToQueue(notificationInput: any) {
    const queueUrl = await this.configParameters.getNotificationSqsQueueUrl();
    const params = {
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(notificationInput),
      DelaySeconds: 0,
    };

    const result = await this.sqsClient.sendMessage(params);
    return result.MessageId;
  }

  private async deleteSqsMessage(sqsMessageId: string) {
    const queueUrl = await this.configParameters.getNotificationSqsQueueUrl();

    const params = {
      QueueUrl: queueUrl,
      ReceiptHandle: sqsMessageId,
    };

    await this.sqsClient.deleteMessage(params);
  }

  private calculateDelaySeconds(triggerTime: string): number {
    const delay = moment(triggerTime).diff(moment(), 'seconds');
    return Math.max(0, delay);
  }
}
