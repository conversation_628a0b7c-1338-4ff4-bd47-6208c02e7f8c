// modules

import { filterFormatter } from 'src/common/helper/filter-helper';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { ListNotificationInput } from '../dto/list-notification';
// import {
//   NotificationData,
//   NotificationsData,
// } from '../entities/notification.entity';
import { AppConfigParameters } from 'src/config/config';
export class QueryNotifications {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryNotifications(filter: ListNotificationInput): Promise<any> {
    posLogger.info('notifications', 'queryNotifications', { input: filter });
    try {
      const NOTIFICATION_TABLE =
        await this.configParameters.getNotificationTableName();

      const { searchArray, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: NOTIFICATION_TABLE,
        });

        size = bodyRes?.count;
      }

      if (searchArray.length) {
        const { body: bodyRes } = await esHandler.count({
          index: NOTIFICATION_TABLE,
          body: {
            query: {
              bool: {
                must: [...searchArray],
              },
            },
          },
        });
        const response = await esHandler.search({
          index: NOTIFICATION_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data: Notification[] = response.body.hits.hits.map(
          (hit) => hit._source,
        );

        return { data, count: bodyRes?.count };
      }

      const response = await esHandler.search({
        index: NOTIFICATION_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });
      const { body: bodyRes } = await esHandler.count({
        index: NOTIFICATION_TABLE,
      });
      const data: Notification[] = response.body.hits.hits.map(
        (hit) => hit._source,
      );

      return { data, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('notification', 'queryNotifications', { error: e });
      throw e;
    }
  }
}
