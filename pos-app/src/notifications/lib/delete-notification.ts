// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { Notification } from '../entities/notification.entity';
import { AppConfigParameters } from 'src/config/config';

export class RemoveNotification {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async removeNotification(id: string): Promise<Notification> {
    posLogger.info('notification', 'RemoveNotification', { input: { id } });
    try {
      const [NOTIFICATION_TABLE] = await Promise.all([
        await this.configParameters.getNotificationTableName(),
      ]);

      const deleteCommand = new UpdateCommand({
        TableName: NOTIFICATION_TABLE,
        Key: { id },
        UpdateExpression: 'SET #isDisabled = :isDisabled',
        ExpressionAttributeNames: {
          '#isDisabled': 'isDisabled',
        },
        ExpressionAttributeValues: {
          ':isDisabled': true,
        },
        ConditionExpression: 'attribute_exists(id)', // Ensure the item exists
        ReturnValues: 'ALL_NEW', // Return all attributes after update
      });

      const response = await this.docClient.updateItem(deleteCommand);

      return response.Attributes;
    } catch (e) {
      posLogger.error('notification', 'removeNotification', { error: { e } });
      throw e;
    }
  }
}
