// modules
import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { StoreNotificationData } from '../entities/notification.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';

export class ListStoreNotifications {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async ListStoreNotifications(
    storeId: string,
  ): Promise<StoreNotificationData> {
    posLogger.info('notification', 'listStoreNotifications', {
      input: { storeId },
    });
    try {
      const NOTIFICATION_LOGS_TABLE =
        await this.configParameters.getNotificationLogsTableName();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const params = {
        TableName: NOTIFICATION_LOGS_TABLE,
        IndexName: 'StoreIdCreatedAtIndex',
        KeyConditionExpression:
          'storeId = :storeId AND createdAt >= :createdAt',
        ExpressionAttributeValues: {
          ':storeId': storeId,
          ':createdAt': thirtyDaysAgo.toISOString(),
        },
        Limit: 1000,
      };

      const command = new QueryCommand(params);
      const { Items } = await this.docClient.queryItems(command);

      if (!Items) {
        throw new CustomError(
          'Invalid ID! No notifications found for given store ID.',
          404,
        );
      }
      const parseISOString = (isoString: any) => new Date(isoString).getTime();

      const sortedNotifications = Items.sort(
        (a, b) => parseISOString(b.triggerTime) - parseISOString(a.triggerTime),
      );

      const now = Date.now();
      const triggeredNotifications = sortedNotifications.filter(
        (notification) => parseISOString(notification.triggerTime) <= now,
      );

      posLogger.info('notification', 'listStoreNotifications', {
        res: { Items },
      });
      return triggeredNotifications || [];
    } catch (e) {
      posLogger.error('notification', 'listStoreNotifications', {
        error: e,
      });
      throw e;
    }
  }
}
