import { PutCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { Notification } from '../entities/notification.entity';
import { CreateNotificationsInput } from '../dto/create-notifications.input';
import { AppConfigParameters } from 'src/config/config';
import { v4 as uuid } from 'uuid';
import { AppSqsClient } from 'src/common/sqs-client/sqs-client';

export class CreateNotification {
  constructor(
    private docClient: AppDocumentClient,
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private sqsClient: AppSqsClient,
  ) {}

  async createNotification(
    notificationInput: CreateNotificationsInput,
  ): Promise<Notification> {
    posLogger.info('CreateNotification', 'createNotification', {
      input: notificationInput,
    });
    console.log('notificationInput', notificationInput?.triggerTime);
    try {
      const NOTIFICATION_TABLE =
        await this.configParameters.getNotificationTableName();
      const id = uuid();

      const Item: Notification = {
        ...notificationInput,
        id,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      };

      const command = new PutCommand({
        TableName: NOTIFICATION_TABLE,
        Item,
      });

      await this.docClient.createItem(command);

      return Item;
    } catch (e) {
      posLogger.error('CreateNotification', 'createNotification', {
        error: e,
      });
      throw e;
    }
  }

  private async addNotificationToQueue(notificationInput: any) {
    console.log('notificationInput', notificationInput);
    const queueUrl = await this.configParameters.getNotificationSqsQueueUrl();
    console.log('input to add', notificationInput);
    const params = {
      QueueUrl: `${'https://sqs.ap-south-1.amazonaws.com/************/'}${queueUrl}`,
      MessageBody: JSON.stringify(notificationInput),
      DelaySeconds: 0,
    };

    const result = await this.sqsClient.sendMessage(params);
    return result.MessageId;
  }
}
