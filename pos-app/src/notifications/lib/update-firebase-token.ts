import { PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { v4 as uuid } from 'uuid';
import { AppSqsClient } from 'src/common/sqs-client/sqs-client';
import { UpdateFirebaseTokenInput } from '../dto/update-firebase-token';
import { FirebaseToken } from '../entities/notification.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

export class UpdateFirebaseToken {
  constructor(
    private docClient: AppDocumentClient,
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private sqsClient: AppSqsClient,
  ) {}

  async updateFirebaseToken(
    updateTokenInput: UpdateFirebaseTokenInput,
  ): Promise<FirebaseToken> {
    posLogger.info('UpdateFirebaseToken', 'updateFirebaseToken', {
      input: updateTokenInput,
    });
    console.log('updateFirebaseTokenInput', updateTokenInput);
    try {
      const FIREBASE_TOKEN__TABLE =
        await this.configParameters.getFirebaseTokenTableName();

      const Item = {
        ...updateTokenInput,
        id: uuid(),
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      };

      const scanParams = {
        TableName: FIREBASE_TOKEN__TABLE,
        KeyConditionExpression: 'storeId = :storeId',
        ExpressionAttributeValues: {
          ':storeId': updateTokenInput.storeId,
        },
      };

      const scanCommand = new QueryCommand(scanParams);
      const scanResult = await this.docClient.queryItems(scanCommand);

      if (scanResult.Items && scanResult.Items.length > 0) {
        const tokenExists = scanResult.Items.some(
          (item) => item.token === updateTokenInput.token,
        );

        if (tokenExists) {
          console.log(
            'An item with the same storeId and tokenKey already exists',
          );
          throw new CustomError(
            `An item with the same storeId and token already exists`,
            400,
          );
        }
      }
      const command = new PutCommand({
        TableName: FIREBASE_TOKEN__TABLE,
        Item,
      });

      try {
        await this.docClient.createItem(command);
        posLogger.info('Token created/updated successfully', 'create', {});
      } catch (error) {
        if (error.name === 'ConditionalCheckFailedException') {
          posLogger.info('ConditionalCheckFailedException', 'create', error);
        } else {
          posLogger.error('UpdateFirebaseToken', 'updateFirebaseToken', {
            error: error,
          });
          throw error;
        }
      }
      return Item;
    } catch (e) {
      posLogger.error('UpdateFirebaseToken', 'updateFirebaseToken', {
        error: e,
      });
      throw e;
    }
  }
}
