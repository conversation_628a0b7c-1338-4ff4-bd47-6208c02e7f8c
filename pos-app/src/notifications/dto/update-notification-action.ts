import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class UpdateNotificationActionInput {
  @Field(() => String, { description: 'storeId' })
  storeId: string;

  @Field(() => String, { description: 'notificationId' })
  notificationId: string;

  @Field(() => Boolean, { description: 'increaseCount', nullable: true })
  increaseCount?: boolean;

  @Field(() => Boolean, { description: 'isRead', nullable: true })
  isRead?: boolean;
}
