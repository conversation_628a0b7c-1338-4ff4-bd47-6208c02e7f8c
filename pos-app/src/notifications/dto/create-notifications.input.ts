import { InputType, Field, registerEnumType } from '@nestjs/graphql';
import {
  NotificationStatus,
  NotificationType,
} from 'src/common/enum/notifications';
import { StoreAllocationType } from 'src/common/enum/store-allocation';

registerEnumType(NotificationStatus, { name: 'NotificationStatus' });
registerEnumType(NotificationType, { name: 'NotificationType' });
registerEnumType(StoreAllocationType, { name: 'StoreAllocationType' });

@InputType()
export class CreateNotificationsInput {
  @Field(() => String, { description: 'createdBy' })
  createdBy: string;

  @Field(() => String, { description: 'header' })
  header: string;

  @Field(() => String, { description: 'description' })
  description: string;

  @Field(() => NotificationType, { description: 'notificationtype' })
  notificationtype: NotificationType;

  @Field(() => String, { description: 'triggerTime' })
  triggerTime: string;

  @Field(() => String, { description: 'link', nullable: true })
  link?: string;

  @Field(() => Boolean, { description: 'HighAlert' })
  highAlert: boolean;

  @Field(() => Boolean, { description: 'pinned' })
  pinned: boolean;

  @Field(() => String, { description: 'pinnedContent', nullable: true })
  pinnedContent?: string;

  @Field(() => String, { description: 'pinnedStartTime', nullable: true })
  pinnedStartTime?: string;

  @Field(() => String, { description: 'pinnedEndTime', nullable: true })
  pinnedEndTime?: string;

  @Field(() => StoreAllocationType, { description: 'allocationType' })
  allocationType: StoreAllocationType;

  @Field(() => [String], { description: 'stores' })
  stores: string[];

  @Field(() => NotificationStatus, { description: 'status' })
  notificationStatus: NotificationStatus;

  @Field(() => Boolean, { description: 'isDisabled' })
  isDisabled: boolean;

  @Field(() => Boolean, { description: 'sendImmediately' })
  sendImmediately: boolean;
}
