import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { CreateNotificationsInput } from './dto/create-notifications.input';
import { CreateNotification } from './lib/create-notification';
import { UpdateNotificationsInput } from './dto/update-notification';
import { RemoveNotification } from './lib/delete-notification';
import { UpdateNotification } from './lib/update-notification';
import { GetNotification } from './lib/get-notification';
import { ListNotificationInput } from './dto/list-notification';
import { QueryNotifications } from './lib/list-notification';
import { AppSqsClient } from 'src/common/sqs-client/sqs-client';
import { UpdateFirebaseTokenInput } from './dto/update-firebase-token';
import { UpdateFirebaseToken } from './lib/update-firebase-token';
import { ListStoreNotifications } from './lib/list-store-notification';
import { UpdateNotificationActionInput } from './dto/update-notification-action';
import { UpdateNotificationAction } from './lib/update-notification-action';

@Injectable()
export class NotificationsService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private sqsClient: AppSqsClient,
  ) {}
  async create(createNotificationInput: CreateNotificationsInput) {
    const createHandler = new CreateNotification(
      this.docClient,
      this.configService,
      this.ssmClient,
      this.configParameters,
      this.sqsClient,
    );
    return createHandler.createNotification(createNotificationInput);
  }
  async findOne(id: string) {
    const getHandler = new GetNotification(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.getNotificationByID(id);
  }

  async findAll(listNotificationInput: ListNotificationInput) {
    const queryCartsHandler = new QueryNotifications(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return queryCartsHandler.queryNotifications(listNotificationInput);
  }

  async update(id: string, updateNotificationsInput: UpdateNotificationsInput) {
    const updateHandler = new UpdateNotification(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.sqsClient,
    );
    return updateHandler.updateNotification(id, updateNotificationsInput);
  }

  async remove(id: string) {
    const removeHandler = new RemoveNotification(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return removeHandler.removeNotification(id);
  }
  async updateToken(updateFirebaseToken: UpdateFirebaseTokenInput) {
    const updateFirebaseTokenHanlder = new UpdateFirebaseToken(
      this.docClient,
      this.configService,
      this.ssmClient,
      this.configParameters,
      this.sqsClient,
    );
    return updateFirebaseTokenHanlder.updateFirebaseToken(updateFirebaseToken);
  }
  async findAllStoreNotifications(storeId: string) {
    const listStoreNotifications = new ListStoreNotifications(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return listStoreNotifications.ListStoreNotifications(storeId);
  }
  async updateNotificationAction(
    notificationData: UpdateNotificationActionInput,
  ) {
    const listStoreNotifications = new UpdateNotificationAction(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return listStoreNotifications.UpdateStoreNotificationAction(
      notificationData,
    );
  }
}
