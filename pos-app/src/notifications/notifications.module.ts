import { Modu<PERSON> } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationsResolver } from './notifications.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { ConfigParametersModule } from 'src/config/config.module';
import { AppSqsClientModule } from 'src/common/sqs-client/sqs-client.module';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
    AppSqsClientModule,
  ],
  providers: [
    NotificationsResolver,
    NotificationsService,
    SuccessHandler,
    ErrorHandler,
  ],
})
export class NotificationModule {}
