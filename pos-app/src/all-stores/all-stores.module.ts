import { Module } from '@nestjs/common';
import { AllStoresService } from './all-stores.service';
import { AllStoresController, SkuPriceMasterController } from './all-stores.controller';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { SkuPriceMasterService } from 'src/sku-price-master/sku-price-master.service';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
@Module({
  imports: [SsmClientModule, ConfigParametersModule],
  controllers: [AllStoresController,SkuPriceMasterController],
  providers: [
    AllStoresService,
    AppDocumentClient,
    SkuPriceMasterService,
    AppS3Client,
    AppConfigParameters,
  ],
})
export class AllStoresModule {}
