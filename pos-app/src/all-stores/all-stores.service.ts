import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { AppConfigParameters } from 'src/config/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';
import { posLogger } from 'src/common/logger';

@Injectable()
export class AllStoresService {
  private esHandler: ElasticClient;
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {
    this.esHandler = new ElasticClient(this.configService, this.ssmClient);
  }

  async query({
    index,
    limit: size = 100,
    page = 1,
    filter,
    sort,
    nextToken: nt,
  }) {
    let searchAfter;
    if (nt) {
      searchAfter = nt
        ? JSON.parse(Buffer.from(nt, 'base64').toString('ascii'))
        : undefined;
    }
    //Building search request
    const searchParams = {
      index,
      size,
      from: (page - 1) * size,
      body: {
        version: false,
        track_total_hits: true,
        search_after: searchAfter,
        query: filter,
        sort: [...sort],
      },
    };
    console.log('searchParams', searchParams?.body?.query?.bool);
    // Executing the OpenSearch request
    const { body } = await this.esHandler.search(searchParams);

    const { hits } = body;
    const { hits: results = [], total } = hits;
    const lastResult = results[results.length - 1];
    const nextToken =
      lastResult && lastResult.sort
        ? Buffer.from(JSON.stringify(lastResult.sort), 'ascii').toString(
            'base64',
          )
        : null;

    return {
      page,
      pageSize: size,
      totalPages: Math.ceil(total.value / size),
      total: total.value,
      items: results.map(({ _source }) => _source),
      nextToken: nextToken,
    };
  }

  async queryAll({ index, filter, nextToken: nT }) {
    const sortItems = [{ 'id.keyword': { order: 'desc' } }];

    const { items, nextToken } = await this.query({
      index,
      filter,
      sort: sortItems,
      nextToken: nT,
      limit: 9999,
    });

    if (nextToken) {
      const nextItems = await this.queryAll({
        index,
        filter,
        nextToken,
      });
      return [...items, ...nextItems];
    }
    return items;
  }

  async findAll(isActive, pgtype, city) {
    posLogger.info('AllStoresService', 'findAll', { isActive, pgtype, city });

    const must: any = [
      {
        term: {
          isActive: {
            value: true,
          },
        },
      },
    ];
    const STORE_TABLE = await this.configParameters.getStoreTableName();

    const data = await this.queryAll({
      index: STORE_TABLE,
      filter: {
        bool: {
          must,
        },
      },
      nextToken: null,
    });

    let finalStores = data.filter(
      (d) =>
        d?.id.toUpperCase() !== 'TEST-STORE' &&
        d?.id.toUpperCase() !== 'TEST-STORE-2' &&
        !d?.name.toUpperCase().includes('ACETECH'),
    );

    let selectedStoreIds: string[] = [];
    let discountStrip: string | null = null;

    if (pgtype) {
      try {
        const getGlobalConfigurationHandler = new GetGlobalConfiguration(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );

        const { value: restrictStoreConfig } =
          await getGlobalConfigurationHandler.getGlobalConfiguration(
            'RESTRICT_STORES',
          );

        const parsedConfig = JSON.parse(restrictStoreConfig);

        // Ensure parsedConfig is an array, if not, wrap it in an array
        const configArray = Array.isArray(parsedConfig)
          ? parsedConfig
          : [parsedConfig];

        // Iterate through the array and extract store_ids & discount_strip for pgtype
        configArray.forEach((config) => {
          if (config[pgtype]) {
            const storeData = config[pgtype];
            if (storeData.store_ids) {
              selectedStoreIds = [
                ...selectedStoreIds,
                ...storeData.store_ids.map((id) => id.toUpperCase()),
              ];
            }
            if (storeData.discount_strip) {
              discountStrip = storeData.discount_strip;
            }
          }
        });

        if (selectedStoreIds.length === 0) {
          throw new Error(`No stores found for page type: ${pgtype}`);
        }

        finalStores = finalStores.filter((d) =>
          selectedStoreIds.includes(d.id.toUpperCase()),
        );
      } catch (error) {
        console.log('Error fetching global configuration:', error);
      }
    }

    if (city) {
      finalStores = finalStores.filter(
        (d) => d?.address.city.toUpperCase() === city.toUpperCase(),
      );
    }

    return finalStores.map((d) => {
      const store = {
        id: d.id,
        storeName: d.name,
        state: d.address.state,
        city:
          d.address.city.charAt(0).toUpperCase() +
          d.address.city.slice(1).toLowerCase(),
        pincode: d.address.pincode,
        address: d.address,
        number: d.phone,
        email: d.email,
        image: d?.storeImage,
        locationkey: d?.getStoreLocationKey,
        mapLink: d?.mapLink,
        storeTimings: d?.storeTimings,
        storeShortCode: d?.storeShortCode,
      };

      // Add the discount_strip field if available
      if (discountStrip) {
        store['discount_strip'] = discountStrip;
      }

      return store;
    });
  }
}
