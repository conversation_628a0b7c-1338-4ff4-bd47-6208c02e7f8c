import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { AllStoresService } from './all-stores.service';
import { SAPCustomAuthGuard } from 'src/auth/sap.guard';
import { SkuPriceMasterService } from 'src/sku-price-master/sku-price-master.service';

@Controller('stores')
export class AllStoresController {
  constructor(
    private readonly allStoresService: AllStoresService,
    private readonly skuPriceMasterService: SkuPriceMasterService,
  ) {}

  @Get('/all')
  @UseGuards(SAPCustomAuthGuard)
  async findAll(
    @Query('isActive') isActive?: boolean,
    @Query('pageType') pgtype?: string,
    @Query('city') city?: string,
  ) {
    return await this.allStoresService.findAll(isActive, pgtype, city);
  }
}

@Controller('sku-price-master')
export class SkuPriceMasterController {
  constructor(private readonly skuPriceMasterService: SkuPriceMasterService) {}
  @Get('/sku/:id')
  @UseGuards(SAPCustomAuthGuard)
  async getSkuDetailsFromPriceMaster(@Param('id') id: string) {
    const data = await this.skuPriceMasterService.findOne(id);
    return data;
  }
}
