import { Test, TestingModule } from '@nestjs/testing';
import { AllStoresController } from './all-stores.controller';
import { AllStoresService } from './all-stores.service';

describe('AllStoresController', () => {
  let controller: AllStoresController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AllStoresController],
      providers: [AllStoresService],
    }).compile();

    controller = module.get<AllStoresController>(AllStoresController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
