import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';

import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { GRN } from '../entities/grn.entity';
import { posLogger } from 'src/common/logger';

export class ListGRNBySTN {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configService: ConfigService,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async listGRNBySTN(stnId: string): Promise<GRN[]> {
    posLogger.info('GRN', 'listGRNBySTN', {
      stnId,
    });

    try {
      const GRN_TABLE_NAME = await this.configParameters.getGRNTableName();

      const queryCommand = new QueryCommand({
        TableName: GRN_TABLE_NAME,
        KeyConditionExpression: 'stnId = :stnId',
        ExpressionAttributeValues: {
          ':stnId': stnId,
        },
      });

      const result = await this.docClient.queryItems(queryCommand);

      return result.Items as GRN[];
    } catch (error) {
      posLogger.error('GRN', 'listGRNBySTN', {
        error,
      });
      throw error;
    }
  }
}
