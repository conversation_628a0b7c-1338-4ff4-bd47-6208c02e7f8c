import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GetSTN } from 'src/stn/lib/get-stn-by-id';
import { CreateSTN } from 'src/stn/lib/create-stn';
import { ListPOsBySTN } from './list-po-by-stn-id';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { posLogger } from 'src/common/logger';
import { STNData } from 'src/stn/entity/stn.entity';
import {
  STNRequestMode,
  STNStatus,
  STNTransferMode,
} from 'src/common/enum/stn';
import moment from 'moment';
import { GetSkuPrice } from 'src/sku-price-master/lib/get-sku-price';
import { GetInventoryCredential } from 'src/inventory-credentials/lib/get-inventory-credential';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';

export class SearchAllPOsById {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configService: ConfigService,
    private readonly ssmClient: AppSsmClient,
    private readonly configParameters: AppConfigParameters,
  ) {}
  async createPayloadByOrderDetail(orderDetails: any): Promise<STNData> {
    const skuPriceHandler = new GetSkuPrice(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const {
      reference_code,
      order_date,
      storeId,
      order_id,
      invoice_id,
      order_items,
    } = orderDetails;
    const ecomSubOrderId =
      order_items[order_items.length - 1].suborder_id.toString();

    const products = await Promise.all(
      order_items.map(async (item) => {
        const skuData = await skuPriceHandler.getSkuPrice(item.ean);
        const { title } = skuData;

        return {
          sku: item.ean,
          title,
          price: item.mrp,
          quantity: Number(item.suborder_quantity),
        };
      }),
    );
    const order_date_iso = moment(
      order_date,
      'YYYY-MM-DD HH:mm:ss',
    ).toISOString();

    const payload: STNData = {
      id: reference_code,
      products: products,
      storeId: storeId,
      requestMode: STNRequestMode.FORWARD,
      status: STNStatus.CREATED,
      createdAt: order_date_iso,
      updatedAt: order_date_iso,
      ecomOrderId: order_id.toString(),
      ecomInvoiceId: invoice_id.toString(),
      ecomSubOrderId,
      transferMode: STNTransferMode.WAREHOUSE,
      bulkSTNCode: '',
    };

    return payload;
  }

  async searchAllPOsById(stnId: string, storeId: string): Promise<any[]> {
    posLogger.info('PO', 'searchAllPOsBySTN', { stnId, storeId });

    const getSTNHandler = new GetSTN(this.docClient, this.configParameters);
    const listPOsHandler = new ListPOsBySTN(
      this.docClient,
      this.configService,
      this.ssmClient,
      this.configParameters,
    );

    try {
      await getSTNHandler.getSTN(stnId);
      return await listPOsHandler.listPOsSTN(stnId);
    } catch (error) {
      if (error instanceof CustomError && error.statusCode === 404) {
        posLogger.info('PO', 'STN not found, trying to create', { stnId });

        try {
          const createSTNHandler = new CreateSTN(
            this.docClient,
            this.configService,
            this.ssmClient,
            this.configParameters,
          );
          const getInventoryCredentialHandler = new GetInventoryCredential(
            this.docClient,
            this.configParameters,
          );
          const getStoreHandler = new GetStore(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
          );
          const easyEcomHandler = new EasyEcomService(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
          );
          const { sourceWarehouseMappingId } =
            await getStoreHandler.getStore(storeId);

          const { locationKey: sourceLocationKey } =
            await getInventoryCredentialHandler.getInventoryCredential(
              sourceWarehouseMappingId,
            );
          const orderDetails = await easyEcomHandler.getOrderDetails(
            stnId,
            sourceLocationKey,
          );

          if (!orderDetails) {
            throw new CustomError(`Order ${stnId} not found in EasyEcom`, 404);
          }

          const createSTNInput: STNData = await this.createPayloadByOrderDetail(
            { ...orderDetails, storeId },
          );

          await createSTNHandler.createSTN({
            ...createSTNInput,
            sourceWarehouseMappingId,
          });

          return await listPOsHandler.listPOsSTN(stnId);
        } catch (error) {
          posLogger.error('PO', 'Failed to create STN', { error });
          throw new CustomError(
            `Failed to create STN or list purchase orders: ${error.message}`,
            500,
          );
        }
      } else {
        posLogger.error('PO', 'searchAllPOsBySTN failed', { error });
        throw new CustomError(
          `${error.message || 'PO has not been generated against this STN yet, please try again later after sometime '}`,
          500,
        );
      }
    }
  }
}
