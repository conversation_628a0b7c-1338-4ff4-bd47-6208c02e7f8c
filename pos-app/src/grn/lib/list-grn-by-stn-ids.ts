import { ConfigService } from '@nestjs/config';
import { AppConfigParameters } from 'src/config/config';
import { GRN } from '../entities/grn.entity';
import { posLogger } from 'src/common/logger';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { QueryHandlerService } from 'src/common/query-handler/query-handler';

export class ListGRNBySTNIds {
  constructor(
    private readonly configService: ConfigService,
    private ssmClient: AppSsmClient,

    private readonly configParameters: AppConfigParameters,
  ) {}

  async listGRNBySTNIds(stnIds: string[]): Promise<GRN[]> {
    posLogger.info('GRN', 'listGRNBySTNIds', {
      stnIds,
    });

    try {
      const GRN_TABLE_NAME = await this.configParameters.getGRNTableName();
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      const query = {
        terms: {
          'stnId.keyword': stnIds,
        },
      };

      const sortItems = [{ 'stnId.keyword': { order: 'desc' } }];
      const queryHandler = new QueryHandlerService(esHandler);

      // Fetch all data using queryAll
      const data = await queryHandler.queryAll({
        index: GRN_TABLE_NAME,
        filter: query,
        nextToken: null,
        sortItems,
      });
      console.log('data', data);

      // Return the items directly from the queryAll result
      return data;
    } catch (error) {
      posLogger.error('GRN', 'listGRNBySTNIds', {
        error,
      });
      throw error;
    }
  }
}
