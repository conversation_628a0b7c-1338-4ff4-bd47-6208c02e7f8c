import { ConfigService } from '@nestjs/config';
import moment from 'moment';

import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GetSTN } from 'src/stn/lib/get-stn-by-id';
import { GetInventoryCredential } from 'src/inventory-credentials/lib/get-inventory-credential';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { posLogger } from 'src/common/logger';

export class ListPOsBySTN {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configService: ConfigService,
    private readonly ssmClient: AppSsmClient,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async listPOsSTN(stnId: string): Promise<any[]> {
    posLogger.info('PO', 'listPOBySTN', { stnId });

    try {
      const getSTNHandler = new GetSTN(this.docClient, this.configParameters);
      const { updatedAt, storeId } = await getSTNHandler.getSTN(stnId);

      const getInventoryCredentialHandler = new GetInventoryCredential(
        this.docClient,
        this.configParameters,
      );
      const { locationKey: destinationLocationKey } =
        await getInventoryCredentialHandler.getInventoryCredential(storeId);

      const easyEcomHandler = new EasyEcomService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const created_after = moment(updatedAt).format('YYYY-MM-DD');
      const created_before = moment().add(1, 'day').format('YYYY-MM-DD');

      const data = await easyEcomHandler.fetchPurchaseOrderById(
        created_after,
        created_before,
        `${stnId}`,
        destinationLocationKey,
      );

      return data;
    } catch (error) {
      posLogger.error('PO', 'listPOBySTN', { error });
      throw new CustomError(`Failed to list purchase orders: ${error}`, 500);
    }
  }
}
