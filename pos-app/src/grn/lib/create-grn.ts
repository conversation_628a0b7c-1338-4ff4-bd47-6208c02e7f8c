import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import moment from 'moment';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateGRNInput } from '../dto/create-grn.input';
import { GRN } from '../entities/grn.entity';
import { GetSTN } from 'src/stn/lib/get-stn-by-id';
import { GetInventoryCredential } from 'src/inventory-credentials/lib/get-inventory-credential';
import {
  STNProgressStatus,
  STNRequestMode,
  STNTransferMode,
} from 'src/common/enum/stn';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { ListGRNBySTN } from './list-grn-by-stn-id';
import { posLogger } from 'src/common/logger';
import { GenerateCode } from 'src/common/helper/generate-code';

import { ListPOsBySTN } from './list-po-by-stn-id';
import { UpdateSTN } from 'src/stn/lib/update-stn';
import { PurchaseOrder } from '../entities/po.entity';

export class CreateGRN {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configService: ConfigService,
    private readonly ssmClient: AppSsmClient,
    private readonly configParameters: AppConfigParameters,
  ) {}
  async createGRN(createGRNInput: CreateGRNInput): Promise<GRN> {
    try {
      posLogger.info('GRN', 'createGRN', {
        message: 'Creating GRN',
        createGRNInput,
      });
      const { stnId, products, poId, vendorId } = createGRNInput;

      const getSTNHandler = new GetSTN(this.docClient, this.configParameters);
      const {
        storeId,
        requestMode,
        transferMode,
        products: stnProducts,
      } = await getSTNHandler.getSTN(stnId);

      const listGRNBySTNHandler = new ListGRNBySTN(
        this.docClient,
        this.configService,
        this.configParameters,
      );

      const codeHandler = new GenerateCode(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      console.log('codeHandler', stnProducts, products);

      const existingGRNs = await listGRNBySTNHandler.listGRNBySTN(stnId);

      await this.validateGRNQuantities(products, existingGRNs, stnProducts);
      const items = products.map((item) => {
        return {
          ...item,
          price: Number(
            stnProducts.find((stnItem) => stnItem.sku === item.sku).price,
          ),
          title: stnProducts.find((stnItem) => stnItem.sku === item.sku).title,
        };
      });
      console.log('items', items);

      if (!vendorId) {
        throw new CustomError(
          'Vendor id is required for warehouse transfer',
          400,
        );
      }
      const getStoreHandler = new GetStore(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const { sourceWarehouseMappingId } =
        await getStoreHandler.getStore(storeId);

      const destinationLocationId =
        requestMode === STNRequestMode.FORWARD
          ? storeId
          : sourceWarehouseMappingId;

      const getInventoryCredentialHandler = new GetInventoryCredential(
        this.docClient,
        this.configParameters,
      );

      const { locationKey: destinationLocationKey } =
        await getInventoryCredentialHandler.getInventoryCredential(
          destinationLocationId,
        );

      const easyEcomHandler = new EasyEcomService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const grnItems = items.map(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        ({ assignedQuantity: _, receivedQuantity, price, ...rest }) => {
          return {
            ...rest,
            quantity: Number(receivedQuantity),
            cost: price,
          };
        },
      );

      const { queueId } = await easyEcomHandler.createGRNAgainstPO(
        poId,
        vendorId,
        grnItems,
        destinationLocationKey,
      );

      const grnId = await codeHandler.generateCode(
        storeId,
        'STN_COUNT',
        `GRN${storeId}-`,
      );

      const createdAt = moment().toISOString();
      const listPOBySTNHandler = new ListPOsBySTN(
        this.docClient,
        this.configService,
        this.ssmClient,
        this.configParameters,
      );
      const data = await listPOBySTNHandler.listPOsSTN(stnId);

      const purchaseOrder: PurchaseOrder = data.find(
        (po) => po.po_id === Number(poId),
      );

      if (!purchaseOrder) {
        throw new CustomError(`Purchase Order with ID ${poId} not found`, 404);
      }

      const { po_created_date } = purchaseOrder;
      const grnItem: GRN = {
        ...createGRNInput,
        grnId,
        products: items,
        queueId,
        createdAt,
        poCreatedAt: po_created_date,
        updatedAt: createdAt,
      };

      const GRN_TABLE_NAME = await this.configParameters.getGRNTableName();
      console.log('GRN_TABLE_NAME', GRN_TABLE_NAME, grnItem);

      const putCommand = new PutCommand({
        TableName: GRN_TABLE_NAME,
        Item: grnItem,
        ConditionExpression: 'attribute_not_exists(grnId)',
      });

      await this.docClient.createItem(putCommand);
      if (transferMode === STNTransferMode.STORE) {
        const grnCompleted = await this.validateGRNWithPOQuantities(
          purchaseOrder,
          products,
          existingGRNs,
        );
        const updateSTNHandler = new UpdateSTN(
          this.docClient,
          this.configService,
          this.ssmClient,
          this.configParameters,
        );
        if (grnCompleted) {
          await updateSTNHandler.updateSTNProgressStatus(stnId, {
            progressStatus: STNProgressStatus.COMPLETED,
          });
        }
      }

      return grnItem;
    } catch (error) {
      console.error('Failed to create GRN:', error);
      throw new CustomError(`Failed to create GRN ${error}`, 500);
    }
  }

  private async validateGRNQuantities(
    grnProducts: CreateGRNInput['products'],
    existingGRNs: GRN[],
    stnProducts: { sku: string; quantity: number }[],
  ): Promise<void> {
    const existingQuantities = existingGRNs.reduce(
      (acc, grn) => {
        grn.products.forEach((item) => {
          acc[item.sku] = (acc[item.sku] || 0) + Number(item.receivedQuantity);
        });
        return acc;
      },
      {} as Record<string, number>,
    );

    const remainingQuantities = stnProducts.reduce(
      (acc, item) => {
        acc[item.sku] =
          Number(item.quantity) - (existingQuantities[item.sku] || 0);
        return acc;
      },
      {} as Record<string, number>,
    );

    for (const item of grnProducts) {
      const remainingQuantity = remainingQuantities[item.sku];
      if (Number(item.receivedQuantity) > remainingQuantity) {
        throw new CustomError(
          `Requested quantity for SKU ${item.sku} exceeds remaining quantity`,
          400,
        );
      }
    }
    posLogger.info('grn', 'validateGRNQuantities', 'GRN quantity validated');
  }
  private async validateGRNWithPOQuantities(
    purchaseOrder: any,
    grnProducts: CreateGRNInput['products'],
    existingGRNs: GRN[],
  ): Promise<boolean> {
    const { po_items } = purchaseOrder;

    const existingQuantities = existingGRNs.reduce(
      (acc, grn) => {
        grn.products.forEach((item) => {
          acc[item.sku] = (acc[item.sku] || 0) + Number(item.receivedQuantity);
        });
        return acc;
      },
      {} as Record<string, number>,
    );

    for (const poItem of po_items) {
      const { model_no, original_quantity } = poItem;

      const grnProduct = grnProducts.find(
        (product) => product.sku === model_no,
      );

      const totalRequestedQuantity =
        (existingQuantities[model_no] || 0) +
        Number(grnProduct?.receivedQuantity || 0);

      if (totalRequestedQuantity !== Number(original_quantity)) {
        console.log(totalRequestedQuantity, original_quantity);

        return false;
      }
    }

    return true;
  }
}
