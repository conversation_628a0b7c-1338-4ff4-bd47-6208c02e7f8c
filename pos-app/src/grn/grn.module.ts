import { Module } from '@nestjs/common';
import { GRNService } from './grn.service';
import { GRNResolver } from './grn.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';

@Module({
  imports: [DocumentClientModule, SsmClientModule, ConfigParametersModule],
  providers: [GRNResolver, GRNService, SuccessHandler, ErrorHandler],
})
export class GRNModule {}
