import { InputType, Field, Int } from '@nestjs/graphql';

@InputType()
export class GRNProductInput {
  @Field(() => String, { description: 'SKU of the product' })
  sku: string;

  @Field(() => Number, { description: 'Quantity of the product ordered' })
  receivedQuantity: number;

  @Field(() => Number, { description: 'Quantity of the product ordered' })
  assignedQuantity: number;
}

@InputType()
export class CreateGRNInput {
  @Field(() => String, {
    description: ' PO Id',
  })
  poId: string;

  @Field(() => String, {
    description: ' STN Id',
  })
  stnId: string;

  @Field(() => Int, {
    description: 'Vendor Id',
    nullable: true,
  })
  vendorId?: number;

  @Field(() => String, {
    description: 'Partial GRN Reason',
    nullable: true,
  })
  partialGRNReason?: string;

  @Field(() => String, {
    description: 'Remarks',
    nullable: true,
  })
  remarks?: string;

  @Field(() => String, {
    description: 'attachement',
    nullable: true,
  })
  attachmentS3Key?: string;

  @Field(() => [GRNProductInput], {
    description: 'List of products included in the order',
  })
  products: GRNProductInput[];
}
