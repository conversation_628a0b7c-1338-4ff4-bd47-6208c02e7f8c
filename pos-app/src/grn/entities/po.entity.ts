import { ObjectType, Field, Int } from '@nestjs/graphql';

@ObjectType()
export class PurchaseOrderItem {
  @Field(() => Int, { description: 'Purchase Order Detail ID' })
  purchase_order_detail_id: number;

  @Field(() => Int, { description: 'CP ID' })
  cp_id: number;

  @Field(() => Int, { description: 'Product ID' })
  product_id: number;

  @Field(() => String, { description: 'SKU of the product' })
  sku: string;

  @Field(() => String, { description: 'HSN code of the product' })
  hsn: string;

  @Field(() => String, { description: 'Model number of the product' })
  model_no: string;

  @Field(() => String, {
    description: 'EAN code of the product',
    nullable: true,
  })
  ean?: string;

  @Field(() => String, { description: 'Product description' })
  product_description: string;

  @Field(() => Int, { description: 'Original quantity ordered' })
  original_quantity: number;

  @Field(() => Int, { description: 'Pending quantity' })
  pending_quantity: number;

  @Field(() => String, { description: 'Item price' })
  item_price: string;
}

@ObjectType()
export class PurchaseOrder {
  @Field(() => Int, { description: 'Purchase Order ID' })
  po_id: number;

  @Field(() => String, { description: 'Total value of the purchase order' })
  total_po_value: string;

  @Field(() => Int, { description: 'Purchase Order number' })
  po_number: number;

  @Field(() => String, { description: 'Purchase Order reference number' })
  po_ref_num: string;

  @Field(() => Int, { description: 'Purchase Order status ID' })
  po_status_id: number;

  @Field(() => String, { description: 'Creation date of the purchase order' })
  po_created_date: string;

  @Field(() => String, {
    description: 'Last update date of the purchase order',
  })
  po_updated_date: string;

  @Field(() => String, {
    description: 'Warehouse where the purchase order was created',
  })
  po_created_warehouse: string;

  @Field(() => Int, {
    description: 'Warehouse ID where the purchase order was created',
  })
  po_created_warehouse_c_id: number;

  @Field(() => String, {
    description:
      'Location key of the warehouse where the purchase order was created',
  })
  po_created_location_key: string;

  @Field(() => String, { description: 'Vendor name' })
  vendor_name: string;

  @Field(() => Int, { description: 'Vendor ID' })
  vendor_c_id: number;

  @Field(() => String, { description: 'Vendor location key' })
  vendor_location_key: string;

  @Field(() => [PurchaseOrderItem], {
    description: 'List of items in the purchase order',
  })
  po_items: PurchaseOrderItem[];
}

@ObjectType()
export class PurchaseOrdersData {
  @Field(() => [PurchaseOrder], {
    nullable: true,
    description: 'List of purchase orders',
  })
  data: PurchaseOrder[];

  @Field(() => String, { description: 'Response message' })
  message: string;

  @Field(() => Int, { description: 'Response status code' })
  status: number;

  @Field(() => Boolean, { description: 'Response success flag' })
  success: boolean;
}
