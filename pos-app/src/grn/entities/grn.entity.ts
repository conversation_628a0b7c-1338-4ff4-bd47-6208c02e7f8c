import { ObjectType, Field, Int } from '@nestjs/graphql';

@ObjectType()
export class GRNProduct {
  @Field(() => String, { description: 'SKU of the product' })
  sku: string;

  @Field(() => Number, { description: 'Quantity of the product ordered' })
  receivedQuantity: number;

  @Field(() => Number, { description: 'Quantity of the product ordered' })
  assignedQuantity: number;

  @Field(() => String, { description: 'Title of the product' })
  title: string;

  @Field(() => Number, {
    description: 'Variant of the product',
    nullable: true,
  })
  price: number;
}

@ObjectType()
export class GRN {
  @Field(() => String, { description: 'STN Id' })
  stnId: string;

  @Field(() => String, { description: 'PO Id' })
  poId: string;

  @Field(() => String, { description: 'GRN Id', nullable: true })
  grnId: string;

  @Field(() => Int, { description: 'vendor Id', nullable: true })
  vendorId?: number;

  @Field(() => String, { description: ' Id', nullable: true })
  requestedStoreId?: string;

  @Field(() => [GRNProduct], {
    description: 'List of products included in the order',
  })
  products: GRNProduct[];

  @Field(() => String, { description: 'GRN queue Id', nullable: true })
  queueId?: string;

  @Field(() => String, { description: 'Creation timestamp of the order' })
  createdAt: string;

  @Field(() => String, { description: 'Creation timestamp of the order' })
  poCreatedAt: string;

  @Field(() => String, {
    description: 'Partial GRN Reason',
    nullable: true,
  })
  partialGRNReason?: string;
  @Field(() => String, {
    description: 'Remarks',
    nullable: true,
  })
  remarks?: string;

  @Field(() => String, {
    description: 'attachement',
    nullable: true,
  })
  attachmentS3Key?: string;

  @Field(() => String, { description: 'Last update timestamp of the order' })
  updatedAt: string;
}

@ObjectType()
export class GRNsData {
  @Field(() => [GRN], {
    nullable: true,
    description: 'List of goods received notes',
  })
  data: GRN[];

  @Field(() => String, { description: 'Response message' })
  message: string;

  @Field(() => Int, { description: 'Response status' })
  status: number;

  @Field(() => Boolean, { description: 'Response success flag' })
  success: boolean;
}
@ObjectType()
export class GRNData {
  @Field(() => GRN, {
    nullable: true,
    description: 'List of goods received notes',
  })
  data: GRN;

  @Field(() => String, { description: 'Response message' })
  message: string;

  @Field(() => Int, { description: 'Response status' })
  status: number;

  @Field(() => Boolean, { description: 'Response success flag' })
  success: boolean;
}
