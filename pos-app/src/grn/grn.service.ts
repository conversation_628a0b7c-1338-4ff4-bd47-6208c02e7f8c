import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { CreateGRN } from './lib/create-grn';
import { CreateGRNInput } from './dto/create-grn.input';

import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ListGRNBySTN } from './lib/list-grn-by-stn-id';
import { SearchAllPOsById } from './lib/search-all-pos-by-id';

@Injectable()
export class GRNService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private ssmClient: AppSsmClient,
  ) {}

  async create(createGRNInput: CreateGRNInput) {
    const createHandler = new CreateGRN(
      this.docClient,
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return createHandler.createGRN(createGRNInput);
  }

  async listGRNBySTN(stnId: string) {
    const listHandler = new ListGRNBySTN(
      this.docClient,
      this.configService,
      this.configParameters,
    );
    return listHandler.listGRNBySTN(stnId);
  }
  async searchPOs(stnId: string, storeId: string) {
    const searchPOsHandler = new SearchAllPOsById(
      this.docClient,
      this.configService,
      this.ssmClient,
      this.configParameters,
    );

    return await searchPOsHandler.searchAllPOsById(stnId, storeId);
  }
}
