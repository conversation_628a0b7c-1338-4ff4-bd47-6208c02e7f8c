import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { GRNService } from './grn.service';
import { GRN, GRNData, GRNsData } from './entities/grn.entity';
import { CreateGRNInput } from './dto/create-grn.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { PurchaseOrdersData } from './entities/po.entity';
import { StaffGuard } from 'src/auth/roles.guard';
import { CustomAuthGuard } from 'src/auth/auth.guard';

@Resolver(() => GRN)
@UseGuards(CustomAuthGuard, StaffGuard)
export class GRNResolver {
  constructor(
    private readonly grnService: GRNService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: <PERSON><PERSON>r<PERSON>and<PERSON>,
  ) {}

  @Mutation(() => GRNData)
  async createGRN(@Args('createGRNInput') createGRNInput: CreateGRNInput) {
    try {
      const data = await this.grnService.create(createGRNInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create GRN',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => GRNsData)
  async listGRNBySTN(@Args('stnId') stnId: string) {
    try {
      const data = await this.grnService.listGRNBySTN(stnId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to fetch GRNs by STN',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Query(() => PurchaseOrdersData)
  async searchPOs(
    @Args('stnId') stnId: string,
    @Args('storeId') storeId: string,
  ) {
    try {
      const data = await this.grnService.searchPOs(stnId, storeId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to fetch POs by STN',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
