import { Test, TestingModule } from '@nestjs/testing';
import { GRNResolver } from './grn.resolver';
import { GRNService } from './grn.service';

describe('GRNResolver', () => {
  let resolver: GRNResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GRNResolver, GRNService],
    }).compile();

    resolver = module.get<GRNResolver>(GRNResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
