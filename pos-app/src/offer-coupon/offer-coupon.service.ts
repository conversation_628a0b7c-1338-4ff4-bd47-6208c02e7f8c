import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GetCampaignCoupon } from 'src/campaign-coupons/lib/get-campaign-coupon';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';

@Injectable()
export class OfferCouponService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async findOne(code: string) {
    const handler = new GetCampaignCoupon(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await handler.getCampaignCoupon(code);
  }
}
