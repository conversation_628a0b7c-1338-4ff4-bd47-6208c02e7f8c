import { Test, TestingModule } from '@nestjs/testing';
import { OfferCouponController } from './offer-coupon.controller';
import { OfferCouponService } from './offer-coupon.service';

describe('OfferCouponController', () => {
  let controller: OfferCouponController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OfferCouponController],
      providers: [OfferCouponService],
    }).compile();

    controller = module.get<OfferCouponController>(OfferCouponController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
