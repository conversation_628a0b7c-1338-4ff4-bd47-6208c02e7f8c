import { Module } from '@nestjs/common';
import { OfferCouponService } from './offer-coupon.service';
import { OfferCouponController } from './offer-coupon.controller';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';

@Module({
  imports: [SsmClientModule, ConfigParametersModule, DocumentClientModule],
  controllers: [OfferCouponController],
  providers: [OfferCouponService],
})
export class OfferCouponModule {}
