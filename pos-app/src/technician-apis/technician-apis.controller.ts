import { Controller, Get, HttpStatus, Param } from '@nestjs/common';
import { TechnicianApisService } from './technician-apis.service';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';

@Controller('technician-apis')
export class TechnicianApisController {
  constructor(
    private readonly technicianApisService: TechnicianApisService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Get('/products/:productid/variants/:variantid/parts')
  async findProductParts(
    @Param('productid') id: string,
    @Param('variantid') variantId: string,
  ) {
    try {
      const data = await this.technicianApisService.findProductParts(
        id,
        variantId,
      );
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return res;
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list part products',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
