import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { uniq } from 'lodash';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { QueryProductsByIds } from 'src/products/lib/list-products-by-ids';
import { QueryVariants } from 'src/products/lib/list-variants';

@Injectable()
export class TechnicianApisService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async findProductParts(id: string, variantId: string) {
    const PRODUCT_TABLE = await this.configParameters.getProductTableName();
    const getCommand = new GetCommand({
      TableName: PRODUCT_TABLE,
      Key: {
        pk: 'PRODUCT',
        sk: id,
      },
      ProjectionExpression: 'metafields',
    });
    const { Item } = await this.docClient.getItem(getCommand);

    if (!Item) {
      throw new CustomError('Product not found', 404);
    }

    const { metafields } = Item;

    if (!metafields || !metafields.length) {
      throw new CustomError("Product doesn't have any parts", 404);
    }

    const partProducts = JSON.parse(
      metafields.find((item: any) => item.key === 'replacement_part_product_id')
        ?.value || null,
    );

    if (
      !partProducts ||
      !partProducts[variantId] ||
      !partProducts[variantId].length
    ) {
      throw new CustomError("Product doesn't have any parts", 404);
    }

    const queryProductsHandler = new QueryProductsByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );

    const productIds: string[] = uniq(partProducts?.[variantId] ?? []);
    const parts = await queryProductsHandler.queryProductsByIds(
      productIds,
    );
    const queryVariantshandler = new QueryVariants(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );

    return await Promise.all(
      parts.map(async ({ id, title }) => {
        const { data: variants } = await queryVariantshandler.queryVariants({
          from: '0',
          size: '3',
          termSearchFields: { product_id: id.toString() },
        });
        return {
          productId: id,
          variantId: variants?.[0]?.id || null,
          title,
          sku: variants?.[0]?.sku || null,
        };
      }),
    );
  }
}
