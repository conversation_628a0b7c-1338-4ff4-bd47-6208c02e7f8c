import { Test, TestingModule } from '@nestjs/testing';
import { TechnicianApisController } from './technician-apis.controller';
import { TechnicianApisService } from './technician-apis.service';

describe('TechnicianApisController', () => {
  let controller: TechnicianApisController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TechnicianApisController],
      providers: [TechnicianApisService],
    }).compile();

    controller = module.get<TechnicianApisController>(TechnicianApisController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
