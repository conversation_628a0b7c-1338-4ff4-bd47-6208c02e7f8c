import { Test, TestingModule } from '@nestjs/testing';
import { LocalConfigurationsService } from './local-configurations.service';

describe('LocalConfigurationsService', () => {
  let service: LocalConfigurationsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LocalConfigurationsService],
    }).compile();

    service = module.get<LocalConfigurationsService>(
      LocalConfigurationsService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
