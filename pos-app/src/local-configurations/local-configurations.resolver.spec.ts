import { Test, TestingModule } from '@nestjs/testing';
import { LocalConfigurationsResolver } from './local-configurations.resolver';
import { LocalConfigurationsService } from './local-configurations.service';

describe('LocalConfigurationsResolver', () => {
  let resolver: LocalConfigurationsResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LocalConfigurationsResolver, LocalConfigurationsService],
    }).compile();

    resolver = module.get<LocalConfigurationsResolver>(
      LocalConfigurationsResolver,
    );
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
