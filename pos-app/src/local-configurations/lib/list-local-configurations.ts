import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { LocalConfigurationData } from 'src/local-configurations/entities/local-configuration.entity';
import { AppConfigParameters } from 'src/config/config';

export class QueryLocalConfiguration {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryLocalConfiguration(
    storeId: string,
  ): Promise<LocalConfigurationData[]> {
    posLogger.info('LocalConfiguration', 'listLocalConfiguration', '');
    try {
      const LOCAL_CONFIGURATION_TABLE =
        await this.configParameters.getLocalConfigurationName();

      const command = new QueryCommand({
        TableName: LOCAL_CONFIGURATION_TABLE,
        KeyConditionExpression: 'storeId = :storeId',
        ExpressionAttributeValues: {
          ':storeId': storeId,
        },
      });

      const { Items }: { Items: LocalConfigurationData[] } =
        await this.docClient.queryItems(command);

      return Items;
    } catch (e) {
      posLogger.error('LocalConfiguration', 'listLocalConfiguration', {
        error: e,
      });
      throw e;
    }
  }
}
