// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { LocalConfigurationData } from 'src/local-configurations/entities/local-configuration.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';

export class GetLocalConfiguration {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getLocalConfiguration(
    storeId: string,
    key: string,
  ): Promise<LocalConfigurationData> {
    posLogger.info('LocalConfiguration', 'getLocalConfiguration', {
      input: key,
    });
    try {
      const LOCAL_CONFIGURATION_TABLE =
        await this.configParameters.getLocalConfigurationName();

      const command = new GetCommand({
        TableName: LOCAL_CONFIGURATION_TABLE,
        Key: { storeId, key },
      });

      const { Item } = await this.docClient.getItem(command);

      if (Item?.isDeleted) {
        throw new CustomError(`${key} configuration not found!`, 404);
      }
      return Item;
    } catch (e) {
      posLogger.error('LocalConfiguration', 'getLocalConfiguration', {
        error: e,
      });
      throw e;
    }
  }
}
