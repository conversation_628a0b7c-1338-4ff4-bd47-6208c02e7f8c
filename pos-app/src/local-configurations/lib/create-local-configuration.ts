// modules
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { LocalConfigurationData } from '../entities/local-configuration.entity';
import { CreateLocalConfigurationInput } from '../dto/create-local-configuration.input';
import { AppConfigParameters } from 'src/config/config';

export class CreateLocalConfiguration {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createLocalConfiguration(
    configuration: CreateLocalConfigurationInput,
  ): Promise<LocalConfigurationData> {
    posLogger.info('LocalConfiguration', 'createLocalConfiguration', {
      input: configuration,
    });
    try {
      const LOCAL_CONFIGURATION_TABLE =
        await this.configParameters.getLocalConfigurationName();

      const Item: LocalConfigurationData = {
        ...configuration,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      };

      const command = new PutCommand({
        TableName: LOCAL_CONFIGURATION_TABLE,
        Item,
      });

      await this.docClient.createItem(command);
      return Item;
    } catch (e) {
      posLogger.error('LocalConfiguration', 'createLocalConfiguration', {
        error: e,
      });
      throw e;
    }
  }
}
