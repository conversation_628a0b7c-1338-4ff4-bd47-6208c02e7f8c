// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { LocalConfigurationData } from '../entities/local-configuration.entity';
import { CreateLocalConfigurationInput } from '../dto/create-local-configuration.input';
import { AppConfigParameters } from 'src/config/config';

export class UpdateLocalConfiguration {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateLocalConfiguration(
    configuration: CreateLocalConfigurationInput,
  ): Promise<LocalConfigurationData> {
    posLogger.info('LocalConfiguration', 'updateLocalConfiguration', {
      input: configuration,
    });
    try {
      const { storeId, key, value } = configuration;

      const LOCAL_CONFIGURATION_TABLE =
        await this.configParameters.getLocalConfigurationName();

      const param = new UpdateCommand({
        TableName: LOCAL_CONFIGURATION_TABLE,
        Key: {
          storeId,
          key,
        },
        UpdateExpression: 'SET #value = :value, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
          '#value': 'value',
        },
        ExpressionAttributeValues: {
          ':value': value,
          ':updatedAt': moment().toISOString(),
        },
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: LocalConfigurationData } =
        await this.docClient.updateItem(param);

      if (!Attributes) {
        throw new Error(
          'Invalid ID! No Local Configuration found for given ID.',
        );
      }

      return Attributes;
    } catch (e) {
      posLogger.error('LocalConfiguration', 'updateLocalConfiguration', {
        error: e,
      });
      throw e;
    }
  }
}
