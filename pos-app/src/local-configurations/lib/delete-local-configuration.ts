// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { LocalConfigurationData } from '../entities/local-configuration.entity';
import moment from 'moment';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
export class RemoveLocalConfiguration {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async removeLocalConfiguration(
    storeId: string,
    key: string,
  ): Promise<LocalConfigurationData> {
    posLogger.info('localConfiguration', 'removeLocalConfiguration', {
      input: { key },
    });
    try {
      const LOCAL_CONFIGURATION_TABLE =
        await this.configParameters.getLocalConfigurationName();

      const param = new UpdateCommand({
        TableName: LOCAL_CONFIGURATION_TABLE,
        Key: {
          storeId,
          key,
        },
        UpdateExpression: 'SET isDeleted = :isDeleted, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
          ':isDeleted': true,
          ':updatedAt': moment().toISOString(),
        },
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: LocalConfigurationData } =
        await this.docClient.updateItem(param);
      if (!Attributes) {
        throw new CustomError(
          'Invalid ID! No Local Configuration found for given ID.',
          404,
        );
      }

      return Attributes;
    } catch (error) {
      posLogger.error('localConfiguration', 'removeLocalConfiguration', {
        error,
      });
      throw error;
    }
  }
}
