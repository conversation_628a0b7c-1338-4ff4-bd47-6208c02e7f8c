import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class LocalConfigurationData {
  @Field(() => String, { description: 'StoreId' })
  storeId: string;

  @Field(() => String, { description: 'Key' })
  key: string;

  @Field(() => Number, { description: 'Value' })
  value: number;

  @Field(() => String, { description: 'CreatedAt' })
  createdAt: string;

  @Field(() => String, { description: 'UpdatedAt' })
  updatedAt: string;
}

@ObjectType()
export class LocalConfiguration {
  @Field(() => LocalConfigurationData, { description: 'Local Configuration' })
  data: LocalConfigurationData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class LocalConfigurations {
  @Field(() => [LocalConfigurationData], { description: 'Local Configuration' })
  data: LocalConfigurationData[];

  @Field(() => Number, { description: 'Count of Total data', nullable: true })
  count: number;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
