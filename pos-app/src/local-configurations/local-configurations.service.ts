import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateLocalConfigurationInput } from './dto/create-local-configuration.input';
import { CreateLocalConfiguration } from './lib/create-local-configuration';
import { QueryLocalConfiguration } from './lib/list-local-configurations';
import { RemoveLocalConfiguration } from './lib/delete-local-configuration';
import { GetLocalConfiguration } from './lib/get-local-configuration';
import { UpdateLocalConfiguration } from './lib/update-local-configuration';
import { AppConfigParameters } from 'src/config/config';

@Injectable()
export class LocalConfigurationsService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}
  async create(createLocalConfigurationInput: CreateLocalConfigurationInput) {
    const createHandler = new CreateLocalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return createHandler.createLocalConfiguration(
      createLocalConfigurationInput,
    );
  }

  findAll(storeId: string) {
    const queryHandler = new QueryLocalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return queryHandler.queryLocalConfiguration(storeId);
  }

  findOne(storeId: string, key: string) {
    const getHandler = new GetLocalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.getLocalConfiguration(storeId, key);
  }

  update(updateLocalConfigurationInput: CreateLocalConfigurationInput) {
    const updateHandler = new UpdateLocalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return updateHandler.updateLocalConfiguration(
      updateLocalConfigurationInput,
    );
  }

  remove(storeId: string, key: string) {
    const removeHandler = new RemoveLocalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return removeHandler.removeLocalConfiguration(storeId, key);
  }
}
