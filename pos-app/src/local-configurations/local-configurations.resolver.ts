import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { LocalConfigurationsService } from './local-configurations.service';
import {
  LocalConfiguration,
  LocalConfigurations,
} from './entities/local-configuration.entity';
import { CreateLocalConfigurationInput } from './dto/create-local-configuration.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CRMGuard } from 'src/auth/roles.guard';

@Resolver(() => LocalConfiguration)
@UseGuards(CustomAuthGuard, CRMGuard)
export class LocalConfigurationsResolver {
  constructor(
    private readonly localConfigurationsService: LocalConfigurationsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: Error<PERSON>and<PERSON>,
  ) {}

  @Mutation(() => LocalConfiguration)
  async createLocalConfiguration(
    @Args('createLocalConfigurationInput')
    createLocalConfigurationInput: CreateLocalConfigurationInput,
  ) {
    try {
      const data = await this.localConfigurationsService.create(
        createLocalConfigurationInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create local configuration',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => LocalConfigurations, { name: 'localConfigurations' })
  async findAll(@Args('storeId', { type: () => String }) storeId: string) {
    try {
      const data = await this.localConfigurationsService.findAll(storeId);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count: data.length };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list local configuration',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => LocalConfiguration, { name: 'localConfiguration' })
  async findOne(
    @Args('storeId', { type: () => String }) storeId: string,
    @Args('key', { type: () => String }) key: string,
  ) {
    try {
      const data = await this.localConfigurationsService.findOne(storeId, key);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get local configuration',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => LocalConfiguration)
  async updateLocalConfiguration(
    @Args('updateLocalConfigurationInput')
    updateLocalConfigurationInput: CreateLocalConfigurationInput,
  ) {
    try {
      const data = await this.localConfigurationsService.update(
        updateLocalConfigurationInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update local configuration',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => LocalConfiguration)
  async removeLocalConfiguration(
    @Args('storeId', { type: () => String }) storeId: string,
    @Args('key', { type: () => String }) key: string,
  ) {
    try {
      const data = await this.localConfigurationsService.remove(storeId, key);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to delete local configuration',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
