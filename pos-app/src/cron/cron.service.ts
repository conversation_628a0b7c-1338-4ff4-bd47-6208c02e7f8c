import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import {
  createDiscountCodePayload,
  createPriceRulePayload,
  getDate,
  listMetafields,
  listShopifyDiscountCodes,
  listShopifyPriceRules,
  listShopifyProducts,
  fetchDataFromGoogleSheet,
  listShopifyCollections,
  fetchAllResetCoupons,
  LeadSquareApi,
  fetchPreviousDayRecords,
  listShopifyPriceRulesGraphQL,
  listShopifyDiscountCodesGraphQL,
} from './helper/lib';
import {
  DeleteCommand,
  PutCommand,
  UpdateCommand,
  ScanCommand,
} from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { QueryVariants } from 'src/products/lib/list-variants';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { getPriceRuleStatus } from 'src/common/helper/get-price-rule-status';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';
import { ScanDB } from 'src/common/helper/scan-table';
import { UpdateGlobalConfiguration } from 'src/global-configurations/lib/update-global-configurations';
import { ProductStatus } from 'src/common/enum/products';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';
import { ExportSTNs } from 'src/stn/lib/export-stns';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { PriceService } from 'src/price/price.service';
import { EmiToolService } from 'src/common/emi-tool/emi-tool.service';

import { json2csv } from 'json-2-csv';
import * as fs from 'fs';
import * as path from 'path';
import { S3Interactions } from 'src/common/helper/interaction-to-s3';
import { GraphQLClient } from 'graphql-request';

@Injectable()
export class CronService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {}

  emiToolServiceHandler = new EmiToolService(
    this.configService,
    this.docClient,
    this.ssmClient,
    this.configParameters,
  );

  @Cron(CronExpression.EVERY_HOUR, { name: 'cronFetchEMIDetails' })
  async fetchEMIDetails() {
    try {
      console.log('Starting nightly EMI update cron job');
      await this.emiToolServiceHandler.fetchEmiOptions();
      console.log('Nightly EMI update completed successfully');
    } catch (error) {
      console.log(`Error in EMI cron job: ${error.message}`);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_9PM, { name: 'cronFetchShopifyProducts' })
  async fetchShopifyProducts() {
    posLogger.info('cron', 'fetchShopifyProducts', 'started');
    const axios = require('axios');
    try {
      const [SHOPIFY_ADMIN_BASE_URL, SHOPIFY_ACCESS_TOKEN, PRODUCT_TABLE] =
        await Promise.all([
          await this.shopifyClient.getShopifyAdminBaseUrl(),
          await this.shopifyClient.getShopifyAccessToken(),
          await this.configParameters.getProductTableName(),
        ]);
      let draftProductsArray = [];
      const date = getDate(1);

      let graphqlEndpoint;
      if (SHOPIFY_ADMIN_BASE_URL.includes('/admin/api/')) {
        const shopBaseUrl = SHOPIFY_ADMIN_BASE_URL.split('/admin/api/')[0];
        graphqlEndpoint = `${shopBaseUrl}/admin/api/2023-10/graphql.json`;
      } else {
        graphqlEndpoint = `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`;
      }

      posLogger.info(
        'cron',
        'fetchShopifyProducts',
        `Constructed GraphQL endpoint: ${graphqlEndpoint}`,
      );

      let allProducts = [];
      let hasNextPage = true;
      let cursor = null;

      while (hasNextPage) {
        const paginationParams = cursor ? `, after: "${cursor}"` : '';

        const productsQuery = `
          query {
            products(first: 249, query: "updated_at:>=${date}"${paginationParams}) {
              pageInfo {
                hasNextPage
              }
              edges {
                cursor
                node {
                  id
                  title
                  handle
                  status
                  productType
                  createdAt
                  updatedAt
                  publishedAt
                  vendor
                  templateSuffix
                  tags
                  bodyHtml
                  options {
                    id
                    name
                    position
                    values
                  }
                  variants(first: 249) {
                    edges {
                      node {
                        id
                        title
                        sku
                        price
                        compareAtPrice
                        inventoryQuantity
                        inventoryPolicy
                        inventoryManagement
                        barcode
                        weight
                        weightUnit
                        requiresShipping
                        taxable
                        createdAt
                        updatedAt
                        position
                        displayName
                        selectedOptions {
                          name
                          value
                        }
                        image {
                          id
                          originalSrc
                          altText
                          width
                          height
                        }
                      }
                    }
                  }
                  images(first: 50) {
                    edges {
                      node {
                        id
                        originalSrc
                        altText
                        width
                        height
                      }
                    }
                  }
                  metafields(first: 249) {
                    edges {
                      node {
                        id
                        namespace
                        key
                        value
                        type
                        description
                        createdAt
                        updatedAt
                      }
                    }
                  }
                }
              }
            }
          }
        `;

        posLogger.info(
          'cron',
          'fetchShopifyProducts',
          `Querying GraphQL endpoint with cursor: ${cursor || 'initial request'}`,
        );

        let response;
        let result;

        try {
          if (typeof axios !== 'undefined') {
            response = await axios.post(
              graphqlEndpoint,
              { query: productsQuery },
              {
                headers: {
                  'Content-Type': 'application/json',
                  'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
                },
              },
            );

            result = response.data;
            posLogger.info(
              'cron',
              'fetchShopifyProducts',
              'Successfully received GraphQL response with axios',
            );
          } else {
            response = await fetch(graphqlEndpoint, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
              },
              body: JSON.stringify({
                query: productsQuery,
              }),
            });

            if (!response.ok) {
              const errorText = await response.text();
              posLogger.error(
                'cron',
                'fetchShopifyProducts',
                `HTTP error response: ${errorText}`,
              );
              throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const text = await response.text();
            if (!text || text.trim() === '') {
              throw new Error('Empty response received from GraphQL endpoint');
            }
            posLogger.info(
              'cron',
              'fetchShopifyProducts',
              `Response text length: ${text.length}`,
            );
            result = JSON.parse(text);
            posLogger.info(
              'cron',
              'fetchShopifyProducts',
              'Successfully parsed GraphQL response',
            );
          }
        } catch (error) {
          posLogger.error(
            'cron',
            'fetchShopifyProducts',
            `Failed to fetch from GraphQL: ${error.message}`,
          );
          throw error;
        }

        if (result.errors) {
          throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
        }

        if (
          !result.data ||
          !result.data.products ||
          !result.data.products.edges
        ) {
          posLogger.error(
            'cron',
            'fetchShopifyProducts',
            `Invalid GraphQL response format: ${JSON.stringify(result)}`,
          );
          throw new Error('Invalid GraphQL response format');
        }

        const pageProducts = result.data.products.edges.map((edge) => {
          const product = edge.node;

          const legacyId = parseInt(product.id.split('/').pop());
          const productMetafields = [];
          if (
            product.metafields &&
            product.metafields.edges &&
            product.metafields.edges.length > 0
          ) {
            for (const metaEdge of product.metafields.edges) {
              const metafield = metaEdge.node;
              if (!metafield || !metafield.id) continue;

              const metafieldId = parseInt(metafield.id.split('/').pop());
              productMetafields.push({
                id: metafieldId,
                admin_graphql_api_id: metafield.id,
                namespace: metafield.namespace || '',
                key: metafield.key || '',
                value: metafield.value || '',
                type: metafield.type || '',
                description: metafield.description || null,
                created_at: metafield.createdAt || product.createdAt,
                updated_at: metafield.updatedAt || product.updatedAt,
                owner_id: legacyId,
                owner_resource: 'product',
              });
            }
          }

          return {
            id: legacyId,
            title: product.title,
            metafields: productMetafields,
            handle: product.handle,
            admin_graphql_api_id: product.id,
            body_html: product.bodyHtml,
            created_at: product.createdAt,
            updated_at: product.updatedAt,
            published_at: product.publishedAt,
            vendor: product.vendor,
            product_type: product.productType,
            status: product.status,
            tags: product.tags.join(','),
            template_suffix: product.templateSuffix,
            options: product.options.map((option) => ({
              id: parseInt(option.id.split('/').pop()),
              name: option.name,
              position: option.position,
              product_id: legacyId,
              values: option.values,
            })),
            variants: product.variants.edges.map((variantEdge) => {
              const variant = variantEdge.node;
              const variantLegacyId = parseInt(variant.id.split('/').pop());

              let option1 = null,
                option2 = null,
                option3 = null;
              if (
                variant.selectedOptions &&
                variant.selectedOptions.length > 0
              ) {
                option1 = variant.selectedOptions[0].value;
                if (variant.selectedOptions.length > 1) {
                  option2 = variant.selectedOptions[1].value;
                }
                if (variant.selectedOptions.length > 2) {
                  option3 = variant.selectedOptions[2].value;
                }
              }

              return {
                id: variantLegacyId,
                product_id: legacyId,
                admin_graphql_api_id: variant.id,
                title: variant.title,
                price: variant.price,
                compare_at_price: variant.compareAtPrice,
                sku: variant.sku,
                position: variant.position,
                inventory_policy: variant.inventoryPolicy
                  ? variant.inventoryPolicy.toLowerCase()
                  : 'deny',
                inventory_management: variant.inventoryManagement
                  ? variant.inventoryManagement.toLowerCase()
                  : null,
                inventory_quantity: variant.inventoryQuantity || 0,
                old_inventory_quantity: variant.inventoryQuantity || 0,
                requires_shipping: variant.requiresShipping,
                taxable: variant.taxable,
                barcode: variant.barcode || '',
                grams: 0,
                weight: variant.weight || 0,
                weight_unit: variant.weightUnit
                  ? variant.weightUnit.toLowerCase()
                  : 'kg',
                fulfillment_service: 'manual',
                inventory_item_id: parseInt(variant.id.split('/').pop()),
                option1: option1,
                option2: option2,
                option3: option3,
                created_at: variant.createdAt,
                updated_at: variant.updatedAt,
                image_id: variant.image
                  ? parseInt(variant.image.id.split('/').pop())
                  : null,
                image: variant.image
                  ? {
                      id: parseInt(variant.image.id.split('/').pop()),
                      admin_graphql_api_id: variant.image.id,
                      src: variant.image.originalSrc,
                      alt: variant.image.altText,
                      width: variant.image.width,
                      height: variant.image.height,
                      position: 1,
                      created_at: product.createdAt,
                      updated_at: product.updatedAt,
                      product_id: legacyId,
                      variant_ids: [],
                    }
                  : null,
              };
            }),
            images: product.images.edges.map((imageEdge, index) => {
              const image = imageEdge.node;
              return {
                id: parseInt(image.id.split('/').pop()),
                admin_graphql_api_id: image.id,
                src: image.originalSrc,
                alt: image.altText,
                width: image.width,
                height: image.height,
                position: index + 1,
                created_at: product.createdAt,
                updated_at: product.updatedAt,
                product_id: legacyId,
                variant_ids: [],
              };
            }),
            image:
              product.images.edges.length > 0
                ? {
                    id: parseInt(
                      product.images.edges[0].node.id.split('/').pop(),
                    ),
                    admin_graphql_api_id: product.images.edges[0].node.id,
                    src: product.images.edges[0].node.originalSrc,
                    alt: product.images.edges[0].node.altText,
                    width: product.images.edges[0].node.width,
                    height: product.images.edges[0].node.height,
                    position: 1,
                    created_at: product.createdAt,
                    updated_at: product.updatedAt,
                    product_id: legacyId,
                    variant_ids: [],
                  }
                : null,
          };
        });

        allProducts = [...allProducts, ...pageProducts];

        hasNextPage = result.data.products.pageInfo.hasNextPage;
        if (hasNextPage && result.data.products.edges.length > 0) {
          cursor =
            result.data.products.edges[result.data.products.edges.length - 1]
              .cursor;
        }

        posLogger.info(
          'cron',
          'fetchShopifyProducts',
          `Fetched ${pageProducts.length} products on this page. Total products so far: ${allProducts.length}. Has next page: ${hasNextPage}`,
        );
      }

      posLogger.info(
        'cron',
        'fetchShopifyProducts',
        `Total products fetched across all pages: ${allProducts.length}`,
      );

      const priceService = new PriceService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const priceLogging = [];

      try {
        const getGlobalConfigurationHandler = new GetGlobalConfiguration(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const { value: draftProductsConfig } =
          await getGlobalConfigurationHandler.getGlobalConfiguration(
            'DRAFT_PRODUCTS',
          );

        try {
          draftProductsArray = JSON.parse(
            draftProductsConfig.replace(/'/g, '"'),
          )?.map((p) => p.id);
        } catch (jsonError) {
          posLogger.error(
            'cron',
            'fetchShopifyProducts',
            'JSON parse error for DraftProducts Config',
          );
          draftProductsArray = [];
        }
      } catch (e) {
        posLogger.error(
          'cron',
          'fetchShopifyProducts',
          'Error for DraftProducts Config',
        );
        draftProductsArray = [];
      }
      console.log('allProducts :>>🥰🥰🥰 ', allProducts.length);

      for (const product of allProducts) {
        try {
          const { id, variants, product_type, status, ...productAttributes } =
            product;

          if (
            draftProductsArray?.length &&
            draftProductsArray?.includes(id.toString())
          ) {
            continue;
          }

          try {
            const queryVariantsHandler = new QueryVariants(
              this.configService,
              this.ssmClient,
              this.configParameters,
            );
            const { data: dbVariants } =
              await queryVariantsHandler.queryVariants({
                from: '0',
                size: '1000',
                termSearchFields: {
                  product_id: id.toString(),
                },
              });
            const shopifyVariantsIds = variants.map((variant) => variant.id);
            const removedVariants = dbVariants.filter(
              ({ id }) => !shopifyVariantsIds.includes(Number(id)),
            );

            console.log(
              'removedVariants.sk :>> ',
              removedVariants.map(
                ({ id, product_id }) => `${product_id}#${id}`,
              ),
            );
            await Promise.all(
              removedVariants.map(async ({ id, product_id }) => {
                try {
                  const variantCMD = new DeleteCommand({
                    TableName: PRODUCT_TABLE,
                    Key: {
                      pk: 'VARIANT',
                      sk: `${product_id}#${id}`,
                    },
                  });
                  await this.docClient.deleteItem(variantCMD);
                } catch (error) {
                  console.log('Deleting variant error :>> ', error);
                }
              }),
            );
          } catch (error) {
            posLogger.error(
              'cron',
              'fetchShopifyProducts',
              `removedVariants error :>> ${error} for products from shopify`,
            );
          }

          if (id.toString() == '7920839229669') {
            productAttributes.title = 'Smart Ortho Mattress - Grey';
          } else if (id.toString() == '8095641174245') {
            productAttributes.title = 'Smart Ortho Mattress - Grey - Custom';
          }

          const datasourcePayload = {
            ...productAttributes,
            sk: id.toString(),
            pk: 'PRODUCT',
            id: id.toString(),
            product_type: product_type ? product_type : 'Others',
            type: 'PRODUCT',
            status: status ? status.toLowerCase() : null,
            metafields: product.metafields,
            price: variants?.[0]?.price || '0',
            createdAt: moment().toISOString(),
            updatedAt: moment().toISOString(),
          };

          const datasourceCMD = new PutCommand({
            TableName: PRODUCT_TABLE,
            Item: datasourcePayload,
          });
          console.log(
            `🌹datasourcePayloadddddd`,
            datasourcePayload.sk,
            JSON.stringify(datasourcePayload),
            '🌹',
          );
          await this.docClient.createItem(datasourceCMD);

          await Promise.all(
            variants.map(async (variant) => {
              try {
                const {
                  id,
                  product_id,
                  image_id,
                  sku,
                  price,
                  ...variantAttributes
                } = variant;

                let metafields = [];
                try {
                  const variantMetafieldsQuery = `
                    query {
                      productVariant(id: "${variant.admin_graphql_api_id}") {
                        metafields(first: 50) {
                          edges {
                            node {
                              id
                              namespace
                              key
                              value
                              type
                              description
                              createdAt
                              updatedAt
                            }
                          }
                        }
                      }
                    }
                  `;

                  const variantMetafieldsResponse = await fetch(
                    graphqlEndpoint,
                    {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                        'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
                      },
                      body: JSON.stringify({
                        query: variantMetafieldsQuery,
                      }),
                    },
                  );

                  if (!variantMetafieldsResponse.ok) {
                    throw new Error(
                      `HTTP error! Status: ${variantMetafieldsResponse.status}`,
                    );
                  }

                  const responseText = await variantMetafieldsResponse.text();
                  if (!responseText || responseText.trim() === '') {
                    throw new Error(
                      'Empty response received for variant metafields',
                    );
                  }

                  const variantMetafieldsResult = JSON.parse(responseText);

                  if (
                    variantMetafieldsResult.data?.productVariant?.metafields
                      ?.edges
                  ) {
                    const edges =
                      variantMetafieldsResult.data.productVariant.metafields
                        .edges;
                    console.log(
                      `Found ${edges.length} metafields for variant ${id}`,
                    );

                    metafields = edges.map((edge) => {
                      const metafield = edge.node;
                      const metafieldId = parseInt(
                        metafield.id.split('/').pop(),
                      );
                      return {
                        id: metafieldId,
                        admin_graphql_api_id: metafield.id,
                        namespace: metafield.namespace || '',
                        key: metafield.key || '',
                        value: metafield.value || '',
                        type: metafield.type || '',
                        description: metafield.description || null,
                        created_at: metafield.createdAt || variant.createdAt,
                        updated_at: metafield.updatedAt || variant.updatedAt,
                        owner_id: id,
                        owner_resource: 'variant',
                      };
                    });
                  } else {
                    console.log(`No metafields found for variant ${id}`);
                  }
                } catch (metafieldError) {
                  console.log(
                    `Failed to fetch variant metafields: ${metafieldError.message}`,
                  );
                }

                if (image_id) {
                  const { images } = productAttributes;
                  const image = images?.find((image) => image.id === image_id);
                  if (image) {
                    variantAttributes.image = image;
                  }
                } else {
                  variantAttributes.image = product.image;
                }

                const variantPayload = {
                  ...variantAttributes,
                  title: undefined,
                  variantTitle: variant.title,
                  sk: `${product_id}#${id}`,
                  pk: `VARIANT`,
                  id: id.toString(),
                  product_id: product_id.toString(),
                  type: 'VARIANT',
                  productTitle: product.title,
                  metafields,
                  image_id,
                  status: status ? status.toLowerCase() : null,
                  sku,
                  price,
                  createdAt: moment().toISOString(),
                  updatedAt: moment().toISOString(),
                };
                const variantCMD = new PutCommand({
                  TableName: PRODUCT_TABLE,
                  Item: variantPayload,
                });
                console.log(
                  `🔴variantPayloadpppppp`,
                  variantPayload.sk,
                  JSON.stringify(variantPayload),
                  '🔴',
                );
                await this.docClient.createItem(variantCMD);

                const lastLoggedPrice = await priceService.findLatestOne({
                  product_id: product_id.toString(),
                  variant_id: id.toString(),
                });

                if (
                  !lastLoggedPrice ||
                  Number(lastLoggedPrice.price) !== Number(price)
                ) {
                  priceLogging.push({
                    product_id: product_id.toString(),
                    variant_id: id.toString(),
                    price: price || '0',
                    sku,
                  });
                }
              } catch (error) {
                console.log('variant error :>> ', error);
              }
            }),
          );
        } catch (error) {
          console.log('product error :>> ', error);
        }
      }

      console.log(priceLogging.length, ':::::: <<< priceLogging.length');
      if (priceLogging.length) {
        try {
          await priceService.create(priceLogging);
        } catch (e) {
          console.log('Error creating price ::::', e);
        }
      }

      const globalConfigurationsService = new UpdateGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      await globalConfigurationsService.updateGlobalConfiguration(
        'LAST_SYNC_PRODUCTS',
        {
          key: 'LAST_SYNC_PRODUCTS',
          value: moment().toISOString(),
        },
      );
    } catch (error) {
      console.log('fetchShopifyProducts error :>> ', error);
    }
    posLogger.info('cron', 'fetchShopifyProducts', `Finished`);
  }

  async scanTableRecursively(scanCommand: ScanCommand): Promise<any[]> {
    const results: any[] = [];
    let lastEvaluatedKey: any;

    do {
      const commandInput = {
        ...scanCommand.input,
        ExclusiveStartKey: lastEvaluatedKey,
      };

      const response = await this.docClient.scanItems(
        new ScanCommand(commandInput),
      );

      if (response.Items) {
        results.push(...response.Items);
      }

      lastEvaluatedKey = response.LastEvaluatedKey;
    } while (lastEvaluatedKey);

    return results;
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, { name: 'leadsquareScheduler' })
  async sendLeads() {
    try {
      posLogger.info('cron', 'leadsquareScheduler ', 'started');
      const getGlobalConfigurationHandler = new GetGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const [QUOTATIONS_TABLE, API_GATEWAY_URL] = await Promise.all([
        this.configParameters.getQuotationTableName(),
        this.configParameters.getApiGatewayUrl(),
      ]);

      const { value: QUOTATION_EXPIRE_INTERVAL_IN_DAYS } =
        await getGlobalConfigurationHandler.getGlobalConfiguration(
          'QUOTATION_EXPIRE_INTERVAL_IN_DAYS',
        );

      const interval = Number(QUOTATION_EXPIRE_INTERVAL_IN_DAYS);

      const halfInterval = moment()
        .subtract(Math.floor(interval / 2), 'days')
        .toDate();
      const oneDayBeforeInterval = moment()
        .subtract(interval - 1, 'days')
        .toDate();

      const halfIntervalStart = moment(halfInterval)
        .startOf('day')
        .toISOString();
      const halfIntervalEnd = moment(halfInterval).endOf('day').toISOString();

      const oneDayBeforeStart = moment(oneDayBeforeInterval)
        .startOf('day')
        .toISOString();
      const oneDayBeforeEnd = moment(oneDayBeforeInterval)
        .endOf('day')
        .toISOString();

      const halfIntervalCmd = new ScanCommand({
        TableName: QUOTATIONS_TABLE,
        FilterExpression: '#createdAt BETWEEN :startVal AND :endVal',
        ExpressionAttributeNames: {
          '#createdAt': 'createdAt',
        },
        ExpressionAttributeValues: {
          ':startVal': halfIntervalStart,
          ':endVal': halfIntervalEnd,
        },
      });

      const oneDayBeforeCmd = new ScanCommand({
        TableName: QUOTATIONS_TABLE,
        FilterExpression: '#createdAt BETWEEN :startVal AND :endVal',
        ExpressionAttributeNames: {
          '#createdAt': 'createdAt',
        },
        ExpressionAttributeValues: {
          ':startVal': oneDayBeforeStart,
          ':endVal': oneDayBeforeEnd,
        },
      });

      const [halfIntervalItems, oneDayBeforeItems] = await Promise.all([
        this.scanTableRecursively(halfIntervalCmd),
        this.scanTableRecursively(oneDayBeforeCmd),
      ]);

      const sendToLeadSquare = async (items: any[], label: string) => {
        const BATCH_SIZE = 40;
        const DELAY_MS = 10000;
        const result: any[] = [];
        let filteredItems;
        try {
          filteredItems = items.filter((item) => item.status !== 'CONVERTED');
        } catch (error) {
          posLogger.error('cron', 'leadsquareScheduler error :>> ', error);
          filteredItems = items;
        }

        const batches = [];
        for (let i = 0; i < filteredItems.length; i += BATCH_SIZE) {
          batches.push(filteredItems.slice(i, i + BATCH_SIZE));
        }

        for (const batch of batches) {
          const batchResult = await Promise.allSettled(
            batch.map((item: any) =>
              LeadSquareApi(item, label, API_GATEWAY_URL),
            ),
          );
          result.push(...batchResult);

          if (batches.indexOf(batch) < batches.length - 1) {
            await new Promise((resolve) => setTimeout(resolve, DELAY_MS));
          }
        }

        return result;
      };

      await Promise.allSettled([
        sendToLeadSquare(
          halfIntervalItems,
          `Quotation Expiry - ${Math.floor(interval / 2)} days`,
        ),
        sendToLeadSquare(oneDayBeforeItems, 'Quotation Expiry - 1 day'),
      ]);
      posLogger.info('cron', 'leadsquareScheduler', 'success');
    } catch (error) {
      posLogger.error('cron', 'leadsquareScheduler error :>> ', error);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_11PM, { name: 'stnExportScheduler' })
  async exportSTNForAllEmails() {
    try {
      posLogger.info('cron', 'stnExportScheduler started', 'started');

      const emailArray: string[] = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      const currentDate = moment().format('YYYY-MM-DD');
      const fromDate = currentDate;
      const toDate = currentDate;

      for (const email of emailArray) {
        const filter = {
          fromDate,
          toDate,
        };

        const exportHandler = new ExportSTNs(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.s3Client,
          this.configParameters,
        );

        await exportHandler.exportSTNs(email, filter, true);
        posLogger.info('cron', `STN export sent to ${email}`, 'success');
      }

      posLogger.info('cron', 'stnExportScheduler finished', 'success');
    } catch (error) {
      posLogger.error('cron', 'stnExportScheduler error :>> ', error);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_11PM, {
    name: 'cronFetchShopifyCoupons',
  })
  async fetchShopifyCoupons() {
    try {
      console.log('=======fetchShopifyCoupons started======');
      posLogger.info('cron', 'fetchShopifyCoupons ', 'started');

      const [SHOPIFY_ADMIN_BASE_URL, SHOPIFY_ACCESS_TOKEN, COUPONS_TABLE] =
        await Promise.all([
          await this.shopifyClient.getShopifyAdminBaseUrl(),
          await this.shopifyClient.getShopifyAccessToken(),
          await this.configParameters.getCouponTableName(),
        ]);

      const date = getDate(1);
      // Construct the GraphQL endpoint
      const graphqlEndpoint = `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`;

      const newPriceRules = await listShopifyPriceRulesGraphQL(
        graphqlEndpoint,
        SHOPIFY_ACCESS_TOKEN,
        date,
      );

      console.log('=======newPriceRules======', newPriceRules);

      for (let i = 0; i < newPriceRules.length; i++) {
        try {
          const priceRule = newPriceRules[i];
          posLogger.info(
            'cron',
            'fetchShopifyCoupons Currently running priceRule',
            priceRule.id,
          );

          const datasourcePayload = createPriceRulePayload(priceRule);
          console.log('=====datasourcePayload====', datasourcePayload);

          const datasourceCMD = new PutCommand({
            TableName: COUPONS_TABLE,
            Item: datasourcePayload,
          });
          const priceRuleItem = await this.docClient.createItem(datasourceCMD);
          console.log('=====priceRuleItem====', priceRuleItem);

          // let link = `${SHOPIFY_ADMIN_BASE_URL}/price_rules/${priceRule.id}/discount_codes.json`;
          const priceRuleId = `gid://shopify/PriceRule/${priceRule.id}`;
          console.log('====priceRuleId====', priceRuleId);
          const discountCodes = await listShopifyDiscountCodesGraphQL(
            graphqlEndpoint,
            SHOPIFY_ACCESS_TOKEN,
            priceRuleId,
          );

          console.log('=======discountCodes======', discountCodes);

          for (const discount of discountCodes) {
            try {
              const discountCode = discount?.code;
              if (
                discountCode.endsWith('tool') ||
                discountCode.startsWith('AMZ') ||
                discountCode.startsWith('OC-')
              ) {
                posLogger.info(
                  'cron',
                  'fetchShopifyCoupons',
                  'Skipping tool and Bulk Shopify(AMZ) Coupon, no need to import',
                );
                continue;
              }

              // Save discount code to DynamoDB
              const discountPayload = createDiscountCodePayload(discount);
              console.log(
                '=====discountPayload====',
                JSON.stringify(discountPayload),
              );
              const discountCMD = new PutCommand({
                TableName: COUPONS_TABLE,
                Item: discountPayload,
              });
              console.log('=====discountCMD====', discountCMD);
              await this.docClient.createItem(discountCMD);
            } catch (error) {
              posLogger.error(
                'cron',
                'fetchShopifyCoupons Discount Code error :>> ',
                error,
              );
              await new Promise((resolve) => setTimeout(resolve, 90000));
              // Retry the same discount code
              discountCodes.unshift(discount); // Re-insert to retry
              continue;
            }
          }
          // Discount Codes
        } catch (error) {
          posLogger.error(
            'cron',
            'fetchShopifyCoupons Price Rules error :>> ',
            error,
          );
          // Wait for  90 seconds before retrying
          await new Promise((resolve) => setTimeout(resolve, 90000));
          // Retry the same price rule
          i--;
        }
      }

      posLogger.info('cron', 'fetchShopifyCoupons', `Finished`);

      const globalConfigurationsService = new UpdateGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      await globalConfigurationsService.updateGlobalConfiguration(
        'LAST_SYNC_PROMOTIONAL_COUPONS',
        {
          key: 'LAST_SYNC_PRODUCTS',
          value: moment().toISOString(),
        },
      );
    } catch (error) {
      posLogger.error('cron', 'fetchShopifyCoupons error :>> ', error);
    }
  }

  // @Cron(CronExpression.EVERY_10_SECONDS)
  // async fetchShopifyProducts(cursor?) {
  //   console.log('Task executed every 10 seconds');
  //   const variables = cursor ? { cursor } : {};
  //   try {
  //     const response = await fetch(`${SHOPIFY_ADMIN_BASE_URL}/graphql.json`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //         'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
  //       },
  //       body: JSON.stringify({
  //         query: listProductQuery(80),
  //         variables,
  //       }),
  //     });
  //     const data = await response.json();
  //     const entites = data.data.products.edges.map((edge) => edge.node);
  //     const hasNextPage = data.data.products.pageInfo.hasNextPage;
  //     if (hasNextPage) {
  //       const cursor =
  //         data.data.products.edges.length > 0
  //           ? data.data.products.edges[data.data.products.edges.length - 1]
  //               .cursor
  //           : null;
  //       const nextEntities = await this.fetchShopifyProducts(cursor);
  //       entites.push(...nextEntities);
  //     }
  //     console.log('entites :>> ', JSON.stringify(entites));
  //     return entites;
  //   } catch (error) {
  //     console.error('Error fetching products:', error);
  //   }
  //   // Make a GraphQL request to Shopify
  //   // fetch(`${SHOPIFY_ADMIN_BASE_URL}/graphql.json`, {
  //   //   method: 'POST',
  //   //   headers: {
  //   //     'Content-Type': 'application/json',
  //   //     'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
  //   //   },
  //   //   body: JSON.stringify({
  //   //     query: listProductQuery(80),
  //   //   }),
  //   // })
  //   //   .then((response) => response.json())
  //   //   .then((data) => {
  //   //     // Handle the GraphQL response here
  //   //     console.log(JSON.stringify(data, null, 2));
  //   //   })
  //   //   .catch((error) => console.error('Error fetching products:', error));
  // }

  @Cron(CronExpression.EVERY_DAY_AT_10PM, {
    name: 'cronMarkCampaignCouponsStatus',
  })
  async markExistingCampaignCouponsStatus() {
    posLogger.info('cron', 'markExistingCampaignCouponsStatus :>> ', 'Started');
    try {
      const STORE_COUPON_TABLE =
        await this.configParameters.getCampaignCouponTableName();

      const scanDBHandler = new ScanDB(this.docClient);
      const campaignCoupons = await scanDBHandler.scanTable(STORE_COUPON_TABLE);

      posLogger.info(
        'cron',
        'markExistingCampaignCouponsStatus campaignCoupons :>> ',
        campaignCoupons.length,
      );

      for (const coupon of campaignCoupons) {
        const { code, startsAt, endsAt, status, isActive } = coupon;
        const updatedStatus = getPriceRuleStatus(startsAt, endsAt, isActive);

        if (updatedStatus !== status) {
          const command = new UpdateCommand({
            TableName: STORE_COUPON_TABLE,
            Key: {
              code,
            },
            UpdateExpression: 'SET #status = :status, #updatedAt = :updatedAt',
            ExpressionAttributeNames: {
              '#status': 'status',
              '#updatedAt': 'updatedAt',
            },
            ExpressionAttributeValues: {
              ':status': updatedStatus,
              ':updatedAt': moment().toISOString(),
            },
          });

          await this.docClient.updateItem(command);
        }
      }
      posLogger.info(
        'cron',
        'markExistingCampaignCouponsStatus :>> ',
        'Finished',
      );
    } catch (error) {
      posLogger.error(
        'cron',
        'markExistingCampaignCouponsStatus error :>> ',
        error,
      );
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_8PM, {
    name: 'cronFetchShopifyCollections',
  })
  async fetchShopifyCollections() {
    posLogger.info('cron', 'cronFetchShopifyCollectionsGQL', 'started');

    try {
      const [SHOPIFY_ADMIN_BASE_URL, SHOPIFY_ACCESS_TOKEN, COLLECTION_TABLE] =
        await Promise.all([
          this.shopifyClient.getShopifyAdminBaseUrl(),
          this.shopifyClient.getShopifyAccessToken(),
          this.configParameters.getCollectionTableName(),
        ]);

      const date = getDate(1);
      const formattedDate = new Date(date).toISOString();

      const fetchCollectionsQuery = async (
        collectionType: 'custom' | 'smart',
      ) => {
        const query = `
          query {
            collections(first: 250, query: "updated_at:>=${formattedDate} AND collection_type:${collectionType}") {
              edges {
                node {
                  id
                  title
                  handle
                  descriptionHtml
                  updatedAt
                  sortOrder
                  templateSuffix
                  resourcePublicationsV2(first: 1) {
                    nodes {
                      publishDate
                    }
                  }
                  ruleSet {
                    appliedDisjunctively
                    rules {
                      column
                      relation
                      condition
                    }
                  }
                }
              }
            }
          }
        `;

        const response = await fetch(`${SHOPIFY_ADMIN_BASE_URL}/graphql.json`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          },
          body: JSON.stringify({ query }),
        });

        const result = await response.json();
        console.log('result', result, '🚨🚨');

        return result.data.collections.edges.map((edge: any) => edge.node);
      };

      const [customCollections, smartCollections] = await Promise.all([
        fetchCollectionsQuery('custom'),
        fetchCollectionsQuery('smart'),
      ]);

      posLogger.info(
        'cron',
        'fetchShopifyCollectionsGQL',
        `Fetched ${customCollections.length}, ${smartCollections.length} collections from Shopify`,
      );

      const allCollections = [...customCollections, ...smartCollections];

      for (const collection of allCollections) {
        const collectionId = collection.id;

        try {
          const productsQuery = `
            query {
              collection(id: "${collectionId}") {
                products(first: 250) {
                  edges {
                    node {
                      id
                      status
                    }
                  }
                }
              }
            }
          `;

          const productResponse = await fetch(
            `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
              },
              body: JSON.stringify({ query: productsQuery }),
            },
          );

          const productData = await productResponse.json();
          const products = productData.data.collection.products.edges.map(
            (edge: any) => edge.node,
          );

          const activeProductIds = products
            .filter((product: any) => product.status === 'ACTIVE')
            .map((product: any) => product.id.split('/').pop());

          const isSmart = !!collection.ruleSet;
          const rules =
            isSmart && collection.ruleSet.rules ? collection.ruleSet.rules : [];
          const disjunctive = isSmart
            ? collection.ruleSet.appliedDisjunctively
            : false;

          const datasourcePayload = {
            id: collectionId.split('/').pop(),
            admin_graphql_api_id: collectionId,
            title: collection.title,
            handle: collection.handle,
            body_html: collection.descriptionHtml || '',
            createdAt: collection.updatedAt, // fallback as createdAt not available
            updated_at: collection.updatedAt,
            updatedAt: moment().toISOString(),
            published_at:
              collection.resourcePublicationsV2?.nodes?.[0]?.publishDate ||
              null,
            template_suffix: collection.templateSuffix,
            sort_order: collection.sortOrder,
            rules,
            disjunctive,
            productIds: activeProductIds,
          };

          const datasourceCMD = new PutCommand({
            TableName: COLLECTION_TABLE,
            Item: datasourcePayload,
          });
          console.log('datasourcePayload🚨🚨🚨', datasourcePayload);

          await this.docClient.createItem(datasourceCMD);
        } catch (error) {
          console.log('collection error :>> ', error);
        }
        const globalConfigurationsService = new UpdateGlobalConfiguration(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );

        await globalConfigurationsService.updateGlobalConfiguration(
          'LAST_SYNC_COLLECTIONS',
          {
            key: 'LAST_SYNC_COLLECTIONS',
            value: moment().toISOString(),
          },
        );
      }
    } catch (error) {
      console.log('fetchShopifyCollections error :>> ', error);
    }

    posLogger.info('cron', 'fetchShopifyCollectionsGQL', `Finished`);
  }

  // @Cron(CronExpression.EVERY_DAY_AT_8PM)
  async fetchSheetInteriorArchitectures() {
    try {
      posLogger.info('cron', 'fetchSheetInteriorArchitectures :>> ', 'Started');
      const data = await fetchDataFromGoogleSheet(
        '1r9zL_qSNnWiHKD1jRKEahWKEWpdso0ifwGh30LNY7Qk',
        'ConnectOnboarded',
        'AIzaSyDeFSOTDCsgAjdXMKvzEnCTqZbp4MMbTt0',
      );

      if (!data || !data.length) {
        return;
      }

      const INTERIOR_ARCHITECTURE_TABLE =
        await this.configParameters.getInteriorArchitectureTableName();

      for (const d of data) {
        const { unique: id, ...item }: any = d;

        if (!id) continue;

        const putCommand = new PutCommand({
          TableName: INTERIOR_ARCHITECTURE_TABLE,
          Item: {
            id,
            ...item,
            createdAt: moment().toISOString(),
          },
        });
        await this.docClient.createItem(putCommand);
      }

      posLogger.info(
        'cron',
        'fetchSheetInteriorArchitectures :>> ',
        'Finished',
      );

      const globalConfigurationsService = new UpdateGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      await globalConfigurationsService.updateGlobalConfiguration(
        'LAST_SYNC_INTERIOR_ARCHITECTURES',
        {
          key: 'LAST_SYNC_PRODUCTS',
          value: moment().toISOString(),
        },
      );
    } catch (error) {
      posLogger.error(
        'cron',
        'fetchSheetInteriorArchitectures error :>> ',
        error,
      );
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
    name: 'updateDailyUsageCountCampaignCoupons',
    timeZone: 'Asia/Kolkata',
  })
  async updateDailyUsageCountCampaignCoupons() {
    posLogger.info(
      'cron',
      'updateDailyUsageCountCampaignCoupons :>> ',
      'Started',
    );
    try {
      const STORE_COUPON_TABLE =
        await this.configParameters.getCampaignCouponTableName();

      const campaignCoupons = await fetchAllResetCoupons(
        STORE_COUPON_TABLE,
        'DAILY',
        this.configService,
        this.ssmClient,
      );

      posLogger.info(
        'cron',
        'updateDailyUsageCountCampaignCoupons campaignCoupons :>> ',
        campaignCoupons.length,
      );

      for (const coupon of campaignCoupons) {
        const { code, storeWiseUsageCount } = coupon;

        const updatedUsageCount = storeWiseUsageCount?.map((coupon) => {
          return { storeId: coupon?.storeId, usageCount: 0 };
        });
        if (updatedUsageCount) {
          const command = new UpdateCommand({
            TableName: STORE_COUPON_TABLE,
            Key: {
              code,
            },
            UpdateExpression:
              'SET #storeWiseUsageCount = :storeWiseUsageCount, #updatedAt = :updatedAt',
            ExpressionAttributeNames: {
              '#storeWiseUsageCount': 'storeWiseUsageCount',
              '#updatedAt': 'updatedAt',
            },
            ExpressionAttributeValues: {
              ':storeWiseUsageCount': updatedUsageCount,
              ':updatedAt': moment().toISOString(),
            },
          });
          await this.docClient.updateItem(command);
        }
      }
      posLogger.info(
        'cron',
        'updateDailyUsageCountCampaignCoupons :>> ',
        'Finished',
      );
    } catch (error) {
      posLogger.error(
        'cron',
        'updateDailyUsageCountCampaignCoupons error :>> ',
        error,
      );
    }
  }
  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT, {
    name: 'updateMonthlyUsageCountCampaignCoupons',
    timeZone: 'Asia/Kolkata',
  })
  async updateMonthlyUsageCountCampaignCoupons() {
    posLogger.info(
      'cron',
      'updateMonthlyUsageCountCampaignCoupons :>> ',
      'Started',
    );
    try {
      const STORE_COUPON_TABLE =
        await this.configParameters.getCampaignCouponTableName();

      const campaignCoupons = await fetchAllResetCoupons(
        STORE_COUPON_TABLE,
        'MONTHLY',
        this.configService,
        this.ssmClient,
      );

      posLogger.info(
        'cron',
        'updateMonthlyUsageCountCampaignCoupons campaignCoupons :>> ',
        campaignCoupons.length,
      );

      for (const coupon of campaignCoupons) {
        const { code, storeWiseUsageCount } = coupon;

        const updatedUsageCount = storeWiseUsageCount?.map((coupon) => {
          return { storeId: coupon?.storeId, usageCount: 0 };
        });

        const command = new UpdateCommand({
          TableName: STORE_COUPON_TABLE,
          Key: {
            code,
          },
          UpdateExpression:
            'SET #storeWiseUsageCount = :storeWiseUsageCount, #updatedAt = :updatedAt',
          ExpressionAttributeNames: {
            '#storeWiseUsageCount': 'storeWiseUsageCount',
            '#updatedAt': 'updatedAt',
          },
          ExpressionAttributeValues: {
            ':storeWiseUsageCount': updatedUsageCount,
            ':updatedAt': moment().toISOString(),
          },
        });

        await this.docClient.updateItem(command);
      }
      posLogger.info(
        'cron',
        'updateMonthlyUsageCountCampaignCoupons :>> ',
        'Finished',
      );
    } catch (error) {
      posLogger.error(
        'cron',
        'updateMonthlyUsageCountCampaignCoupons error :>> ',
        error,
      );
    }
  }
  @Cron(CronExpression.EVERY_WEEK, {
    name: 'updateWeeklyUsageCountCampaignCoupons',
    timeZone: 'Asia/Kolkata',
  })
  async updateWeeklyUsageCountCampaignCoupons() {
    posLogger.info(
      'cron',
      'updateWeeklyUsageCountCampaignCoupons :>> ',
      'Started',
    );
    try {
      const STORE_COUPON_TABLE =
        await this.configParameters.getCampaignCouponTableName();

      const campaignCoupons = await fetchAllResetCoupons(
        STORE_COUPON_TABLE,
        'WEEKLY',
        this.configService,
        this.ssmClient,
      );

      posLogger.info(
        'cron',
        'updateWeeklyUsageCountCampaignCoupons campaignCoupons :>> ',
        campaignCoupons.length,
      );

      for (const coupon of campaignCoupons) {
        const { code, storeWiseUsageCount } = coupon;

        const updatedUsageCount = storeWiseUsageCount?.map((coupon) => {
          return { storeId: coupon?.storeId, usageCount: 0 };
        });

        const command = new UpdateCommand({
          TableName: STORE_COUPON_TABLE,
          Key: {
            code,
          },
          UpdateExpression:
            'SET #storeWiseUsageCount = :storeWiseUsageCount, #updatedAt = :updatedAt',
          ExpressionAttributeNames: {
            '#storeWiseUsageCount': 'storeWiseUsageCount',
            '#updatedAt': 'updatedAt',
          },
          ExpressionAttributeValues: {
            ':storeWiseUsageCount': updatedUsageCount,
            ':updatedAt': moment().toISOString(),
          },
        });

        await this.docClient.updateItem(command);
      }
      posLogger.info(
        'cron',
        'updateWeeklyUsageCountCampaignCoupons :>> ',
        'Finished',
      );
    } catch (error) {
      posLogger.error(
        'cron',
        'updateWeeklyUsageCountCampaignCoupons error :>> ',
        error,
      );
    }
  }

  private async _dumpData({
    label,
    filePrefix,
    getTableName,
    dateFields,
    sortBy,
  }: {
    label: string;
    filePrefix: string;
    getTableName: () => Promise<string>;
    dateFields: ('createdAt' | 'updatedAt')[];
    sortBy: string;
  }) {
    posLogger.info('cron', `${label} dump started`, '');

    try {
      const tableName = await getTableName();

      let records = await fetchPreviousDayRecords(
        tableName,
        dateFields,
        sortBy,
        this.configService,
        this.ssmClient,
      );

      const dateStr = moment().format('YYYY-MM-DD');
      const fileName = `${filePrefix}-${dateStr}.csv`;
      fs.mkdirSync(path.join(__dirname, '../../temp'), { recursive: true });
      const filePath = path.join(__dirname, '../../temp', fileName);

      const isEmpty = records.length === 0;

      if (isEmpty) {
        fs.writeFileSync(filePath, ''); // Write an empty CSV file
        posLogger.info(
          'cron',
          `No ${label.toLowerCase()} records found — writing empty master.csv`,
          '',
        );
      } else {
        let csv: any;
        if (label === 'STN') {
          const handler = new ExportSTNs(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.s3Client,
            this.configParameters,
          );
          csv = await handler.exportSTNCSV(records);
        } else if (label === 'Order') {
          records = await filteredOrders(records);
          csv = await json2csv(records, {
            delimiter: { field: ',' },
          });
        } else {
          csv = await json2csv(records, {
            delimiter: { field: ',' },
          });
        }
        fs.writeFileSync(filePath, csv);
        posLogger.info('cron', `${label} CSV created at:`, filePath);
      }

      const handler = new S3Interactions(
        this.configService,
        this.docClient,
        this.s3Client,
      );

      const stack = this.configService.get('STACK_NAME').split('-')[1];
      const s3Path = `${stack}/${filePrefix}/${fileName}`;
      const masterFilePath = `${stack}/${filePrefix}/master.csv`;

      // Upload versioned CSV only if there's data
      if (!isEmpty) {
        const awsRes = await handler.uploadToS3(
          fs.createReadStream(filePath),
          s3Path,
          'text/csv',
          'big-data-analysis-datastore',
        );
        posLogger.info('cron', `${label} uploaded to S3`, awsRes);
      } else {
        posLogger.info('cron', `${label} no records to upload`, '');
      }

      // Always upload to master.csv (even if empty)
      const awsRes = await handler.uploadToS3(
        fs.createReadStream(filePath),
        masterFilePath,
        'text/csv',
        'big-data-analysis-datastore',
      );

      posLogger.info('cron', `${label} uploaded to S3`, awsRes);

      fs.unlinkSync(filePath);
      posLogger.info('cron', `${label} dump completed`, '');
    } catch (error) {
      console.log(error, `\n\n\n${label}`, 'Dump failed');
      posLogger.error('cron', `${label} dump error :>>`, error);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
    // @Cron(CronExpression.EVERY_10_SECONDS, {
    name: 'dumpDailyData',
    timeZone: 'Asia/Kolkata',
  })
  async dumpDailyData() {
    const tasks = [];

    tasks.push(
      this._dumpData({
        label: 'Order',
        filePrefix: 'orders',
        getTableName: () => this.configParameters.getOrdersLogTableName(),
        dateFields: ['createdAt', 'updatedAt'],
        sortBy: 'loggedAt',
      }),
    );

    tasks.push(
      this._dumpData({
        label: 'STN',
        filePrefix: 'stn',
        getTableName: () => this.configParameters.getSTNTableName(),
        dateFields: [],
        sortBy: 'createdAt',
      }),
    );

    await Promise.all(tasks);
  }
}

async function filteredOrders(records: any[]) {
  if (!Array.isArray(records)) return [];

  // Helper function to format dates consistently
  const formatDate = (date: string | Date): string => {
    if (!date) return '';
    return moment(date).utcOffset('+05:30').format('DD/MM/yyyy HH:mm');
  };

  // Helper function to join address values
  const formatAddress = (
    address: Record<string, any> | null | undefined,
  ): string => {
    if (!address) return '';
    return Object.values(address)
      .map((val) =>
        String(val)
          .replace(/[\n\r]+/g, ' ') // replace newlines with space
          .replace(/"/g, '') // remove double quotes
          .trim(),
      )
      .join(', ');
  };

  // Helper function to get customer full name
  const getCustomerName = (customer: any): string => {
    if (!customer) return '';
    const { firstName = '', lastName = '' } = customer;
    return `${firstName} ${lastName}`.trim();
  };

  // Helper function to find promotional discount
  const getDIYInstallDiscount = (coupons: any[]): string | number => {
    if (!Array.isArray(coupons)) return '';

    const diyDiscount = coupons.find(
      (item) => item?.promotionalCode === 'DIYINSTALL',
    );
    const amount = diyDiscount?.promotionalDiscountAmount || 0;
    return amount > 0 ? amount : '';
  };

  // Helper function to format custom code
  const formatCustomCode = (customCode: any): string => {
    if (!customCode) return '';
    const { value_type = '', value = '' } = customCode;
    return `${value_type} ${value}`.trim();
  };

  return records.map((record) => {
    // Destructure commonly used nested objects for better performance
    const {
      customer = {},
      shippingAddress = {},
      billingAddress = {},
      gstDetails = {},
      customCode,
      additionalPromotionalCoupons,
    } = record;

    // Calculate derived values once
    const totalAmount = record.totalAmount || 0;
    const finalAmount = record.finalDiscountedAmount || 0;
    const deliveryCharge = record.deliveryCharge || 0;

    return {
      loggedAt: record.loggedAt,
      'Order ID': record.shopifyOrderName || '',
      orderNo: record.shopifyOrderId || '',
      'Order Created by': record.loggedusername || '',
      'Order date on tool': formatDate(record.createdAt),
      'Invoice date': formatDate(record.createdAt),
      'Order date on shopify': formatDate(record.shopifyCreatedAt),
      customerPhone: customer.phone || '',
      'customer name': getCustomerName(customer),
      customerEmail: customer.email || '',
      'Employee Id': record.employeeId || '',
      'Shipping address': formatAddress(shippingAddress),
      'Shipping Pincode': shippingAddress.pinCode || '',
      'Billing address': formatAddress(billingAddress),
      'Order status': record.status || '',
      'Hold Order': record.holdOrder || false,
      'Hold Order Reason': record.holdOrderReason || '',
      unHoldFutureDispatchDate: record.unHoldFutureDispatchDate || '',
      customCode: formatCustomCode(customCode),
      'Discount Approved By': customCode?.approver || '',
      customDiscountAmount: record.customDiscountAmount || 0,
      promotionalCode: record.promotionalCode || '',
      promotionalDiscountAmount: record.promotionalDiscountAmount || 0,
      campaignCode: record.campaignCode || '',
      campaignDiscountAmount: record.campaignDiscountAmount || 0,
      DIYINSTALL: getDIYInstallDiscount(additionalPromotionalCoupons),
      'Delivery Charge applied': deliveryCharge > 0 ? 'Yes' : 'No',
      'Delivery Charge': deliveryCharge > 0 ? deliveryCharge : '',
      totalAmount,
      'order value': finalAmount,
      'discount amount': totalAmount - finalAmount,
      'Shopify Customer ID': record.customerId,
      'company name': gstDetails.companyName || '',
      gstnumber: gstDetails.gstNumber || '',
      'POS Order id': record.id,
      'invoice no': record.id,
      storeId: record.storeId,
      notes: record.notes,
      Source: record.source,
      'Interior Architecture': JSON.stringify(record.interiorArchitecture),
      'Customer Attachment': record.customerIdentityProof,
      status: record.status,
      quotationId: record.quotationId,
      'transaction details': JSON.stringify(record.transactions),
      shopifyOrderStatus: record.shopifyOrderStatus,
      orderProducts: record.orderProducts,
    };
  });
}
