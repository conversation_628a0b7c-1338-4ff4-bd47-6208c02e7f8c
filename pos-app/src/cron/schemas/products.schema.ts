// export const listProductQuery = (first: number) => `
//   {
//     products(first: ${first}, query: "status:active AND published_status:published") {
//       availablePublicationCount
//       createdAt
//       defaultCursor
//       description
//       descriptionHtml
//       giftCardTemplateSuffix
//       handle
//       hasOnlyDefaultVariant
//       hasOutOfStockVariants
//       id
//       isGiftCard
//       legacyResourceId
//       onlineStorePreviewUrl
//       onlineStoreUrl
//       productType
//       publicationCount
//       publishedAt
//       storefrontId
//       tags
//       templateSuffix
//       title
//       totalInventory
//       totalVariants
//       vendor
//       tracksInventory
//       publishedScope
//       updatedAt
//       resourcePublicationOnCurrentPublication {
//         publication {
//           name
//           id
//         }
//         publishDate
//         isPublished
//       }
//       variants(first: 250) {
//         edges {
//           node {
//             id
//             title
//             price
//             sku
//             position
//             inventoryPolicy
//             compareAtPrice
//             fulfillmentService
//             inventoryManagement
//             option1
//             option2
//             option3
//             createdAt
//             updatedAt
//             taxable
//             barcode
//             grams
//             imageId
//             weight
//             weightUnit
//             inventoryItemId
//             inventoryQuantity
//             oldInventoryQuantity
//             requiresShipping
//             metafields(first: 250) {
//               edges {
//                 node {
//                   id
//                   namespace
//                   key
//                   value
//                   created_at
//                   updated_at
//                   owner_id
//                   owner_resource
//                   type
//                   description
//                 }
//               }
//             }
//           }
//         }
//       }
//       collections(first: 250) {
//         edges {
//           node {
//             handle
//           }
//         }
//       }
//       featuredImage {
//         id
//         alt
//         position
//         src
//         variantIds
//       }
//       images(first: 250) {
//         edges {
//           node {
//             id
//             alt
//             position
//             src
//             variantIds
//           }
//         }
//       }
//       metafields(first: 250) {
//         edges {
//           node {
//             id
//             namespace
//             key
//             value
//             created_at
//             updated_at
//             owner_id
//             owner_resource
//             type
//             description
//           }
//         }
//       }
//       options {
//         id
//         name
//         position
//         values
//       }
//       priceRange {
//         maxVariantPrice {
//           amount
//         }
//         minVariantPrice {
//           amount
//         }
//       }
//     }
//   }`;

export const listProductQuery = (first: number) => `
  {
    products(first: ${first}, query: "status:active AND published_status:published") {
      edges {
        cursor
        node {
          id
          title
          bodyHtml
          vendor
          productType
          createdAt
          handle
          updatedAt
          publishedAt
          templateSuffix
          tags
          status
          variants(first: 200) {
            edges {
              node {
                id
                title
                price
                sku
                position
                inventoryPolicy
                compareAtPrice
                inventoryManagement
                createdAt
                updatedAt
                taxable
                barcode
                weight
                weightUnit
                inventoryQuantity
                requiresShipping
                metafields(first: 50) {
                  edges {
                    node {
                      id
                      namespace
                      key
                      value
                      type
                      description
                    }
                  }
                }
              }
            }
          }
          metafields(first: 50) {
            edges {
              node {
                id
                namespace
                key
                value
                type
                description
              }
            }
          }
          featuredImage {
            id
            src
          }
        }
      }
      pageInfo {
        hasNextPage
        hasPreviousPage
      }
    }
  }`;
