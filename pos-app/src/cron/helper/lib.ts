import appendQuery from 'append-query';

import moment from 'moment';
// import dynamoDBClient from 'src/common/aws-sdk/doc-client';
import { posLogger } from 'src/common/logger';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { GraphQLClient } from 'graphql-request';
// import config from 'src/config/config';

// const appConfig = config[process.env.NODE_ENV];
// const { COUPONS_TABLE } = appConfig;

export const getDate = (difference?: number, returnType?: string) => {
  const today = new Date();
  today.setDate(today.getDate() - (difference ? difference : 0));

  let dd: number | string = today.getDate();
  let mm: number | string = today.getMonth() + 1; // getMonth() is zero-based
  const yyyy = today.getFullYear();

  // Pad the day and month with leading zeros if they are less than  10
  if (dd < 10) {
    dd = '0' + dd;
  }
  if (mm < 10) {
    mm = '0' + mm;
  }

  if (returnType && returnType === 'YEAR') return `${yyyy}`;
  if (returnType && returnType === 'MONTH') return `${yyyy}-${mm}`;
  return `${yyyy}-${mm}-${dd}`;
};

export const LeadSquareApi = async (
  newItem: any,
  leadDay: string,
  Configurl: string,
) => {
  try {
    const payload = { newItem, leadDay };
    const response = await fetch(`${Configurl}/leadsquare/create-lead`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
    console.log('Response status:', response.status);
  } catch (error) {
    throw error;
  }
};

export const getShopifyCollectionProducts = async (
  linkUrl: string,
  shopifyToken: string,
) => {
  try {
    const url = appendQuery(linkUrl, `limit=250`);

    const response = await fetch(url, {
      headers: {
        'X-Shopify-Access-Token': shopifyToken,
      },
    });

    const { products } = await response.json();

    const entities = [...(products || [])];
    const nextLink = response.headers.get('link');

    if (nextLink) {
      const nextLinkUrl = nextLink.match(/<([^>]+)>; rel="next"/)?.[1] || '';

      if (nextLinkUrl) {
        const nextEntities = await listShopifyProducts(
          nextLinkUrl,
          shopifyToken,
        );

        posLogger.info(
          'cron',
          'getShopifyCollectionProducts nextEntities',
          nextEntities.length,
        );
        entities.push(...nextEntities);
      }
    }

    return entities;
  } catch (error) {
    throw error;
  }
};

export const listShopifyCollections = async (
  linkUrl: string,
  shopifyToken: string,
  queryKey: string,
) => {
  try {
    const url = appendQuery(linkUrl, `limit=250`);

    const response = await fetch(url, {
      headers: {
        'X-Shopify-Access-Token': shopifyToken,
      },
    });

    const { [queryKey]: collections } = await response.json();

    const entities = [...(collections || [])];
    const nextLink = response.headers.get('link');

    if (nextLink) {
      const nextLinkUrl = nextLink.match(/<([^>]+)>; rel="next"/)?.[1] || '';

      if (nextLinkUrl) {
        const nextEntities = await listShopifyProducts(
          nextLinkUrl,
          shopifyToken,
        );

        posLogger.info(
          'cron',
          'listShopifyProducts nextEntities',
          nextEntities.length,
        );
        entities.push(...nextEntities);
      }
    }

    return entities;
  } catch (error) {
    throw error;
  }
};

export const listShopifyProducts = async (
  linkUrl: string,
  shopifyToken: string,
) => {
  try {
    const url = appendQuery(linkUrl, `limit=250`);

    const response = await fetch(url, {
      headers: {
        'X-Shopify-Access-Token': shopifyToken,
      },
    });

    const { products } = await response.json();

    const entities = [...(products || [])];
    const nextLink = response.headers.get('link');

    if (nextLink) {
      const nextLinkUrl = nextLink.match(/<([^>]+)>; rel="next"/)?.[1] || '';

      if (nextLinkUrl) {
        const nextEntities = await listShopifyProducts(
          nextLinkUrl,
          shopifyToken,
        );

        posLogger.info(
          'cron',
          'listShopifyProducts nextEntities',
          nextEntities.length,
        );
        entities.push(...nextEntities);
      }
    }

    return entities;
  } catch (error) {
    throw error;
  }
};

export const listShopifyPriceRules = async (
  linkUrl: string,
  shopifyToken: string,
) => {
  let entities = [];
  try {
    posLogger.info('cron', 'listShopifyPriceRules', `Started ${linkUrl}`);
    const url = appendQuery(linkUrl, `limit=250`);

    const response = await fetch(url, {
      headers: {
        'X-Shopify-Access-Token': shopifyToken,
      },
    });

    const { price_rules } = await response.json();

    entities = [...(price_rules || [])];
    const nextLink = response.headers.get('link');

    if (nextLink) {
      const nextLinkUrl = nextLink.match(/<([^>]+)>; rel="next"/)?.[1] || '';

      posLogger.info('cron', 'listShopifyPriceRules nextLinkUrl', nextLinkUrl);

      if (nextLinkUrl) {
        const nextEntities = await listShopifyPriceRules(
          nextLinkUrl,
          shopifyToken,
        );

        posLogger.info(
          'cron',
          'listShopifyPriceRules nextEntities',
          nextEntities.length,
        );
        entities.push(...nextEntities);
      }
    }

    posLogger.info('cron', 'listShopifyPriceRules entities', entities.length);
    return entities;
  } catch (error) {
    posLogger.error('Error listShopifyPriceRules:', linkUrl, error);

    await new Promise((resolve) => setTimeout(resolve, 90000));
    const nextEntities = await listShopifyPriceRules(linkUrl, shopifyToken);
    entities.push(...nextEntities);
  }
};

export const listShopifyDiscountCodes = async (
  linkUrl: string,
  shopifyToken: string,
) => {
  try {
    const url = appendQuery(linkUrl, `limit=250`);

    const response = await fetch(url, {
      headers: {
        'X-Shopify-Access-Token': shopifyToken,
      },
    });

    const { discount_codes = [] } = await response.json();

    const nextLink = response.headers.get('link');
    let nextLinkUrl = '';

    if (nextLink) {
      nextLinkUrl = nextLink.match(/<([^>]+)>; rel="next"/)?.[1] || '';
      posLogger.info(
        'cron',
        'listShopifyDiscountCodes nextLinkUrl',
        nextLinkUrl,
      );
    }

    posLogger.info(
      'cron',
      'listShopifyDiscountCodes entities',
      discount_codes.length,
    );

    return { entities: discount_codes, nextLinkUrl };
  } catch (error) {
    console.error('Error listShopifyDiscountCodes:', error);
    await new Promise((resolve) => setTimeout(resolve, 90000));
    return await listShopifyDiscountCodes(linkUrl, shopifyToken);
  }
};

export const listMetafields = async (linkUrl: string, shopifyToken: string) => {
  const url = appendQuery(linkUrl, `limit=250`);

  const response = await fetch(url, {
    headers: {
      'X-Shopify-Access-Token': shopifyToken,
    },
  });

  const { metafields } = await response.json();

  const data = [...(metafields || [])];
  const nextLink = response.headers.get('link');

  if (nextLink) {
    const nextLinkUrl = nextLink.match(/<([^>]+)>; rel="next"/)?.[1] || '';
    const nextMetafields = await listMetafields(nextLinkUrl, shopifyToken);
    data.push(...nextMetafields);
  }

  return data;
};

// export const fetchPriceRules = async (nextToken?: Record<string, any>) => {
//   let Items = [];
//   try {
//     const command = new QueryCommand({
//       TableName: COUPONS_TABLE,
//       KeyConditionExpression: 'pk = :pk',
//       ExpressionAttributeValues: {
//         ':pk': 'PRICE_RULE',
//       },
//       ExclusiveStartKey: nextToken,
//     });

//     const response = await dynamoDBClient.send(command);
//     const { LastEvaluatedKey } = response;
//     Items = response.Items;

//     if (LastEvaluatedKey) {
//       const entities = await fetchPriceRules(LastEvaluatedKey);
//       return [...Items, ...entities];
//     }

//     return Items;
//   } catch (error) {
//     console.error('Error fetching price rules:', error);

//     await new Promise((resolve) => setTimeout(resolve, 90000));
//     const entities = await fetchPriceRules(nextToken);
//     return [...Items, ...entities];
//   }
// };

export const createPriceRulePayload = (priceRule) => {
  const datasourcePayload = {
    ...priceRule,
    pk: 'PRICE_RULE',
    sk: priceRule.id.toString(),
    type: 'PRICE_RULE',
    starts_at: moment(priceRule.starts_at).toISOString(),
    ends_at: priceRule.ends_at ? moment(priceRule.ends_at).toISOString() : null,
    createdAt: moment().toISOString(),
    updatedAt: moment().toISOString(),
  };

  return datasourcePayload;
};

export const createDiscountCodePayload = (discount) => {
  const discountPayload = {
    ...discount,
    pk: 'COUPON',
    sk: discount.code,
    createdAt: moment().toISOString(),
    updatedAt: moment().toISOString(),
  };

  return discountPayload;
};

export const listShopifyPriceRulesGraphQL = async (
  shopifyGraphQLEndpoint: string,
  shopifyToken: string,
  updatedAtMin: string,
) => {
  let allPriceRules: any[] = [];
  let afterCursor: string | null = null;

  const query = `
    query PriceRules($first: Int!, $after: String, $query: String) {
      priceRules(first: $first, after: $after, query: $query) {
        edges {
          node {
            id
            adminGraphqlApiId: id
            title
            valueV2 {
              ... on MoneyV2 {
                amount
                currencyCode
              }
              ... on PricingPercentageValue {
                percentage
              }
            }
            allocationMethod
            allocationLimit
            oncePerCustomer
            usageLimit
            startsAt
            endsAt
            createdAt
            customerSelection {
              customers(first: 10) {
                edges {
                  node {
                    id
                  }
                }
              }
              segments {
                id
              }
            }
            itemEntitlements {
              collections(first: 50) {
                edges {
                  node {
                    id
                  }
                }
              }
              products(first: 50) {
                edges {
                  node {
                    id
                  }
                }
              }
              productVariants(first: 50) {
                edges {
                  node {
                    id
                  }
                }
              }
              targetAllLineItems
            }
            itemPrerequisites {
              collections(first: 50) {
                edges {
                  node {
                    id
                  }
                }
              }
              products(first: 50) {
                edges {
                  node {
                    id
                  }
                }
              }
              productVariants(first: 50) {
                edges {
                  node {
                    id
                  }
                }
              }
            }
            prerequisiteSubtotalRange {
              greaterThanOrEqualTo
            }
            prerequisiteQuantityRange {
              greaterThanOrEqualTo
            }
            prerequisiteShippingPriceRange {
              lessThanOrEqualTo
            }
            prerequisiteToEntitlementQuantityRatio {
              entitlementQuantity
              prerequisiteQuantity
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  `;

  do {
    try {
      const variables = {
        first: 250,
        after: afterCursor,
        query: `updated_at:>=${updatedAtMin}`,
      };

      const response = await fetch(shopifyGraphQLEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': shopifyToken,
        },
        body: JSON.stringify({ query, variables }),
      });
      const data = await response.json();
      posLogger.info(
        'cron',
        'listShopifyPriceRulesGraphQL',
        `Raw data: ${JSON.stringify(data)}`,
      );

      if (!data?.data?.priceRules)
        throw new Error('No price rules data returned');
      console.log(
        '=====node?.valueV2',
        data?.priceRules?.edges[0]?.node?.valueV2,
      );

      const priceRules = data?.data?.priceRules?.edges?.map((edge: any) => {
        const node = edge?.node;
        return {
          id: parseInt(node?.id?.split('/')?.pop()),
          admin_graphql_api_id: node?.adminGraphqlApiId,
          title: node?.title,
          value: node?.valueV2?.amount
            ? `${node?.valueV2?.amount}`
            : `${node?.valueV2?.percentage}`,
          value_type: node?.valueV2?.amount ? 'fixed_amount' : 'percentage',
          allocation_method: node?.allocationMethod.toLowerCase(),
          allocation_limit: node?.allocationLimit,
          once_per_customer: node?.oncePerCustomer,
          usage_limit: node?.usageLimit,
          starts_at: node?.startsAt,
          ends_at: node?.endsAt,
          created_at: node?.createdAt,
          createdAt: new Date(node?.createdAt)?.toISOString(),
          updated_at: node?.createdAt,
          updatedAt: new Date(node?.createdAt)?.toISOString(),
          customer_selection: node?.customerSelection?.customers?.edges?.length
            ? 'prerequisite'
            : 'all',
          target_type: 'line_item',
          target_selection: node?.itemEntitlements?.targetAllLineItems
            ? 'all'
            : 'entitled',
          entitled_collection_ids:
            node?.itemEntitlements?.collections?.edges?.map((e: any) =>
              parseInt(e?.node?.id?.split('/')?.pop()),
            ) ?? [],
          entitled_country_ids: [],
          entitled_product_ids:
            node?.itemEntitlements?.products?.edges?.map((e: any) =>
              parseInt(e?.node?.id?.split('/')?.pop()),
            ) ?? [],
          entitled_variant_ids:
            node?.itemEntitlements?.productVariants?.edges?.map((e: any) =>
              parseInt(e?.node?.id?.split('/')?.pop()),
            ) ?? [],
          prerequisite_collection_ids:
            node?.itemPrerequisites?.collections?.edges?.map((e: any) =>
              parseInt(e?.node?.id?.split('/')?.pop()),
            ) ?? [],
          prerequisite_customer_ids:
            node?.customerSelection?.customers?.edges?.map((e: any) =>
              parseInt(e?.node?.id?.split('/')?.pop()),
            ) ?? [],
          prerequisite_product_ids:
            node?.itemPrerequisites?.products?.edges?.map((e: any) =>
              parseInt(e?.node?.id?.split('/')?.pop()),
            ) ?? [],
          prerequisite_variant_ids:
            node?.itemPrerequisites?.productVariants?.edges?.map((e: any) =>
              parseInt(e?.node?.id?.split('/')?.pop()),
            ) ?? [],
          prerequisite_quantity_range: node?.prerequisiteQuantityRange
            ? {
                greater_than_or_equal_to:
                  node?.prerequisiteQuantityRange?.greaterThanOrEqualTo,
              }
            : null,
          prerequisite_shipping_price_range:
            node?.prerequisiteShippingPriceRange
              ? {
                  less_than_or_equal_to:
                    node?.prerequisiteShippingPriceRange?.lessThanOrEqualTo,
                }
              : null,
          prerequisite_subtotal_range: node?.prerequisiteSubtotalRange
            ? {
                greater_than_or_equal_to:
                  node?.prerequisiteSubtotalRange?.greaterThanOrEqualTo,
              }
            : null,
          prerequisite_to_entitlement_purchase: {
            prerequisite_amount: null,
          },
          prerequisite_to_entitlement_quantity_ratio:
            node?.prerequisiteToEntitlementQuantityRatio
              ? {
                  entitled_quantity:
                    node?.prerequisiteToEntitlementQuantityRatio
                      ?.entitlementQuantity,
                  prerequisite_quantity:
                    node?.prerequisiteToEntitlementQuantityRatio
                      ?.prerequisiteQuantity,
                }
              : {
                  entitled_quantity: null,
                  prerequisite_quantity: null,
                },
          customer_segment_prerequisite_ids:
            node?.customerSelection?.segments?.map((e: any) =>
              parseInt(e?.id?.split('/')?.pop()),
            ) ?? [],
        };
      });

      console.log(
        '=====listShopifyPriceRulesGraphQL priceRules=====',
        priceRules?.length,
      );

      allPriceRules.push(...(priceRules ?? []));

      const pageInfo = data?.data?.priceRules?.pageInfo;
      afterCursor = pageInfo?.hasNextPage ? pageInfo?.endCursor : null;

      posLogger.info(
        'cron',
        'listShopifyPriceRulesGraphQL',
        `Fetched ${priceRules?.length} price rules, hasNextPage: ${pageInfo?.hasNextPage}`,
      );
    } catch (error) {
      posLogger.error('cron', 'listShopifyPriceRulesGraphQL error', error);
      await new Promise((resolve) => setTimeout(resolve, 90000));
      continue;
    }
  } while (afterCursor);

  return allPriceRules;
};

export const listShopifyDiscountCodesGraphQL = async (
  shopifyGraphQLEndpoint: string,
  shopifyToken: string,
  priceRuleId: string,
) => {
  let allDiscountCodes: any[] = [];
  let afterCursor: string | null = null;

  const query = `
    query PriceRule($id: ID!, $first: Int!, $after: String) {
      priceRule(id: $id) {
        id
        discountCodes(first: $first, after: $after) {
          edges {
            node {
              id
              code
              usageCount
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    }
  `;

  do {
    try {
      const variables = {
        id: priceRuleId,
        first: 250,
        after: afterCursor,
      };

      const response = await fetch(shopifyGraphQLEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': shopifyToken,
        },
        body: JSON.stringify({ query, variables }),
      });

      const data = await response.json();
      posLogger.info(
        'cron',
        'listShopifyDiscountCodesGraphQL',
        `Raw data: ${JSON.stringify(data)}`,
      );

      if (!data?.data?.priceRule?.discountCodes) {
        throw new Error('No discount codes data returned');
      }

      const discountCodes =
        data?.data?.priceRule?.discountCodes?.edges?.map((edge: any) => {
          const node = edge?.node;
          return {
            id: parseInt(node?.id?.split('/')?.pop()),
            code: node?.code,
            usage_count: node?.usageCount,
            createdAt: moment()?.toISOString(),
            created_at: moment()?.toISOString(),
            updatedAt: moment()?.toISOString(),
            updated_at: moment()?.toISOString(),
            price_rule_id: parseInt(priceRuleId?.split('/')?.pop()),
          };
        }) ?? [];

      allDiscountCodes.push(...discountCodes);

      const pageInfo = data?.data?.priceRule?.discountCodes?.pageInfo;
      afterCursor = pageInfo?.hasNextPage ? pageInfo?.endCursor : null;

      posLogger.info(
        'cron',
        'listShopifyDiscountCodesGraphQL',
        `Fetched ${discountCodes.length} discount codes for priceRule ${priceRuleId}, hasNextPage: ${pageInfo?.hasNextPage}`,
      );
    } catch (error) {
      posLogger.error('cron', 'listShopifyDiscountCodesGraphQL error', error);
      await new Promise((resolve) => setTimeout(resolve, 90000));
      continue;
    }
  } while (afterCursor);

  return allDiscountCodes;
};

// export const fetchCouponsByStatus = async (
//   status: string,
//   nextToken?: Record<string, any>,
// ) => {
//   try {
//     const command = new QueryCommand({
//       TableName: COUPONS_TABLE,
//       IndexName: BY_COUPON_STATUS_INDEX,
//       KeyConditionExpression: 'sk = :sk AND #status = :status',
//       ExpressionAttributeValues: {
//         ':sk': 'COUPON',
//         ':status': status,
//       },
//       ExpressionAttributeNames: {
//         '#status': 'status',
//       },
//       ExclusiveStartKey: nextToken,
//       ProjectionExpression: 'pk, sk, starts_at, ends_at',
//     });

//     const { LastEvaluatedKey, Items = [] } = await dynamoDBClient.send(command);

//     if (LastEvaluatedKey) {
//       const entities = await fetchCouponsByStatus(status, LastEvaluatedKey);
//       return [...Items, ...entities];
//     }

//     return Items;
//   } catch (error) {
//     throw error;
//   }
// };

export const fetchDataFromGoogleSheet = async (
  spreadsheetId: string,
  sheetName: string,
  apiKey: string,
) => {
  try {
    posLogger.info(
      'cron',
      'fetchDataFromGoogleSheet',
      `Started https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${sheetName}?key=${apiKey}`,
    );

    const response = await fetch(
      `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${sheetName}?key=${apiKey}`,
    );
    const data = (await response.json()).values;

    if (!data || !Array.isArray(data) || !data.length) {
      return null;
    }

    const headers = data[0].map((header) =>
      header.toLowerCase().replaceAll(' ', '-'),
    );

    const items = data.slice(1);

    posLogger.info(
      'cron',
      'fetchDataFromGoogleSheet',
      `Success ${items.length}`,
    );

    return items.map(function (row) {
      const item = {};
      headers.forEach(function (header, index) {
        if (header && row[index]) {
          item[header] = row[index] || null;
        }
      });
      return item;
    });
  } catch (error) {
    posLogger.error(
      'cron',
      'fetchDataFromGoogleSheet',
      JSON.stringify(error, null, 2),
    );
  }
};

export const fetchAllResetCoupons = async (
  STORE_COUPON_TABLE,
  resetType,
  configService,
  ssmClient,
) => {
  const esHandler = new ElasticClient(configService, ssmClient);

  const query = async ({
    index,
    limit: size = 100,
    page = 1,
    filter,

    nextToken: nt,
  }) => {
    let searchAfter;
    if (nt) {
      searchAfter = JSON.parse(Buffer.from(nt, 'base64').toString('ascii'));
    }

    const searchParams = {
      index,
      size,
      from: (page - 1) * size,
      body: {
        version: false,
        track_total_hits: true,
        search_after: searchAfter,
        query: filter,
      },
    };

    const { body } = await esHandler.search(searchParams);
    const { hits } = body;
    const { hits: results = [], total } = hits;
    const lastResult = results[results.length - 1];
    const nextToken =
      lastResult && lastResult.sort
        ? Buffer.from(JSON.stringify(lastResult.sort), 'ascii').toString(
            'base64',
          )
        : null;

    return {
      page,
      pageSize: size,
      totalPages: Math.ceil(total.value / size),
      total: total.value,
      items: results.map(({ _source }) => _source),
      nextToken,
    };
  };

  const queryAll = async ({ index, filter, nextToken: nT }) => {
    const { items, nextToken } = await query({
      index,
      filter,

      nextToken: nT,
      limit: 9999,
    });

    if (nextToken) {
      const nextItems = await queryAll({
        index,
        filter,
        nextToken,
      });
      return [...items, ...nextItems];
    }
    return items;
  };

  const must = [
    { term: { 'usageResetType.keyword': { value: resetType } } },
    { term: { isActive: { value: true } } },
  ];
  const data = await queryAll({
    index: STORE_COUPON_TABLE,
    filter: {
      bool: {
        must,
      },
    },
    nextToken: null,
  });

  return data;
};

const getPreviousDayRange = () => {
  const start = moment().subtract(1, 'day').startOf('day').toISOString();
  const end = moment().subtract(1, 'day').endOf('day').toISOString();
  // const start = moment().startOf('day').toISOString();
  // const end = moment().endOf('day').toISOString();
  return { start, end };
};

interface QueryParams {
  index: string;
  size?: number;
  filter?: any;
  nextToken?: string;
}

export const fetchPreviousDayRecords = async (
  index: string,
  timestampFields: ('createdAt' | 'updatedAt')[],
  sortBy: string,
  configService,
  ssmClient,
) => {
  const esHandler = new ElasticClient(configService, ssmClient);
  const { start, end } = getPreviousDayRange();

  const buildFilter = (): any => {
    if (timestampFields.length === 0) return undefined;

    const rangeQueries = timestampFields.map((field) => ({
      range: {
        [field]: {
          gte: start,
          lte: end,
          format: 'strict_date_optional_time',
        },
      },
    }));

    return rangeQueries.length === 1
      ? rangeQueries[0]
      : { bool: { should: rangeQueries } };
  };

  const fetchBatch = async ({
    index,
    size = 100,
    filter,
    nextToken,
  }: QueryParams) => {
    const searchAfter = nextToken
      ? JSON.parse(Buffer.from(nextToken, 'base64').toString('ascii'))
      : undefined;

    const searchParams = {
      index,
      size,
      body: {
        version: false,
        track_total_hits: true,
        search_after: searchAfter,
        sort: [{ [sortBy]: 'asc' }],
        query: filter,
      },
    };

    const { body } = await esHandler.search(searchParams);
    const {
      hits: { hits = [], total = { value: 0 } },
    } = body;

    const lastResult = hits[hits.length - 1];
    const newNextToken = lastResult?.sort
      ? Buffer.from(JSON.stringify(lastResult.sort), 'ascii').toString('base64')
      : null;

    return {
      items: hits.map(({ _source }) => _source),
      total: total.value,
      nextToken: newNextToken,
    };
  };

  const fetchAll = async (params: QueryParams): Promise<any[]> => {
    const { items, nextToken } = await fetchBatch(params);
    if (!nextToken) return items;
    const nextItems = await fetchAll({ ...params, nextToken });
    return [...items, ...nextItems];
  };

  const filter = buildFilter();

  const results = await fetchAll({ index, filter });

  posLogger.info(
    'cron',
    'fetchPreviousDayRecords',
    `Found ${results.length} records from index ${index}`,
  );

  return results;
};
