import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
import { json2csv } from 'json-2-csv';
import moment from 'moment';
import { InventoryTrackingData } from '../entities/inventory-tracking.entity';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { TemplateType } from 'src/common/enum/template-type';
import { QueryProducts } from 'src/products/lib/list-products';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { filterFormatter } from 'src/common/helper/filter-helper';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { QueryHandlerService } from 'src/common/query-handler/query-handler';
import { ProductData, Products } from 'src/products/entities/product.entity';
import { QueryProductsByIds } from 'src/products/lib/list-products-by-ids';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { ListInventoryTrackingInput } from '../dto/list-inventory-tracking.input';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

export class ExportInventoryTrackingService {
  private readonly BATCH_SIZE = 80;

  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
  ) {}

  private async fetchProductDetailsInBatchesBySKUs(
    skus: string[],
  ): Promise<any[]> {
    const queryProductsHandler = new QueryProducts(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );

    const batchedQueries = [];
    for (let i = 0; i < skus.length; i += this.BATCH_SIZE) {
      batchedQueries.push(skus.slice(i, i + this.BATCH_SIZE).join(','));
    }

    let productsDataObjs: Products[] = [];
    for (const skuBatch of batchedQueries) {
      const batchData: Products =
        await queryProductsHandler.queryProducts(skuBatch);
      productsDataObjs = productsDataObjs.concat(batchData);
    }

    return productsDataObjs.reduce((acc, batch) => acc.concat(batch.data), []);
  }
  private async fetchProductDetailsInBatchesByProductIds(
    productIds: string[],
  ): Promise<ProductData[]> {
    const queryProductsByIdsHandler = new QueryProductsByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    const uniqueProductIds = Array.from(new Set(productIds));

    // Create batches of SKUs
    const batchedQueries = [];
    for (let i = 0; i < uniqueProductIds.length; i += this.BATCH_SIZE) {
      const batch = uniqueProductIds.slice(i, i + this.BATCH_SIZE);
      batchedQueries.push(batch);
    }

    const productsDataObjs: ProductData[] = [];

    for (const idBatch of batchedQueries) {
      const batchData =
        await queryProductsByIdsHandler.queryProductsByIds(idBatch);

      productsDataObjs.push(...batchData);
    }

    return productsDataObjs;
  }

  private async enrichInventoryData(
    inventoryTrackingData: InventoryTrackingData[],
    storeName: string,
  ): Promise<any[]> {
    const skus = inventoryTrackingData.map((item) => item.id);
    const productDetails = await this.fetchProductDetailsInBatchesBySKUs(skus);
    const productIds = productDetails.map((product) => {
      return product.pk === 'VARIANT' ? product.product_id : product.id;
    });

    const productDetailsByProductIds =
      await this.fetchProductDetailsInBatchesByProductIds(productIds);

    const productMap = new Map(
      productDetails.map((product) => [product.sku, product]),
    );

    return inventoryTrackingData.map((item) => {
      const product = productMap.get(item.id) || {};
      const { id = '', product_id = '', pk } = product;

      const productCategory = productDetailsByProductIds.find(
        (product) => product.id === (pk === 'VARIANT' ? product_id : id),
      )?.product_type;
      const formattedUpdatedAt = moment(item.updatedAt)
        .utcOffset('+05:30')
        .format('DD/MM/yyyy HH:mm');

      return {
        'Store ID': item.storeId,
        'Store Name': storeName,
        'IO ID': item.id,
        'Product Id': product_id,
        'Variant Id': id,
        'Product Name': item.title,
        'Product Category': productCategory || '-',
        'Total Quantity': item.quantity,
        'Display Item Quantity': item.displayItemQuantity || 0,
        'Sellable Quantity': item.quantity - (item.displayItemQuantity || 0),
        'Inventory Transaction Completed Date': formattedUpdatedAt,
      };
    });
  }

  async exportInventoryTrackingCSV(
    inventoryTrackingData: InventoryTrackingData[],
    storeName: string,
  ) {
    const enrichedData = await this.enrichInventoryData(
      inventoryTrackingData,
      storeName,
    );
    return await json2csv(enrichedData, {});
  }

  async exportInventoryTracking(
    email: string,
    filter: ListInventoryTrackingInput,
  ) {
    const IO_TABLE =
      await this.configParameters.getInventoryTrackingTableName();
    const getHandler = new GetStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const searchArray = [];
    const { termSearchFields } = filter || {};
    const { storeId = '' } = termSearchFields;
    if (!storeId) {
      throw new CustomError(
        'Store ID is required to filter inventory tracking',
        400,
      );
    }
    const { sapLocationCode } = await getHandler.getStore(storeId);

    // if (fromDate && toDate) {
    //   searchArray.push({
    //     range: {
    //       updatedAt: {
    //         gte: fromDate,
    //         lte: toDate,
    //       },
    //     },
    //   });
    // }

    const { searchArray: additionalFilters } = await filterFormatter(
      sortingFilter,
      searchingFilter,
      sortingFilterType,
      filter,
    );
    searchArray.push(...additionalFilters);

    const esHandler = new ElasticClient(this.configService, this.ssmClient);
    const queryHandler = new QueryHandlerService(esHandler);

    const sortItems = [{ updatedAt: { order: 'asc' } }];

    const ioData = await queryHandler.queryAll({
      index: IO_TABLE,
      filter: {
        bool: {
          must: [...searchArray],
        },
      },
      nextToken: null,
      sortItems,
    });

    if (!ioData || !ioData.length) {
      throw new Error('No data found for the given filters.');
    }

    const ioCSV = await this.exportInventoryTrackingCSV(
      ioData,
      sapLocationCode,
    );

    const mailService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    await mailService.sendEmailWithFileAttachment(
      email,
      'IO Export',
      'Here is your Inventory Tracking export file',
      'text/csv',
      TemplateType.REPORT,
      'csv',
      [{ name: 'Inventory Detail', content: ioCSV }],
    );

    return { message: 'Email has been sent' };
  }
}
