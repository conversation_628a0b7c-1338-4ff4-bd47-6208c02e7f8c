import { BatchGetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import {
  InventoryTracking,
  InventoryTrackingData,
} from '../entities/inventory-tracking.entity';
import { AppConfigParameters } from 'src/config/config';

export class QueryInventoryTrackingByIds {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryInventoryTrackingByIds(
    storeId: string,
    ids: string[],
  ): Promise<InventoryTracking> {
    posLogger.info('inventory', 'queryInventoryTrackingByIds', {
      input: { storeId, ids },
    });

    if (!ids.length) return { data: [] };

    try {
      const INVENTORY_TRACKING_TABLE =
        await this.configParameters.getInventoryTrackingTableName();

      const batchGetItemsCommand = new BatchGetCommand({
        RequestItems: {
          [INVENTORY_TRACKING_TABLE]: {
            Keys: ids.map((id) => ({
              storeId, // Partition key
              id, // Sort key
            })),
          },
        },
      });

      const { Responses } =
        await this.docClient.batchGetItems(batchGetItemsCommand);

      const res: InventoryTrackingData[] =
        Responses?.[INVENTORY_TRACKING_TABLE] ?? [];
      console.log('res :>> ', res);

      return { data: res || [] };
    } catch (e) {
      posLogger.error('inventory', 'queryInventoryTrackingByIds', {
        error: e.message,
      });
      throw e;
    }
  }
}
