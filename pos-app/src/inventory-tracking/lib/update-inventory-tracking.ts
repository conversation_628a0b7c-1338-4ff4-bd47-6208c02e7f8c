import { Injectable } from '@nestjs/common';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

import {
  InventoryTracking,
  InventoryTrackingData,
} from '../entities/inventory-tracking.entity';
import {
  UpdateInventoryTrackingInput,
  UpdateInventoryTrackingProductInput,
} from '../dto/update-inventory-tracking-input';

@Injectable()
export class UpdateInventoryTrackings {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private docClient: AppDocumentClient,
  ) {}

  // Function to update a single product and return the full DynamoDB update response
  private async updateProduct(
    storeId: string,
    product: UpdateInventoryTrackingProductInput,
    inventoryTrackingTable: string,
  ) {
    const { id, displayItemQuantity = 0 } = product;

    const updateExpressionString =
      'SET displayItemQuantity = :displayItemQuantity, updatedAt = :updatedAt';
    const expressionAttributeValues: Record<string, any> = {
      ':displayItemQuantity': displayItemQuantity,
      ':updatedAt': moment().toISOString(),
    };

    const command = new UpdateCommand({
      TableName: inventoryTrackingTable,
      Key: {
        storeId,
        id,
      },
      UpdateExpression: updateExpressionString,
      ExpressionAttributeValues: expressionAttributeValues,
      ConditionExpression: 'attribute_exists(id)',
      ReturnValues: 'ALL_NEW',
    });

    try {
      const { Attributes: updateResult } =
        await this.docClient.updateItem(command);
      posLogger.info('InventoryTracking', 'updateProduct', {
        message: `Successfully updated product with ID: ${id}`,
        updateResult,
      });

      return updateResult;
    } catch (e) {
      posLogger.error('InventoryTracking', 'updateProduct', {
        error: `Failed to update product with ID: ${id}`,
        details: e,
      });
      throw new CustomError(`Failed to update product with ID: ${id}`, 500);
    }
  }

  async updateInventoryTracking(
    updateInventoryTrackingInput: UpdateInventoryTrackingInput,
  ): Promise<InventoryTracking> {
    posLogger.info('InventoryTracking', 'updateInventoryTracking', {
      input: updateInventoryTrackingInput,
    });

    try {
      const INVENTORY_TRACKING_TABLE =
        await this.configParameters.getInventoryTrackingTableName();

      const { storeId, products } = updateInventoryTrackingInput;

      const updatedProducts: InventoryTrackingData[] = await Promise.all(
        products.map((product) =>
          this.updateProduct(storeId, product, INVENTORY_TRACKING_TABLE),
        ),
      );

      posLogger.info('InventoryTracking', 'updateInventoryTracking', {
        message: 'Successfully updated all products',
        updatedProducts,
      });

      return { data: updatedProducts };
    } catch (e) {
      posLogger.error('InventoryTracking', 'updateInventoryTracking', {
        error: e,
      });
      throw e;
    }
  }
}
