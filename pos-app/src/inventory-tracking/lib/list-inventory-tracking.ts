// modules
import { filterFormatter } from 'src/common/helper/filter-helper';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';
import { ListInventoryTrackingInput } from '../dto/list-inventory-tracking.input';
import { InventoryTracking } from '../entities/inventory-tracking.entity';

export class QueryInventoryTrackings {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryInventoryTrackings(
    filter: ListInventoryTrackingInput,
  ): Promise<InventoryTracking> {
    posLogger.info('inventory-tracking', 'queryInventoryTrackings', {
      input: filter,
    });
    try {
      const { searchArray, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        { ...filter, sortBy: { id: 'asc' } },
      );

      const INVENTORY_TRACKING_TABLE =
        await this.configParameters.getInventoryTrackingTableName();

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: INVENTORY_TRACKING_TABLE,
        });

        size = bodyRes?.count;
      }

      if (searchArray.length) {
        const { body: bodyRes } = await esHandler.count({
          index: INVENTORY_TRACKING_TABLE,
          body: {
            query: {
              bool: {
                must: [...searchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: INVENTORY_TRACKING_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data = response.body.hits.hits.map((hit) => hit._source);

        return { data, count: bodyRes?.count };
      }

      const response = await esHandler.search({
        index: INVENTORY_TRACKING_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data = response.body.hits.hits.map((hit) => hit._source);

      const { body: bodyRes } = await esHandler.count({
        index: INVENTORY_TRACKING_TABLE,
      });

      return { data, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('inventory-tracking', 'queryInventoryTrackings', {
        e,
      });
      throw e;
    }
  }
}
