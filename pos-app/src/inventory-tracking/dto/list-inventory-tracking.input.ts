import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class InventoryTrackingTermSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  storeId?: string;
}

@InputType()
export class ListInventoryTrackingInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size: string;

  @Field(() => InventoryTrackingTermSearchFieldsInput, {
    nullable: true,
    description: 'Search Boolean fields to filter the records',
  })
  termSearchFields: InventoryTrackingTermSearchFieldsInput;
}
