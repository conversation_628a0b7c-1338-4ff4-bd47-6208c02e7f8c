import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class UpdateInventoryTrackingInput {
  @Field(() => String, {
    description: 'Id of store',
  })
  storeId: string;

  @Field(() => [UpdateInventoryTrackingProductInput], {
    description: 'List of products to update',
  })
  products: UpdateInventoryTrackingProductInput[];
}

@InputType()
export class UpdateInventoryTrackingProductInput {
  @Field(() => String, {
    description: 'Id of product to update',
  })
  id: string;

  @Field(() => Number, {
    description: 'Quantity of product to update',
  })
  displayItemQuantity: number;
}
