import { Module } from '@nestjs/common';
import { InventoryTrackingsService } from './inventory-tracking.service';
import { InventoryTrackingResolver } from './inventory-tracking.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
    S3ClientModule,
  ],
  providers: [
    InventoryTrackingResolver,
    InventoryTrackingsService,
    SuccessHandler,
    ErrorHandler,
  ],
})
export class InventoryTrackingsModule {}
