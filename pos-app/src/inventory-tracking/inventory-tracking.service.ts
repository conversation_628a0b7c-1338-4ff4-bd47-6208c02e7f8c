import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { QueryInventoryTrackings } from './lib/list-inventory-tracking';
import { UpdateInventoryTrackings } from './lib/update-inventory-tracking';
import { ListInventoryTrackingInput } from './dto/list-inventory-tracking.input';
import { UpdateInventoryTrackingInput } from './dto/update-inventory-tracking-input';
import { QueryInventoryTrackingByIds } from './lib/list-inventory-tracking-by-ids';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { ExportInventoryTrackingService } from './lib/export-inventory-tracking';

@Injectable()
export class InventoryTrackingsService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
  ) {}

  async findAll(listInventoryTrackingInput: ListInventoryTrackingInput) {
    const queryHandler = new QueryInventoryTrackings(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return queryHandler.queryInventoryTrackings(listInventoryTrackingInput);
  }
  async findAllByIds(storeId: string, ids: string[]) {
    const queryHandler = new QueryInventoryTrackingByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    return queryHandler.queryInventoryTrackingByIds(storeId, ids);
  }
  async updateAll(updateInventoryTrackingInput: UpdateInventoryTrackingInput) {
    const updateHandler = new UpdateInventoryTrackings(
      this.configService,
      this.ssmClient,
      this.configParameters,
      this.docClient,
    );
    return updateHandler.updateInventoryTracking(updateInventoryTrackingInput);
  }

  async exportInventoryTracking(
    email: string,
    listInventoryTrackingInput: ListInventoryTrackingInput,
  ) {
    const exportHandler = new ExportInventoryTrackingService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    exportHandler
      .exportInventoryTracking(email, listInventoryTrackingInput)
      .catch((error) => {
        console.error('IO', 'exportIO', error);
      });
    return { message: 'File will be sent on mail' };
  }
}
