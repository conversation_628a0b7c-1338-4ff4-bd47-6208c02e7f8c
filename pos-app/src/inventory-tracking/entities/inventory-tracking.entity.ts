import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class InventoryTrackingData {
  @Field(() => String, { description: 'StoreID for the inventory system' })
  storeId: string;

  @Field(() => String, { description: 'Unique identifier for the credential' })
  id: string;

  @Field(() => Number, { description: 'Quantity for the inventory system' })
  quantity: number;

  @Field(() => Number, {
    description: 'Quantity for the inventory system',
    nullable: true,
  })
  displayItemQuantity: number;

  @Field(() => String, { description: 'UpdatedAt for the inventory' })
  updatedAt: string;

  @Field(() => String, { description: 'title of the product' })
  title: string;
}
@ObjectType()
export class InventoryTracking {
  @Field(() => [InventoryTrackingData])
  data: InventoryTrackingData[];

  @Field(() => Number, { nullable: true, description: 'count' })
  count?: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
