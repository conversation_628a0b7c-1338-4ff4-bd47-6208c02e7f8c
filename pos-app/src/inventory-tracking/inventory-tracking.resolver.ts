import { Resolver, Query, Args, Mutation } from '@nestjs/graphql';
import { InventoryTrackingsService } from './inventory-tracking.service';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { <PERSON>rrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus } from '@nestjs/common';
import { InventoryTracking } from './entities/inventory-tracking.entity';
import { ListInventoryTrackingInput } from './dto/list-inventory-tracking.input';
import { UpdateInventoryTrackingInput } from './dto/update-inventory-tracking-input';
import { ExportResponse } from 'src/stn/entity/stn.entity';

@Resolver(() => InventoryTracking)
export class InventoryTrackingResolver {
  constructor(
    private readonly inventoryTrackingService: InventoryTrackingsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: <PERSON>rror<PERSON>and<PERSON>,
  ) {}

  @Query(() => InventoryTracking, { name: 'listInventoryTracking' })
  // @UseGuards(StaffGuard)
  async listInventoryTracking(
    @Args('listInventoryTrackingInput', { nullable: true })
    listInventoryTrackingInput: ListInventoryTrackingInput,
  ) {
    try {
      const { data, count } = await this.inventoryTrackingService.findAll(
        listInventoryTrackingInput,
      );
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list inventory-tracking',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Query(() => InventoryTracking, { name: 'listInventoryTrackingByIds' })
  async listInventoryTrackingByIds(
    @Args('storeId', { type: () => String })
    storeId: string,
    @Args('ids', { type: () => [String] }) ids: string[],
  ) {
    try {
      const { data } = await this.inventoryTrackingService.findAllByIds(
        storeId,
        ids,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      return this.errorHandler.getErrorResponse({
        message:
          error.message || 'Failed to retrieve inventory-tracking by ids',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
    }
  }

  @Query(() => ExportResponse, { name: 'exportInventoryTracking' })
  async exportInventoryTracking(
    @Args('email', { type: () => String }) email: string,
    @Args('listInventoryTrackingInput', { nullable: true })
    listInventoryTrackingInput: ListInventoryTrackingInput,
  ) {
    try {
      const { message } =
        await this.inventoryTrackingService.exportInventoryTracking(
          email,
          listInventoryTrackingInput,
        );
      return {
        message,
        success: true,
        status: 200,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to export inventory-tracking',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
  @Mutation(() => InventoryTracking, { name: 'updateInventoryTracking' })
  // @UseGuards(StaffGuard)
  async updateInventoryTracking(
    @Args('updateInventoryTrackingInput')
    updateInventoryTrackingInput: UpdateInventoryTrackingInput,
  ) {
    try {
      const { data } = await this.inventoryTrackingService.updateAll(
        updateInventoryTrackingInput,
      );
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update inventory-tracking',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
