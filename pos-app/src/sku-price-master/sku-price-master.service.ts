import { Injectable } from '@nestjs/common';
import { BulkUpdateSkuPriceMasterInput } from './dto/bulk-update-sku-price-master.input';
import { BulkUploadSkuPrice } from './lib/bulk-update-sku-price';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
import { GetSkuPrice } from './lib/get-sku-price';
import { QuerySkuPriceMasters } from './lib/list-sku-price';
import { ListSkuPriceMasterInput } from './dto/list-sku-price-master.input';
import { posLogger } from 'src/common/logger';
import { ExportSkuPriceMasters } from './lib/export-sku-price';
import { QuerySKUPriceMasterByIds } from './lib/list-sku-price-by-ids';

@Injectable()
export class SkuPriceMasterService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
  ) {}
  async bulkUpdate(
    bulkUpdateSkuPriceMasterInput: BulkUpdateSkuPriceMasterInput,
  ) {
    try {
      const bulkUploadHandler = new BulkUploadSkuPrice(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.s3Client,
      );
      return await bulkUploadHandler.bulkUploadSku(
        bulkUpdateSkuPriceMasterInput.filePath,
      );
    } catch (error) {
      console.log(error, ':::::: bulkUpdate');
      throw error;
    }
  }

  async findOne(id: string) {
    const getHandler = new GetSkuPrice(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.getSkuPrice(id);
  }

  async findAll(listSkuPriceMasterInput: ListSkuPriceMasterInput) {
    const getHandler = new QuerySkuPriceMasters(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.querySkuPriceMasters(listSkuPriceMasterInput);
  }
  async findAllByIds(ids: string[]) {
    const querySKUPriceMasterByIdsHandler = new QuerySKUPriceMasterByIds(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    return querySKUPriceMasterByIdsHandler.querySKUPriceMasterByIds(ids);
  }

  async exportSkuPriceMaster(email: string) {
    const exportHandler = new ExportSkuPriceMasters(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    exportHandler.exportSkuPriceMasters(email).catch((error) => {
      posLogger.error('PRICE MASTER', 'exportSkuPriceMaster', error);
    });
    return { message: 'File will be sent on mail' };
  }
}
