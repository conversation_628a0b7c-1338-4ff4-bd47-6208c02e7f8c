import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class SkuPriceMasterTextSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  id?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  title?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  STNOrderVisibility?: string;
}

@InputType()
export class SkuPriceMasterSortingFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class ListSkuPriceMasterInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size?: string;

  @Field(() => SkuPriceMasterTextSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  textSearchFields?: SkuPriceMasterTextSearchFieldsInput;

  @Field(() => SkuPriceMasterSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { createdAt: 'desc' },
  })
  sortBy?: SkuPriceMasterSortingFieldsInput;
}
