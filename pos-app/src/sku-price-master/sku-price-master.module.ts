import { Module } from '@nestjs/common';
import { SkuPriceMasterService } from './sku-price-master.service';
import { SkuPriceMasterResolver } from './sku-price-master.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    S3ClientModule,
    ConfigParametersModule,
  ],
  providers: [
    SkuPriceMasterResolver,
    SuccessHandler,
    <PERSON>rror<PERSON><PERSON><PERSON>,
    SkuPriceMasterService,
  ],
})
export class SkuPriceMasterModule {}
