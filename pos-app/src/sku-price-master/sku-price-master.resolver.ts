import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { SkuPriceMasterService } from './sku-price-master.service';
import {
  SkuPriceMaster,
  SkuPriceMasters,
} from './entities/sku-price-master.entity';
import { BulkUpdateSkuPriceMasterInput } from './dto/bulk-update-sku-price-master.input';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { AdminGuard } from 'src/auth/roles.guard';
import { ListSkuPriceMasterInput } from './dto/list-sku-price-master.input';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CRMGuard } from 'src/auth/roles.guard';
import { Response } from 'src/products/entities/product.entity';

@Resolver(() => SkuPriceMaster)
@UseGuards(CustomAuthGuard, CRMGuard)
export class SkuPriceMasterResolver {
  constructor(
    private readonly skuPriceMasterService: SkuPriceMasterService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => SkuPriceMaster, { name: 'bulkUpdateSkuPriceMaster' })
  @UseGuards(AdminGuard)
  async bulkUpdateSkuPriceMaster(
    @Args('bulkUpdateSkuPriceMasterInput')
    bulkUpdateSkuPriceMasterInput: BulkUpdateSkuPriceMasterInput,
  ) {
    try {
      const data = await this.skuPriceMasterService.bulkUpdate(
        bulkUpdateSkuPriceMasterInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      console.log(error, '::::: error bulkUpdateSkuPriceMaster');
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to bulk update price master',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => SkuPriceMaster, { name: 'getSkuPriceMaster' })
  async findOne(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.skuPriceMasterService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get sku price master',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => SkuPriceMasters, { name: 'listSkuPriceMasters' })
  async findAll(
    @Args('listSkuPriceMasterInput', { nullable: true })
    listSkuPriceMasterInput: ListSkuPriceMasterInput,
  ) {
    try {
      const { data, count } = await this.skuPriceMasterService.findAll(
        listSkuPriceMasterInput,
      );
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list orders',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Response, { name: 'exportSkuPriceMasters' })
  async exportSkuPriceMaster(
    @Args('email', { type: () => String }) email: string,
  ) {
    try {
      const { message } =
        await this.skuPriceMasterService.exportSkuPriceMaster(email);
      return {
        message,
        success: true,
        status: 200,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to export sku price master',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => SkuPriceMasters, { name: 'listSKUPriceMasterByIds' })
  async querySKUPriceMasterByIds(
    @Args('ids', { type: () => [String] }) ids: string[],
  ) {
    try {
      const { data } = await this.skuPriceMasterService.findAllByIds(ids);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to export sku price master by ids',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
