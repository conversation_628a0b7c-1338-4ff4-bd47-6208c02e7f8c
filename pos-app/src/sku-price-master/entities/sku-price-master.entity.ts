import { ObjectType, Field, Int } from '@nestjs/graphql';

@ObjectType()
export class SkuPriceMasterData {
  @Field(() => String, { description: 'Action', nullable: true })
  action: string;

  @Field(() => String, { description: 'SKU' })
  id: string;

  @Field(() => String, { description: 'Title' })
  title: string;

  @Field(() => String, { description: 'Title', nullable: true })
  shopifyTitle?: string;

  @Field(() => String, { description: 'Parent SKU' })
  parentSku: string;

  @Field(() => Number, { description: 'New Price' })
  price: number;

  @Field(() => String, { description: 'STNOrderVisibility' })
  STNOrderVisibility: string;

  @Field(() => Number, { description: 'Old Price' })
  multiplier: number;

  @Field(() => Number, { description: 'Replacement Window', nullable: true })
  replacementWindow?: number;

  @Field(() => Number, { description: 'Return Shipping Cost', nullable: true })
  returnShippingCost?: number;

  @Field(() => Number, {
    description: 'Replacement Shipping Cost',
    nullable: true,
  })
  replacementShippingCost?: number;

  @Field(() => String, { description: 'Created At' })
  createdAt: string;

  @Field(() => String, { description: 'Updated At' })
  updatedAt: string;
}

@ObjectType()
export class SkuPriceMaster {
  @Field(() => SkuPriceMasterData, { nullable: true })
  data?: SkuPriceMasterData;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class SkuPriceMasters {
  @Field(() => [SkuPriceMasterData], { nullable: true })
  data?: SkuPriceMasterData[];

  @Field(() => Int, { nullable: true, description: 'count' })
  count?: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
