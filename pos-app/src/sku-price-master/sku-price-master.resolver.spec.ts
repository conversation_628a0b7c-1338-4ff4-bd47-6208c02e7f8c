import { Test, TestingModule } from '@nestjs/testing';
import { SkuPriceMasterResolver } from './sku-price-master.resolver';
import { SkuPriceMasterService } from './sku-price-master.service';

describe('SkuPriceMasterResolver', () => {
  let resolver: SkuPriceMasterResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SkuPriceMasterResolver, SkuPriceMasterService],
    }).compile();

    resolver = module.get<SkuPriceMasterResolver>(SkuPriceMasterResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
