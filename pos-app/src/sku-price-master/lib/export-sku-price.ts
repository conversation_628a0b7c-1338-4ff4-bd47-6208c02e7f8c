// modules
import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { QuerySkuPriceMasters } from './list-sku-price';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { TemplateType } from 'src/common/enum/template-type';
import { json2csv } from 'json-2-csv';

export class ExportSkuPriceMasters {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
  ) {}

  async exportSkuPriceMasters(email: string) {
    posLogger.info('SkuPriceMaster', 'exportSkuPriceMaster', { input: email });
    try {
      const querySkuPriceMasters = new QuerySkuPriceMasters(
        this.configService,
        this.ssmClient,
        this.configParameters,
      );
      const { data } = await querySkuPriceMasters.querySkuPriceMasters();
      const csvData = await json2csv(data, {});

      const mailService = new PdfService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.s3Client,
        this.configParameters,
      );

      await mailService.sendEmailWithFileAttachment(
        email,
        'Greetings from The Sleep Company',
        `Following is the exported price master data`,
        'text/csv',
        TemplateType.REPORT,
        'csv',
        [{ name: 'PriceMaster', content: csvData }],
      );

      return { message: 'Email has been sent' };
    } catch (e) {
      posLogger.error('SkuPriceMaster', 'exportSkuPriceMasters', { error: e });
      throw e;
    }
  }
}
