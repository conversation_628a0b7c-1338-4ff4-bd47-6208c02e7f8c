// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { SkuPriceMasterData } from '../entities/sku-price-master.entity';
export class GetSkuPrice {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getSkuPrice(
    id: string,
    checkSkuExist = true,
  ): Promise<SkuPriceMasterData> {
    posLogger.info('sku-price-master', 'getSkuPrice', { input: id });
    try {
      const PRICE_MASTER_TABLE =
        await this.configParameters.getPriceMasterTableName();
  
      const command = new GetCommand({
        TableName: PRICE_MASTER_TABLE,
        Key: { id },
      });
  
      const sku = (await this.docClient.getItem(command)).Item;
      
  
      if (!sku) {
        if (checkSkuExist) {
          throw new CustomError(`SKU ${id} not found`, 404);
        }
        return {} as SkuPriceMasterData; // Return an empty object instead for th
      }
  
      return sku;
    } catch (e) {
      posLogger.error('sku-price-master', 'getSkuPrice', { error: e });
      throw e;
    }
  }
  
}
