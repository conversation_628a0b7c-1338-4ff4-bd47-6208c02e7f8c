import { BatchGetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import {
  SkuPriceMasterData,
  SkuPriceMasters,
} from '../entities/sku-price-master.entity';
import { AppConfigParameters } from 'src/config/config';

export class QuerySKUPriceMasterByIds {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async querySKUPriceMasterByIds(ids: string[]): Promise<SkuPriceMasters> {
    posLogger.info('inventory', 'querySkuPriceMasterByIds', {
      input: { ids },
    });

    if (!ids.length) return { data: [] };

    try {
      const PRICE_MASTER_TABLE =
        await this.configParameters.getPriceMasterTableName();

      const batchGetItemsCommand = new BatchGetCommand({
        RequestItems: {
          [PRICE_MASTER_TABLE]: {
            Keys: ids.map((id) => ({
              id,
            })),
          },
        },
      });

      const { Responses } =
        await this.docClient.batchGetItems(batchGetItemsCommand);

      const res: SkuPriceMasterData[] = Responses?.[PRICE_MASTER_TABLE] ?? [];
      console.log('res :>> ', res);

      return { data: res };
    } catch (e) {
      posLogger.error('inventory', 'querySkuPriceMasterByIds', {
        error: e.message,
      });
      throw e;
    }
  }
}
