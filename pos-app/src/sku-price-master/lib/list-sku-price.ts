// modules

import { ConfigService } from '@nestjs/config';
import { filterFormatter } from 'src/common/helper/filter-helper';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { AppConfigParameters } from 'src/config/config';
import { ListSkuPriceMasterInput } from '../dto/list-sku-price-master.input';
import {
  SkuPriceMasterData,
  SkuPriceMasters,
} from '../entities/sku-price-master.entity';

export class QuerySkuPriceMasters {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async querySkuPriceMasters(
    filter?: ListSkuPriceMasterInput,
  ): Promise<SkuPriceMasters> {
    posLogger.info('SkuPriceMaster', 'querySkuPriceMaster', { input: filter });
    try {
      const PRICE_MASTER_TABLE =
        await this.configParameters.getPriceMasterTableName();
      const searchArray = [];

      const { searchArray: filteredSearch, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );

      searchArray.push(...filteredSearch);

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: PRICE_MASTER_TABLE,
        });

        size = bodyRes?.count;
      }

      if (searchArray.length) {
        const { body: bodyRes } = await esHandler.count({
          index: PRICE_MASTER_TABLE,
          body: {
            query: {
              bool: {
                must: [...searchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: PRICE_MASTER_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data: SkuPriceMasterData[] = response.body.hits.hits.map(
          (hit) => hit._source,
        );

        return { data, count: bodyRes?.count };
      }

      const response = await esHandler.search({
        index: PRICE_MASTER_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data: SkuPriceMasterData[] = response.body.hits.hits.map(
        (hit) => hit._source,
      );

      const { body: bodyRes } = await esHandler.count({
        index: PRICE_MASTER_TABLE,
      });

      return { data, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('SkuPriceMaster', 'querySkuPriceMasters', { error: e });
      throw e;
    }
  }
}
