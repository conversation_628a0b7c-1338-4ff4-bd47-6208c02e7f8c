import { ConfigService } from '@nestjs/config';
import path from 'path';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import csv from 'csvtojson';
import { GetS3Object } from 'src/s3-uploader/lib/get-s3-object';
import { posLogger } from 'src/common/logger';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { QueryProducts } from 'src/products/lib/list-products';
import { csvToArray } from 'src/common/helper/csv-to-array';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

export class BulkUploadSkuPrice {
  private PRICE_MASTER_TABLE;
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
  ) {}

  async init() {
    if (!this.PRICE_MASTER_TABLE) {
      this.PRICE_MASTER_TABLE =
        await this.configParameters.getPriceMasterTableName();
    }
  }

  async updateSkuPrice(
    TABLE: string,
    {
      sku,
      title,
      price,
      parentSku,
      multiplier,
      inventoryUOM,
      itemProperty,
      itemCategory,
      itemSubCategory,
      shopifyTitle,
      STNOrderVisibility,
      hsn,
      gstRate,
    },
  ) {
    posLogger.info('bulkUploadSku', 'updateSkuPrice', {
      sku,
      title,
      price,
      parentSku,
      multiplier,
    });

    try {
      const parsePrice = (priceToBeParsed: string): number => {
        const cleanedPrice = priceToBeParsed.replace(/,/g, '');
        return parseFloat(cleanedPrice);
      };
      const command = new UpdateCommand({
        TableName: TABLE,
        Key: { id: sku },
        UpdateExpression:
          'SET title = :title, price = :price, updatedAt = :updatedAt, parentSku = :parentSku, multiplier = :multiplier , inventoryUOM= :inventoryUOM , itemProperty = :itemProperty ,itemCategory = :itemCategory, itemSubCategory = :itemSubCategory, hsn = :hsn ,gstRate = :gstRate , shopifyTitle = :shopifyTitle,STNOrderVisibility = :STNOrderVisibility',
        ExpressionAttributeValues: {
          ':title': title,
          ':price': parsePrice(price),
          ':parentSku': parentSku,
          ':multiplier': multiplier,
          ':inventoryUOM': inventoryUOM,
          ':itemProperty': itemProperty,
          ':itemCategory': itemCategory,
          ':itemSubCategory': itemSubCategory,
          ':shopifyTitle': shopifyTitle,
          ':STNOrderVisibility': STNOrderVisibility,
          ':hsn': hsn,
          ':gstRate': gstRate,
          ':updatedAt': moment().toISOString(),
        },
      });

      return await this.docClient.updateItem(command);
    } catch (error) {
      posLogger.error('bulkUploadSku', 'updateSkuPrice', error.message + sku);
    }
  }

  async bulkUploadSku(filePath: string) {
    try {
      await this.init();
      console.log('11111111111');
      const getHandler = new GetS3Object(this.configService, this.s3Client);
      const csvProductsData: any = await csvToArray(filePath, getHandler);
      const BATCH_SIZE = 80;
      const skus = csvProductsData.map((product) => product.sku);
      const batchedQueries = [];

      for (let i = 0; i < skus.length; i += BATCH_SIZE) {
        batchedQueries.push(skus.slice(i, i + BATCH_SIZE).join(','));
      }

      const queryProductsHandler = new QueryProducts(
        this.configService,
        this.ssmClient,
        this.configParameters,
      );

      let productsDataObjs: any = [];
      for (const skuBatch of batchedQueries) {
        const batchData = await queryProductsHandler.queryProducts(skuBatch);
        console.log(
          'batchData',
          batchData.data.find((product) => product.sku === 'TSC013'),
          batchData.data.length,
        );

        productsDataObjs = productsDataObjs.concat(batchData);
      }
      const productsData = productsDataObjs.reduce(
        (acc, batch) => acc.concat(batch.data),
        [],
      );

      console.log('productsDatappp', productsData);

      if (!productsData.length) {
        throw new CustomError('Product data not found', 400);
      }

      return await new Promise(async (bulkUploadResolve) => {
        const products = [];

        const onComplete = async () => {
          posLogger.info('bulkUploadSku', 'onComplete', 'Started');

          try {
            await Promise.all(
              products.map((p) => {
                console.log('onCompleteee', p.sku);

                const product = productsData.find((pro) => pro.sku === p.sku);
                let shopifyTitle = null;
                console.log('productWithShopifyTitle', product);
                if (!product) {
                  posLogger.error(
                    'Bulk Upload',
                    'Product not found in Shopify',
                    p.sku,
                  );
                } else {
                  shopifyTitle =
                    (product?.productTitle || '') +
                    ' - ' +
                    (product?.variantTitle || '');
                }

                const productWithShopifyTitle = {
                  ...p,
                  shopifyTitle,
                };

                return this.updateSkuPrice(
                  this.PRICE_MASTER_TABLE,
                  productWithShopifyTitle,
                );
              }),
            );
            return bulkUploadResolve({ action: 'Successfully updated' });
          } catch (error) {
            console.log(error, ':::::: await Promise.all');
            posLogger.error('bulkUploadSku', 'onComplete', error);
            return bulkUploadResolve({ action: error.message });
          }
        };

        const onCSVRow = (product): Promise<void> => {
          // posLogger.info('bulkUploadSku', 'onCSVRow', product.sku);
          return new Promise((resolve) => {
            const {
              sku,
              title,
              price,
              parentSku,
              multiplier,
              inventoryUOM,
              itemProperty,
              itemCategory,
              itemSubCategory,
              hsn,
              gstRate,
              STNOrderVisibility,
            } = product;

            const missingFields = [];

            if (!sku) missingFields.push('sku');
            if (!title) missingFields.push('title');
            if (!parentSku) missingFields.push('parentSku');
            if (!multiplier) missingFields.push('multiplier');
            if (!price) missingFields.push('price');
            if (!STNOrderVisibility) missingFields.push('STNOrderVisibility');

            if (missingFields.length > 0) {
              return bulkUploadResolve({
                action: `The following fields are missing for ${sku || title || ''}: ${missingFields.join(', ')}`,
              });
            }

            products.push({
              sku,
              title,
              price,
              STNOrderVisibility: STNOrderVisibility.toLowerCase(),
              parentSku,
              multiplier: Number(multiplier),
              inventoryUOM,
              itemProperty,
              itemCategory,
              itemSubCategory,
              hsn,
              gstRate,
            });
            resolve(null);
          });
        };

        const onError = async (error) => {
          console.log(error, ':::::: onError');
          posLogger.error('bulkUploadSku', 'onError', error);
          return bulkUploadResolve({ action: error.message });
        };

        const ext = path.extname(filePath);

        if (ext === '.csv') {
          const getHandler = new GetS3Object(this.configService, this.s3Client);
          try {
            const csvData = await (
              await getHandler.getObject(filePath)
            )?.Body?.transformToString();

            csv({})
              .fromString(csvData)
              .subscribe(onCSVRow, onError, onComplete);
          } catch (error) {
            console.log(error, ':::::: csvData');
            posLogger.error('bulkUploadSku', 'csvData', error);
            return bulkUploadResolve({ action: error.message });
          }
        } else {
          console.log(':::::: else');
          posLogger.error(
            'bulkUploadSku',
            'bulkUploadSku',
            `${ext} Invalid file type`,
          );
          return bulkUploadResolve({ action: `${ext}: Invalid file type` });
        }
      });
    } catch (error) {
      console.log(error, ':::::: catch');
      posLogger.error('bulkUploadSku', 'bulkUploadSku', error);
      throw error;
    }
  }
}
