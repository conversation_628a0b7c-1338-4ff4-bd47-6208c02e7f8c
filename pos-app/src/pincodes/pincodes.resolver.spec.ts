import { Test, TestingModule } from '@nestjs/testing';
import { PincodesResolver } from './pincodes.resolver';
import { PincodesService } from './pincodes.service';

describe('PincodesResolver', () => {
  let resolver: PincodesResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PincodesResolver, PincodesService],
    }).compile();

    resolver = module.get<PincodesResolver>(PincodesResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
