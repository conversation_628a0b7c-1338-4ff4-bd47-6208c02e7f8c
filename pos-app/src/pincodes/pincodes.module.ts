import { Modu<PERSON> } from '@nestjs/common';
import { PincodesService } from './pincodes.service';
import { PincodesResolver } from './pincodes.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { ConfigParametersModule } from 'src/config/config.module';

@Module({
  imports: [DocumentClientModule, SsmClientModule, ConfigParametersModule],
  providers: [PincodesResolver, PincodesService, SuccessHandler, ErrorHandler],
})
export class PincodesModule {}
