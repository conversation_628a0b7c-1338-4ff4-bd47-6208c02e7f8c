import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class PincodeData {
  @Field(() => String, { description: 'Pincode' })
  id: string;

  @Field(() => String, { description: 'State', nullable: true })
  state?: string;

  @Field(() => String, { description: 'SLA', nullable: true })
  SLA?: string;

  @Field(() => String, { description: 'applicability', nullable: true })
  applicability?: string;

  @Field(() => Boolean, { description: 'isDeliverable', nullable: true })
  isDeliverable?: boolean;

  @Field(() => String, { description: 'City', nullable: true })
  city?: string;

  @Field(() => String, { description: 'State code', nullable: true })
  stateCode?: string;

  @Field(() => String, { description: 'State code', nullable: true })
  stateNo?: string;
}

@ObjectType()
export class Pincode {
  @Field(() => PincodeData, { nullable: true })
  data: PincodeData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
