import { Resolver, Query, Args } from '@nestjs/graphql';
import { PincodesService } from './pincodes.service';
import { Pincode } from './entities/pincode.entity';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { <PERSON>rror<PERSON>andler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CRMGuard } from 'src/auth/roles.guard';

@Resolver(() => Pincode)
@UseGuards(CustomAuthGuard, CRMGuard)
export class PincodesResolver {
  constructor(
    private readonly pincodesService: PincodesService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Query(() => Pincode, { name: 'validatePincode' })
  async validatePincode(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.pincodesService.validatePincode(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to validate pincode',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
