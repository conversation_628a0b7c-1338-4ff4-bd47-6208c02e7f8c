import { Injectable } from '@nestjs/common';
import { ValidatePinCode } from './lib/get-pincode';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';

@Injectable()
export class PincodesService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validatePincode(id: string) {
    const validatePincodeHandler = new ValidatePinCode(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await validatePincodeHandler.validatePincode(id);
  }
}
