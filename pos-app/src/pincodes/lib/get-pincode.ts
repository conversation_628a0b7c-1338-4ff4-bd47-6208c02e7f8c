import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { PincodeData } from '../entities/pincode.entity';
import { AppConfigParameters } from 'src/config/config';

export class ValidatePinCode {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validatePincode(id: string): Promise<PincodeData> {
    posLogger.info('pincode', 'getPinCode', { input: { id } });
    try {
      const PINCODE_TABLE = await this.configParameters.getPincodeTableName();

      const command = new GetCommand({
        TableName: PINCODE_TABLE,
        Key: {
          id,
        },
      });

      const { Item } = await this.docClient.getItem(command);

      if (!Item) {
        throw new CustomError(
          `Invalid Pincode ${id}! No Pincode found for given ID.`,
          404,
        );
      }

      return Item;
    } catch (e) {
      posLogger.error('pincode', 'getPinCode', { error: e });
      throw e;
    }
  }
}
