import { Test, TestingModule } from '@nestjs/testing';
import { CancelOrderResolver } from './cancel-order.resolver';
import { CancelOrderService } from './cancel-order.service';

describe('CancelOrderResolver', () => {
  let resolver: CancelOrderResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CancelOrderResolver, CancelOrderService],
    }).compile();

    resolver = module.get<CancelOrderResolver>(CancelOrderResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
