import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GetCommand, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppShopify } from 'src/common/shopify/shopify';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetInventoryCredential } from 'src/inventory-credentials/lib/get-inventory-credential';
import { GetOrder } from 'src/orders/lib/get-order';
import { RequestApprovalService } from 'src/replacement-orders/lib/update-replacement-request';
import { CreateIssueTicket } from 'src/orders/lib/create-issue-ticket';
import { RequestRefundService } from 'src/refund-details/lib/refund-request-handler';
import { v4 as uuid } from 'uuid';
import moment from 'moment';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { CreateRefundDetail } from 'src/refund-details/lib/create-refund-detail';
import { determineIsBackToSource } from 'src/refund-details/lib/refund-option';
import { AppS3Client } from 'src/common/s3-client/s3-client';
@Injectable()
export class OrderCancellationService {
  private locationKey: string | null = null;

  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private easyEcomService: EasyEcomService,
    private appShopify: AppShopify,
    private approveRequest: RequestApprovalService,
    private s3Client: AppS3Client,
  ) {}

  async initLocationKey(): Promise<void> {
    if (this.locationKey) return; // already initialized

    try {
      const stackName = this.configService.get('STACK_NAME');
      const parameterName = `/${stackName}/easyecom/locationKey`;

      const {
        Parameter: { Value },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: parameterName,
          WithDecryption: true,
        }),
      );

      this.locationKey = Value;
      posLogger.info('OrderCancellationService', 'initLocationKey', {
        locationKey: this.locationKey,
      });
    } catch (error) {
      posLogger.error('OrderCancellationService', 'initLocationKey', {
        error: error.message || error,
      });
      this.locationKey = 'ne10776308481'
    }
  }

  /**
   * Verifies if an order can be cancelled without actually cancelling it
   * @param referenceCode EasyEcom reference code for the order
   * @returns Object with isEligible flag and reason if not eligible
   */
  async verifyOrderCancellation(referenceCode: string) {
    await this.initLocationKey()
    posLogger.info('OrderCancellationService', 'verifyOrderCancellation', {
      referenceCode,
    });

    try {
      // Get order details from EasyEcom API

      const orderDetails = await this.easyEcomService.getOrderDetails(
        referenceCode,
        this.locationKey,
      );

      console.log('order details on stage from easyecom stage', orderDetails);

      // Initialize result object
      const result = {
        isEligible: true,
        reason: '',
        orderDetails: {
          order_status: orderDetails.order_status,
          manifest_date: orderDetails.manifest_date,
          warehouse_id: orderDetails.warehouse_id,
          customer_name: orderDetails.customer_name,
          reference_code: orderDetails.reference_code,
          order_date: orderDetails.order_date,
          total_amount: orderDetails.total_amount,
        },
      };

      // Check if order is already cancelled
      if (orderDetails.order_status === 'Cancelled') {
        result.isEligible = false;
        result.reason = `Order ${referenceCode} is already cancelled`;
        return result;
      }

      // Check if order has a manifest date
      if (orderDetails.manifest_date) {
        result.isEligible = false;
        result.reason = `Order ${referenceCode} cannot be cancelled as it has already been manifested for shipping`;
        return result;
      }

      // Check if order has status that prevents cancellation
      const nonCancellableStatuses = [
        'Delivered',
        'Out For Delivery',
        'Shipped',
      ];
      if (nonCancellableStatuses.includes(orderDetails.order_status)) {
        result.isEligible = false;
        result.reason = `Order ${referenceCode} cannot be cancelled as it is in ${orderDetails.order_status} status`;
        return result;
      }

      // If we get here, the order is eligible for cancellation
      posLogger.info('OrderCancellationService', 'verifyOrderCancellation', {
        isEligible: true,
        orderStatus: orderDetails.order_status,
      });

      return result;
    } catch (error) {
      posLogger.error('OrderCancellationService', 'verifyOrderCancellation', {
        error: error.message || error,
      });

      throw new CustomError(
        `Failed to verify order cancellation: ${error.message || 'Unknown error'}`,
        error.statusCode || 500,
      );
    }
  }

  async getOrder(url: string, apiKey: string) {
    try {
      const orderResponse = await fetch(url, {
        method: 'GET',
        headers: {
          'x-api-key': apiKey,
          'Content-Type': 'application/json',
        },
      });
      const response = await orderResponse.json();
      return response?.data;
    } catch (error) {
      posLogger.error('OrderCancellationService', 'getOrder', error.message);
      return error?.response;
    }
  }

  async getOrderfromServerlessOms(orderId: string) {
    try {
      const {
        Parameter: { Value: baseURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/url`,
          WithDecryption: true,
        }),
      );
      const {
        Parameter: { Value: apiKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/apiKey`,
          WithDecryption: true,
        }),
      );
      const finalURL = `${baseURL}/order/orderInfo?orderId=${orderId}`;
      console.log('finalURL', finalURL);
      console.log('apiKey', apiKey);
      const order = await this.getOrder(finalURL, apiKey);

      if (Array.isArray(order)) {
        const [shopifyOrderData] = order;
        return shopifyOrderData;
      }

      return null;
    } catch (error) {
      posLogger.error(
        'OrderCancellationService',
        'getOrderfromServerlessOms',
        error.message,
      );
      throw error;
    }
  }

  /**
   * Creates a ticket for refund processing
   * Adapted from CreateFinalOrder.createTicket
   */
  async createRefundTicketForCancellation(
    createRefundTicketForCancellation: any,
  ): Promise<any> {
    posLogger.info('order-cancellation', 'createRefundTicket', {
      createRefundTicketForCancellation,
    });

    console.log(
      'going to create ticket for cancellation',
      createRefundTicketForCancellation,
    );

    const {
      phone,
      subject,
      description,
      name,
      email,
      orderId,
      status,
      priority,
      voc1,
    } = createRefundTicketForCancellation;
    const createdAt = moment().toISOString();

    const apiData = {
      description,
      subject,
      email,
      name,
      phone,
      priority,
      status,
      type: 'Complaint',
      group_id: ***********,
      internal_group_id: ***********,
      internal_agent_id: 84044206026,
      custom_fields: {
        cf_order_id: orderId.toString(),
        cf_voc_level_1: voc1,
      },
    };
    console.log('create ticket ', apiData);
    try {
      const createTicketHandler = new CreateIssueTicket(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const responseData = await createTicketHandler.createTicket(apiData);
      console.log('order-cancellation', 'createRefundTicket', responseData);

      if (responseData?.id) {
        const { id: ticketId } = responseData;
        const raisedTicket = {
          subject,
          description,
          createdAt,
          ticketId,
        };

        return raisedTicket;
      } else {
        throw new CustomError(`Failed to raise refund ticket`, 400);
      }
    } catch (error) {
      posLogger.error('order-cancellation', 'createRefundTicket', error);
      throw error;
    }
  }

  /**
   * Sends SMS notification for refunds
   * Adapted from CreateFinalOrder.sendSMSNotification
   */
  async sendSMSNotification(payload) {
    try {
      const url = `https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/send_sms`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to send SMS: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      throw new CustomError(
        `Error sending SMS notification: ${error.message}`,
        400,
      );
    }
  }

  extractPaymentInfo(noteAttributes: any[] | null | undefined) {
    // Default values if noteAttributes is null or undefined
    if (!noteAttributes || !noteAttributes.length) {
      return {
        isRazorpay: false,
        transactionModes: [],
        transactionIDs: [],
      };
    }

    // Check if Razorpay was used as a payment method
    const isRazorpay = noteAttributes.some(
      (note) =>
        note.name.startsWith('Transaction Mode') && note.value === 'RAZORPAY',
    );

    // Extract all transaction modes
    const transactionModes = noteAttributes
      .filter((attr) => attr.name.includes('Transaction Mode'))
      .map((attr) => attr.value);

    // Extract all transaction IDs
    const transactionIDs = noteAttributes
      .filter((attr) => attr.name.includes('TransactionID'))
      .map((attr) => attr.value);

    return {
      isRazorpay,
      transactionModes,
      transactionIDs,
    };
  }

  /**
   * Main method to cancel an order in both EasyEcom and Shopify systems
   * @param orderId Order ID to cancel
   * @returns Results from both cancellation operations
   */
  async cancelOrder(
    referenceCode: string,
    freshdeskId: string,
    requestReason: string,
    requestId: string,
  ) {

    await this.initLocationKey()
    posLogger.info('OrderCancellationService', 'cancelOrder', {
      referenceCode,
    });

    try {
      //GETTING ORDER FROM SERVERLESS OMS.
      const shopifyOrderData =
        await this.getOrderfromServerlessOms(referenceCode);
      console.log(
        'shopifyOrderData for the order we are going to cancel',
        shopifyOrderData,
      );
      if (!shopifyOrderData) {
        throw new CustomError('Order not found', 400);
      }

      // Extract necessary data from shopifyOrderData
      const {
        created_at,
        note_attributes: noteAttributes,
        price_before_discount: priceBeforeDiscount,
        BankDiscount: bankDiscount = 0,
        price_after_discount: priceAfterDiscount,
        customerphone,
        id: shopifyOrderId,
        customer: {
          first_name: customerFirstName,
          last_name: customerLastName,
          email: customerEmail,
          phone: customerPhone,
        },
      } = shopifyOrderData || {};

      console.log('noteAttributes', noteAttributes, shopifyOrderId);

      const { isRazorpay, transactionModes, transactionIDs } =
        this.extractPaymentInfo(noteAttributes);

      console.log('payment info', isRazorpay, transactionModes, transactionIDs);

      // Step 2: Cancel order in EasyEcom
      const easyEcomResult = await this.cancelOrderInEasyEcom(
        referenceCode,
        this.locationKey,
      );
      console.log('easyEcomResult', easyEcomResult);

      const orderDetails = await this.easyEcomService.getOrderDetails(
        referenceCode,
        this.locationKey,
      );

      //CANCEL ORDER ON SHOPIFY
      try {
        const shopifyResult = await this.cancelOrderInShopify(
          Number(shopifyOrderId),
        );
        console.log('shopifyResult', shopifyResult);
      } catch (err) {
        //TODO: ADD A MAIL HERE TO THE POS TEAM
      }

      // Step 3: Approval process for request
      let isBankDetailsAvailableInRequest = false;
      let requestDetailsAfterApproval = {} as any;
      const id = uuid(); // Generate a unique ID for this cancellation

      // Determine if refund should go back to source
      const isBackToSource = determineIsBackToSource(transactionModes);
      console.log('isBackToSource', isBackToSource);

      // Extract customer details from order

      try {
        const requestApprovalService = this.approveRequest;

        requestDetailsAfterApproval =
          await requestApprovalService.approveRequest(
            requestId,
            'CANCELLATION',
          );
        posLogger.info('RequestApprovalService', 'approveRequest', {
          message: `Successfully approved request ${referenceCode}`,
        });
      } catch (approvalError) {
        posLogger.error('RequestApprovalService', 'approveRequest', {
          error: approvalError.message || approvalError,
          referenceCode,
        });
        // We continue with order CANCELLATION even if approval fails
      }

      console.log('requestDetailsAfterApproval', requestDetailsAfterApproval);

      // Check if we have refund details already
      const { data: requestData = {} } = requestDetailsAfterApproval;
      if (requestData && Object.keys(requestData).length > 0) {
        const {
          _id: requestId,
          bank_details: bankDetails,
          upi_details: upiDetails,
        } = requestData;

        if (
          (bankDetails && Object.keys(bankDetails).length) ||
          (upiDetails && Object.keys(upiDetails).length)
        ) {
          isBankDetailsAvailableInRequest = true;
        }
      }

      console.log(
        'isBankDetailsAvailableInRequest after request details',
        isBankDetailsAvailableInRequest,
      );
      

      // Create refund ticket with updated description
      const ticketResponse = await this.createRefundTicketForCancellation({
        orderId: referenceCode,
        email: customerEmail,
        name: `${customerFirstName} ${customerLastName}`,
        phone: customerPhone,
        description: `<span>
          Type : CANCELLATION
          Order ID: ${referenceCode || '-'}<br/>
          Cancellation Date: ${moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss') || '-'}<br/>
          Order Amount: ${orderDetails.total_amount || '0'}<br/>
          Reason for Cancellation: ${requestReason || '-'}<br/>
          Freshdesk Ticket ID: ${freshdeskId || '-'}<br/>
          Bank Details Provided: ${isBankDetailsAvailableInRequest ? 'Yes' : 'No'}<br/>
          Original Payment Mode: ${transactionModes.join(', ') || ''}<br/>
          Original Transaction IDs: ${transactionIDs.join(', ') || ''}<br/>
        </span>`,
        priority: 1,
        status: 12,
        type:"Complaint",
        subject: 'Order Cancellation - Process Refund',
        group_id: ***********,
        voc1: 'Booked Return Refund',
      });
      console.log('ticket res after cancellation', ticketResponse);

      // Step 4: Record cancellation in DynamoDB
      const CANCEL_ORDER_TABLE =
        await this.configParameters.getCancelledOrderTableName();

      let cancelOrderPayload = {
        pk: id,
        sk: referenceCode,
        order_id: referenceCode,
        freshdeskId,
        reasonForCancellation: requestReason,
        cancelled_at: new Date().toISOString(),
        refundTicketId: ticketResponse?.ticketId || '',
        refundDetailAlreadyAdded:
          isBankDetailsAvailableInRequest || isBackToSource,
        isRazorpay,
        originalPaymentMode: transactionModes || [],
        transactionIDs: transactionIDs || [],
        priceBeforeDiscount: priceBeforeDiscount || 0,
        priceAfterDiscount: priceAfterDiscount || 0,
        bankDiscount: bankDiscount || 0,
        isBackToSource,
        ...(!isBankDetailsAvailableInRequest &&
          !isBackToSource && {
            refundFormLink: `https://refund-form-${this.configService.get('NODE_ENV')}.thesleepcompany.in/?shopifyOrderId=${referenceCode}&id=${id}&type=CANCELLATION`,
          }),
      };

      console.log('cancelOrderPayload', cancelOrderPayload, CANCEL_ORDER_TABLE);

      const cancelOrderCommand = new PutCommand({
        TableName: CANCEL_ORDER_TABLE,
        Item: cancelOrderPayload,
        ConditionExpression:
          'attribute_not_exists(pk) AND attribute_not_exists(sk)',
      });

      // Add DynamoDB entry
      const res = await this.docClient.createItem(cancelOrderCommand);
      console.log('dynamo db cancellation added', res);

      // Process refund based on isBackToSource and isBankDetailsAvailableInRequest
      if (isBankDetailsAvailableInRequest || isBackToSource) {
        console.log(
          'Processing refund with existing details or back to source',
        );
        try {
          const refundRequestHandler = new RequestRefundService(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
            this.s3Client,
          );

          const res = await refundRequestHandler.processRefundAgainstARequest(
            id,
            referenceCode,
            'CANCELLATION',
            requestId, // No specific product ID for cancellation
            isBackToSource,
          );

          console.log('Refund processed successfully:', res);
        } catch (err) {
          posLogger.error(
            'RequestRefundService',
            'processRefundAgainstARequest',
            {
              error: err.message || err,
              id,
            },
          );
          // Continue with order creation even if refund processing fails
        }
      } else {
        // Create refund details if bank details not available and not back to source
        console.log('Creating refund detail for customer cancellation');
        try {
          const createRefundHandler = new CreateRefundDetail(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
            this.s3Client,
          );

          const refundDetail =
            await createRefundHandler.createCancellationRefundDetail({
              id,
              shopifyOrderId: referenceCode,
              isBackToSource,
              isFormSubmitted: false,
              requestId,
              customerEmail,
              customerPhone,
              customerFirstName,
              customerLastName,
            });

          console.log('refundDetail', refundDetail);

          // Send SMS notification if bank details not available and not back to source
          try {
            console.log('Sending SMS notification for refund details');
            const SMSpayload = {
              template_attributes: {
                templateName: 'CancellationRefund',
                phoneNo: customerPhone,
                customerName: customerFirstName,
                refund_link: `https://refund-form-${this.configService.get('NODE_ENV')}.thesleepcompany.in/?shopifyOrderId=${referenceCode}%26id=${id}%26type=CANCELLATION`,
              },
            };

            const smsResponse = await this.sendSMSNotification(SMSpayload);
            console.log('SMS sent successfully:', smsResponse);
          } catch (smsError) {
            posLogger.error('OrderCancellationService', 'sendSMSNotification', {
              error: smsError.message || smsError,
            });
            // Continue even if SMS fails
          }
        } catch (err) {
          posLogger.error(
            'OrderCancellationService',
            'createCancellationRefundDetail',
            {
              error: err.message || err,
            },
          );
        }
      }

      return cancelOrderPayload;
    } catch (error) {
      posLogger.error('OrderCancellationService', 'cancelOrder', {
        message: 'Error cancelling order',
        error: error.message || error,
      });

      throw new CustomError(
        `Failed to cancel order: ${error.message || 'Unknown error'}`,
        500,
      );
    }
  }

  /**
   * Get location key for the given order
   * @param orderId Order ID to look up
   * @returns Location key for EasyEcom API
   */

  /**
   * Cancels an order in the EasyEcom system
   * @param orderId Order ID to cancel
   * @param locationKey EasyEcom location key
   * @returns Result from EasyEcom cancellation operation
   */
  async cancelOrderInEasyEcom(orderId: string, locationKey: string) {
    posLogger.info('OrderCancellationService', 'cancelOrderInEasyEcom', {
      orderId,
      locationKey,
    });

    try {
      const easyEcomResult = await this.easyEcomService.cancelOrder(
        orderId,
        locationKey,
      );

      posLogger.info('OrderCancellationService', 'cancelOrderInEasyEcom', {
        message: 'EasyEcom cancellation completed',
        easyEcomResult,
      });

      // Check if EasyEcom cancellation was successful
      if (easyEcomResult?.code !== 200) {
        throw new CustomError(
          `EasyEcom cancellation returned non-success code: ${easyEcomResult?.code}`,
          400,
        );
      }

      return easyEcomResult;
    } catch (easyEcomError) {
      posLogger.error('OrderCancellationService', 'cancelOrderInEasyEcom', {
        message: 'EasyEcom cancellation failed',
        error: easyEcomError.message || easyEcomError,
      });

      throw new CustomError(
        `Failed to cancel order in EasyEcom: ${easyEcomError.message || 'Unknown error'}`,
        400,
      );
    }
  }

  /**
   * Cancels an order in the Shopify system
   * @param orderId Order ID to cancel
   * @returns Result from Shopify cancellation operation
   */
  async cancelOrderInShopify(orderId: number) {
    posLogger.info('OrderCancellationService', 'cancelOrderInShopify', {
      orderId,
    });

    try {
      // Get Shopify access token and admin base URL
      const shopifyAccessToken = await this.appShopify.getShopifyAccessToken();
      const shopifyAdminBaseUrl =
        await this.appShopify.getShopifyAdminBaseUrl();

      // Prepare GraphQL query
      const graphqlQuery = `
      mutation orderCancel(
        
        $orderId: ID!, 
        $reason: OrderCancelReason!, 
        $refund: Boolean!, 
        $restock: Boolean!, 
        
      ) {
        orderCancel(
        
          orderId: $orderId, 
          reason: $reason, 
          refund: $refund, 
          restock: $restock, 
          
        ) {
          job {
            id
            
            
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

      // Convert numeric orderId to Shopify GraphQL ID format if needed
      const shopifyOrderId = `gid://shopify/Order/${orderId}`;

      // Execute GraphQL query
      const response = await fetch(`${shopifyAdminBaseUrl}/graphql.json`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': shopifyAccessToken,
        },
        body: JSON.stringify({
          query: graphqlQuery,
          variables: {
            orderId: shopifyOrderId,
            reason: 'OTHER',
            refund: true,
            restock: true,
          },
        }),
      });

      const result = await response.json();
      console.log('result in cancelling the order on shopify', result);
      // Check for GraphQL errors
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      // Check for user errors in the response
      if (result.data?.orderCancel?.userErrors?.length > 0) {
        throw new Error(result.data.orderCancel.userErrors[0].message);
      }

      const shopifyCancellationResult = result.data?.orderCancel?.job;

      posLogger.info('OrderCancellationService', 'cancelOrderInShopify', {
        message: 'Shopify cancellation completed',
        shopifyCancellationResult,
      });

      return shopifyCancellationResult;
    } catch (shopifyError) {
      posLogger.error('OrderCancellationService', 'cancelOrderInShopify', {
        message: 'Shopify cancellation failed',
        error: shopifyError.message || shopifyError,
      });

      throw new CustomError(
        `Failed to cancel order in Shopify: ${shopifyError.message || 'Unknown error'}`,
        500,
      );
    }
  }

  async getCancelledOrder(id: string) {
    posLogger.info('OrderCancellationService', 'getCancelledOrder', {
      id,
    });

    try {
      const CANCEL_ORDER_TABLE =
        await this.configParameters.getCancelledOrderTableName();

      const queryCancelOrderById = new QueryCommand({
        TableName: CANCEL_ORDER_TABLE,
        KeyConditions: {
          pk: {
            ComparisonOperator: 'EQ',
            AttributeValueList: [id],
          },
        },
      });
      const result = await this.docClient.queryItems(queryCancelOrderById);
      return result?.Items[0] || {};
    } catch (err) {
      posLogger.error('OrderCancellationService', 'getCancelledOrder', {
        id,
        error: err.message || err,
      });
    }
  }

  async holdOrderOnEEcom(referenceCode: string) {
    await this.initLocationKey()
    posLogger.info('OrderCancellationService', 'holdOrderOnEEcom', {
      referenceCode,
    });

    try {
      const orderDetails = await this.easyEcomService.getOrderDetails(
        referenceCode,
        this.locationKey,
      );
      console.log('orderDetails from ee', orderDetails);
      // Check if order has a valid status for being put on hold
      const nonHoldableStatuses = [
        'Cancelled',
        'Delivered',
        'Out For Delivery',
        'Shipped',
      ];

      if (nonHoldableStatuses.includes(orderDetails.order_status)) {
        throw new CustomError(
          `Order ${referenceCode} cannot be put on hold as it is in ${orderDetails.order_status} status`,
          400,
        );
      }

      // Step 3: Call EasyEcom API to hold the order

      const { invoice_id: invoiceId = '' } = orderDetails;

      if (!invoiceId) {
        throw new CustomError(
          `Order ${referenceCode} does not have an invoice id`,
          400,
        );
      }

      const result = await this.easyEcomService.holdOrder(
        invoiceId,
        this.locationKey,
      );

      posLogger.info('OrderCancellationService', 'holdOrderOnEEcom', {
        message: 'Hold order operation completed',
        result,
      });

      // Check if the operation was successful
      if (result?.code !== 200) {
        throw new CustomError(
          `EasyEcom hold order returned non-success code: ${result?.code}`,
          400,
        );
      }

      return result;
    } catch (error) {
      posLogger.error('OrderCancellationService', 'holdOrderOnEEcom', {
        message: 'Hold order operation failed',
        error: error.message || error,
      });

      throw new CustomError(
        `Failed to put order on hold: ${error.message || 'Unknown error'}`,
        error.statusCode || 500,
      );
    }
  }

  // private async getLocationKeyFromOrderId(
  //   referenceCode: string,
  // ): Promise<string> {
  //   try {
  //     posLogger.info('OrderCancellationService', 'getLocationKeyFromOrderId', {
  //       referenceCode,
  //     });

  //     const orderDetails = await this.easyEcomService.getOrderDetails(
  //       referenceCode,
  //       this.locationKey,
  //     );

  //     if (orderDetails.order_status === 'Cancelled') {
  //       throw new CustomError(
  //         `Order ${referenceCode} is already cancelled`,
  //         400,
  //       );
  //     }

  //     // Check if order has a manifest date
  //     if (orderDetails.manifest_date) {
  //       throw new CustomError(
  //         `Order ${referenceCode} cannot be cancelled as it has already been manifested for shipping`,
  //         400,
  //       );
  //     }
  //     // Extract warehouse ID from the response
  //     const warehouseId = orderDetails.warehouse_id;

  //     if (!warehouseId) {
  //       throw new CustomError(
  //         `No warehouse ID found in order details for reference code ${referenceCode}`,
  //         400,
  //       );
  //     }

  //     posLogger.info('OrderCancellationService', 'getLocationKeyFromOrderId', {
  //       warehouseId,
  //     });

  //     // Step 2: Get location key from inventory credentials using warehouse ID as secret key
  //     const getInventoryCredentialHandler = new GetInventoryCredential(
  //       this.docClient,
  //       this.configParameters,
  //     );

  //     const inventoryCredential =
  //       await getInventoryCredentialHandler.getInventoryCredentialBySecretKey(
  //         warehouseId.toString(),
  //       );

  //     const locationKey = inventoryCredential?.locationKey || 'ne10776308481';

  //     posLogger.info('OrderCancellationService', 'getLocationKeyFromOrderId', {
  //       locationKey,
  //     });

  //     return locationKey;
  //   } catch (error) {
  //     posLogger.error('OrderCancellationService', 'getLocationKeyFromOrderId', {
  //       error: error.message || error,
  //     });

  //     throw new CustomError(
  //       `Failed to get location key: ${error.message || 'Unknown error'}`,
  //       error.statusCode || 500,
  //     );
  //   }
  // }
}
