import {
    Controller,
    Get,
    HttpException,
    HttpStatus,
    Param,
    UseGuards,
    Post
} from '@nestjs/common';

import { SAPCustomAuthGuard } from 'src/auth/sap.guard';
import { posLogger } from 'src/common/logger';
import { OrderCancellationService } from './lib/cancel-order';

@Controller('order-cancellation')
export class OrderCancellationController {
  constructor(
    private readonly orderCancellationService: OrderCancellationService,
  ) {}

  /**
   * Endpoint to verify if an order can be cancelled
   * @param referenceCode EasyEcom reference code for the order
   * @returns Object with eligibility status and order details
   */
  @Get('verify/:id')
  @UseGuards(SAPCustomAuthGuard)
  async verifyOrderCancellation(@Param('id') referenceCode: string) {
    posLogger.info('OrderCancellationController', 'verifyOrderCancellation', {
      referenceCode,
    });

    try {
      const result =
        await this.orderCancellationService.verifyOrderCancellation(
          referenceCode,
        );

      return {
        success: true,
        isEligible: result.isEligible,
        reason: result.reason,
        orderDetails: result.orderDetails,
      };
    } catch (error) {
      posLogger.error(
        'OrderCancellationController',
        'verifyOrderCancellation',
        {
          error: error.message || error,
        },
      );

      throw new HttpException(
        {
          success: false,
          message: error.message || 'Failed to verify order cancellation',
        },
        error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('hold/:id')
  @UseGuards(SAPCustomAuthGuard)
  async holdOrderOnEasyEcom(@Param('id') referenceCode: string) {
    posLogger.info('OrderCancellationController', 'verifyOrderCancellation', {
      referenceCode,
    });
    console.log("in hold order controller",referenceCode)
    try {
      const result =
        await this.orderCancellationService.holdOrderOnEEcom(
          referenceCode,
        );

      return {
        success: true,
        data:result,
      };
    } catch (error) {
      posLogger.error(
        'OrderCancellationController',
        'verifyOrderCancellation',
        {
          error: error.message || error,
        },
      );

      throw new HttpException(
        {
          success: false,
          message: error.message || 'Failed to verify order cancellation',
        },
        error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
