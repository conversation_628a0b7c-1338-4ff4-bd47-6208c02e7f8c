import { Module } from '@nestjs/common';
import { CancelOrderService } from './cancel-order.service';
import { CancelOrderResolver } from './cancel-order.resolver';
import { OrderCancellationService } from './lib/cancel-order';
import { AppConfigParameters } from 'src/config/config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { EasyEcomService } from 'src/common/easy-ecom/easy-ecom';
import { AppShopify } from 'src/common/shopify/shopify';
import { OrderCancellationController } from './cancel-order.controller';
import { RequestApprovalService } from 'src/replacement-orders/lib/update-replacement-request';
import { AppS3Client } from 'src/common/s3-client/s3-client';

@Module({
  controllers: [OrderCancellationController],
  providers: [
    CancelOrderResolver,
    CancelOrderService,
    OrderCancellationService,
    AppDocumentClient,
    AppSsmClient,
    ConfigService,
    AppConfigParameters,
    SuccessHandler,
    EasyEcomService,
    AppShopify,
    RequestApprovalService,
    AppS3Client,
  ],
})
export class CancelOrderModule {}
