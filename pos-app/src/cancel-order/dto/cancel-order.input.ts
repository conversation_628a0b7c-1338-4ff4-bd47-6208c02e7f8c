import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CancelOrderInput {
  // @Field(() => Number, { description: 'Order ID to be canceled' })
  // orderId: string;

  @Field(() => String, {
    description: 'Reference code for the cancellation on easycom',
  })
  referenceCode: string;

  @Field(() => String, { description: 'Freshdesk ticket Id' })
  freshdeskId: string;

  @Field(() => String, { description: 'Reason for cancellation' })
  requestReason: string;

  @Field(() => String, { description: 'Request Id for cancellation' })
  requestId: string;
}
