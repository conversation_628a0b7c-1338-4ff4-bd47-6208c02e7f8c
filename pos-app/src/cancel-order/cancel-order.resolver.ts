import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { CancelOrderService } from './cancel-order.service';
import { CancelOrder, CancelOrderData } from './entities/cancel-order.entity';
import { CancelOrderInput } from './dto/cancel-order.input';

@Resolver(() => CancelOrder)
export class CancelOrderResolver {
  constructor(private readonly cancelOrderService: CancelOrderService) {}

  

  @Mutation(() => CancelOrder)
  cancelOrder(
    @Args('cancelOrderInput') cancelOrderInput: CancelOrderInput,
  ) {
    console.log("cancelOrderInput",cancelOrderInput)
    return this.cancelOrderService.cancel(cancelOrderInput);
  }

  @Query(() => CancelOrderData)
  getCancelledOrder(@Args('id') id: string) {
    console.log("in getting cancelled order",id)
    return this.cancelOrderService.getCancelledOrder(id);
  }
}
