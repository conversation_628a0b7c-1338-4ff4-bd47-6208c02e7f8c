import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class CancelOrderData {
  @Field(() => String, {
    description: 'Primary key after cancellation added to db',
    nullable: true,
  })
  pk?: string;

  @Field(() => String, {
    description: 'Secondary key after cancellation added to db',
    nullable: true,
  })
  sk?: string;

  @Field(() => String, {
    description: 'Reference code for the cancellation on easycom',
    nullable: true,
  })
  order_id?: string;

  @Field(() => String, { description: 'Freshdesk ticket Id', nullable: true })
  freshdeskId?: string;

  @Field(() => String, {
    description: 'Reason for cancellation',
    nullable: true,
  })
  reasonForCancellation?: string;

  @Field(() => String, {
    description: 'Creation timestamp of the order',
    nullable: true,
  })
  cancelled_at?: string;

  @Field(() => String, { nullable: true })
  refundTicketId?: string;

  @Field(() => String, { nullable: true })
  priceAfterDiscount?: string;

  @Field(() => String, { nullable: true })
  priceBeforeDiscount?: string;

  @Field(() => [String], { nullable: true })
  transactionIDs?: [string];

  @Field(() => [String], { nullable: true })
  originalPaymentMode?: [string];

  @Field(() => Boolean, { nullable: true })
  isRazorpay?: boolean;

  @Field(() => Number, { nullable: true })
  bankDiscount?: number;

  @Field(() => Boolean, { nullable: true })
  refundDetailAlreadyAdded?: boolean;

  @Field(() => String, { nullable: true })
  refundFormLink?: string;
}

@ObjectType()
export class CancelOrder {
  @Field(() => String, {
    description: 'Status of the cancellation',
    nullable: true,
  })
  status?: string;

  @Field(() => CancelOrderData, { nullable: true })
  data?: CancelOrderData;
}
