import { Injectable } from '@nestjs/common';
import { CancelOrderInput } from './dto/cancel-order.input';

import { posLogger } from 'src/common/logger';
import { OrderCancellationService } from './lib/cancel-order';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';

@Injectable()
export class CancelOrderService {
  constructor(
    private orderCancellationService: OrderCancellationService,
    private readonly successHandler: SuccessHandler,
  ) {}

  //TODO: ADD A FUCNTION OF CANCEL VERIFICATION USING ROHITS API.
  async cancel(cancelOrderInput: CancelOrderInput) {
    const { referenceCode, freshdeskId, requestReason,requestId="" } = cancelOrderInput;
    console.log('cancelOrderInput', cancelOrderInput);
    posLogger.info('CancelOrderService', 'cancel', {
      referenceCode: referenceCode || 'No reference code provided',
    });

    try {
      // Call the OrderCancellationService to handle the actual cancellation
      const result = await this.orderCancellationService.cancelOrder(
        referenceCode,
        freshdeskId,
        requestReason,
        requestId
      );
      console.log("result from cancelling order in serivce",result)
      return this.successHandler.getSuccessResponse({
        data: {...result},
        code: 200,
      });
    } catch (error) {
      // Log the error but let it propagate up to be handled by global error handlers
      posLogger.error('CancelOrderService', 'cancel', {
        referenceCode: referenceCode || 'No reference code provided',
        error: error.message || error,
      });

      throw error;
    }
  }

  async getCancelledOrder(id: string) {
    try {
      const result = await this.orderCancellationService.getCancelledOrder(id);
      console.log('result of getting cancelled order', result);
      return result;
    } catch (err) {
      posLogger.error('CancelOrderService', 'getCancelledOrder', {
        id: id || 'No reference code provided',
        error: err.message || err,
      });
      throw err;
    }
  }
}
