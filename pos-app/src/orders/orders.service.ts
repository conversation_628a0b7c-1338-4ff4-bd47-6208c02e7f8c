import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateOrderInput } from './dto/create-order.input';
import { CreateOrder } from './lib/create-order';
import { GetOrder } from './lib/get-order';
import { QueryOrders } from './lib/list-orders';
import {
  GetOrderByQuotationIdInput,
  ListOrderInput,
} from './dto/list-order.input';
import { FinalOrderConfirm } from './lib/final-confirm-order';
import { QueryOrderItems } from './lib/list-order-items';
import { SyncShopifyOrderStatus } from './lib/sync-shopify-order-status';
import { ExportOrders } from './lib/export-orders';
import { GetOrderAndStoreAndSendPdf } from 'src/common/helper/get-order-and-store';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { UpdateOrderInput } from './dto/update-order.input';
import { UpdateOrder } from './lib/update-order';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';
import { GetOrderByQuotationId } from './lib/get-order-by-quotation-id';
import { QueryShopifyOrders } from './lib/list-shopify-orders';
import { UpdateOrderStatus } from './lib/update-order-status';
import { OrderStatus } from 'src/common/enum/order';
import { CreateIssueTicketInput } from './dto/create-issue-ticket.input';
import { CreateIssueTicket } from './lib/create-issue-ticket';
import { posLogger } from 'src/common/logger';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { TemplateType } from 'src/common/enum/template-type';
import moment from 'moment-timezone';

@Injectable()
export class OrdersService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}
  async create(createOrderInput: CreateOrderInput) {
    const createHandler = new CreateOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await createHandler.createOrder(createOrderInput);
  }

  async findOne(id: string) {
    const getHandler = new GetOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await getHandler.getOrder(id);
  }

  async findOneByQuotationId({
    quotationId,
    subOrderType = undefined,
  }: GetOrderByQuotationIdInput) {
    const getHandler = new GetOrderByQuotationId(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await getHandler.getOrderByQuotationId({
      quotationId,
      subOrderType,
    });
  }

  async findAll(listOrderInput: ListOrderInput) {
    const getHandler = new QueryOrders(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return await getHandler.queryOrders(listOrderInput);
  }

  async exportOrders(
    email: string,
    listOrderInput: ListOrderInput,
    exportTypes: string[],
  ) {
    const exportHandler = new ExportOrders(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );
    const mailService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );
    exportHandler
      .exportOrders(email, listOrderInput, exportTypes)
      .catch((error) => {
        const { fromDate = null, toDate = null } = listOrderInput || {};
        mailService.sendEmailWithFileAttachment(
          email,
          `SmartServe POS Export | ${moment(fromDate).tz('Asia/Kolkata').format('DD/MM/YYYY')} to ${moment(toDate).tz('Asia/Kolkata').format('DD/MM/YYYY')}The Sleep Company`,
          `Please contact administrator for this data. There is an issue loading the data with to this date range`,
          'text/csv',
          TemplateType.REPORT,
          'csv',
          [],
        );
        posLogger.error('ORDERS', 'exportOrders', error);
      });
    return { message: 'File will be sent on mail' };
  }

  async finalOrderConfirm(orderId: string, storeId: string) {
    const getHandler = new FinalOrderConfirm(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
      this.s3Client,
    );
    return await getHandler.finalOrderConfirm(orderId, storeId);
  }

  async sendOrderPdf(orderId: string, type: string) {
    const getHandler = new GetOrderAndStoreAndSendPdf(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );
    return await getHandler.getOrderAndStoreAndSendPdf(orderId, type);
  }

  async findAllOrderItems(orderId: string) {
    const queryOrderItemsByOrderIdHandler = new QueryOrderItems(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return await queryOrderItemsByOrderIdHandler.queryOrderItemsByOrderId(
      orderId,
    );
  }

  async syncShopifyOrderStatus(orderId: string) {
    const syncShopifyOrderStatusHandler = new SyncShopifyOrderStatus(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
    );
    return await syncShopifyOrderStatusHandler.syncShopifyOrderStatus(orderId);
  }

  async update(updateOrderInput: UpdateOrderInput) {
    const updateHandler = new UpdateOrder(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    return await updateHandler.updateOrder(updateOrderInput);
  }

  async findAllShopifyOrders(customerId: string) {
    const queryHandler = new QueryShopifyOrders(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
    );
    return await queryHandler.queryShopifyOrders(customerId);
  }

  async holdOrder(orderId: string) {
    const orderStatusInstance = new UpdateOrderStatus(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    await orderStatusInstance.updateOrderStatus(orderId, OrderStatus.ON_HOLD);
  }
  async createIssueTicket(issueData: CreateIssueTicketInput) {
    const createIssueTicketHandler = new CreateIssueTicket(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await createIssueTicketHandler.createIssueTicket(issueData);
  }
}
