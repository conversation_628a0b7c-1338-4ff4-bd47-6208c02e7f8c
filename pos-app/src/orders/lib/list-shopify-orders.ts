import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';
import { ShopifyOrderData } from '../entities/shopify-order.entity';

export class QueryShopifyOrders {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}

  async queryShopifyOrders(customerId: string): Promise<ShopifyOrderData[]> {
    posLogger.info('order', 'queryShopifyOrders', {
      customerId,
    });

    try {
      const [SHOPIFY_ACCESS_TOKEN, SHOPIFY_ADMIN_BASE_URL] = await Promise.all([
        await this.shopifyClient.getShopifyAccessToken(),
        await this.shopifyClient.getShopifyAdminBaseUrl(),
      ]);

      const shopDomain = SHOPIFY_ADMIN_BASE_URL.split('/admin')[0];
      const graphqlEndpoint = `${shopDomain}/admin/api/2024-01/graphql.json`;

      const formattedCustomerId = customerId.includes('gid://shopify/Customer/')
        ? customerId
        : `gid://shopify/Customer/${customerId}`;

      const customerOrdersQuery = `
        query getCustomerOrders($customerId: ID!) {
          customer(id: $customerId) {
            legacyResourceId
            orders(first: 250) {
              edges {
                node {
                  id
                  legacyResourceId
                  name
                  phone
                  email
                  confirmationNumber
                  currentTotalPriceSet {
                    shopMoney {
                      amount
                    }
                  }
                  createdAt
                  lineItems(first: 250) {
                    edges {
                      node {
                        title
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `;

      const customerOrdersVariables = {
        customerId: formattedCustomerId,
      };

      const customerOrdersResponse = await fetch(graphqlEndpoint, {
        method: 'POST',
        headers: {
          'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: customerOrdersQuery,
          variables: customerOrdersVariables,
        }),
      });

      const customerOrdersData = await customerOrdersResponse.json();
      console.log('customerOrdersData', customerOrdersData);

      // Log the customer orders response for debugging
      posLogger.info(
        'order',
        'queryShopifyOrders_customerOrdersData',
        JSON.stringify(customerOrdersData),
      );

      if (customerOrdersData.errors) {
        throw new Error(JSON.stringify(customerOrdersData.errors));
      }

      if (!customerOrdersData.data?.customer?.orders?.edges?.length) {
        return [];
      }

      const orderDetailsQuery = `
        query getOrderDetails($orderId: ID!) {
          order(id: $orderId) {
            id
            legacyResourceId
            orderStatusUrl
          }
        }
      `;

      const orderPromises = customerOrdersData.data.customer.orders.edges.map(
        async (edge) => {
          const order = edge.node;

          const orderVariables = {
            orderId: order.id,
          };

          const orderResponse = await fetch(graphqlEndpoint, {
            method: 'POST',
            headers: {
              'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: orderDetailsQuery,
              variables: orderVariables,
            }),
          });

          const orderData = await orderResponse.json();
          console.log('orderData', orderData);

          posLogger.info(
            'order',
            'queryShopifyOrder_orderData',
            JSON.stringify({ orderId: order.id, response: orderData }),
          );

          const orderId = order.legacyResourceId || order.id.split('/').pop();

          const lineItemTitles = order.lineItems.edges.map(
            (lineItem) => lineItem.node.title,
          );

          let order_status_url = orderData.data?.order?.orderStatusUrl || '';

          posLogger.info('order', 'shopify_order_status_url', {
            order_id: orderId,
            order_status_url,
          });

          return {
            id: orderId,
            name: order.name,
            phone: order.phone,
            email: order.email,
            current_total_price: order.currentTotalPriceSet.shopMoney.amount,
            order_status_url,
            created_at: order.createdAt,
            title: lineItemTitles.join(', '),
          };
        },
      );

      const shopifyOrders = await Promise.all(orderPromises);
      return shopifyOrders;
    } catch (error) {
      posLogger.error('order', 'queryShopifyOrders', {
        error,
        stack: error.stack,
      });
      throw error;
    }
  }
}
