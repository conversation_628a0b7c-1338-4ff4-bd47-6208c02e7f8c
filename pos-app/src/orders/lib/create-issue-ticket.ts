import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreateIssueTicketInput } from '../dto/create-issue-ticket.input';
import { GetOrder } from './get-order';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import fetch from 'node-fetch';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { RaisedIssueData } from '../entities/order.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { OrderChangeLog } from './order-change-log';

@Injectable()
export class CreateIssueTicket {
  private apiUrl: string;
  private accessKey: string;
  private password: string;
  private groupId: string;

  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async init() {
    if (!this.apiUrl) {
      const {
        Parameter: { Value: apiUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/freshdesk/api-url`,
          WithDecryption: true,
        }),
      );

      this.apiUrl = apiUrl;
    }

    if (!this.accessKey) {
      const {
        Parameter: { Value: accessKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/freshdesk/api-key`,
          WithDecryption: true,
        }),
      );
      this.accessKey = accessKey;
    }

    if (!this.password) {
      const {
        Parameter: { Value: password },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/freshdesk/password`,
          WithDecryption: true,
        }),
      );
      this.password = password;
    }

    if (!this.groupId) {
      const {
        Parameter: { Value: groupId },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/freshdesk/group-id`,
          WithDecryption: true,
        }),
      );
      this.groupId = groupId;
    }
  }

  async createTicket(apiData) {
    await this.init();
    // console.log(object)
    // apiData['group_id'] = Number(this.groupId) || 84000291687;
    try {
      const response = await fetch(this.apiUrl + '/api/v2/tickets', {
        method: 'POST',
        headers: {
          Accept: '*/*',
          'Content-Type': 'application/json',
          Authorization: 'Basic ' + btoa(this.accessKey + ':' + this.password),
        },
        body: JSON.stringify(apiData),
      });
      const res = await response.json();
      return res;
    } catch (error) {
      throw error;
    }
  }

  async updateTicket(apiData) {
    await this.init();
    const ticketId = apiData['RRTicketId'];
    delete apiData['RRTicketId'];
    try {
      const response = await fetch(
        this.apiUrl + `/api/v2/tickets/${ticketId}`,
        {
          method: 'PUT',
          headers: {
            Accept: '*/*',
            'Content-Type': 'application/json',
            Authorization:
              'Basic ' + btoa(this.accessKey + ':' + this.password),
          },
          body: JSON.stringify(apiData),
        },
      );

      return await response.json();
    } catch (error) {
      throw error;
    }
  }

  async createIssueTicket(
    createIssueTicketInput: CreateIssueTicketInput,
  ): Promise<RaisedIssueData> {
    posLogger.info('Orders', 'createIssueTicket', {
      createIssueTicketInput,
    });
    await this.init();

    const {
      phone,
      subject,
      description,
      id,
      name,
      email,
      orderId,
      status,
      priority,
    } = createIssueTicketInput;
    const createdAt = moment().toISOString();

    const apiData = {
      description,
      subject,
      email,
      name,
      phone,
      priority,
      status,
      type: 'Query',
      group_id: Number(this.groupId),
      custom_fields: {
        cf_order_id: orderId,
        cf_product: 'No Product',
      },
    };

    try {
      const responseData = await this.createTicket(apiData);
      posLogger.info('orders', 'createIssueTicket', responseData);
      let raisedIssue: RaisedIssueData = null;

      if (responseData.id) {
        const { id: ticketId } = responseData;
        raisedIssue = {
          subject,
          description,
          createdAt,
          ticketId,
        };

        const getOrderHandler = new GetOrder(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const order = await getOrderHandler.getOrder(id);

        const raisedIssues = order.raisedIssues || [];
        raisedIssues.push(raisedIssue);

        const ORDER_TABLE = await this.configParameters.getOrderTableName();

        const updateCommand = new UpdateCommand({
          TableName: ORDER_TABLE,
          Key: { id },
          UpdateExpression:
            'SET #updatedAt = :updatedAt, raisedIssues = :raisedIssues',
          ExpressionAttributeNames: {
            '#updatedAt': 'updatedAt',
          },
          ExpressionAttributeValues: {
            ':updatedAt': moment().toISOString(),
            ':raisedIssues': raisedIssues,
          },
          ConditionExpression: 'attribute_exists(id)',
          ReturnValues: 'ALL_NEW',
        });

        const { Attributes } = await this.docClient.updateItem(updateCommand);

        const orderChangeLog = new OrderChangeLog(
          this.configParameters,
          this.docClient,
        );
        orderChangeLog.log(Attributes);

        return raisedIssue;
      } else {
        throw new CustomError(`Failed to create issue ticket`, 404);
      }
    } catch (error) {
      posLogger.error('orders', 'createIssueTicket', error);
      throw new CustomError(`Failed to create issue ticket ${error}`, 404);
    }
  }
}
