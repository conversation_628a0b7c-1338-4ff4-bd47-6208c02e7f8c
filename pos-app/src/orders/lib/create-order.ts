import { BatchWriteCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import moment from 'moment';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { v4 as uuid } from 'uuid';
import { CreateOrderInput } from '../dto/create-order.input';
import { getDiscountAndFinalPrice } from '../helpers/get-item-discount';
import { GetQuotation } from 'src/quotations/lib/get-quotation';
import { OrderProductData } from '../entities/order.entity';
import { OrderStatus, OrderType, SubOrderType } from 'src/common/enum/order';
import { QuotationStatus, QuotationType } from 'src/common/enum/quotations';
import { GenerateCode } from 'src/common/helper/generate-code';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { UpdateQuotationStatus } from 'src/quotations/lib/set-quotation-status';
import { AppConfigParameters } from 'src/config/config';
import { GetHsnBySku } from 'src/common/helper/get-hsn-by-sku';
import { getGstRateCalculator } from 'src/common/helper/gst-calculator';
import { ValidateCouponUsage } from './validate-coupon-usage';
import { ValidateProducts } from 'src/common/helper/validate-products';
import { deepEqual } from 'src/utils/compareJson';
import { PaymentStatus } from 'src/common/enum/payment';
import { GetOrderByQuotationId } from './get-order-by-quotation-id';
import { UpdateOrder } from './update-order';
import { OrderChangeLog } from './order-change-log';

export class CreateOrder {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createBNPLProductOrderPayload(orderPayload: any) {
    const handler = new GetOrderByQuotationId(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const superOrder = await handler.getOrderByQuotationId({
      quotationId: orderPayload.quotationId,
      subOrderType: undefined,
    });

    const superOrderNotes = superOrder.notes || '';
    const inputNotes = orderPayload.notes || '';
    const combinedNotes =
      inputNotes && superOrderNotes
        ? `${superOrderNotes}\n${inputNotes}`
        : inputNotes || superOrderNotes || null;

    orderPayload = {
      ...orderPayload,
      id: `${superOrder.id}_02`,
      subOrderType: SubOrderType.FINAL_ORDER,
      superOrderId: superOrder.id,
      notes: combinedNotes, // Use combined notes
    };

    const { orderProducts } = orderPayload;
    const superOrderUpdates: any = {
      subOrderIds: [...superOrder.subOrderIds, orderPayload.id],
      bookingAmountStatus: PaymentStatus.PAID,
    };

    const updateOrderHandler = new UpdateOrder(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );

    try {
      console.log('Updating super order.');
      await updateOrderHandler.updateOrder({
        id: superOrder.id,
        orderProducts,
        ...superOrderUpdates,
      });
    } catch (e) {
      console.log('Error while updating super order', e);
      posLogger.error('createOrder', 'updateSuperOrder', { e });
      throw e;
    }

    return orderPayload;
  }

  async createOrder({
    orderProducts,
    quotationId,
    storeId,
    deliveryStatus,
    deliveryDate,
    minDeliveryDate,
    unHoldFutureDispatchDate,
    tentativePurchaseDate,
    notes: inputNotes,
    dispatchDate,
    eddHeaderId,
    posFallbackEdd,
    ...createOrderInput
  }: CreateOrderInput) {
    try {
      posLogger.info('order', 'createOrder', {
        input: { createOrderInput, orderProducts, quotationId },
      });

      const handler = new UpdateQuotationStatus(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const getQuotationHandler = new GetQuotation(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const orderInput = await getQuotationHandler.getQuotation(quotationId);

      const isBNPL = orderInput.type === QuotationType.BNPL;
      const isBNPLPriceLockCheckout =
        (isBNPL && orderInput.bookingAmountStatus !== PaymentStatus.PAID) ||
        false;

      const validateProducthandler = new ValidateProducts(
        this.configService,
        this.ssmClient,
        this.docClient,
        this.configParameters,
      );

      await validateProducthandler.validateProducts(orderProducts);

      const getStoreHandler = new GetStore(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const {
        invoiceStartCode,
        isActive,
        accountCode,
        cardCode,
        sapLocationCode,
        warehouseMappingId,
      } = await getStoreHandler.getStore(storeId);

      if (!isActive) {
        throw new CustomError(`Store is not active`, 400);
      }

      if (!invoiceStartCode) {
        throw new CustomError(
          `UNEXPECTED FLOW: invoiceStartCode prefix is not present in store`,
          400,
        );
      }

      if (
        !(
          orderInput.status === QuotationStatus.ACTIVE ||
          orderInput.status === QuotationStatus.PRICE_LOCKED
        )
      ) {
        throw new CustomError(
          `Quotation is in ${orderInput.status} status`,
          403,
        );
      }

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { quotationProducts, ...orderInputWithoutQuotationProducts } =
        orderInput;

      const validateCouponUsageHandler = new ValidateCouponUsage(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      await validateCouponUsageHandler.validateCampaignCouponUsage(
        orderInputWithoutQuotationProducts.campaignCode,
        orderInputWithoutQuotationProducts.storeId,
      );

      const hsnService = new GetHsnBySku(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const finalOrderProducts: OrderProductData[] = await Promise.all(
        orderInput.quotationProducts.map(async (p) => {
          const {
            productId,
            variantId,
            quantity,
            sku,
            price,
            // priceType,
            customData,
            ...rest
          } = p;
          const product = orderProducts.find(
            (orderProduct) =>
              orderProduct.productId == productId &&
              orderProduct.variantId == variantId &&
              orderProduct.quantity == quantity &&
              deepEqual(orderProduct.customData, customData),
          );

          if (!product)
            throw new CustomError('Product not present in quotation!', 404);

          const hsn = sku ? await hsnService.getHsnBySku(sku) : null;

          const { itemDiscount, finalItemPrice } = getDiscountAndFinalPrice(
            quantity,
            price,
            orderInput.totalAmount -
              orderInput.finalDiscountedAmount +
              (orderInput?.deliveryCharge || 0),
            orderInput.totalAmount,
          );
          const {
            gstRate,
            gstAmount,
            finalItemPriceWithoutGst,
            gstPercentage,
          } = getGstRateCalculator({
            ...p,
            ...product,
            finalItemPrice,
          });
          const temp = {
            ...rest,
            ...product,
            gstRate,
            hsn,
            gstAmount,
            itemDiscount,
            finalItemPriceWithoutGst,
            finalItemPrice,
            gstPercentage,
            id: uuid(),
            storeId: storeId,
            createdAt: moment().toISOString(),
            updatedAt: moment().toISOString(),
          };

          // if (priceType) {
          //   temp.priceType = priceType;
          // }

          return temp;
        }),
      );

      const { customer, shippingAddress, billingAddress } = orderInput;
      if (!customer || !shippingAddress || !billingAddress) {
        throw new CustomError('Invalid order request!', 400);
      }

      const [ORDER_TABLE, ORDER_ITEM_TABLE] = await Promise.all([
        await this.configParameters.getOrderTableName(),
        await this.configParameters.getOrderItemTableName(),
      ]);

      const codeHandler = new GenerateCode(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const code = await codeHandler.generateCode(
        storeId,
        'INVOICE_COUNT',
        invoiceStartCode,
      );

      const quotationNotes = orderInput.notes || '';
      const combinedNotes =
        inputNotes && quotationNotes
          ? `${quotationNotes}\n${inputNotes}` // Append with a newline for clarity
          : inputNotes || quotationNotes || null;

      let orderPayload: any = {
        ...orderInputWithoutQuotationProducts,
        quotationId,
        id: code,
        series: `SK0${codeHandler.getFinancialYear()}`,
        acctcode: accountCode,
        cardcode: cardCode,
        loggedusername: sapLocationCode,
        whscode: warehouseMappingId,
        status: OrderStatus.PENDING,
        orderProducts: finalOrderProducts,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
        deliveryDate,
        deliveryStatus,
        minDeliveryDate,
        unHoldFutureDispatchDate,
        orderType: OrderType.POS,
        isFirstTimeVisit: true,
        notes: combinedNotes,
        dispatchDate,
        eddHeaderId,
        posFallbackEdd,
      };

      let superOrderPayload;
      if (isBNPL) {
        if (isBNPLPriceLockCheckout) {
          const subOrderId = `${code}_01`;
          superOrderPayload = {
            ...orderPayload,
            id: code,
            orderType: OrderType.BNPL,
            tentativePurchaseDate,
            subOrderIds: [subOrderId],
            notes: combinedNotes,
          };

          orderPayload = {
            ...orderPayload,
            id: subOrderId,
            finalDiscountedAmount: orderInput.bookingAmount,
            tentativePurchaseDate,
            subOrderType: SubOrderType.LOCK_PRICE,
            productFinalAmount: orderInput.finalDiscountedAmount,
            superOrderId: code,
            notes: combinedNotes,
          };

          try {
            const superOrderCommand = new PutCommand({
              TableName: ORDER_TABLE,
              Item: superOrderPayload,
              ConditionExpression: 'attribute_not_exists(id)',
            });

            await this.docClient.createItem(superOrderCommand);
          } catch (e) {
            posLogger.error('order', 'createOrder - super order', e);
          }
        } else {
          orderPayload = await this.createBNPLProductOrderPayload(orderPayload);
        }
      }

      const orderCommand = new PutCommand({
        TableName: ORDER_TABLE,
        Item: orderPayload,
        ConditionExpression: 'attribute_not_exists(id)',
      });

      await this.docClient.createItem(orderCommand);

      const orderChangeLog = new OrderChangeLog(
        this.configParameters,
        this.docClient,
      );
      orderChangeLog.log(orderPayload);

      if (
        !isBNPL ||
        !orderPayload.subOrderType ||
        orderPayload.subOrderType !== SubOrderType.LOCK_PRICE
      ) {
        console.log('Creating Order Products.');
        const putRequests = finalOrderProducts.map((orderProduct) => {
          return {
            PutRequest: {
              Item: {
                ...orderProduct,
                orderId: orderPayload.id,
              },
            },
          };
        });

        const orderItemCommand = new BatchWriteCommand({
          RequestItems: {
            [ORDER_ITEM_TABLE]: putRequests,
          },
        });

        await this.docClient.createItems(orderItemCommand);
      }

      if (isBNPLPriceLockCheckout) {
        await handler.updateQuotationStatus(
          quotationId,
          QuotationStatus.ORDER_CREATED,
          orderPayload.id,
          code,
        );
      } else {
        await handler.updateQuotationStatus(
          quotationId,
          QuotationStatus.ORDER_CREATED,
          orderPayload.id,
        );
      }

      posLogger.info(
        'order',
        'createOrder',
        `quotation converted , order created ${code}`,
      );
      return orderPayload;
    } catch (error) {
      posLogger.error('order', 'createOrder', { error });
      throw error;
    }
  }
}
