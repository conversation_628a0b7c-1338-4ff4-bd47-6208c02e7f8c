import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { PaymentHelpers } from 'src/payments/lib/payment-helpers';
import { OrderData } from '../entities/order.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { ValidateProducts } from 'src/common/helper/validate-products';
import { OrderStatus } from 'src/common/enum/order';

//types for return of function in promise generic

export class GetOrder {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getOrder(id: string): Promise<OrderData> {
    try {
      posLogger.info('order', 'getOrder', {
        input: { id },
      });

      const ORDER_TABLE = await this.configParameters.getOrderTableName();

      const param = new GetCommand({
        TableName: ORDER_TABLE,
        Key: { id },
      });
      const { Item: order } = await this.docClient.getItem(param);

      if (!order) {
        throw new CustomError(
          `Invalid ID! No order found for given ID ${id}.`,
          404,
        );
      }

      const { status, orderProducts } = order;
      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const payments = await paymentHelper.getAllPaymentsByOrderId(id);
      let isPaymentCompleted = false;

      if (
        payments &&
        payments?.transactionDetails &&
        payments?.transactionDetails.length > 0
      ) {
        if (
          payments.transactionDetails.some(
            (payment) => payment.status === 'COMPLETED',
          )
        ) {
          isPaymentCompleted = true;
        }
      }
      if (
        status !== OrderStatus.SENT_TO_SHOPIFY &&
        status !== OrderStatus.CANCELLED &&
        status !== OrderStatus.DELETED_FROM_SHOPIFY &&
        !isPaymentCompleted
      ) {
        const validateProducthandler = new ValidateProducts(
          this.configService,
          this.ssmClient,
          this.docClient,
          this.configParameters,
        );
        await validateProducthandler.validateProducts(orderProducts);
      }

      const paymentHandler = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const paymentDetails = await paymentHandler.getAllPaymentsByOrderId(id);
      
      return {
        ...order,
        orderProducts,
        transactions: paymentDetails,
      };
    } catch (error) {
      posLogger.error('order', 'getOrder', { error });
      throw error;
    }
  }
}
