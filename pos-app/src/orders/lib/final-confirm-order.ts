import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GetOrder } from './get-order';
import { OrderStatus, SubOrderType } from 'src/common/enum/order';
import { OrderData } from '../entities/order.entity';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { StoreData } from 'src/stores/entities/store.entity';
import { DeliveryType } from 'src/common/enum/delivery';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { EmployeeData } from 'src/employees/entities/employee.entity';
import { GetEmployee } from 'src/employees/lib/get-employee';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';
import { QuotationStatus } from 'src/common/enum/quotations';
import { UpdateQuotationStatus } from 'src/quotations/lib/set-quotation-status';
import { PaymentMode, PaymentStatus } from 'src/common/enum/payment';
import { PaymentHelpers } from 'src/payments/lib/payment-helpers';
import moment from 'moment-timezone';
import { EInvoiceService } from 'src/common/einvoice/einvoice.service';
import { PincodesService } from 'src/pincodes/pincodes.service';
import { EInvoice } from 'src/common/einvoice/einvoice';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';
import { GetQuotation } from 'src/quotations/lib/get-quotation';
import { UpdateCustomDiscountStatus } from 'src/custom-discount-verification/lib/update-custom-discount-status';
import { CustomDiscountApprovalStatus } from 'src/custom-discount-verification/entities/custom-discount-verification.entity';
import { getUnifiedPaymentMode } from 'src/utils/get-unified-payment-mode';
import { AdditionalPromotionalCouponInput } from 'src/quotations/dto/update-quotation.input';
import { UpdateQuotation } from 'src/quotations/lib/update-quotation';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { OrderChangeLog } from './order-change-log';
import { EddService } from 'src/common/edd-service/edd-service';

export class FinalOrderConfirm {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {}

  async sendAdditionalShippingData(orderId: any, data: any) {
    const payload = {
      orderId: orderId.toString(),
      location_detail: data,
    };

    console.log('Payload ::::', payload);

    const apiConfig = {
      'pos-stage': {
        url: 'https://15vz409ax0.execute-api.ap-south-1.amazonaws.com/dev/update/location_coordinate',
        key: 'rmK6iPQK2G0xc1UTHUHhiDpZ7RYADu3yOD5hUAf0',
      },
      'pos-prod': {
        url: 'https://gybqro5fdj.execute-api.ap-south-1.amazonaws.com/prod/update/location_coordinate',
        key: 'PpuHE5OWtR3xYmZTZWRBwaa3ogMvAumX8kWc4Alm',
      },
    };

    const env = process.env.STACK_NAME;
    const config = apiConfig[env];

    if (!config) {
      throw new CustomError(`Invalid STACK_NAME :::: ${env}`, 500);
    }

    try {
      setTimeout(async () => {
        const response = await fetch(config.url, {
          method: 'POST',
          headers: {
            'x-api-key': config.key,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });
        const data = await response.json();
        console.log(data, '::::: data');
      }, 20 * 1000);

      return true;
    } catch (error) {
      console.error('Error sending shipping data:', error);
      throw error;
    }
  }

  async getDeliveryType(order: OrderData) {
    const { deliveryStatus, holdOrder } = order;
    const eddService = new EddService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const eddResponse = await eddService.getEstimatedDeliveryDate({
      data: order.orderProducts,
      drop_pincode: order.shippingAddress.pinCode,
    });

    const isXpressDelivery =
      eddResponse?.data?.some((item) => item.response?.isXpressDelivery) ||
      eddResponse?.cartIsXpressDelivery;

    const getGlobalConfigurationHandler = new GetGlobalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const [{ value: ELITE_SOFAS }, { value: AI_MATTRESS }] = await Promise.all([
      getGlobalConfigurationHandler.getGlobalConfiguration(
        'POS_ELITE_SOFA_PRODUCTS',
      ),
      getGlobalConfigurationHandler.getGlobalConfiguration(
        'POS_AI_MATTRESS_PRODUCTS',
      ),
    ]);

    const orderProductIds = new Set(
      order.orderProducts.map((item) => item.productId),
    );

    const isExistEliteSofaProductIds = ELITE_SOFAS?.split(',').some((id) =>
      orderProductIds.has(id?.trim()),
    );
    const isExistAiMattressProductIds = AI_MATTRESS?.split(',').some((id) =>
      orderProductIds.has(id?.trim()),
    );

    const conditions = [
      isXpressDelivery ? 'FYND Own Delivery' : null,
      deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE
        ? 'Future Delivery'
        : null,
      holdOrder ? 'Hold Order' : null,
      isExistEliteSofaProductIds ? 'Elite Sofa' : null,
      isExistAiMattressProductIds ? 'AI Mattress' : null,
    ].filter(Boolean);
    console.log(ELITE_SOFAS, AI_MATTRESS, orderProductIds);

    return conditions.join(', ');
  }

  async holdOrder(
    normalOrderId: string,
    SHOPIFY_ACCESS_TOKEN: string,
    SHOPIFY_ADMIN_BASE_URL: string,
    reason: string,
  ) {
    try {
      console.log('Fetching fulfillment orders for order ID:', normalOrderId);

      const getFulfillmentOrdersResponse = await fetch(
        `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          },
          body: JSON.stringify({
            query: `query getFulfillmentOrders($id: ID!) {
            order(id: $id) {
              fulfillmentOrders(first: 5) {
                edges {
                  node {
                    id
                    status
                  }
                }
              }
            }
          }`,
            variables: {
              id: `gid://shopify/Order/${normalOrderId}`,
            },
          }),
        },
      );

      const fulfillmentOrdersData = await getFulfillmentOrdersResponse.json();

      if (!fulfillmentOrdersData.data || !fulfillmentOrdersData.data.order) {
        throw new CustomError(
          'Order not found or does not have fulfillment orders.',
          400,
        );
      }

      const fulfillmentOrders =
        fulfillmentOrdersData.data.order.fulfillmentOrders.edges;

      if (fulfillmentOrders.length === 0) {
        throw new CustomError(
          'No fulfillment orders found for this order.',
          400,
        );
      }

      const fulfillmentOrderId = fulfillmentOrders[0].node.id;
      console.log('Fulfillment Order ID:', fulfillmentOrderId);

      console.log('Holding fulfillment order:', fulfillmentOrderId);

      const holdFulfillmentOrderResponse = await fetch(
        `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          },
          body: JSON.stringify({
            query: `mutation fulfillmentOrderHold($id: ID!, $fulfillmentHold: FulfillmentOrderHoldInput!) {
            fulfillmentOrderHold(id: $id, fulfillmentHold: $fulfillmentHold) {
              fulfillmentOrder {
                id
                status
              }
              userErrors {
                field
                message
              }
            }
          }`,
            variables: {
              id: fulfillmentOrderId,
              fulfillmentHold: {
                reason: 'OTHER',
                reasonNotes: reason,
              },
            },
          }),
        },
      );

      const holdResult = await holdFulfillmentOrderResponse.json();

      if (holdResult.data.fulfillmentOrderHold.userErrors.length > 0) {
        throw new CustomError(
          JSON.stringify(holdResult.data.fulfillmentOrderHold.userErrors),
          400,
        );
      }

      console.log(
        'Fulfillment Order successfully put on hold:',
        holdResult.data.fulfillmentOrderHold.fulfillmentOrder,
      );

      return holdResult.data.fulfillmentOrderHold.fulfillmentOrder;
    } catch (error) {
      console.error('Error in holdOrder:', error.message);
      throw new CustomError(
        `Failed to hold fulfillment order: ${error.message}`,
        400,
      );
    }
  }

  async confirmBookingOrder(order: OrderData) {
    const {
      transactions,
      createdAt,
      finalDiscountedAmount,
      quotationId,
      shippingAddress,
      deliveryCharge,
      additionalPromotionalCoupons,
      productFinalAmount,
    } = order;
    const { transactionDetails } = transactions;
    const paymentHelpers = new PaymentHelpers(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    transactions.transactionDetails = await paymentHelpers.updateAllPayments({
      transactionDetails,
      createdAt,
      shopifyCreatedAt: moment().toISOString(),
      shopifyOrderId: '-',
      shopifyOrderName: '-',
      orderStatus: OrderStatus.CONFIRMED,
      finalDiscountedAmount,
    });

    const ORDER_TABLE = await this.configParameters.getOrderTableName();

    const command = new UpdateCommand({
      TableName: ORDER_TABLE,
      Key: { id: order.id },
      UpdateExpression:
        'SET shopifyOrderId = :shopifyOrderId, #status = :status, shopifyOrderStatus = :shopifyOrderStatus, shopifyPaymentStatus = :shopifyPaymentStatus, shopifyShipmentStatus = :shopifyShipmentStatus, shopifyOrderName = :shopifyOrderName, shopifyDiscountType = :shopifyDiscountType, shopifyDiscountCode = :shopifyDiscountCode, transactions = :transactions, shopifyCreatedAt = :shopifyCreatedAt, storeId = :storeId, updatedAt = :updatedAt, isCreatedOnEE = :isCreatedOnEE',
      ExpressionAttributeNames: {
        '#status': 'status',
      },
      ExpressionAttributeValues: {
        ':shopifyOrderId': '-',
        ':shopifyOrderName': '-',
        ':shopifyOrderStatus': '-',
        ':shopifyPaymentStatus': '-',
        ':shopifyShipmentStatus': '-',
        ':shopifyDiscountCode': '-',
        ':shopifyDiscountType': '-',
        ':transactions': transactions,
        ':status': OrderStatus.CONFIRMED,
        ':updatedAt': moment().toISOString(),
        ':shopifyCreatedAt': moment().toISOString(),
        ':storeId': order.storeId,
        ':isCreatedOnEE': false,
      },
      ConditionExpression: 'attribute_exists(id)',
      ReturnValues: 'ALL_NEW',
    });

    const { Attributes } = await this.docClient.updateItem(command);

    const orderChangeLog = new OrderChangeLog(
      this.configParameters,
      this.docClient,
    );
    orderChangeLog.log(Attributes);

    const quotationHandler = new UpdateQuotation(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
      this.shopifyClient,
    );

    await quotationHandler.updateQuotation(quotationId, {
      id: quotationId,
      status: QuotationStatus.PRICE_LOCKED,
      expiresAt: moment().add(1, 'year').toISOString(),
      bookingAmountStatus: PaymentStatus.PAID,
      shippingAddress,
      deliveryCharge,
      additionalPromotionalCoupons,
      finalDiscountedAmount: productFinalAmount,
      orderId: null,
      checkShopifyInventory: false,
    });

    return order;
  }

  async finalOrderConfirm(
    orderId: string,
    storeId: string,
  ): Promise<OrderData> {
    try {
      posLogger.info(
        'order',
        'finalOrderConfirm ',
        `started ${orderId} ${storeId}`,
      );
      const getOrderHandler = new GetOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const order: OrderData = await getOrderHandler.getOrder(orderId);

      if (order?.status === OrderStatus.SENT_TO_SHOPIFY) return order;

      const {
        id,
        createdAt,
        customerId,
        employeeId,
        finalDiscountedAmount,
        orderProducts,
        totalAmount,
        customer,
        promotionalCode,
        campaignCode,
        customCode,
        promotionalDiscountAmount,
        campaignDiscountAmount,
        customDiscountAmount,
        transactions,
        interiorArchitecture,
        gstDetails,
        billingAddress,
        shippingAddress,
        notes,
        source,
        holdOrder,
        quotationId,
        customerAcknowledgement,
        customerIdentityProof,
        deliveryDate,
        deliveryStatus,
        minDeliveryDate,
        unHoldFutureDispatchDate = null,
        holdOrderReason = '',
        additionalPromotionalCoupons,
        deliveryCharge,
        superOrderId,
        subOrderType,
        dispatchDate,
        eddHeaderId,
        posFallbackEdd,
      } = order;

      const isEliteSofaOrder = notes && notes.includes('Elite Sofa');

      const { transactionDetails, totalPaidAmount } = transactions ?? {};
      const firstTxnStoreId = transactionDetails?.[0]?.storeId;
      if (firstTxnStoreId !== storeId) {
        throw new CustomError(
          'Store ID mismatch. First payment store ID: ' +
            firstTxnStoreId +
            ', Current store ID: ' +
            storeId,
          400,
        );
      }
      let couponName = '_';

      const isBNPL = subOrderType && subOrderType.length;
      const isBNPLPriceLockOrder =
        isBNPL && subOrderType === SubOrderType.LOCK_PRICE;

      if (totalPaidAmount !== finalDiscountedAmount) {
        throw new CustomError(
          `Order amount ${finalDiscountedAmount} and paid amount ${totalPaidAmount} does not match!`,
          400,
        );
      }

      console.log('=======Final Order Confirm======', order);

      if (
        deliveryStatus === DeliveryType.CASH_AND_CARRY &&
        gstDetails &&
        gstDetails.gstNumber
      ) {
        console.log(
          '=====calling generateEInvoiceForGstOrders function ======',
        );
        const eInvoiceHandler = new EInvoiceService(
          new EInvoice(this.configService, this.ssmClient),
          new PincodesService(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
          ),
          this.docClient,
          this.configParameters,
        );

        const irnResponse =
          await eInvoiceHandler.generateEInvoiceForGstOrders(order);
        if (!irnResponse.success) {
          posLogger.error(
            'order',
            'finalOrderConfirm ',
            `failed to generate IRN for the order ${orderId} ${storeId}, ${irnResponse}`,
          );
          throw new CustomError(
            `Failed to generate IRN for the order, ${irnResponse}`,
            400,
          );
        }
      }

      if (isBNPLPriceLockOrder) {
        return await this.confirmBookingOrder(order);
      }

      const getStorehandler = new GetStore(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const store: StoreData = await getStorehandler.getStore(storeId);

      const getEmployeehandler = new GetEmployee(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const employee: EmployeeData =
        await getEmployeehandler.getEmployee(employeeId);

      const note_attributes = [
        {
          key: 'Store Name',
          value: store?.sapLocationCode ?? '',
        },
        {
          key: 'Store Code',
          value: store?.id ?? '',
        },
        {
          key: 'orderID',
          value: id,
        },
        {
          key: 'invoice no',
          value: id,
        },
        {
          key: 'invoice date',
          value: moment(createdAt)
            .utcOffset('+05:30')
            .format('DD/MM/yyyy HH:mm'),
        },
        {
          key: 'Employee Id',
          value: employeeId ?? '',
        },
        {
          key: 'Employee Name',
          value: `${employee?.firstName ?? ''} ${employee?.lastName ?? ''}`,
        },
        {
          key: 'Acknowledge File uploaded',
          value: `${customerAcknowledgement ? `Yes` : `No`}`,
        },
        {
          key: 'customer Identity proof uploaded',
          value: `${customerIdentityProof ? `Yes` : `No`}`,
        },
      ];

      if (
        ((holdOrder && deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE) ||
          (!holdOrder && deliveryStatus !== DeliveryType.CASH_AND_CARRY)) &&
        dispatchDate
      ) {
        note_attributes.push({
          key: 'Dispatch Date',
          value: `${moment
            .utc(dispatchDate)
            .tz('Asia/Kolkata')
            .format('DD-MM-YYYY')}`,
        });
      }

      if (!holdOrder && eddHeaderId) {
        note_attributes.push({
          key: 'EDD Header ID',
          value: `${eddHeaderId}`,
        });
      }
      if (!holdOrder && posFallbackEdd) {
        note_attributes.push({
          key: 'POS Fallback EDD',
          value: 'TRUE',
        });
      }

      const deliveryType = await this.getDeliveryType(order);
      if (deliveryType) {
        note_attributes.push({
          key: 'Delivery Types',
          value: `${deliveryType}`,
        });
      }

      if (customerIdentityProof?.length > 0) {
        note_attributes.push({
          key: 'Customer Attachment',
          value: `${customerIdentityProof}`,
        });
      }

      let tags = `${store?.id || ''}, ${store?.sapLocationCode || ''}, Retailstore, ${store?.asm || ''}, ${store?.zonalHead || ''}`;

      if (
        ((holdOrder && deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE) ||
          (!holdOrder && deliveryStatus !== DeliveryType.CASH_AND_CARRY)) &&
        dispatchDate
      ) {
        tags += `, Order Dispatch Date ${moment
          .utc(dispatchDate)
          .tz('Asia/Kolkata')
          .format('Do MMMM YYYY')}`;
      }

      if (!!holdOrder) tags += `, Hold Order , ${holdOrderReason}`;
      if (
        (!holdOrder ||
          (holdOrder &&
            order.deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE)) &&
        deliveryStatus !== DeliveryType.CASH_AND_CARRY
      ) {
        tags += `, POSMIN ${moment
          .utc(minDeliveryDate)
          .tz('Asia/Kolkata')
          .format('Do MMMM YYYY')}`;
        if (!isEliteSofaOrder) {
          tags += `, POSMAX ${moment
            .utc(deliveryDate)
            .tz('Asia/Kolkata')
            .format('Do MMMM YYYY')}`;
        }
        if (isEliteSofaOrder) {
          tags += `, POSMAX `;
        }
      }

      if (
        (!holdOrder ||
          (holdOrder &&
            order.deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE)) &&
        deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE &&
        unHoldFutureDispatchDate
      ) {
        tags += `, UNHOLD DATE ${moment
          .utc(unHoldFutureDispatchDate)
          .tz('Asia/Kolkata')
          .format('Do MMMM YYYY')}`;

        note_attributes.push({
          key: 'Unhold Date',
          value: `${moment
            .utc(unHoldFutureDispatchDate)
            .tz('Asia/Kolkata')
            .format('DD-MM-YYYY')}`,
        });
      }

      if (source) {
        tags += `, Source - ${source}`;
      }

      if (deliveryStatus === DeliveryType.BOOK_NOW_PAY_LATER) {
        tags += `, ${deliveryStatus}`;
      }

      if (isBNPL && superOrderId) {
        tags += `, Super Order - ${superOrderId}`;
      }

      console.log('tsgs', tags);

      if (interiorArchitecture && interiorArchitecture.id) {
        note_attributes.push(
          {
            key: 'Interior Architect Name',
            value: interiorArchitecture?.name ?? '',
          },
          {
            key: 'Interior Architect Unique no',
            value: interiorArchitecture?.id ?? '',
          },
          {
            key: 'Interior Architect Source',
            value: interiorArchitecture?.source ?? '',
          },
          {
            key: 'Interior Architect Commissioned',
            value: `${!!interiorArchitecture?.commissioned}`,
          },
        );
        tags += `, Interior Architecture, ${interiorArchitecture?.name ?? ''}, ${interiorArchitecture?.id ?? ''}, ${interiorArchitecture?.source ?? ''}, commissioned-${!!interiorArchitecture?.commissioned}`;
      }

      if (promotionalCode) {
        note_attributes.push(
          {
            key: 'Promotional code',
            value: promotionalCode,
          },
          {
            key: 'Promotional code Discount',
            value: `${promotionalDiscountAmount}`,
          },
        );
        couponName += `${promotionalCode}_`;
      }

      if (
        additionalPromotionalCoupons &&
        additionalPromotionalCoupons.length > 0
      ) {
        const DIYINSTALL: AdditionalPromotionalCouponInput =
          additionalPromotionalCoupons?.find(
            (coupon) => coupon?.promotionalCode === 'DIYINSTALL',
          );
        if (DIYINSTALL) {
          tags += `, DIYINSTALL`;
        }

        additionalPromotionalCoupons.forEach((coupon) => {
          note_attributes.push(
            {
              key: 'Promotional code',
              value: coupon?.promotionalCode,
            },
            {
              key: 'Promotional code Discount',
              value: `${coupon?.promotionalDiscountAmount}`,
            },
          );
          couponName += `${coupon?.promotionalCode}_`;
        });
      }

      if (campaignCode) {
        note_attributes.push(
          {
            key: 'Campaign code',
            value: campaignCode,
          },
          {
            key: 'Campaign code Discount',
            value: `${campaignDiscountAmount}`,
          },
        );
        couponName += `${campaignCode}_`;
      }
      if (deliveryCharge && deliveryCharge > 0) {
        note_attributes.push({
          key: 'Delivery Charge',
          value: `${deliveryCharge}`,
        });
      }

      if (customCode) {
        note_attributes.push(
          {
            key: 'Custom code',
            value: customCode?.value_type,
          },
          {
            key: 'Custom code Discount',
            value: `${customDiscountAmount}`,
          },
          {
            key: 'Discount Approved By [Employee name]',
            value: customCode?.approver ?? '',
          },
        );
        couponName += `custom_`;
      }

      if (subOrderType && subOrderType === SubOrderType.FINAL_ORDER) {
        couponName += `BookingAmount_`;
      }

      const couponNameLength = couponName.length - 1;
      couponName = couponName.substring(1, couponNameLength || 1);

      if (couponName) {
        order.shopifyDiscountCode = couponName;
        order.shopifyDiscountType = 'fixed_amount';
      }

      if (gstDetails) {
        note_attributes.push(
          {
            key: 'Company',
            value: gstDetails?.companyName ?? '',
          },
          {
            key: 'CustomerGSTIN',
            value: gstDetails?.gstNumber ?? '',
          },
        );
      }

      const getGlobalConfigurationHandler = new GetGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const { value: ON_HOLD_PRODUCT_IDS } =
        await getGlobalConfigurationHandler.getGlobalConfiguration(
          'ON_HOLD_PRODUCT_IDS',
        );
      const onHoldProductIds = ON_HOLD_PRODUCT_IDS?.split(',').map((item) =>
        item.trim(),
      );
      const isExistOnHoldProductIds = orderProducts?.some((item) =>
        onHoldProductIds?.includes(item?.productId),
      );
      const markOrderOnHold =
        deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE ||
        holdOrder ||
        isExistOnHoldProductIds;

      const totalDiscount =
        totalAmount + (deliveryCharge || 0) - finalDiscountedAmount;

      const lineItems = orderProducts.map(
        ({
          productId,
          variantId,
          price,
          title,
          quantity,
          deliveryStatus,
          deliveryDate,
          minDeliveryDate,
          dispatchDate,
          customData,
          fallbackEdd,
        }) => {
          const deliverType =
            deliveryStatus === DeliveryType.CASH_AND_CARRY ||
            storeId.toUpperCase() === 'TEST-STORE'
              ? 'CASH_AND_CARRY'
              : deliveryStatus === DeliveryType.WEBSITE_DELIVERY_DATE
                ? 'STANDARD'
                : 'FUTURE';

          // Add delivery type and date to note_attributes
          note_attributes.push({
            key: `${variantId} - Delivery Type`,
            value: deliverType,
          });

          if (
            (holdOrder &&
              deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE) ||
            (!holdOrder && deliveryStatus !== DeliveryType.CASH_AND_CARRY)
          ) {
            note_attributes.push(
              {
                key: `${variantId} - Min Delivery Date`,
                value:
                  moment
                    .utc(minDeliveryDate)
                    .tz('Asia/Kolkata')
                    .format('DD-MM-YYYY') || '-',
              },
              {
                key: `${variantId} - Max Delivery Date`,
                value:
                  moment
                    .utc(deliveryDate)
                    .tz('Asia/Kolkata')
                    .format('DD-MM-YYYY') || '-',
              },
            );
          }

          if (
            ((holdOrder &&
              deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE) ||
              (!holdOrder && deliveryStatus !== DeliveryType.CASH_AND_CARRY)) &&
            dispatchDate
          ) {
            note_attributes.push({
              key: `${variantId} - Dispatch Date`,
              value: moment(dispatchDate)
                .utc()
                .tz('Asia/Kolkata')
                .format('DD-MM-YYYY'),
            });
          }

          if (!holdOrder && fallbackEdd) {
            note_attributes.push({
              key: `${variantId} - Fallback EDD`,
              value: 'TRUE',
            });
          }

          if (deliveryStatus === DeliveryType.CASH_AND_CARRY) {
            tags += `, CashAndCarry`;
          } else if (deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE) {
            if (!isEliteSofaOrder) {
              tags += `, Future Delivery`;
            }
            note_attributes.push({
              key: `Delivery Type`,
              value: 'Future Delivery Date',
            });
            if (
              !holdOrder ||
              (holdOrder &&
                deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE)
            ) {
              note_attributes.push({
                key: `Post Delivery Date`,
                value: deliveryDate,
              });
            }
          }
          if (!holdOrder && deliverType !== DeliveryType.CASH_AND_CARRY) {
            tags += `, ${deliverType}-${variantId}`;
          }
          const lineItem = {
            variantId: `gid://shopify/ProductVariant/${variantId}`,
            title: title,
            quantity,
            priceSet: {
              shopMoney: {
                amount: Number(price).toFixed(2),
                currencyCode: 'INR',
              },
              presentmentMoney: {
                amount: Number(price).toFixed(2),
                currencyCode: 'INR',
              },
            },
            properties: [],
          };

          if (customData) {
            if (customData?.length) {
              lineItem.properties.push({
                name: 'LENGTH',
                value: customData.length.toString(),
              });
            }

            if (customData?.breadth) {
              lineItem.properties.push({
                name: 'BREADTH',
                value: customData.breadth.toString(),
              });
            }

            if (customData?.height) {
              lineItem.properties.push({
                name: 'HEIGHT',
                value: customData.height.toString(),
              });
            }
          }

          return lineItem;
        },
      );

      let totalAmountPaid = 0;
      let completedTransactionIndex = 0;

      transactionDetails?.forEach(
        ({
          status,
          externalRefId,
          transactionId,
          mode,
          transactionAmount,
          paymentID,
          amountPaid,
          paymentMethod,
          transactionCompletedDate,
          ...rest
        }) => {
          if (status === 'COMPLETED') {
            completedTransactionIndex++;

            const transactionType = getUnifiedPaymentMode(paymentMethod);
            note_attributes.push(
              {
                key: `TransactionID ${completedTransactionIndex}`,
                value: transactionId,
              },
              {
                key: `Transaction Mode ${completedTransactionIndex}`,
                value: mode,
              },
              {
                key: `Transaction Amount ${completedTransactionIndex}`,
                value: `${transactionAmount}`,
              },
            );
            if (transactionCompletedDate) {
              const formattedDate = moment(transactionCompletedDate).format(
                'DD-MM-YYYY HH:mm:ss',
              );

              note_attributes.push({
                key: `Transaction Completed Date ${completedTransactionIndex}`,
                value: formattedDate,
              });
            }

            const fieldMappings = [
              { key: 'UPI ID', value: rest.upiId },
              { key: 'RRN', value: rest.rrn },
              { key: 'Bank Name', value: rest.bankName },
            ];

            fieldMappings.forEach(({ key, value }) => {
              if (value != null && value !== '') {
                note_attributes.push({
                  key: `${key} ${completedTransactionIndex}`,
                  value,
                });
              }
            });

            if (rest.cardDetails) {
              const {
                cardHolderName,
                cardType,
                emiEligible,
                international,
                issuer,
                last4Digits,
                network,
              } = rest.cardDetails;
              const cardDetails = [];
              if (cardHolderName)
                cardDetails.push(`CardholderName: ${cardHolderName}`);
              if (cardType) cardDetails.push(`CardType: ${cardType}`);
              cardDetails.push(
                `EmiEligible: ${emiEligible === 'true' ? 'Yes' : 'No'}`,
              );
              cardDetails.push(
                `International: ${international === 'true' ? 'Yes' : 'No'}`,
              );
              if (issuer) cardDetails.push(`Issuer: ${issuer}`);
              if (last4Digits)
                cardDetails.push(`Last 4 Digits: ${last4Digits}`);
              if (network) cardDetails.push(`Network: ${network}`);

              note_attributes.push({
                key: `Card Details ${completedTransactionIndex}`,
                value: cardDetails.join('\n'),
              });
            }

            if (
              [
                PaymentMode.RAZORPAY,
                PaymentMode.RAZORPAY_POS,
                PaymentMode.PINELABS,
                PaymentMode.SNAPMINT,
                PaymentMode.PAYU,
              ].includes(mode) &&
              paymentID
            ) {
              note_attributes.push({
                key: `Payment ID ${completedTransactionIndex}`,
                value: paymentID.toString(),
              });
            }

            if (transactionType) {
              note_attributes.push({
                key: `Transaction Type ${completedTransactionIndex}`,
                value: transactionType,
              });
            }
            if (paymentID || externalRefId) {
              if (mode === PaymentMode.RAZORPAY) {
                note_attributes.push({
                  key: `razorpay_payment_id ${completedTransactionIndex}`,
                  value: paymentID ?? externalRefId ?? '',
                });
              }
              tags += `,${paymentID ?? externalRefId}`;
            }

            totalAmountPaid += amountPaid
              ? Number(amountPaid)
              : Number(transactionAmount);
          }
        },
      );

      note_attributes.push(
        {
          key: 'Total Amount Paid',
          value: `${totalAmountPaid}`,
        },
        {
          key: 'Total Bank Discount',
          value: `${finalDiscountedAmount - totalAmountPaid}`,
        },
      );

      const [SHOPIFY_ACCESS_TOKEN, SHOPIFY_ADMIN_BASE_URL, ORDER_TABLE] =
        await Promise.all([
          this.shopifyClient.getShopifyAccessToken(),
          this.shopifyClient.getShopifyAdminBaseUrl(),
          this.configParameters.getOrderTableName(),
        ]);

      let shopifyCustomerId = `gid://shopify/Customer/${customerId}`;
      const customerQuery = `
        query getCustomerByEmail($email: String!) {
          customers(first: 1, query: $email) {
            edges {
              node {
                id
                email
              }
            }
          }
        }
      `;

      const customerResponse = await fetch(
        `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`,
        {
          method: 'POST',
          headers: {
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: customerQuery,
            variables: { email: customer?.email },
          }),
        },
      );

      const customerResult = await customerResponse.json();

      if (customerResult?.data?.customers?.edges?.length > 0) {
        // Customer exists, use their Shopify customer ID
        shopifyCustomerId = customerResult?.data?.customers?.edges[0]?.node?.id;
      } else {
        const customerCreateMutation = `
            mutation customerCreate($input: CustomerCreateInput!) {
              customerCreate(input: $input) {
                customer {
                  firstName
                  lastName
                  email
                  phone
                }
                userErrors {
                  field
                  message
                }
              }
            }
          `;

        const customerCreateResponse = await fetch(
          `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`,
          {
            method: 'POST',
            headers: {
              'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: customerCreateMutation,
              variables: {
                input: {
                  firstName: customer?.firstName,
                  lastName: customer?.lastName,
                  email: customer?.email,
                  phone: customer?.phone,
                },
              },
            }),
          },
        );

        const customerCreateResult = await customerCreateResponse.json();

        if (
          customerCreateResult?.data?.customerCreate?.userErrors?.length > 0
        ) {
          throw new CustomError(
            JSON.stringify(
              customerCreateResult?.data?.customerCreate?.userErrors,
            ),
            400,
          );
        }

        shopifyCustomerId =
          customerCreateResult?.data?.customerCreate?.customer?.id;
      }

      console.log(note_attributes, unHoldFutureDispatchDate);
      // throw new CustomError('Note attributes', 500);
      // Construct orderInput for GraphQL orderCreate mutation

      const orderInput: any = {
        customer: {
          toUpsert: {
            id: shopifyCustomerId,
            email: customer?.email,
          },
        },
        shippingAddress: {
          address1: shippingAddress?.line1,
          address2: shippingAddress?.line2 ?? '',
          firstName: customer?.firstName,
          lastName: customer?.lastName,
          city: shippingAddress?.city,
          province: shippingAddress?.state,
          countryCode: 'IN',
          zip: shippingAddress?.pinCode,
          phone: customer?.phone,
        },
        billingAddress: {
          address1: billingAddress?.line1,
          address2: billingAddress?.line2 ?? '',
          firstName: customer?.firstName,
          lastName: customer?.lastName,
          city: billingAddress?.city,
          province: billingAddress?.state,
          countryCode: 'IN',
          zip: billingAddress?.pinCode,
          phone: customer?.phone,
        },
        lineItems,
        taxLines: [
          {
            title: 'SGST',
            rate: 0.18,
            priceSet: {
              shopMoney: {
                amount: (Number(finalDiscountedAmount) * 0.18).toFixed(2),
                currencyCode: 'INR',
              },
              presentmentMoney: {
                amount: (Number(finalDiscountedAmount) * 0.18).toFixed(2),
                currencyCode: 'INR',
              },
            },
          },
        ],
        shippingLines: [
          {
            priceSet: {
              shopMoney: {
                amount: deliveryCharge ? deliveryCharge?.toFixed(2) : '00.00',
                currencyCode: 'INR',
              },
            },
            title: 'Delivery Charge',
          },
        ],
        discountCode:
          couponName && totalDiscount > 0
            ? {
                itemFixedDiscountCode: {
                  amountSet: {
                    shopMoney: {
                      amount: Number(totalDiscount).toFixed(2),
                      currencyCode: 'INR',
                    },
                    presentmentMoney: {
                      amount: Number(totalDiscount).toFixed(2),
                      currencyCode: 'INR',
                    },
                  },
                  code: couponName,
                },
              }
            : null,
        transactions: [
          {
            kind: 'SALE',
            status: 'SUCCESS',
            amountSet: {
              shopMoney: {
                amount: Number(finalDiscountedAmount).toFixed(2),
                currencyCode: 'INR',
              },
              presentmentMoney: {
                amount: Number(finalDiscountedAmount).toFixed(2),
                currencyCode: 'INR',
              },
            },
            gateway: 'manual',
          },
        ],
        fulfillmentStatus: null,
        financialStatus: 'PAID',
        email: customer?.email,
        phone: customer?.phone,
        tags: tags
          .replaceAll(new RegExp('[^A-Za-z0-9-,_ ]+', 'g'), '')
          .split(', ')
          .map((tag) => tag.trim()),
        customAttributes: note_attributes,
        note: notes,
        sourceName: 'Retailstore',
        taxesIncluded: true,
      };

      if (
        deliveryStatus === DeliveryType.CASH_AND_CARRY ||
        // deliveryStatus === DeliveryType.BOOK_NOW_PAY_LATER ||
        storeId.toUpperCase() === 'TEST-STORE'
      ) {
        orderInput.fulfillmentStatus = 'FULFILLED';
      }

      console.log(
        '=======orderInput=======',
        JSON.stringify(orderInput, null, 2),
      );

      const orderCreateMutation = `
        mutation orderCreate($order: OrderCreateOrderInput!) {
          orderCreate(order: $order) {
            order {
              id
              name
              createdAt
              displayFinancialStatus
              displayFulfillmentStatus
              shippingAddress {
                address1
                address2
                firstName
                lastName
                city
                province
                countryCode
                zip
                phone
              }
              billingAddress {
                address1
                address2
                firstName
                lastName
                city
                province
                countryCode
                zip
                phone
              }
              totalPriceSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
              customAttributes {
                key
                value
              }
              customer {
                id
                email
              }
              totalDiscountsSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
              discountCodes
              discountApplications(first: 5) {
                nodes {
                  value {
                    ... on MoneyV2 {
                      amount
                      currencyCode
                    }
                    ... on PricingPercentageValue {
                      percentage
                    }
                  }
                }
              }
              cancelledAt
              totalTaxSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
              lineItems(first: 10) {
                nodes {
                  id
                  title
                  quantity
                  variant {
                    id
                  }
                }
              }
              taxLines {
                title
                rate
                priceSet {
                  shopMoney {
                    amount
                    currencyCode
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      let graphqlEndpoint;
      if (SHOPIFY_ADMIN_BASE_URL.includes('/admin/api/')) {
        const shopBaseUrl = SHOPIFY_ADMIN_BASE_URL.split('/admin/api/')[0];
        graphqlEndpoint = `${shopBaseUrl}/admin/api/2025-04/graphql.json`;
      } else {
        graphqlEndpoint = `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`;
      }

      const orderCreateResponse = await fetch(graphqlEndpoint, {
        method: 'POST',
        headers: {
          'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: orderCreateMutation,
          variables: { order: orderInput },
        }),
      });

      const orderCreateResult = await orderCreateResponse.json();

      if (
        orderCreateResult?.errors ||
        orderCreateResult?.data?.orderCreate?.userErrors?.length > 0
      ) {
        throw new CustomError(
          JSON.stringify(
            orderCreateResult.errors ||
              orderCreateResult.data.orderCreate.userErrors,
          ),
          400,
          'Shopify',
        );
      }

      const shopifyOrder = orderCreateResult?.data?.orderCreate?.order;

      order.shopifyOrderId = shopifyOrder?.id?.split('/')?.pop();
      order.shopifyOrderName = shopifyOrder?.name?.substring(1);
      order.shopifyOrderStatus = shopifyOrder?.cancelledAt
        ? 'Cancelled'
        : 'Confirmed';
      order.shopifyPaymentStatus = shopifyOrder?.displayFinancialStatus;
      order.shopifyShipmentStatus =
        shopifyOrder?.displayFulfillmentStatus ?? 'undelivered';
      order.shopifyDiscountAmount = `${totalAmount - finalDiscountedAmount}`;
      order.shopifyCreatedAt = moment(shopifyOrder?.createdAt)?.toISOString();

      try {
        const {
          serviceLift,
          accommodationType,
          landmark,
          latitude,
          longitude,
        } = customer ?? {};

        await this.sendAdditionalShippingData(order?.shopifyOrderName, {
          serviceLift,
          accommodationType,
          landmark,
          latitude,
          longitude,
        });
      } catch (error) {
        posLogger.error(
          'finalOrderConfirm',
          'sendAdditionalShippingData',
          `Failed to send additional shipping data for order ${orderId} ${storeId}, ${error}`,
        );
      }

      const paymentHelpers = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      transactions.transactionDetails = await paymentHelpers.updateAllPayments({
        transactionDetails,
        shopifyOrderId: order?.shopifyOrderId,
        shopifyOrderName: order?.shopifyOrderName,
        createdAt,
        shopifyCreatedAt: order?.shopifyCreatedAt,
        orderStatus: OrderStatus.SENT_TO_SHOPIFY,
        finalDiscountedAmount,
      });

      const { value: INVENTORY } =
        await getGlobalConfigurationHandler.getGlobalConfiguration('INVENTORY');
      const enabledInventoryStores = INVENTORY?.split(',').map((item) =>
        item.trim(),
      );

      let isCreatedOnEE = false;
      if (
        deliveryStatus === DeliveryType.CASH_AND_CARRY &&
        (!INVENTORY || (INVENTORY && enabledInventoryStores?.includes(storeId)))
      ) {
        isCreatedOnEE = true;
      }
      console.log('gb', INVENTORY, enabledInventoryStores, isCreatedOnEE);

      const command = new UpdateCommand({
        TableName: ORDER_TABLE,
        Key: { id },
        UpdateExpression:
          'SET shopifyOrderId = :shopifyOrderId, #status = :status, shopifyOrderStatus = :shopifyOrderStatus, shopifyPaymentStatus = :shopifyPaymentStatus, shopifyShipmentStatus = :shopifyShipmentStatus, shopifyOrderName = :shopifyOrderName, shopifyDiscountType = :shopifyDiscountType, shopifyDiscountCode = :shopifyDiscountCode, transactions = :transactions, shopifyCreatedAt = :shopifyCreatedAt, storeId = :storeId, updatedAt = :updatedAt, isCreatedOnEE = :isCreatedOnEE',
        ExpressionAttributeNames: {
          '#status': 'status',
        },
        ExpressionAttributeValues: {
          ':shopifyOrderId': order?.shopifyOrderId,
          ':shopifyOrderName': order?.shopifyOrderName,
          ':shopifyOrderStatus': order?.shopifyOrderStatus,
          ':shopifyPaymentStatus': order?.shopifyPaymentStatus,
          ':shopifyShipmentStatus': order?.shopifyShipmentStatus,
          ':shopifyDiscountCode': order?.shopifyDiscountCode ?? null,
          ':shopifyDiscountType': order?.shopifyDiscountType ?? null,
          ':transactions': transactions,
          ':status': OrderStatus.SENT_TO_SHOPIFY,
          ':updatedAt': moment().toISOString(),
          ':shopifyCreatedAt': order?.shopifyCreatedAt,
          ':storeId': storeId,
          ':isCreatedOnEE': isCreatedOnEE,
        },
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes } = await this.docClient.updateItem(command);

      const orderChangeLog = new OrderChangeLog(
        this.configParameters,
        this.docClient,
      );
      orderChangeLog.log(Attributes);

      const quotationHandler = new UpdateQuotationStatus(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      await quotationHandler.updateQuotationStatus(
        quotationId,
        QuotationStatus.CONVERTED,
        orderId,
      );

      const getHandler = new GetQuotation(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const quotation = await getHandler.getQuotation(quotationId);
      if (
        quotation?.customDiscountVerificationDetails
          ?.customDiscountApprovalStatus ===
        CustomDiscountApprovalStatus.APPROVED
      ) {
        const updateCustomDiscountStatusHandler =
          new UpdateCustomDiscountStatus(this.docClient, this.configParameters);

        await updateCustomDiscountStatusHandler.updateCustomDiscountStatus(
          quotationId,
          quotation?.customDiscountVerificationDetails?.id,
          CustomDiscountApprovalStatus.APPLIED,
          order?.shopifyOrderName,
        );
      }

      if (markOrderOnHold) {
        console.log('Holding order:', order?.shopifyOrderId, holdOrderReason);
        const reasonNotes =
          deliveryStatus === DeliveryType.FUTURE_DISPATCH_DATE
            ? 'Order is marked as Future Delivery.'
            : holdOrder
              ? `Hold Order: ${holdOrderReason ?? ''}`
              : isExistOnHoldProductIds
                ? `Hold Order: Product is on hold.`
                : 'HOLD_ORDER';
        console.log('reasonNotes', reasonNotes);

        // await this.holdOrder(
        //   order?.shopifyOrderId,
        //   SHOPIFY_ACCESS_TOKEN,
        //   SHOPIFY_ADMIN_BASE_URL,
        //   reasonNotes,
        // );
      }

      posLogger.info(
        'order',
        'finalOrderConfirm',
        `Finished ${order?.shopifyOrderId}`,
      );

      return order;
    } catch (error) {
      console.log('error confirm order :::::', orderId, JSON.stringify(error));
      posLogger.error('order', 'finalOrderConfirm ', { error });
      throw error;
    }
  }
}
