import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { ListOrderInput } from '../dto/list-order.input';
import { filterFormatter } from 'src/common/helper/filter-helper';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import moment from 'moment-timezone';
import { OrderData } from '../entities/order.entity';
import { json2csv } from 'json-2-csv';
import { ExportPayments } from 'src/payments/lib/export-payment';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { TemplateType } from 'src/common/enum/template-type';
import { AppConfigParameters } from 'src/config/config';
import { ExportProducts } from './export-products';
import {
  OrderFilterBy,
  OrderStatus,
  OrderType,
  SubOrderType,
} from 'src/common/enum/order';
import { BatchGetCommand } from '@aws-sdk/lib-dynamodb';

type ExtendedOrderData = OrderData & {
  POSBookingOrderId?: string;
  bookingAmountStatus?: string;
  dateOfBooking?: string;
  tentativePurchaseDate?: string;
  productShopifyOrderId?: string;
  POSOrderId?: string;
  bookingAmount?: string;
  remainingAdjustedAmount: string;
  totalAmount?: string;
  dateOfOrderConfirmation?: string;
};

export class ExportOrders {
  private esHandler: ElasticClient;
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
  ) {
    this.esHandler = new ElasticClient(this.configService, this.ssmClient);
  }

  chunkArray<T>(arr: T[], size: number): T[][] {
    return Array.from({ length: Math.ceil(arr.length / size) }, (_, i) =>
      arr.slice(i * size, i * size + size),
    );
  }

  async getAllSuperOrder(ids: string[]) {
    try {
      const ORDER_TABLE = await this.configParameters.getOrderTableName();
      const keys = Array.from(new Set(ids)).map((id) => ({ id }));
      const batchGetCommand = new BatchGetCommand({
        RequestItems: {
          [ORDER_TABLE]: {
            Keys: keys, // assumes PK = id
          },
        },
      });
      const response = await this.docClient.batchGetItems(batchGetCommand);
      return response.Responses?.[ORDER_TABLE] || [];
    } catch (error) {
      console.error('Error fetching super orders:', error);
      return [];
    }
  }

  async BNPLOrderExportData(orders: OrderData[]) {
    try {
      const ORDER_TABLE = await this.configParameters.getOrderTableName();

      const allSubOrderIds = [
        ...new Set(orders.flatMap((o) => o.subOrderIds || [])), // dedupe
      ];

      const subOrderChunks = this.chunkArray(allSubOrderIds, 100);
      const subOrderDataMap = {};

      for (const chunk of subOrderChunks) {
        const batchGetCommand = new BatchGetCommand({
          RequestItems: {
            [ORDER_TABLE]: {
              Keys: chunk.map((id) => ({ id })), // assumes PK = id
            },
          },
        });

        const response = await this.docClient.batchGetItems(batchGetCommand);
        const items = response.Responses?.[ORDER_TABLE] ?? [];

        for (const subOrder of items) {
          subOrderDataMap[subOrder.id] = subOrder;
        }
      }

      const finalBNPLOrderData = orders.map((order: any) => {
        const payload = { ...order };
        const bookingOrder = subOrderDataMap?.[order?.subOrderIds?.[0]];
        const productOrder = subOrderDataMap?.[order?.subOrderIds?.[1]];
        if (bookingOrder) {
          payload.POSBookingOrderId = bookingOrder?.id || '-';
          payload.bookingAmount = bookingOrder?.finalDiscountedAmount || '-';
          payload.bookingAmountStatus =
            bookingOrder?.status === OrderStatus.CONFIRMED ? 'PAID' : 'UNPAID';
          payload.dateOfBooking =
            bookingOrder?.status === OrderStatus.CONFIRMED
              ? moment(bookingOrder.updatedAt).format('DD/MM/yyyy')
              : '-';
          payload.tentativePurchaseDate =
            bookingOrder?.tentativePurchaseDate || '-';
        }
        if (productOrder) {
          payload.productShopifyOrderId = productOrder.shopifyOrderName;
          payload.POSOrderId = productOrder.id;
          payload.remainingAdjustedAmount =
            productOrder.finalDiscountedAmount || '-';
          payload.totalAmount =
            bookingOrder.finalDiscountedAmount +
              productOrder.finalDiscountedAmount || '-';
          payload.dateOfOrderConfirmation =
            bookingOrder?.status === OrderStatus.CONFIRMED ||
            bookingOrder?.status === OrderStatus.SENT_TO_SHOPIFY
              ? moment(bookingOrder.updatedAt).format('DD/MM/yyyy')
              : '-';
        }
        return payload;
      });

      return finalBNPLOrderData;
    } catch (error) {
      console.log(error, 'error while fetching BNPL orders');
      throw error;
    }
  }

  async exportOrderCSV(orders: ExtendedOrderData[], superOrders = []) {
    const data = orders.reduce((acc, order) => {
      const {
        id,
        orderType: type,
        customCode,
        customDiscountAmount,
        promotionalCode,
        promotionalDiscountAmount,
        campaignCode,
        campaignDiscountAmount,
        totalAmount,
        finalDiscountedAmount,
        createdAt,
        shopifyCreatedAt,
        status,
        customer,
        customerId,
        gstDetails,
        shopifyOrderId,
        storeId,
        notes,
        source,
        interiorArchitecture,
        shippingAddress,
        billingAddress,
        shopifyOrderName,
        loggedusername,
        employeeId,
        orderProducts,
        holdOrder,
        holdOrderReason = '',
        customerIdentityProof,
        additionalPromotionalCoupons = null,
        deliveryCharge,
        superOrderId,
        subOrderType,
      } = order;

      let superOrder;

      if (subOrderType && subOrderType === SubOrderType.FINAL_ORDER) {
        superOrder = superOrders?.find((order) => order.id === superOrderId);
      }

      const istOffsetTool = moment(createdAt)
        .utcOffset('+05:30')
        .format('DD/MM/yyyy HH:mm');
      const istOffsetShopify =
        subOrderType === SubOrderType.LOCK_PRICE
          ? '-'
          : moment(shopifyCreatedAt)
              .utcOffset('+05:30')
              .format('DD/MM/yyyy HH:mm');

      const deliveryDate = [],
        deliveryType = [],
        sku = [],
        isAssumed = [],
        productType = [],
        title = [],
        totalGST = [],
        finalNetPrice = [];

      (orderProducts || []).map(
        ({
          deliveryDate: dDate,
          deliveryStatus: dStatus,
          sku: dSku,
          isAssumed: dIsAssumed,
          title: dTitle,
          productType: dProductType,
          gstAmount: gstAmount,
          finalItemPriceWithoutGst: finalItemPriceWithoutGst,
        }) => {
          deliveryDate.push(
            moment(dDate).utcOffset('+05:30').format('DD/MM/yyyy'),
          );
          deliveryType.push(dStatus);
          productType.push(dProductType);
          title.push(dTitle);
          sku.push(dSku);
          isAssumed.push(dIsAssumed ? 'Yes' : 'No');
          totalGST.push(gstAmount);
          finalNetPrice.push(finalItemPriceWithoutGst);
        },
      );
      const DIYINSTALL_DISCOUNT =
        additionalPromotionalCoupons?.find(
          (item) => item?.promotionalCode === 'DIYINSTALL',
        )?.promotionalDiscountAmount || 0;
      let gstamount, netWithoutGst;
      const totalGSTAmount = totalGST.reduce((acc, curr) => acc + curr, 0) || 0;
      const totalNetWithoutGST =
        finalNetPrice.reduce((acc, curr) => acc + curr, 0) || 0;

      const totalCalculatedAmount = totalGSTAmount + totalNetWithoutGST;

      const diff = finalDiscountedAmount - totalCalculatedAmount;

      if (Math.abs(diff) > 0) {
        const adjustment = diff / 2;
        const adjustedGST = totalGSTAmount + adjustment;
        const adjustedNetWithoutGST = totalNetWithoutGST + adjustment;
        gstamount = adjustedGST;
        netWithoutGst = adjustedNetWithoutGST;
      } else {
        gstamount = totalGSTAmount;
        netWithoutGst = totalNetWithoutGST;
      }
      const payload = {
        'Order ID': shopifyOrderName || '-',
        orderNo: shopifyOrderId || '-',
        'Order Created by': loggedusername || '-',
        'order date on tool': istOffsetTool || '-',
        'invoice date': istOffsetTool || '-',
        'order date on shopify': istOffsetShopify || '-',
        customerphone: customer?.phone || '-',
        'customer name':
          `${customer?.firstName || ''} ${customer?.lastName || ''}` || '-',
        customeremail: customer?.email || '-',
        'Employee Id': employeeId || '-',
        'Shipping address':
          Object.values(shippingAddress || {}).join(', ') || '-',
        'Shipping Pincode': shippingAddress.pinCode || '-',
        'Billing address':
          Object.values(billingAddress || {}).join(', ') || '-',
        'Order status': status || '-',
        'Hold Order': holdOrder || false,
        'Hold Order Reason': holdOrderReason || '-',
        customCode: customCode
          ? `${customCode?.value_type || ''} ${customCode?.value || ''}`
          : '-',
        'Discount Approved By': customCode?.approver || '-',
        customDiscountAmount: customDiscountAmount || 0,
        promotionalCode: promotionalCode || '-',
        promotionalDiscountAmount: promotionalDiscountAmount || 0,
        campaignCode: campaignCode || '-',
        campaignDiscountAmount: campaignDiscountAmount || 0,
        DIYINSTALL: DIYINSTALL_DISCOUNT > 0 ? DIYINSTALL_DISCOUNT : '-',
        'Delivery Charge applied': deliveryCharge > 0 ? 'Yes' : 'No',
        'Delivery Charge': deliveryCharge > 0 ? deliveryCharge || '-' : '-',
        totalAmount: totalAmount || '-',
        'order value': finalDiscountedAmount || '-',
        'discount amount': totalAmount - finalDiscountedAmount || '-',
        'Shopify Customer ID': customerId || '-',
        'company name': gstDetails ? gstDetails.companyName : '-',
        gstnumber: gstDetails ? gstDetails.gstNumber : '-',
        'Pos Order ID': id || '-',
        'invoice no': id || '-',
        storeId: storeId || '-',
        notes: notes || '-',
        Source: source || '-',
        'Interior Architecture': interiorArchitecture
          ? JSON.stringify(interiorArchitecture || {})
          : '-',
        'Delivery Date': deliveryDate.join(', ') || '-',
        'Delivery type': deliveryType.join(', ') || '-',
        SKU: sku.join(', ') || '-',
        'Is Assumed': isAssumed.join(', ') || '-',
        'Product Title': title.join(', ') || '-',
        'Product Type': productType.join(', ') || '-',
        'Total GST': gstamount || '-',
        'Total Net Without GST': netWithoutGst || '-',
        'customer Attachment': customerIdentityProof || '-',
        'Super Order Id': superOrderId || '-',
        'Booking Order':
          subOrderType != null ? subOrderType === SubOrderType.LOCK_PRICE : '-',
        'Booking Order POS Id':
          subOrderType != null
            ? subOrderType === SubOrderType.FINAL_ORDER
              ? superOrder?.subOrderIds?.[0] || '-'
              : '-'
            : '-',
        'Booking amount':
          subOrderType != null
            ? subOrderType === SubOrderType.LOCK_PRICE
              ? finalDiscountedAmount
              : (order.bnplProduct?.price ?? '-')
            : '-',
        'Product total amount':
          subOrderType === SubOrderType.LOCK_PRICE
            ? order.productFinalAmount
            : finalDiscountedAmount,
        ...(type === OrderType.BNPL
          ? {
              'Booking Shopify Order Id': '-',
              'POS Booking Id': order.POSBookingOrderId || '-',
              'Booking Amount Status': order.bookingAmountStatus || '-',
              'Date of booking': order.dateOfBooking || '-',
              'Tentative Purchase Date':
                moment(order.tentativePurchaseDate).format('DD/MM/yyyy') || '-',
              'Product Shopify Order Id': order.productShopifyOrderId || '-',
              'POS Order Id': order.POSOrderId || '-',
              'Booking Amount': order.bookingAmount || '-',
              'Remaining Adjusted Amount': order.remainingAdjustedAmount || '-',
              'Total Amount (Booking + Product)': order.totalAmount || '-',
              'Date of confirmation': order.dateOfOrderConfirmation || '-',
            }
          : {}),
      };

      return [...acc, payload];
    }, []);

    // const processedData = data.map(replaceEmptyFields);

    const csvData = await json2csv(data, {});
    return csvData;

    // const csvKey = `private/exports/${new Date().valueOf()}-orders.csv`;

    // await s3
    //   .putObject({
    //     Bucket: S3_BUCKET,
    //     Key: csvKey,
    //     Body: csvData,
    //     ContentType: 'text/csv',
    //   })
    //   .promise();

    // return csvKey;
  }

  async query({
    index,
    limit: size = 100,
    page = 1,
    filter,
    sort,
    nextToken: nt,
  }) {
    let searchAfter;
    if (nt) {
      searchAfter = nt
        ? JSON.parse(Buffer.from(nt, 'base64').toString('ascii'))
        : undefined;
    }

    const searchParams = {
      index,
      size,
      from: (page - 1) * size,
      body: {
        version: false,
        track_total_hits: true,
        search_after: searchAfter,
        query: filter,
        sort: [...sort],
      },
    };

    // Executing the OpenSearch request
    const { body } = await this.esHandler.search(searchParams);

    const { hits } = body;
    const { hits: results = [], total } = hits;
    const lastResult = results[results.length - 1];
    const nextToken =
      lastResult && lastResult.sort
        ? Buffer.from(JSON.stringify(lastResult.sort), 'ascii').toString(
            'base64',
          )
        : null;

    return {
      page,
      pageSize: size,
      totalPages: Math.ceil(total.value / size),
      total: total.value,
      items: results.map(({ _source }) => _source),
      nextToken: nextToken,
    };
  }

  async queryAll({ index, filter, nextToken: nT }) {
    const sortItems = [{ createdAt: { order: 'desc' } }];

    const { items, nextToken } = await this.query({
      index,
      filter,
      sort: sortItems,
      nextToken: nT,
      limit: 9999,
    });

    if (nextToken) {
      const nextItems = await this.queryAll({
        index,
        filter,
        nextToken,
      });
      return [...items, ...nextItems];
    }

    return items;
  }
  async queryPaymentDetailsByOrderId(orderId, index) {
    const params = {
      index: index,
      body: {
        query: {
          match_phrase_prefix: {
            orderId: orderId,
          },
        },
      },
    };

    try {
      const response = await this.esHandler.search(params);
      const paymentDetails = response.body?.hits.hits?.map(
        (hit) => hit._source,
      );
      return paymentDetails || [];
    } catch (error) {
      console.error(
        `Error querying payment details for orderId ${orderId}:`,
        error,
      );
      return [];
    }
  }

  async fetchTransactionDetailsFromPayments(orderId, index) {
    const paymentDetails = await this.queryPaymentDetailsByOrderId(
      orderId,
      index,
    );
    return paymentDetails || [];
  }

  async exportOrders(
    email: string,
    filter: ListOrderInput,
    exportTypes: string[],
  ) {
    const ORDER_TABLE = await this.configParameters.getOrderTableName();
    const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();
    let { fromDate = null, toDate = null } = filter || {};
    const { filterBy } = filter || {};

    if (filter) {
      filter.sortBy = null;
    }

    let searchArray = [];
    //  searchArray.push({ term: { 'orderType.keyword': OrderType.POS } });

    const { searchArray: filteredSearch } = await filterFormatter(
      sortingFilter,
      searchingFilter,
      sortingFilterType,
      filter,
    );
    if (fromDate) {
      fromDate = moment(fromDate)
        .tz('Asia/Kolkata')
        .startOf('day')
        .utc()
        .format();
    }

    if (toDate) {
      toDate = moment(toDate).tz('Asia/Kolkata').endOf('day').utc().format();
    }
    const filterByField =
      filterBy === OrderFilterBy.INVOICE
        ? 'createdAt'
        : filterBy === OrderFilterBy.TRANSACTION
          ? 'transactionCompletedDate'
          : 'shopifyCreatedAt';

    if (fromDate && toDate) {
      searchArray.push({
        range: {
          [filterByField]: {
            gte: fromDate,
            lte: toDate,
          },
        },
      });
    } else if (fromDate) {
      searchArray.push({
        range: {
          [filterByField]: {
            gte: fromDate,
          },
        },
      });
    } else if (toDate) {
      searchArray.push({
        range: {
          [filterByField]: {
            lte: toDate,
          },
        },
      });
    }

    if (filteredSearch?.length && filterBy !== OrderFilterBy.TRANSACTION) {
      searchArray = searchArray.concat(filteredSearch);
    }

    let orderIds = [];
    let orders = [];

    if (filterBy === OrderFilterBy.TRANSACTION) {
      const data = await this.queryAll({
        index: PAYMENT_TABLE,
        filter: {
          bool: {
            must: [...searchArray],
          },
        },
        nextToken: null,
      });
      const allOrderIds = data.map((hit) => hit.orderId);
      orderIds = [...new Set(allOrderIds)];

      if (orderIds.length > 0) {
        const orderHits = await this.queryAll({
          index: ORDER_TABLE,
          // filter: [
          //   {
          //     terms: {
          //       'id.keyword': orderIds,
          //     },
          //   },
          //   ...(filteredSearch ? filteredSearch : []),
          // ],
          filter: {
            bool: {
              filter: [
                {
                  terms: {
                    'id.keyword': orderIds,
                  },
                },
                ...(filteredSearch ? filteredSearch : []),
              ],
            },
          },
          nextToken: null,
        });

        if (orderHits.length > 0) {
          const orderIdToTransactions = data.reduce((map, paymentHit) => {
            const orderId = paymentHit.orderId.toString();
            if (!map[orderId]) {
              map[orderId] = [];
            }
            map[orderId].push(paymentHit);
            return map;
          }, {});

          orders = orderHits.map((orderHit) => {
            const orderData = orderHit;

            const transactionDetails =
              orderIdToTransactions[orderData.id.toString()] || [];

            return {
              ...orderData,
              transactions: {
                ...orderData.transactions,
                transactionDetails:
                  orderData?.transactions?.transactionDetails?.length > 0
                    ? [...orderData.transactions.transactionDetails]
                    : transactionDetails,
              },
            };
          });
        } else {
          console.log('No order hits found.');
        }
      }
    } else if (filterBy === OrderFilterBy.INVOICE) {
      const allOrders = await this.queryAll({
        index: ORDER_TABLE,
        filter: {
          bool: {
            must: [...searchArray],
          },
        },
        nextToken: null,
      });

      for (const order of allOrders) {
        if (
          !order.transactions ||
          !order.transactions.transactionDetails ||
          order.transactions.transactionDetails.length === 0
        ) {
          const transactionDetails =
            await this.fetchTransactionDetailsFromPayments(
              order.id,
              PAYMENT_TABLE,
            );
          if (!order.transactions) {
            order.transactions = {};
          }

          order.transactions.transactionDetails = transactionDetails;
        } else {
          continue;
        }
      }

      orders = allOrders;
    } else {
      orders = await this.queryAll({
        index: ORDER_TABLE,
        filter: {
          bool: {
            must: [
              ...searchArray,
              {
                bool: {
                  should: [
                    {
                      terms: {
                        'status.keyword': ['SENT_TO_SHOPIFY', 'CANCELLED'],
                      },
                    },
                    {
                      bool: {
                        must: [
                          { term: { 'status.keyword': 'CONFIRMED' } },
                          { term: { 'subOrderType.keyword': 'LOCK_PRICE' } },
                        ],
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        nextToken: null,
      });
      if (
        filter.termSearchFields.orderType &&
        filter.termSearchFields.orderType === OrderType.BNPL
      ) {
        orders = await this.BNPLOrderExportData(orders);
      }
    }

    const sortedData = orders.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    );

    const ordersWithoutTestStore = sortedData?.filter(
      (order) => order?.storeId.toUpperCase() !== 'TEST-STORE',
    );
    const attachments = [];
    console.log('ordersWithoutTestStore', ordersWithoutTestStore?.length);
    if (exportTypes.includes('ORDERS')) {
      const superOrderIds = ordersWithoutTestStore?.reduce(
        (acc, order: any) => {
          if (order.superOrderId && order.superOrderId !== null) {
            acc.push(order.superOrderId);
          }
          return acc;
        },
        [],
      );
      let superOrders = [];
      if (superOrderIds?.length > 0) {
        superOrders = await this.getAllSuperOrder(superOrderIds);
      }
      const ordersCsv = await this.exportOrderCSV(
        ordersWithoutTestStore,
        superOrders,
      );
      console.log('ordersCsv', ordersCsv?.length);
      attachments.push({ name: 'Orders', content: ordersCsv });
    }

    if (exportTypes.includes('PAYMENTS')) {
      const exportPaymentHandler = new ExportPayments(
        this.configService,
        this.ssmClient,
        this.configParameters,
      );
      const paymentsCsv = await exportPaymentHandler.exportPaymentCSV(
        ordersWithoutTestStore,
        filterBy,
        fromDate,
        toDate,
      );
      console.log('paymentsCsv', paymentsCsv?.length);
      attachments.push({ name: 'Payments', content: paymentsCsv });
    }

    if (exportTypes.includes('ORDER_PRODUCTS')) {
      const exportProductsHandler = new ExportProducts(
        this.configService,
        this.ssmClient,
        this.configParameters,
      );
      const productsCsv = await exportProductsHandler.exportProductsCSV(
        ordersWithoutTestStore,
      );
      attachments.push({ name: 'Products', content: productsCsv });
    }

    const mailService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    const dateRange =
      fromDate && toDate
        ? ` | Range: ${moment(fromDate).tz('Asia/Kolkata').format('DD/MM/YYYY')} - ${moment(toDate).tz('Asia/Kolkata').format('DD/MM/YYYY')}`
        : '';
    for (const attachment of attachments) {
      await mailService.sendEmailWithFileAttachment(
        email,
        `SmartServe POS Export | Type:  ${attachment.name}${dateRange} | The Sleep Company`,
        `Following is the ${attachment.name} ${TemplateType.REPORT}`,
        'text/csv',
        TemplateType.REPORT,
        'csv',
        [attachment],
      );
    }

    return { message: 'Email has been sent' };
  }
}
