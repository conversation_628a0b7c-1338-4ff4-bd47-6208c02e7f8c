import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import moment from 'moment';
import { json2csv } from 'json-2-csv';
import { AppConfigParameters } from 'src/config/config';
import { OrderData } from 'src/orders/entities/order.entity';
import { SubOrderType } from 'src/common/enum/order';

export class ExportProducts {
  private esHandler: ElasticClient;
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {
    this.esHandler = new ElasticClient(this.configService, this.ssmClient);
  }

  async exportProductsCSV(orders: OrderData[]) {
    const data = orders.reduce((acc: any, order: any) => {
      const {
        shopifyOrderName,
        id: invoiceNo,
        storeId,
        orderProducts,
        shopifyCreatedAt,
        subOrderType,
      } = order;

      const shopifyDateIstOffset = shopifyCreatedAt
        ? moment(shopifyCreatedAt)
            .utcOffset('+05:30')
            .format('DD/MM/yyyy HH:mm')
        : '';
      const products = orderProducts.map((product: any) => {
        const {
          variantTitle,
          title,
          sku,
          productType,
          quantity,
          price,
          finalItemPrice,
          itemDiscount,
          finalItemPriceWithoutGst,
          gstAmount,
        } = product;

        return {
          'Order ID': shopifyOrderName || '',
          'invoice no': invoiceNo,
          'Store Id': storeId,
          'order date': shopifyDateIstOffset,
          'Product Title': title,
          'Variant Title': variantTitle,
          'Product Type': productType,
          Sku: sku,
          Price: price,
          Quantity: quantity,
          'Item Discount': itemDiscount,
          'Final Discounted Amount ': finalItemPrice,
          'GST Amount': gstAmount,
          'Net Amount without GST': finalItemPriceWithoutGst,
          'BNPL Booking Product':
            subOrderType !== null && subOrderType === SubOrderType.LOCK_PRICE
              ? 'TRUE'
              : 'FALSE',
          'POS Order Id': order?.id || '-',
        };
      });
      return [...acc, ...products];
    }, []);
    const csvData = await json2csv(data, {});
    return csvData;
  }
}
