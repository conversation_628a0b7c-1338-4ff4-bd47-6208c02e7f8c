import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { OrderData } from '../entities/order.entity';
import { GetOrder } from './get-order';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';
import { OrderFilterBy, OrderStatus } from 'src/common/enum/order';
import { PaymentHelpers } from 'src/payments/lib/payment-helpers';
import { QueryOrders } from './list-orders';
import { ListOrderInput } from '../dto/list-order.input';
import { OrderChangeLog } from './order-change-log';

export class SyncShopifyOrderStatus {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}

  async syncShopifyOrderStatus(orderId: string): Promise<OrderData> {
    posLogger.info('order', 'syncShopifyOrderStatus', {
      input: { orderId },
    });

    const getOrderHandler = new GetOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const order: OrderData = await getOrderHandler.getOrder(orderId);

    const {
      shopifyOrderId,
      shopifyOrderName,
      transactions,
      createdAt,
      finalDiscountedAmount,
    } = order;
    const { transactionDetails } = transactions;

    if (shopifyOrderId) {
      const getHandler = new QueryOrders(
        this.configService,
        this.ssmClient,
        this.configParameters,
      );
      const listOrderInput: ListOrderInput = {
        from: '0',
        size: '15',
        filterBy: OrderFilterBy.SHOPIFY,
        textSearchFields: { id: orderId },
      };
      const data = await getHandler.queryOrders(listOrderInput);
      const ESorder = data.data[0];
      console.log('ESorder', ESorder?.shopifyOrderId);

      if (!ESorder?.shopifyOrderId) {
        const ORDER_TABLE = await this.configParameters.getOrderTableName();
        const updatedAt = moment().add(1, 'ms').toISOString();
        const params = {
          TableName: ORDER_TABLE,
          Key: { id: orderId },
          UpdateExpression: 'SET updatedAt = :updatedAt',
          ExpressionAttributeValues: {
            ':updatedAt': updatedAt,
          },
          ConditionExpression: 'attribute_exists(id)',
        };

        const command = new UpdateCommand(params);
        const { Attributes } = await this.docClient.updateItem(command);

        const orderChangeLog = new OrderChangeLog(
          this.configParameters,
          this.docClient,
        );
        orderChangeLog.log(Attributes);

        order.updatedAt = updatedAt;
        return order;
      }
    }

    if (!shopifyOrderId) {
      throw new CustomError(
        `Shopify Order ID not found for given order ID ${orderId}.`,
        404,
      );
    }

    try {
      const [SHOPIFY_ACCESS_TOKEN, SHOPIFY_ADMIN_BASE_URL, ORDER_TABLE] =
        await Promise.all([
          await this.shopifyClient.getShopifyAccessToken(),
          await this.shopifyClient.getShopifyAdminBaseUrl(),
          await this.configParameters.getOrderTableName(),
        ]);

      const response = await fetch(
        `${SHOPIFY_ADMIN_BASE_URL}/orders.json?query=name:${shopifyOrderName}&status=any`,
        {
          headers: {
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
            'Content-Type': 'application/json',
          },
        },
      );

      const {
        orders: [shopifyOrder],
      } = await response.json();
      if (shopifyOrder) {
        const {
          cancelled_at,
          financial_status,
          fulfillment_status,
          shipment_status,
        } = shopifyOrder;

        order.shopifyOrderStatus = cancelled_at ? 'cancelled' : 'confirmed';
        order.shopifyPaymentStatus = financial_status;
        order.shopifyShipmentStatus =
          shipment_status || fulfillment_status || 'undelivered';
      }

      const params = {
        TableName: ORDER_TABLE,
        Key: { id: orderId },
        UpdateExpression:
          'SET  transactions = :transactions, shopifyOrderStatus = :shopifyOrderStatus, shopifyPaymentStatus = :shopifyPaymentStatus, shopifyShipmentStatus = :shopifyShipmentStatus, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
          ':transactions': order.transactions,
          ':shopifyOrderStatus': order.shopifyOrderStatus,
          ':shopifyPaymentStatus': order.shopifyPaymentStatus,
          ':shopifyShipmentStatus': order.shopifyShipmentStatus,
          ':updatedAt': moment().toISOString(),
        },
        ConditionExpression: 'attribute_exists(id)',
      };

      const paymentHelpers = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      if (!shopifyOrder) {
        params.UpdateExpression += ', #status = :status';
        params.ExpressionAttributeValues[':status'] =
          OrderStatus.DELETED_FROM_SHOPIFY;
        params['ExpressionAttributeNames'] = {
          '#status': 'status',
        };
        order.status = OrderStatus.DELETED_FROM_SHOPIFY;

        transactions.transactionDetails =
          await paymentHelpers.updateAllPayments({
            transactionDetails,
            shopifyOrderId: order.shopifyOrderId,
            shopifyOrderName: order.shopifyOrderName,
            createdAt,
            shopifyCreatedAt: order.shopifyCreatedAt,
            orderStatus: OrderStatus.DELETED_FROM_SHOPIFY,
            finalDiscountedAmount,
          });

        order.transactions = transactions;
      } else if (order.shopifyOrderStatus == 'cancelled') {
        params.UpdateExpression += ', #status = :status';
        params.ExpressionAttributeValues[':status'] = OrderStatus.CANCELLED;
        params['ExpressionAttributeNames'] = {
          '#status': 'status',
        };
        order.status = OrderStatus.CANCELLED;

        transactions.transactionDetails =
          await paymentHelpers.updateAllPayments({
            transactionDetails,
            shopifyOrderId: order.shopifyOrderId,
            shopifyOrderName: order.shopifyOrderName,
            createdAt,
            shopifyCreatedAt: order.shopifyCreatedAt,
            orderStatus: OrderStatus.CANCELLED,
            finalDiscountedAmount,
          });
        order.transactions = transactions;
      } else if (shopifyOrder && order.shopifyOrderStatus != 'cancelled') {
        params.UpdateExpression += ', #status = :status';
        params.ExpressionAttributeValues[':status'] =
          OrderStatus.SENT_TO_SHOPIFY;
        params['ExpressionAttributeNames'] = {
          '#status': 'status',
        };
        order.status = OrderStatus.SENT_TO_SHOPIFY;
        transactions.transactionDetails =
          await paymentHelpers.updateAllPayments({
            transactionDetails,
            shopifyOrderId: order.shopifyOrderId,
            shopifyOrderName: order.shopifyOrderName,
            createdAt,
            shopifyCreatedAt: order.shopifyCreatedAt,
            orderStatus: OrderStatus.SENT_TO_SHOPIFY,
            finalDiscountedAmount,
          });
        order.transactions = transactions;
      }

      const command = new UpdateCommand(params);
      const { Attributes } = await this.docClient.updateItem(command);

      const orderChangeLog = new OrderChangeLog(
        this.configParameters,
        this.docClient,
      );
      orderChangeLog.log(Attributes);

      return order;
    } catch (error) {
      posLogger.error('order', 'syncShopifyOrderStatus', { error });
      throw error;
    }
  }
}
