import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetCampaignCoupon } from 'src/campaign-coupons/lib/get-campaign-coupon';
import { CampaignCouponData } from 'src/campaign-coupons/entities/campaign-coupon.entity';
import { validateCouponUsageLimit } from 'src/common/helper/validate-usage-limits';

//types for return of function in promise generic

export class ValidateCouponUsage {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validateCampaignCouponUsage(code: string, storeId: string) {
    try {
      posLogger.info('order', 'validateCampaignCouponUsage', {
        input: { code },
      });

      if (code) {
        const getCampaignCuponHandler = new GetCampaignCoupon(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const {
          usageLimit,
          usageCount: accumulatedUsageCount,
          usageLimitForEachStore = false,
          storeWiseUsageCount = [],
          storeUsageLimit = [],
        }: CampaignCouponData = await getCampaignCuponHandler.getCampaignCoupon(
          code,
        );
        let usageCount = null;
        let storeCouponUsageLimit = null;
        if (!usageLimitForEachStore) {
          storeCouponUsageLimit = usageLimit;
          usageCount = accumulatedUsageCount;
        } else {
          storeCouponUsageLimit =
            storeUsageLimit?.find((limit) => limit.storeId == storeId)
              ?.usageLimit || null;
          usageCount =
            storeWiseUsageCount?.find((usage) => usage.storeId === storeId)
              ?.usageCount || 0;
        }
        return validateCouponUsageLimit(storeCouponUsageLimit, usageCount);
      }
    } catch (error) {
      posLogger.error('order', 'validateCampaignCouponUsage', { error });
      throw error;
    }
  }
}
