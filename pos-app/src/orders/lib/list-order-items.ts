// modules
import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { OrderProductData } from '../entities/order.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';

export class QueryOrderItems {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryOrderItemsByOrderId(orderId: string): Promise<OrderProductData[]> {
    posLogger.info('order', 'queryOrderItemsByOrderId', {
      input: { orderId },
    });

    const ORDER_ITEMS_TABLE =
      await this.configParameters.getOrderItemTableName();

    const esHandler = new ElasticClient(this.configService, this.ssmClient);

    const response = await esHandler.search({
      index: ORDER_ITEMS_TABLE,
      body: {
        size: 1000,
        query: {
          term: {
            'orderId.keyword': orderId,
          },
        },
      },
    });
    const data: OrderProductData[] = response.body.hits.hits.map(
      (hit) => hit._source,
    );

    if (!data.length) {
      throw new CustomError(
        `Order product does not exist for order ID ${orderId}!`,
        404,
      );
    }

    return data;
  }
  catch(e) {
    posLogger.error('order', 'queryOrderItemsByOrderId', { error: e });
    throw e;
  }
}
