import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { OrderData } from '../entities/order.entity';
import { posLogger } from 'src/common/logger';
import { OrderStatus } from 'src/common/enum/order';
import moment from 'moment';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { AppConfigParameters } from 'src/config/config';
import { GetOrder } from './get-order';
import { OrderChangeLog } from './order-change-log';

export class UpdateOrderStatus {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}
  async updateOrderStatus(id: string, status: OrderStatus): Promise<OrderData> {
    try {
      posLogger.info('order', 'updateOrderStatus', {
        input: { id, status },
      });

      const getOrderHandler = new GetOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const order: OrderData = await getOrderHandler.getOrder(id);

      if (
        order.status == OrderStatus.SENT_TO_SHOPIFY ||
        order.status == OrderStatus.SENT_TO_EE ||
        order.status == OrderStatus.CANCELLED ||
        order.status == OrderStatus.DELETED_FROM_SHOPIFY
      )
        return order;

      const ORDER_TABLE = await this.configParameters.getOrderTableName();

      const command = new UpdateCommand({
        TableName: ORDER_TABLE,
        Key: { id },
        UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
          '#status': 'status',
        },
        ExpressionAttributeValues: {
          ':status': status,
          ':updatedAt': moment().toISOString(),
        },
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: OrderData } =
        await this.docClient.updateItem(command);

      const orderChangeLog = new OrderChangeLog(
        this.configParameters,
        this.docClient,
      );
      orderChangeLog.log(Attributes);

      return Attributes;
    } catch (e) {
      posLogger.error('order', 'updateOrderStatus', e);
      throw e;
    }
  }
}
