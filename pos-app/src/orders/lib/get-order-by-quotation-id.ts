// modules

import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { OrderData } from '../entities/order.entity';
import { AppConfigParameters } from 'src/config/config';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { GetOrder } from './get-order';
import { AppDocumentClient } from 'src/common/document-client/document-client';

export class GetOrderByQuotationId {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getOrderByQuotationId({
    quotationId,
    subOrderType = undefined,
  }: {
    quotationId: string;
    subOrderType: string;
  }): Promise<OrderData> {
    posLogger.info('order', 'getOrderByQuotationId', { input: quotationId });
    try {
      const ORDER_TABLE = await this.configParameters.getOrderTableName();
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      const response = await esHandler.search({
        index: ORDER_TABLE,
        body: {
          query: {
            term: {
              'quotationId.keyword': {
                value: quotationId,
              },
            },
          },
          _source: ['id', 'subOrderType'],
        },
      });
      const data = response.body.hits.hits.map((hit) => hit._source);

      if (!data.length) {
        throw new CustomError(
          `Invalid ID! No order found for given quotation ID ${quotationId}.`,
          404,
        );
      }

      const getHandler = new GetOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      console.log(subOrderType, ':::: subOrderType');

      let id = data[0].id;
      if (subOrderType) {
        id = null;
        const subOrder = data.find(
          (order: any) =>
            order.subOrderType && order.subOrderType === subOrderType,
        );
        if (subOrder) {
          id = subOrder.id;
        }
      } else {
        id = data.find(
          (item: any) =>
            item.subOrderType === undefined || item.subOrderType === null,
        )?.id;
      }
      const order = await getHandler.getOrder(id);
      console.log('Order', order);

      //if (order?.orderType === OrderType.POS)
      return order;
    } catch (e) {
      posLogger.error('order', 'queryOrders', { error: e });
      throw e;
    }
  }
}
