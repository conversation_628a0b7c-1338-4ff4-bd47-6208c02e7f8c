import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { OrderData, OrderProductData } from '../entities/order.entity';
import { posLogger } from 'src/common/logger';
import moment from 'moment';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { UpdateOrderInput } from '../dto/update-order.input';
import { AppConfigParameters } from 'src/config/config';
import { GetOrder } from './get-order';
import { OrderStatus, OrderType, SubOrderType } from 'src/common/enum/order';
import { OrderChangeLog } from './order-change-log';

export class UpdateOrder {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}
  async updateOrder({
    id,
    orderProducts,
    ...updateOrderInput
  }: UpdateOrderInput): Promise<OrderData> {
    try {
      posLogger.info('order', 'updateOrder', {
        input: { id, orderProducts },
      });
      const getOrderHandler = new GetOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const order: OrderData = await getOrderHandler.getOrder(id);

      if (
        order.status == OrderStatus.SENT_TO_SHOPIFY ||
        order.status == OrderStatus.SENT_TO_EE ||
        order.status == OrderStatus.CANCELLED ||
        order.status == OrderStatus.DELETED_FROM_SHOPIFY
      )
        return order;

      const [ORDER_TABLE, ORDER_ITEM_TABLE] = await Promise.all([
        await this.configParameters.getOrderTableName(),
        await this.configParameters.getOrderItemTableName(),
      ]);

      //Updating Order
      let updateOrderExpressionString: string = 'Set #updatedAt = :updatedAt, ';
      const orderExpressionAttributeNames: Record<string, string> = {
        '#updatedAt': 'updatedAt',
      };
      const orderExpressionAttributesValues: Record<string, any> = {
        ':updatedAt': moment().toISOString(),
      };

      Object.keys(updateOrderInput).map((key) => {
        updateOrderExpressionString += `#${key} = :${key}, `;
        orderExpressionAttributeNames[`#${key}`] = key;
        orderExpressionAttributesValues[`:${key}`] = updateOrderInput[key];
      });

      updateOrderExpressionString = updateOrderExpressionString.substring(
        0,
        updateOrderExpressionString.length - 2,
      );

      let updatedOrderProducts = null;

      if (
        order.subOrderType &&
        order.subOrderType === SubOrderType.LOCK_PRICE
      ) {
        console.log('Skipped order products updates.');
        updatedOrderProducts = orderProducts.map((item: any) => {
          const existingProduct = order.orderProducts.find(
            (product: any) => product.id === item.id,
          );
          return {
            ...existingProduct,
            ...item,
          };
        });
        updateOrderExpressionString += ', orderProducts = :orderProducts';
        orderExpressionAttributesValues[':orderProducts'] =
          updatedOrderProducts;
      }

      if (
        orderProducts &&
        orderProducts.length > 0 &&
        (!order.subOrderType ||
          order.subOrderType !== SubOrderType.LOCK_PRICE) &&
        order.orderType !== OrderType.BNPL
      ) {
        console.log('Updating Order products');
        updatedOrderProducts = await Promise.all(
          (orderProducts || []).map(async (product) => {
            const {
              id,
              deliveryStatus,
              deliveryDate,
              minDeliveryDate,
              deliveryRange,
              dispatchDate,
              fallbackEdd,
            } = product;
            if (id && deliveryStatus && deliveryDate) {
              const command = new UpdateCommand({
                TableName: ORDER_ITEM_TABLE,
                Key: { id },
                UpdateExpression:
                  'Set #updatedAt = :updatedAt, #deliveryStatus = :deliveryStatus, #deliveryDate = :deliveryDate, #minDeliveryDate = :minDeliveryDate, #deliveryRange = :deliveryRange, #dispatchDate = :dispatchDate, #fallbackEdd = :fallbackEdd',
                ExpressionAttributeNames: {
                  '#updatedAt': 'updatedAt',
                  '#deliveryStatus': 'deliveryStatus',
                  '#deliveryDate': 'deliveryDate',
                  '#minDeliveryDate': 'minDeliveryDate',
                  '#deliveryRange': 'deliveryRange',
                  '#dispatchDate': 'dispatchDate',
                  '#fallbackEdd': 'fallbackEdd',
                },
                ExpressionAttributeValues: {
                  ':updatedAt': moment().toISOString(),
                  ':deliveryStatus': deliveryStatus,
                  ':deliveryDate': deliveryDate,
                  ':minDeliveryDate': minDeliveryDate || null,
                  ':deliveryRange': deliveryRange || null,
                  ':dispatchDate': dispatchDate || null,
                  ':fallbackEdd': fallbackEdd || null,
                },
                ConditionExpression: 'attribute_exists(id)',
                ReturnValues: 'ALL_NEW',
              });

              const { Attributes }: { Attributes: OrderProductData } =
                await this.docClient.updateItem(command);

              return Attributes;
            } else {
              return product;
            }
          }),
        );

        updateOrderExpressionString += ', orderProducts = :orderProducts';
        orderExpressionAttributesValues[':orderProducts'] =
          updatedOrderProducts;
      }

      const command = new UpdateCommand({
        TableName: ORDER_TABLE,
        Key: { id },
        UpdateExpression: `${updateOrderExpressionString}`,
        ExpressionAttributeNames: orderExpressionAttributeNames,
        ExpressionAttributeValues: {
          ...orderExpressionAttributesValues,
        },
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: OrderData } =
        await this.docClient.updateItem(command);

      const orderChangeLog = new OrderChangeLog(
        this.configParameters,
        this.docClient,
      );
      orderChangeLog.log(Attributes);

      return { ...Attributes, orderProducts: updatedOrderProducts };
    } catch (e) {
      posLogger.error('order', 'updateOrder', e);
      throw e;
    }
  }
}
