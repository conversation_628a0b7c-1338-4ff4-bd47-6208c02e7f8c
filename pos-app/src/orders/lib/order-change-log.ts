import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { randomUUID } from 'crypto';

export class OrderChangeLog {
  constructor(
    private configParameters: AppConfigParameters,
    private docClient: AppDocumentClient,
  ) {}

  async log(data: any) {
    try {
      const ORDER_LOGS_TABLE =
        await this.configParameters.getOrdersLogTableName();
      const now = moment();
      const ttlInSeconds = Math.floor(Date.now() / 1000) + 2 * 24 * 60 * 60;
      const item = {
        pk: `orderlog#${now.toISOString()}#${randomUUID()}`,
        loggedAt: now.toISOString(),
        ttl: ttlInSeconds,
        ...data,
      };
      const command = new PutCommand({
        TableName: ORDER_LOGS_TABLE,
        Item: item,
      });
      await this.docClient.createItem(command);
    } catch (error) {
      posLogger.error('order-log', 'Failed to create order change log', error);
      console.error(error);
    }
  }
}
