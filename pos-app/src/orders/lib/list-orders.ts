// modules

import { ConfigService } from '@nestjs/config';
import { filterFormatter } from 'src/common/helper/filter-helper';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ListOrderInput } from '../dto/list-order.input';
import { Orders } from '../entities/order.entity';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { AppConfigParameters } from 'src/config/config';
import { OrderFilterBy } from 'src/common/enum/order';
import moment from 'moment-timezone';

export class QueryOrders {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryOrders(filter: ListOrderInput): Promise<Orders> {
    posLogger.info('order', 'queryOrder', { input: filter });

    try {
      const ORDER_TABLE = await this.configParameters.getOrderTableName();
      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();
      const { fromDate, toDate, filterBy } = filter || {};
      const { searchArray: filteredSearch, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );

      const dateRangeFilter = this.buildDateRangeFilter(
        filterBy,
        fromDate,
        toDate,
      );
      const searchArray = [...filteredSearch, dateRangeFilter].filter(Boolean);

      const size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (filterBy === OrderFilterBy.TRANSACTION) {
        return await this.handleTransactionQuery(
          esHandler,
          PAYMENT_TABLE,
          ORDER_TABLE,
          filteredSearch,
          dateRangeFilter,
          size,
          from,
          sortObject,
        );
      } else {
        return await this.handleOrderQuery(
          esHandler,
          ORDER_TABLE,
          searchArray,
          size,
          from,
          sortObject,
        );
      }
    } catch (e) {
      posLogger.error('order', 'queryOrders', { error: e });
      throw e;
    }
  }

  private buildDateRangeFilter(
    filterBy: OrderFilterBy,
    fromDate: string | undefined,
    toDate: string | undefined,
  ) {
    const field =
      filterBy === OrderFilterBy.INVOICE
        ? 'createdAt'
        : filterBy === OrderFilterBy.TRANSACTION
          ? 'transactionCompletedDate'
          : 'shopifyCreatedAt';

    const rangeFilter: {
      range: { [key: string]: { gte?: string; lte?: string } };
    } = { range: { [field]: {} } };

    if (fromDate && !toDate) {
      rangeFilter.range[field].gte = moment(fromDate)
        .tz('Asia/Kolkata')
        .startOf('day')
        .utc()
        .format();
    }
    if (toDate && !fromDate) {
      rangeFilter.range[field].lte = moment(toDate)
        .tz('Asia/Kolkata')
        .endOf('day')
        .utc()
        .format();
    }
    if (fromDate && toDate) {
      rangeFilter.range[field].lte = moment(toDate)
        .tz('Asia/Kolkata')
        .endOf('day')
        .utc()
        .format();
      rangeFilter.range[field].gte = moment(fromDate)
        .tz('Asia/Kolkata')
        .startOf('day')
        .utc()
        .format();
    }

    return rangeFilter.range[field].gte || rangeFilter.range[field].lte
      ? rangeFilter
      : null;
  }

  private async handleTransactionQuery(
    esHandler,
    PAYMENT_TABLE,
    ORDER_TABLE,
    filteredSearch,
    dateRangeFilter,
    size,
    from,
    sortObject,
  ) {
    console.log('searchArray', dateRangeFilter, filteredSearch, sortObject);

    const fetchAllPayments = async (esHandler, PAYMENT_TABLE, searchArray) => {
      let allPayments = [];
      let currentFrom = 0;
      const batchSize = 10000;
      let totalHits = 0;

      do {
        const paymentResponse = await esHandler.search({
          index: PAYMENT_TABLE,
          body: {
            size: batchSize,
            from: currentFrom,
            query: {
              bool: {
                must: [searchArray],
              },
            },
          },
        });

        const hits = paymentResponse.body.hits.hits;
        totalHits = paymentResponse.body.hits.total.value;

        allPayments = allPayments.concat(
          hits.map((hit) => hit._source.orderId),
        );

        currentFrom += batchSize;
      } while (currentFrom < totalHits);

      return allPayments;
    };

    const fetchAllOrders = async (esHandler, ORDER_TABLE, uniqueOrderIds) => {
      let allOrders = [];
      let currentFrom = 0;
      const batchSize = 10000;
      let totalHits = 0;
      let fetchedHits = 0;

      do {
        const orderResponse = await esHandler.search({
          index: ORDER_TABLE,
          body: {
            size: batchSize,
            from: currentFrom,
            query: {
              bool: {
                filter: [
                  {
                    terms: {
                      'id.keyword': uniqueOrderIds,
                    },
                  },
                  ...(filteredSearch ? filteredSearch : []),
                ],
              },
            },
          },
        });

        const hits = orderResponse.body.hits.hits;
        totalHits = orderResponse.body.hits.total.value;
        fetchedHits += hits.length;

        allOrders = allOrders.concat(hits.map((hit) => hit._source));

        currentFrom += batchSize;
      } while (fetchedHits < totalHits && fetchedHits < uniqueOrderIds.length);

      return allOrders;
    };

    const orderIds = await fetchAllPayments(
      esHandler,
      PAYMENT_TABLE,
      dateRangeFilter,
    );

    const uniqueOrderIds = [...new Set(orderIds)];

    if (uniqueOrderIds.length) {
      const allOrders = await fetchAllOrders(
        esHandler,
        ORDER_TABLE,
        uniqueOrderIds,
      );

      const sortedData = allOrders.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );
      const count = sortedData?.length;

      const splicedData = sortedData?.slice(from, from + size);

      return {
        data: splicedData,
        count: count,
      };
    }

    return { data: [], count: 0 };
  }

  private async handleOrderQuery(
    esHandler,
    ORDER_TABLE,
    searchArray,
    size,
    from,
    sortObject,
  ) {
    if (searchArray.length) {
      const response = await esHandler.search({
        index: ORDER_TABLE,
        body: {
          size,
          from,
          query: {
            bool: {
              must: searchArray,
            },
          },
          sort: [sortObject],
        },
      });

      const data = response.body.hits.hits.map((hit) => hit._source);
      const countResponse = await esHandler.count({
        index: ORDER_TABLE,
        body: {
          query: {
            bool: {
              must: searchArray,
            },
          },
        },
      });

      return { data, count: countResponse.body.count };
    } else {
      const response = await esHandler.search({
        index: ORDER_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data = response.body.hits.hits.map((hit) => hit._source);
      const countResponse = await esHandler.count({
        index: ORDER_TABLE,
        body: {
          query: {
            match_all: {},
          },
        },
      });

      return { data, count: countResponse.body.count };
    }
  }
}
