import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetOrder } from './get-order';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { FinalOrderConfirm } from './final-confirm-order';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppShopify } from 'src/common/shopify/shopify';
import { OrderStatus } from 'src/common/enum/order';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';
import { OrderChangeLog } from './order-change-log';
import { OrderData } from '../entities/order.entity';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { PaymentMode, PaymentStatus } from 'src/common/enum/payment';

@Injectable()
export class AutoConfirmOrder {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {}

  async updateOrder(orderId: string, error: any) {
    try {
      let parsedErrors: any;
      if (error instanceof CustomError) {
        try {
          parsedErrors = JSON.parse(error.message); // ← ✅ safe to parse
        } catch (e) {
          parsedErrors = { message: error.message };
        }
      }

      const ORDER_TABLE = await this.configParameters.getOrderTableName();
      let updateOrderExpressionString: string = 'Set #updatedAt = :updatedAt';
      const orderExpressionAttributeNames: Record<string, string> = {
        '#updatedAt': 'updatedAt',
      };
      const orderExpressionAttributesValues: Record<string, any> = {
        ':updatedAt': moment().toISOString(),
      };

      updateOrderExpressionString += ', errorDetails = :errorDetails';

      orderExpressionAttributesValues[':errorDetails'] = {
        isError: true,
        scope: error?.scope || 'POS',
        message: parsedErrors?.[0]?.message || error?.message,
      };

      const command = new UpdateCommand({
        TableName: ORDER_TABLE,
        Key: { id: orderId },
        UpdateExpression: `${updateOrderExpressionString}`,
        ExpressionAttributeNames: orderExpressionAttributeNames,
        ExpressionAttributeValues: {
          ...orderExpressionAttributesValues,
        },
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: OrderData } =
        await this.docClient.updateItem(command);

      const orderChangeLog = new OrderChangeLog(
        this.configParameters,
        this.docClient,
      );
      orderChangeLog.log(Attributes);
    } catch (error) {
      console.log('updateOrder :::: >', error);
    }
  }

  async verifyPaymentAndConfirmOrder(orderId: string) {
    try {
      const handler = new GetOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const order = await handler.getOrder(orderId);

      if (!order) {
        throw new CustomError(
          `Invalid ID! No order found for given ID ${orderId}.`,
          404,
        );
      }

      const { finalDiscountedAmount, transactions, status, storeId } = order;

      const isCashInvolved = transactions?.transactionDetails?.some(
        (transaction) =>
          transaction.mode === PaymentMode.CASH &&
          transaction.status !== PaymentStatus.CANCELLED,
      );

      if (isCashInvolved) return;

      const getGlobalConfigurationHandler = new GetGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const { value: AUTO_CONFIRMATION_LIVE_STORES } =
        await getGlobalConfigurationHandler.getGlobalConfiguration(
          'AUTO_CONFIRMATION_LIVE_STORES',
        );

      const liveStoresArray =
        typeof AUTO_CONFIRMATION_LIVE_STORES === 'string' &&
        AUTO_CONFIRMATION_LIVE_STORES.length
          ? AUTO_CONFIRMATION_LIVE_STORES.split(',')
          : [];

      if (
        liveStoresArray &&
        liveStoresArray.length &&
        !liveStoresArray?.includes(storeId)
      )
        return;

      if (
        Number(finalDiscountedAmount) -
          Number(transactions?.totalPaidAmount) ===
          0 &&
        status !== OrderStatus.SENT_TO_SHOPIFY
      ) {
        console.log('Auto confirming order :::: >', orderId, order.storeId);
        const handler = new FinalOrderConfirm(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
          this.shopifyClient,
          this.s3Client,
        );
        return await handler.finalOrderConfirm(orderId, order.storeId);
      }
      console.log(
        'Full payment not received yet :::: >',
        orderId,
        order.storeId,
      );
      return true;
    } catch (error) {
      console.log('verifyPaymentAndConfirmOrder :::: >', error);
      await this.updateOrder(orderId, error);
    }
  }
}
