import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { CreateOrderInput } from './dto/create-order.input';
import {
  Order,
  OrderProducts,
  Orders,
  RaisedIssue,
} from './entities/order.entity';
import { OrdersService } from './orders.service';
import {
  GetOrderByQuotationIdInput,
  ListOrderInput,
} from './dto/list-order.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import {
  CustomError,
  ErrorHandler,
} from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { Response } from 'src/products/entities/product.entity';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { UpdateOrderInput } from './dto/update-order.input';
import { ShopifyOrders } from './entities/shopify-order.entity';
import { CRMGuard } from 'src/auth/roles.guard';
import { CreateIssueTicketInput } from './dto/create-issue-ticket.input';
import { posLogger } from 'src/common/logger';

@Resolver(() => Order)
export class OrdersResolver {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => Order)
  @UseGuards(CustomAuthGuard, CRMGuard)
  async createOrder(
    @Args('createOrderInput') createOrderInput: CreateOrderInput,
  ) {
    try {
      const data = await this.ordersService.create(createOrderInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Order, { name: 'getOrder' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async findOne(@Args('id', { type: () => String }) id: string) {
    try {
      const data = await this.ordersService.findOne(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Order, { name: 'getOrderByQuotationId' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async findOneByQuotationId(
    @Args('getOrderByQuotationIdInput')
    getOrderByQuotationIdInput: GetOrderByQuotationIdInput,
  ) {
    try {
      const data = await this.ordersService.findOneByQuotationId(
        getOrderByQuotationIdInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Orders, { name: 'listOrders' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async findAll(
    @Args('listOrderInput', { nullable: true })
    listOrderInput: ListOrderInput,
  ) {
    try {
      const { data, count } = await this.ordersService.findAll(listOrderInput);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list orders',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Response, { name: 'exportOrders' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async exportOrders(
    @Args('email', { type: () => String }) email: string,
    @Args('listOrderInput', { nullable: true })
    listOrderInput: ListOrderInput,
    @Args('exportTypes', { type: () => [String], nullable: false })
    exportTypes: string[],
  ) {
    const validExportTypes = ['ORDERS', 'PAYMENTS', 'ORDER_PRODUCTS'];

    const invalidTypes = exportTypes.filter(
      (type) => !validExportTypes.includes(type),
    );
    if (invalidTypes.length > 0) {
      throw new CustomError(
        `Invalid export types provided: ${invalidTypes.join(', ')}`,
        400,
      );
    }

    try {
      const { message } = await this.ordersService.exportOrders(
        email,
        listOrderInput,
        exportTypes,
      );
      return {
        message,
        success: true,
        status: 200,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to export orders',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Order, { name: 'finalOrderConfirm' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async finalOrderConfirm(
    @Args('orderId', { type: () => String })
    orderId: string,
    @Args('storeId', { type: () => String })
    storeId: string,
  ) {
    try {
      const data = await this.ordersService.finalOrderConfirm(orderId, storeId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create final order till shopify',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => OrderProducts, { name: 'listOrderItemsByOrderId' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async findAllOrderItems(
    @Args('orderId', { type: () => String })
    orderId: string,
  ) {
    try {
      const data = await this.ordersService.findAllOrderItems(orderId);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count: data.length };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list order items',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Order, { name: 'syncShopifyOrderStatus' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async syncShopifyOrderStatus(
    @Args('orderId', { type: () => String })
    orderId: string,
  ) {
    try {
      const data = await this.ordersService.syncShopifyOrderStatus(orderId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to sync shopify order status',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Response, { name: 'orderPdf' })
  async orderPdf(
    @Args('orderId', { type: () => String })
    orderId: string,
    @Args('type', { type: () => String })
    type: string,
  ) {
    try {
      if (!['EMAIL', 'DOWNLOAD', 'WHATSAPP'].includes(type)) {
        throw new CustomError(
          `Type should be either EMAIL or DOWNLOAD or WHATSAPP`,
          400,
        );
      }

      const message = await this.ordersService.sendOrderPdf(orderId, type);

      return {
        message,
        status: 200,
        success: true,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to downloading order pdf',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Order, { name: 'updateOrder' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async updateOrder(
    @Args('updateOrderInput') updateOrderInput: UpdateOrderInput,
  ) {
    try {
      const data = await this.ordersService.update(updateOrderInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to sync shopify order status',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Order, { name: 'holdOrder' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async holdOrder(
    @Args('orderId', { type: () => String })
    orderId: string,
  ) {
    try {
      const data = await this.ordersService.holdOrder(orderId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to sync shopify order status',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => ShopifyOrders, { name: 'listShopifyOrders' })
  @UseGuards(CustomAuthGuard, CRMGuard)
  async listShopifyOrders(
    @Args('customerId', { type: () => String })
    customerId: string,
  ) {
    try {
      const data = await this.ordersService.findAllShopifyOrders(customerId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to fetch shopify orders',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => RaisedIssue)
  @UseGuards(CustomAuthGuard, CRMGuard)
  async createIssueTicket(
    @Args('createIssueTicketInput')
    createIssueTicketInput: CreateIssueTicketInput,
  ) {
    try {
      posLogger.info('orders', 'createIssueTicket', createIssueTicketInput);

      const response = await this.ordersService.createIssueTicket(
        createIssueTicketInput,
      );

      return this.successHandler.getSuccessResponse({
        data: response,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create issue ticket',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  // @Mutation(() => Order)
  // removeOrder(@Args('id', { type: () => Int }) id: number) {
  //   return this.ordersService.remove(id);
  // }
}
