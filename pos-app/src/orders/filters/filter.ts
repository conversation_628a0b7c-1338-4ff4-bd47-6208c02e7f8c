export const searchingFilter = new Map<
  string,
  'string' | 'nonString' | 'arrayString' | 'boolean' | 'date'
>([
  ['id', 'string'],
  ['shopifyOrderName', 'string'],
  ['storeId', 'string'],
  ['customerId', 'string'],
  ['quotationId', 'string'],
  ['employeeId', 'string'],
  ['status', 'string'],
  ['customerphone', 'string'],
  ['customeremail', 'string'],
  ['orderType', 'string'],
  ['holdOrder', 'boolean'],
  ['type', 'string'],
]);

export const sortingFilter = new Map<string, 'string' | 'nonString'>([
  ['createdAt', 'string'],
  ['updatedAt', 'string'],
]);

export const sortingFilterType = {
  createdAt: 'date',
  updatedAt: 'date',
};
