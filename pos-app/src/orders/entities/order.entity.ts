import { ObjectType, Field, Int, registerEnumType, ID } from '@nestjs/graphql';
import {
  CartProduct,
  CustomCode,
  CustomData,
} from 'src/carts/entities/cart.entity';
import { DeliveryType } from 'src/common/enum/delivery';
import { OrderStatus, OrderType, SubOrderType } from 'src/common/enum/order';
import { PickupPriority, ReplacementType } from 'src/common/enum/replacement';
import { PaymentData } from 'src/payments/entities/payment.entity';
import { Image } from 'src/products/entities/product.entity';
import {
  QuotationAddress,
  InteriorArchitectureDetails,
  AdditionalPromotionalCoupon,
  DeliveryChargeApprovalDetails,
  EmployeeDiscountDetails,
} from 'src/quotations/entities/quotation.entity';
import { ReplacedProduct } from 'src/replacement-orders/entities/replacement-order.entity';
import { GstDetails } from 'src/stores/entities/store.entity';

// Register the enum with GraphQL
registerEnumType(DeliveryType, { name: 'DeliveryType' });
registerEnumType(OrderStatus, { name: 'OrderStatus' });
registerEnumType(OrderType, { name: 'OrderType' });
registerEnumType(SubOrderType, { name: 'SubOrderType' });

@ObjectType()
export class OrderCustomerMetadata {
  @Field(() => String, {
    nullable: true,
    description: 'First Name of the customer',
  })
  firstName: string;

  @Field(() => String, {
    nullable: true,
    description: 'First Name of the customer',
  })
  lastName: string;

  @Field(() => String, {
    description: 'Phone number',
  })
  phone: string;

  @Field(() => String, {
    nullable: true,
    description: 'Email of the customer',
  })
  email?: string;

  @Field(() => String, {
    nullable: true,
    description: 'serviceLift',
  })
  serviceLift?: string;

  @Field(() => String, {
    nullable: true,
    description: 'accommodationType',
  })
  accommodationType?: string;

  @Field(() => String, {
    nullable: true,
    description: 'landmark',
  })
  landmark?: string;

  @Field(() => String, {
    nullable: true,
    description: 'latitude',
  })
  latitude?: string;

  @Field(() => String, {
    nullable: true,
    description: 'longitude',
  })
  longitude?: string;
}

@ObjectType()
export class RaisedIssueData {
  @Field(() => String, {
    nullable: true,
    description: 'Timestamp when created',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'description of issue',
  })
  description?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Issue type',
  })
  subject?: string;

  @Field(() => String, {
    nullable: false,
    description: 'LSQ Ticket ID (Link to the ticket)',
  })
  ticketId: string;
}

@ObjectType()
export class OrderProductData {
  @Field(() => String, { nullable: true })
  id?: string;

  @Field(() => String, { nullable: false })
  productId: string;

  @Field(() => String, { nullable: true })
  variantId?: string;

  @Field(() => Int, {
    nullable: false,
    description: 'Quantity of the order product',
  })
  quantity: number;

  @Field(() => CustomData, {
    nullable: true,
    description: 'Custom Meta data',
  })
  customData?: CustomData;

  @Field(() => Number, { description: 'Price of the order product' })
  price: number;

  @Field(() => String, { description: 'Price Type', nullable: true })
  priceType?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Title of the order product',
  })
  title?: string;

  @Field(() => String, {
    nullable: true,
    description: 'VariantTitle of the order product',
  })
  variantTitle?: string;

  @Field(() => Image, {
    nullable: true,
    description: 'Image of the order product',
  })
  image?: Image;

  @Field(() => String, {
    nullable: false,
    description: 'Sku of the order product',
  })
  sku: string;

  @Field(() => String, { nullable: true })
  productType?: string;

  @Field(() => Number, { nullable: true })
  edd?: number;

  @Field(() => String, {
    nullable: true,
    description: 'GST Amount of the order product',
  })
  hsn?: string;

  @Field(() => String, {
    nullable: true,
    description: 'GST Rate of the order product',
  })
  gstRate?: string;

  @Field(() => Number, {
    nullable: true,
    description: 'GST Amount of the order product',
  })
  gstAmount?: number;

  @Field(() => DeliveryType, {
    nullable: true,
    description: 'Delivery Type of the order product',
  })
  deliveryStatus?: DeliveryType;

  @Field(() => String, {
    nullable: true,
    description: 'Delivery date of the order product',
  })
  deliveryDate?: string;

  @Field(() => String, {
    description: 'Delivery range of the order product',
    nullable: true,
  })
  deliveryRange?: string;

  @Field(() => String, {
    description: 'Min Delivery date of the order product',
    nullable: true,
  })
  minDeliveryDate?: string;

  @Field(() => Boolean, {
    description: 'EDD assumed for this order product',
    nullable: true,
  })
  isAssumed?: boolean;

  @Field(() => Number, { nullable: true })
  itemDiscount?: number;

  @Field(() => Number, { nullable: false })
  finalItemPrice: number;

  @Field(() => String, { nullable: true })
  dispatchDate?: string;

  @Field(() => Boolean, { nullable: true })
  fallbackEdd?: boolean;

  @Field(() => Number, {
    description: 'Final Item Price Without GST',
    nullable: true,
  })
  finalItemPriceWithoutGst?: number;
}

@ObjectType()
export class PaymentDetails {
  @Field(() => [PaymentData], {
    nullable: true,
    description: 'Transactions',
  })
  transactionDetails?: PaymentData[];

  @Field(() => Number, {
    nullable: true,
    description: 'Payment Paid by the customer',
  })
  totalPaidAmount?: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Payment Paid by the customer',
  })
  remainingAmount?: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Payment Requested Amount by the customer',
  })
  requestedAmount?: number;
}

@ObjectType()
class ErrorData {
  @Field(() => Boolean, { nullable: true })
  isError: boolean;

  @Field(() => String, { nullable: true })
  scope: string;

  @Field(() => String, { nullable: true })
  message: string;
}

@ObjectType()
export class OrderData {
  @Field(() => ID, {
    nullable: false,
    description: 'ORDER ID of the customer for the order.',
  })
  id: string;

  @Field(() => String, {
    nullable: true,
    description: 'RR order payment link status',
  })
  linkStatus?: string;

  @Field(() => String, {
    nullable: true,
    description: 'RR order payment link time to expire',
  })
  linkTimeToExpire?: string;

  @Field(() => [AdditionalPromotionalCoupon], {
    nullable: true,
    description: 'Additional Promotional Coupons',
  })
  additionalPromotionalCoupons?: AdditionalPromotionalCoupon[];

  @Field(() => String, {
    nullable: true,
    description: 'carrier',
  })
  carrier?: string;

  @Field(() => String, {
    nullable: true,
    description: 'creditNoteId',
  })
  creditNoteId?: string;

  @Field(() => [String], { nullable: true })
  originalPaymentMode?: string[];

  @Field(() => [String], { nullable: true })
  transactionIDs?: string[];

  @Field(() => Boolean, {
    nullable: true,
    description: 'isRazorpay mode',
  })
  isRazorpay?: boolean;

  @Field(() => String, {
    nullable: true,
    description: 'RRTicketId (Link to the ticket)',
  })
  RRTicketId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'AWBApprovedBy name',
  })
  AWBApprovedBy?: string;

  @Field(() => String, {
    description: 'Easy ecom Reference Number',
    nullable: true,
  })
  eeRefNo?: string;

  @Field(() => EmployeeDiscountDetails, { nullable: true, description: '' })
  employeeDiscountDetails?: EmployeeDiscountDetails;

  @Field(() => String, {
    description: 'Easy ecom awbNumber ',
    nullable: true,
  })
  awbNumber?: string;

  @Field(() => Number, {
    nullable: true,
    description: 'Shipping Charges.',
  })
  shippingCost?: number;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Shipping charges.',
  })
  isShippingCharged?: boolean;

  @Field(() => String, {
    nullable: true,
    description: 'Reason for Holding the Order',
  })
  holdOrderReason?: string;

  @Field(() => PickupPriority, { nullable: true })
  pickupPriority?: PickupPriority;

  @Field(() => ReplacementType, { nullable: true })
  replacementType?: ReplacementType;

  @Field(() => String, { nullable: true })
  ticketId: string;

  @Field(() => String, {
    nullable: false,
    description: 'Quotation ID of the customer for the order.',
  })
  quotationId: string;

  @Field(() => ReplacedProduct, {
    nullable: true,
    description: 'Replaced product for the order.',
  })
  replacedProduct: ReplacedProduct;

  @Field(() => String, {
    nullable: true,
    description: 'ID of the customer for the order.',
  })
  customerId?: string;

  @Field(() => String, {
    description: 'Min Delivery date of the order',
    nullable: true,
  })
  minDeliveryDate?: string;

  @Field(() => String, {
    description: 'Delivery date of the order',
    nullable: true,
  })
  deliveryDate?: string;

  @Field(() => String, { nullable: true })
  dispatchDate?: string;

  @Field(() => String, { nullable: true })
  eddHeaderId?: string;

  @Field(() => Boolean, { nullable: true })
  posFallbackEdd?: boolean;

  @Field(() => String, {
    description: 'Unhold future dispatch date of the order',
    nullable: true,
  })
  unHoldFutureDispatchDate?: string;

  @Field(() => DeliveryType, {
    nullable: true,
    description: 'Delivery Type of the order',
  })
  deliveryStatus?: DeliveryType;

  @Field(() => DeliveryChargeApprovalDetails, { nullable: true })
  deliveryChargeApprovalDetails?: DeliveryChargeApprovalDetails;

  @Field(() => Number, {
    nullable: true,
    description: 'delivery amount',
  })
  deliveryCharge?: number;

  @Field(() => [OrderProductData], {
    nullable: true,
    description: 'List of products being ordered.',
  })
  orderProducts?: OrderProductData[];

  @Field(() => PaymentDetails, {
    nullable: true,
    description: 'Payment being ordered.',
  })
  transactions?: PaymentDetails;

  @Field(() => String, {
    nullable: false,
    description: 'ID of the store issuing the order.',
  })
  storeId: string;

  @Field(() => OrderType, {
    nullable: false,
    description: 'Order type.',
  })
  orderType: OrderType;

  @Field(() => OrderStatus, {
    nullable: false,
    description: 'Order status.',
  })
  status: OrderStatus;

  @Field(() => String, {
    nullable: false,
    description: 'ID of the employee creating the order.',
  })
  employeeId: string;

  @Field(() => String, {
    nullable: true,
    description: 'Campaign code associated with the order.',
  })
  campaignCode?: string;

  @Field(() => CustomCode, {
    nullable: true,
    description: 'Custom code for the order.',
  })
  customCode?: CustomCode;

  @Field(() => String, {
    nullable: true,
    description: 'Promotional code for the order.',
  })
  promotionalCode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Notes or comments for the order.',
  })
  notes?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Source for the order.',
  })
  source?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer acknowledgement for the order.',
  })
  customerAcknowledgement?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer Identity proof for the order.',
  })
  customerIdentityProof?: string;

  @Field(() => OrderCustomerMetadata, {
    nullable: true,
    description: 'Customer metadata for the order.',
  })
  customer?: OrderCustomerMetadata;

  @Field(() => QuotationAddress, {
    description: 'Shipping Address for the order.',
  })
  shippingAddress: QuotationAddress;

  @Field(() => QuotationAddress, {
    description: 'Billing Address for the order.',
  })
  billingAddress: QuotationAddress;

  @Field(() => GstDetails, {
    nullable: true,
    description: 'GST Details for the order.',
  })
  gstDetails?: GstDetails;

  @Field(() => InteriorArchitectureDetails, {
    nullable: true,
    description: 'Interior Architecture.',
  })
  interiorArchitecture?: InteriorArchitectureDetails;

  @Field(() => Number, {
    nullable: false,
    description: 'Total amount',
  })
  totalAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  finalDiscountedAmount: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Refunded Amount',
  })
  refundedPrice?: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  promotionalDiscountAmount: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Final Amount',
  })
  customDiscountAmount: number;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  campaignDiscountAmount: number;

  @Field(() => [CartProduct], {
    nullable: true,
    description: 'Free products',
  })
  freeProducts?: CartProduct[];

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Order ID',
  })
  shopifyOrderId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Order status',
  })
  shopifyOrderStatus?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Payment status',
  })
  shopifyPaymentStatus?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Order name',
  })
  shopifyOrderName?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Shipment status',
  })
  shopifyShipmentStatus?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Shipment status',
  })
  shopifyDiscountCode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Shipment status',
  })
  shopifyDiscountType?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Shipment status',
  })
  shopifyDiscountAmount?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Shipment status',
  })
  shopifyCreatedAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Shipment status',
  })
  series?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Shipment status',
  })
  acctcode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Shipment status',
  })
  cardcode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Shipment status',
  })
  loggedusername?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Shopify Shipment status',
  })
  whscode?: string;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Hold Order status',
  })
  holdOrder?: boolean;

  @Field(() => String, { nullable: false, description: 'Created At' })
  createdAt: string;

  @Field(() => String, { nullable: false, description: 'Updated At' })
  updatedAt: string;

  @Field(() => String, {
    nullable: true,
    description: 'Check for first time visit',
  })
  isFirstTimeVisit?: string;

  @Field(() => [RaisedIssueData], {
    nullable: true,
    description: 'List of raised issues associated with the order.',
  })
  raisedIssues?: RaisedIssueData[];

  @Field(() => String, {
    nullable: true,
    description: 'Max Delivery date of the order product',
  })
  ecomInvoiceId?: string;

  @Field(() => String, {
    description: 'Min Delivery date of the order product',
    nullable: true,
  })
  ecomOrderId?: string;

  @Field(() => String, {
    description: 'Min Delivery date of the order product',
    nullable: true,
  })
  ecomSubOrderId?: string;

  @Field(() => String, {
    description: 'refundDetailAlreadyAdded',
    nullable: true,
  })
  refundDetailAlreadyAdded?: boolean;

  @Field(() => String, {
    description: 'Super Order id',
    nullable: true,
  })
  superOrderId?: string;

  @Field(() => [String], {
    description: 'Sub Order Ids',
    nullable: true,
  })
  subOrderIds?: [string];

  @Field(() => String, {
    description: 'Tentative Purchase Date',
    nullable: true,
  })
  tentativePurchaseDate?: string;

  @Field(() => SubOrderType, {
    nullable: true,
    description: 'Booking amount of BNPL Order',
  })
  subOrderType?: SubOrderType;

  @Field(() => Number, {
    nullable: true,
    description: 'Booking amount of BNPL Order',
  })
  productFinalAmount?: number;

  @Field(() => OrderProductData, {
    nullable: true,
    description: 'Booking amount of BNPL Order',
  })
  bnplProduct?: OrderProductData;

  @Field(() => String, {
    nullable: true,
    description: 'Type of order',
  })
  type?: string;

  @Field(() => ErrorData, {
    nullable: true,
    description: 'Error details',
  })
  errorDetails?: ErrorData;
}

@ObjectType()
export class Order {
  @Field(() => OrderData, { nullable: true, description: 'Order Data' })
  data: OrderData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class Orders {
  @Field(() => [OrderData], { nullable: true, description: 'Orders Data' })
  data: OrderData[];

  @Field(() => Int, { nullable: true, description: 'count' })
  count: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class OrderProducts {
  @Field(() => [OrderProductData], {
    nullable: true,
    description: 'Order Product Data',
  })
  data: OrderProductData[];

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
@ObjectType()
export class RaisedIssue {
  @Field(() => RaisedIssueData, {
    nullable: true,
    description: 'Raised Issue Data',
  })
  data: RaisedIssueData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
