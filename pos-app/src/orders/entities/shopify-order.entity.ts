import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class ShopifyOrderData {
  @Field(() => String, {
    nullable: true,
    description: 'Phone number of the order',
  })
  phone: string;

  @Field(() => String, {
    nullable: true,
    description: 'ID of the order',
  })
  id: string;

  @Field(() => String, {
    nullable: true,
    description: 'ID of the order',
  })
  name: string;

  @Field(() => String, {
    nullable: true,
    description: 'Total price of the order',
  })
  current_total_price: string;

  @Field(() => String, {
    nullable: true,
    description: 'Email of the order',
  })
  email: string;

  @Field(() => String, {
    nullable: true,
    description: 'Title of the order',
  })
  title: string;

  @Field(() => String, {
    nullable: true,
    description: 'ID of the order',
  })
  order_status_url: string;

  @Field(() => String, {
    nullable: true,
    description: 'Date of the order',
  })
  created_at: string;
}

@ObjectType()
export class ShopifyOrders {
  @Field(() => [ShopifyOrderData], {
    nullable: true,
    description: 'Shopify Order Data',
  })
  data: ShopifyOrderData[];

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
