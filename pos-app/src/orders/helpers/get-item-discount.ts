export const getDiscountAndFinalPrice = (
  quantity,
  price,
  totalDiscount,
  totalCartAmount,
) => {
  const itemQty = Number(quantity) || 1;
  const itemPrice = Number(price);
  const itemTotal = itemQty * itemPrice;

  const itemDiscount = Math.round(
    (totalDiscount * itemTotal) / totalCartAmount,
  );
  const finalItemPrice = Math.round(itemTotal - itemDiscount);
  return { itemDiscount, finalItemPrice };
};
