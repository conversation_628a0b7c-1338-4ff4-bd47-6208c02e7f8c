import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { InventoryTrackingData } from 'src/inventory-tracking/entities/inventory-tracking.entity';

interface Item {
  id: string;
  quantity: number;
}

interface InventoryTrackingItem {
  id: string;
  quantity: number;
  displayItemQuantity: number;
}

export const isSellableInventoryExist = (
  orderedItems: Item[],
  availableInventory: InventoryTrackingData[],
): boolean => {
  try {
    for (const item of orderedItems) {
      const inventoryItem = availableInventory.find(
        (data: InventoryTrackingItem) => data.id === item.id,
      );

      if (!inventoryItem) {
        return false;
      }
      const { quantity, displayItemQuantity = 0 } = inventoryItem;

      const sellableInventory = quantity - displayItemQuantity;

      if (item.quantity > sellableInventory) {
        return false;
      }
    }

    return true;
  } catch (error: any) {
    console.error('Error checking inventory:', error);
    throw new CustomError(error?.message || 'Failed to check inventory', 500);
  }
};
