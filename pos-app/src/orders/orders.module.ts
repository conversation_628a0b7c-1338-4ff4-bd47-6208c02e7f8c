import { Module } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersResolver } from './orders.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { ShopifyModule } from 'src/common/shopify/shopify.module';

@Module({
  providers: [OrdersResolver, OrdersService, SuccessHandler, ErrorHandler],
  imports: [
    DocumentClientModule,
    SsmClientModule,
    S3ClientModule,
    ConfigParametersModule,
    ShopifyModule,
  ],
})
export class OrdersModule {}
