import { InputType, Field } from '@nestjs/graphql';
import {
  AdditionalPromotionalCouponInput,
  CustomerMetadataInput,
  GSTDetailsInput,
  InteriorArchitectureDetailsInput,
  QuotationAddressInput,
} from 'src/quotations/dto/update-quotation.input';
import { OrderProductInput } from './create-order.input';
import { DeliveryType } from 'src/common/enum/delivery';

@InputType()
export class UpdateOrderInput {
  @Field(() => String, {
    nullable: false,
    description: 'Order ID',
  })
  id: string;

  @Field(() => Boolean, {
    nullable: true,
    description: 'Hold Order',
  })
  holdOrder: boolean;

  @Field(() => String, {
    nullable: true,
    description: 'ID of the employee creating the quotation.',
  })
  employeeId?: string;

  @Field(() => CustomerMetadataInput, {
    nullable: true,
    description: 'Customer metadata for the quotation.',
  })
  customer?: CustomerMetadataInput;

  @Field(() => QuotationAddressInput, {
    nullable: true,
    description: 'Shipping Address for the quotation.',
  })
  shippingAddress?: QuotationAddressInput;

  @Field(() => QuotationAddressInput, {
    nullable: true,
    description: 'Billing Address for the quotation.',
  })
  billingAddress?: QuotationAddressInput;

  @Field(() => GSTDetailsInput, { nullable: true })
  gstDetails?: GSTDetailsInput;

  @Field(() => InteriorArchitectureDetailsInput, {
    nullable: true,
    description: 'Interior Architecture.',
  })
  interiorArchitecture?: InteriorArchitectureDetailsInput;

  @Field(() => String, {
    nullable: true,
    description: 'Notes or comments for the order.',
  })
  notes?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer acknowledgement for the order.',
  })
  customerAcknowledgement?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer Identity proof for the order.',
  })
  customerIdentityProof?: string;

  @Field(() => [OrderProductInput], {
    nullable: true,
    description: 'List of products being quoted.',
  })
  orderProducts: OrderProductInput[];

  @Field(() => String, {
    description: 'Min Delivery date of the order',
    nullable: true,
  })
  minDeliveryDate?: string;

  @Field(() => String, {
    description: 'Unhold future dispatch date of the order',
    nullable: true,
  })
  unHoldFutureDispatchDate?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Reason for Holding the Order',
  })
  holdOrderReason?: string;

  @Field(() => String, { nullable: true })
  dispatchDate?: string;

  @Field(() => String, { nullable: true })
  eddHeaderId?: string;

  @Field(() => Boolean, { nullable: true })
  posFallbackEdd?: boolean;

  @Field(() => String, {
    description: 'Delivery date of the order',
    nullable: true,
  })
  deliveryDate?: string;

  @Field(() => DeliveryType, {
    description: 'Delivery Type of the order',
    nullable: true,
  })
  deliveryStatus: DeliveryType;

  @Field(() => Number, {
    nullable: true,
    description: 'delivery amount',
  })
  deliveryCharge?: number;

  @Field(() => Number, {
    nullable: true,
    description: 'Final Amount',
  })
  finalDiscountedAmount?: number;

  @Field(() => [AdditionalPromotionalCouponInput], {
    nullable: true,
    description: 'Additional Promotional Coupons',
  })
  additionalPromotionalCoupons?: AdditionalPromotionalCouponInput[];

  @Field(() => String, {
    description: 'Tentative Purchase Date',
    nullable: true,
  })
  tentativePurchaseDate?: string;
}
