import { InputType, Field, registerEnumType } from '@nestjs/graphql';
import { OrderFilterBy } from 'src/common/enum/order';
registerEnumType(OrderFilterBy, { name: 'OrderFilterBy' });

@InputType()
export class OrderTermSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'CustomerId from which user wants to retrieve the records',
  })
  customerId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  storeId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'orderType',
  })
  orderType?: string;

  @Field(() => Boolean, {
    nullable: true,
    description: 'holdOrder',
  })
  holdOrder?: boolean;

  @Field(() => String, {
    nullable: true,
    description: 'Type of order',
  })
  type?: string;
}

@InputType()
export class OrderTextSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  id?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  shopifyOrderName?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  quotationId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  employeeId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  status?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer Email from which user wants to retrieve the records',
  })
  'customeremail'?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Customer Phone from which user wants to retrieve the records',
  })
  'customerphone'?: string;
}

@InputType()
export class OrderSortingFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class GetOrderByQuotationIdInput {
  @Field(() => String, {
    nullable: false,
    description: 'Value should be in asc or desc',
  })
  quotationId: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  subOrderType?: string;
}

@InputType()
export class ListOrderInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size: string;

  @Field(() => OrderFilterBy, {
    nullable: false,
    description: 'Filter by which user wants to retrieve the records',
  })
  filterBy: OrderFilterBy;

  @Field(() => OrderTermSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  termSearchFields?: OrderTermSearchFieldsInput;

  @Field(() => OrderTextSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  textSearchFields?: OrderTextSearchFieldsInput;

  @Field(() => OrderSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { createdAt: 'desc' },
  })
  sortBy?: OrderSortingFieldsInput;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  fromDate?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  toDate?: string;
}
