import { InputType, Field, Int } from '@nestjs/graphql';
import { CustomDataInput } from 'src/carts/dto/create-cart.input';

import { DeliveryType } from 'src/common/enum/delivery';
import { ImageInput } from 'src/products/dto/image.input';

@InputType()
export class OrderProductInput {
  @Field(() => String, {
    nullable: true,
    description: 'ID of the order item',
  })
  id?: string;

  @Field(() => String, {
    nullable: false,
    description: 'ProductID of the order product',
  })
  productId: string;

  @Field(() => String, {
    nullable: true,
    description: 'VariantID of the order product',
  })
  variantId?: string;

  @Field(() => Int, {
    nullable: false,
    description: 'Quantity of the order product',
  })
  quantity: number;

  @Field(() => CustomDataInput, {
    nullable: true,
    description: 'Custom Meta data',
  })
  customData?: CustomDataInput;

  @Field(() => Number, { description: 'Price of the order product' })
  price: number;

  @Field(() => String, { description: 'Type of price', nullable: true })
  priceType: string;

  @Field(() => String, {
    nullable: true,
    description: 'Title of the order product',
  })
  title?: string;

  @Field(() => String, {
    nullable: true,
    description: 'VariantTitle of the order product',
  })
  variantTitle?: string;

  @Field(() => ImageInput, {
    nullable: true,
    description: 'Image of the order product',
  })
  image?: ImageInput;

  @Field(() => String, {
    nullable: false,
    description: 'Sku of the order product',
  })
  sku: string;

  @Field(() => String, { nullable: true })
  productType?: string;

  @Field(() => DeliveryType, {
    description: 'Delivery Type of the order product',
  })
  deliveryStatus: DeliveryType;

  @Field(() => String, { description: 'Delivery date of the order product' })
  deliveryDate: string;

  @Field(() => String, {
    description: 'Delivery range of the order product',
    nullable: true,
  })
  deliveryRange?: string;

  @Field(() => String, {
    description: 'Min Delivery date of the order product',
    nullable: true,
  })
  minDeliveryDate?: string;

  @Field(() => Boolean, {
    description: 'EDD assumed for this order product',
    nullable: true,
  })
  isAssumed?: boolean;

  @Field(() => String, { nullable: true })
  dispatchDate?: string;

  @Field(() => Boolean, { nullable: true })
  fallbackEdd?: boolean;

  @Field(() => Boolean, { nullable: true })
  hasColorOptions?: boolean;
}
@InputType()
export class CreateOrderInput {
  @Field(() => String, {
    nullable: false,
    description: 'Quotation ID of the customer for the order.',
  })
  quotationId: string;

  @Field(() => [OrderProductInput], {
    nullable: false,
    description: 'List of products being quoted.',
  })
  orderProducts: OrderProductInput[];

  @Field(() => String, {
    nullable: false,
    description: 'ID of the store issuing the order.',
  })
  storeId: string;

  @Field(() => String, {
    nullable: false,
    description: 'ID of the employee creating the order.',
  })
  employeeId: string;

  @Field(() => String, {
    description: 'Min Delivery date of the order',
    nullable: true,
  })
  minDeliveryDate?: string;

  @Field(() => String, {
    description: 'Unhold future dispatch date of the order',
    nullable: true,
  })
  unHoldFutureDispatchDate?: string;

  @Field(() => String, { description: 'Delivery date of the order' })
  deliveryDate?: string;

  @Field(() => String, { nullable: true })
  dispatchDate?: string;

  @Field(() => String, { nullable: true })
  eddHeaderId?: string;

  @Field(() => Boolean, { nullable: true })
  posFallbackEdd?: boolean;

  @Field(() => DeliveryType, {
    description: 'Delivery Type of the order',
    nullable: true,
  })
  deliveryStatus: DeliveryType;

  @Field(() => String, {
    nullable: true,
    description: 'Order notes',
  })
  notes?: string;

  @Field(() => String, {
    description: 'Tentative Purchase Date',
    nullable: true,
  })
  tentativePurchaseDate?: string;
}
