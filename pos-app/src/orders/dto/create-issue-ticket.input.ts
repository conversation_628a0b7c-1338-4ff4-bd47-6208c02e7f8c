import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CreateIssueTicketInput {
  @Field(() => Number, {
    nullable: false,
    description: 'Current status of the ticket',
  })
  status: number;

  @Field(() => String, {
    nullable: false,
    description: 'Description of the issue',
  })
  description: string;

  @Field(() => String, {
    nullable: true,
    description: 'RRTicketId',
  })
  RRTicketId?: string;

  @Field(() => Number, {
    nullable: false,
    description: 'Priority of the issue',
  })
  priority: number;

  @Field(() => String, {
    nullable: false,
    description: 'Type of the issue',
  })
  subject: string;

  @Field(() => String, {
    nullable: false,
    description: 'Order ID related to the issue',
  })
  id: string;

  @Field(() => String, {
    nullable: false,
    description: 'Shopify Order Name related to the issue',
  })
  orderId: string;

  @Field(() => String, {
    nullable: false,
    description: 'Phone number of the customer',
  })
  phone: string;

  @Field(() => String, {
    nullable: false,
    description: 'Name of the customer',
  })
  name: string;

  @Field(() => String, {
    nullable: false,
    description: 'Email of the customer',
  })
  email: string;

  @Field(() => Number, {
    nullable: true,
    description: 'Group_id of the ticket',
  })
  group_id?: any;

  @Field(() => String, {
    nullable: true,
    description: 'voc1 of the ticket',
  })
  voc1?: string;

  @Field(() => String, {
    nullable: true,
    description: 'voc2 of the ticket',
  })
  voc2?: string;
}
