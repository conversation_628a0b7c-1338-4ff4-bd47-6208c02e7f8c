import { Module } from '@nestjs/common';
import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { join } from 'path';
import { EmployeesModule } from './employees/employees.module';
import { ScheduleModule } from '@nestjs/schedule';
import { CronService } from './cron/cron.service';
import { ProductsModule } from './products/products.module';
import { EmiToolModule } from './common/emi-tool/emi-tool.module';
import { CouponsModule } from './coupons/coupons.module';
import { StoresModule } from './stores/stores.module';
import { QuotationsModule } from './quotations/quotations.module';
import { CartsModule } from './carts/carts.module';
import { CustomersModule } from './customers/customers.module';
import { PincodesModule } from './pincodes/pincodes.module';
import { WishlistModule } from './wishlist/wishlist.module';
import 'isomorphic-fetch';
import { ConfigModule } from '@nestjs/config';
import { DocumentClientModule } from './common/document-client/document-client.module';
import { SsmClientModule } from './common/ssm-client/ssm-client.module';
import { CampaignCouponsModule } from './campaign-coupons/campaign-coupons.module';
import { PaymentsModule } from './payments/payments.module';
import { RazorpayPosModule } from './common/razorpay-pos/razorpay-pos.module';
import { OrdersModule } from './orders/orders.module';
import { InteriorArchitecturesModule } from './interior-architectures/interior-architectures.module';
import { GlobalConfigurationsModule } from './global-configurations/global-configurations.module';
import { LocalConfigurationsModule } from './local-configurations/local-configurations.module';
import { GraphQLError } from 'graphql';
import { S3UploaderModule } from './s3-uploader/s3-uploader.module';
import { S3ClientModule } from './common/s3-client/s3-client.module';
import { ConfigParametersModule } from './config/config.module';
import { ShopifyModule } from './common/shopify/shopify.module';
import { SapModule } from './sap/sap.module';
import { ThankYouModule } from './thank-you/thank-you.module';
import { PayuModule } from './common/payu/payu.module';
import { SnapmintModule } from './common/snapmint/snapmint.module';
import { PineLabsModule } from './common/pine-labs/pine-labs.module';
import { CollectionsModule } from './collections/collections.module';
import { OfferCouponModule } from './offer-coupon/offer-coupon.module';
import { MSwipeModule } from './common/mswipe/m-swipe.module';
import { SkuPriceMasterModule } from './sku-price-master/sku-price-master.module';
import { STNModule } from './stn/stn.module';
import { GRNModule } from './grn/grn.module';
import { EasyEcomModule } from './common/easy-ecom/easy-ecom.module';
import { InventoryCredentialsModule } from './inventory-credentials/inventory-credentials.module';
import { InventoryTrackingsModule } from './inventory-tracking/inventory-tracking.module';
import { EddServiceModule } from './common/edd-service/edd-service.module';

import { NotificationModule } from './notifications/notifications.module';
import { CMSModule } from './cms/cms.module';
import { AllStoresModule } from './all-stores/all-stores.module';
import { StorePoModule } from './store-po/store-po.module';
import { GetDeliveryDateModule } from './get-delivery-date/get-delivery-date.module';
import { ReplacementOrdersModule } from './replacement-orders/replacement-orders.module';
import { RefundDetailsModule } from './refund-details/refund-details.module';
import { TechnicianApisModule } from './technician-apis/technician-apis.module';
import { IoModule } from './io/io.module';
import { SpinWheelModule } from './spin-wheel/spin-wheel.module';

import { CustomDiscountVerificationModule } from './custom-discount-verification/custom-discount-verification.module';
import { StoreOrdersApiKeyModule } from './store-orders-api-key/store-orders-api-key.module';
import { StoreOrdersDataModule } from './store-orders-data/store-orders-data.module';
import { BulkGenerateCouponsModule } from './bulk-generate-coupons/bulk-generate-coupons.module';

import { AiMattressModule } from './ai-mattress/ai-mattress-otp.module';
import { SendWhatsAppModule } from './send-whatsapp/whatsapp.module';
import { CancelOrderModule } from './cancel-order/cancel-order.module';
import { PriceModule } from './price/price.module';
import { PaymentWebhookModule } from './common/payment-webhooks/payment-webhook.module';
import { BulkSTNModule } from './common/bulk-stn/bulk-stn.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env.${process.env.NODE_ENV}`,
    }),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: join(process.cwd(), 'schema.gql'),
      playground: process.env.NODE_ENV !== 'prod',
      context: ({ req, res }) => ({ req, res }),
      formatError: (error: GraphQLError) => {
        return {
          message: error.message,
          locations: error.locations,
          path: error.path,
        };
      },
    }),
    ScheduleModule.forRoot(),
    SsmClientModule,
    S3ClientModule,
    DocumentClientModule,
    EmployeesModule,
    ProductsModule,
    EmiToolModule,
    CouponsModule,
    StoresModule,
    PaymentsModule,
    RazorpayPosModule,
    QuotationsModule,
    PayuModule,
    CartsModule,
    CustomersModule,
    PincodesModule,
    WishlistModule,
    CampaignCouponsModule,
    OrdersModule,
    InteriorArchitecturesModule,
    GlobalConfigurationsModule,
    LocalConfigurationsModule,
    S3UploaderModule,
    ConfigParametersModule,
    ShopifyModule,
    SapModule,
    ThankYouModule,
    SnapmintModule,
    PineLabsModule,
    MSwipeModule,
    CollectionsModule,
    OfferCouponModule,
    SkuPriceMasterModule,
    STNModule,
    GRNModule,
    EasyEcomModule,
    EddServiceModule,
    InventoryCredentialsModule,
    CMSModule,
    InventoryTrackingsModule,
    StorePoModule,
    NotificationModule,
    GetDeliveryDateModule,
    NotificationModule,
    AllStoresModule,
    ReplacementOrdersModule,
    RefundDetailsModule,
    IoModule,
    SpinWheelModule,
    BulkSTNModule,
    CustomDiscountVerificationModule,
    StoreOrdersApiKeyModule,
    StoreOrdersDataModule,
    TechnicianApisModule,
    BulkGenerateCouponsModule,
    CancelOrderModule,
    AiMattressModule,
    SendWhatsAppModule,
    CancelOrderModule,
    PriceModule,
    PaymentWebhookModule,
  ],
  controllers: [AppController],
  providers: [AppService, CronService],
})
export class AppModule {
  // configure(consumer: MiddlewareConsumer) {
  //   consumer.apply(AuthMiddleware).forRoutes('graphql');
  // }
}
