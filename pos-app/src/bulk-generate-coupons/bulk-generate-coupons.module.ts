import { Module } from '@nestjs/common';
import { BulkGenerateCouponsService } from './bulk-generate-coupons.service';
import { BulkGenerateCouponsResolver } from './bulk-generate-coupons.resolver';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { ShopifyModule } from 'src/common/shopify/shopify.module';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';

@Module({
  providers: [
    BulkGenerateCouponsResolver,
    BulkGenerateCouponsService,
    SuccessHandler,
    ErrorHandler,
    SsmClientModule,
    DocumentClientModule,
    AppDocumentClient,
    S3ClientModule,
    ConfigParametersModule,
    ShopifyModule,
    AppSsmClient,
    AppS3Client,
    AppConfigParameters,
    AppShopify,
  ],
  exports: [SuccessHandler, ErrorHandler], // Export if needed in other modules
})
export class BulkGenerateCouponsModule {}
