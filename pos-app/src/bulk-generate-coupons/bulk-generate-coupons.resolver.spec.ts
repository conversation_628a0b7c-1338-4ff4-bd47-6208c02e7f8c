import { Test, TestingModule } from '@nestjs/testing';
import { BulkGenerateCouponsResolver } from './bulk-generate-coupons.resolver';
import { BulkGenerateCouponsService } from './bulk-generate-coupons.service';

describe('BulkGenerateCouponsResolver', () => {
  let resolver: BulkGenerateCouponsResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BulkGenerateCouponsResolver, BulkGenerateCouponsService],
    }).compile();

    resolver = module.get<BulkGenerateCouponsResolver>(
      BulkGenerateCouponsResolver,
    );
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
