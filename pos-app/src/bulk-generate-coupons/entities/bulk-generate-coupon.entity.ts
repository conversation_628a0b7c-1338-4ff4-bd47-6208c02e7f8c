import { ObjectType, Field, registerEnumType } from '@nestjs/graphql';

export enum BulkCouponStatus {
  INITIATED = 'INITIATED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

registerEnumType(BulkCouponStatus, { name: 'BulkCouponStatus' });
@ObjectType()
export class BulkGenerateCouponData {
  @Field(() => String, { description: 'S3 file path for CSV' })
  filePath: string;

  @Field(() => String, { description: 'createdAt' })
  createdAt: string;

  @Field(() => String, { description: 'updatedAt' })
  updatedAt: string;

  @Field(() => BulkCouponStatus, { description: 'status' })
  status: BulkCouponStatus;

  @Field(() => String, { description: 'error', nullable: true })
  errorMessage?: string;
}

@ObjectType()
export class BulkGenerateCoupon {
  @Field(() => BulkGenerateCouponData, { nullable: true })
  data: BulkGenerateCouponData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
@ObjectType()
export class BulkGenerateCoupons {
  @Field(() => [BulkGenerateCouponData], { nullable: true })
  data: [BulkGenerateCouponData];

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
