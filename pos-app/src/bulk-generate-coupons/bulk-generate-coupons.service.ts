import { Injectable } from '@nestjs/common';
import { CreateBulkGenerateCouponInput } from './dto/create-bulk-generate-coupon.input';
import { UpdateBulkGenerateCouponInput } from './dto/update-bulk-generate-coupon.input';
import { CreateBulkCoupons } from './lib/create-bulk-coupons';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
import { GetBulkCoupons } from './lib/list-bulk-coupons-config';
import { AppShopify } from 'src/common/shopify/shopify';

@Injectable()
export class BulkGenerateCouponsService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}
  async create(createBulkGenerateCouponInput: CreateBulkGenerateCouponInput) {
    const createHandler = new CreateBulkCoupons(
      this.configService,
      this.docClient,
      this.configParameters,
      this.s3Client,
    );
    return await createHandler.processBulkCoupons(
      createBulkGenerateCouponInput,
    );
  }

  async findAll() {
    const listHandler = new GetBulkCoupons(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await listHandler.listBulkCoupons();
  }

  findOne(id: number) {
    return `This action returns a #${id} bulkGenerateCoupon`;
  }

  update(
    id: number,
    updateBulkGenerateCouponInput: UpdateBulkGenerateCouponInput,
  ) {
    return `This action updates a #${id} bulkGenerateCoupon ${updateBulkGenerateCouponInput}`;
  }

  remove(id: number) {
    return `This action removes a #${id} bulkGenerateCoupon`;
  }
}
