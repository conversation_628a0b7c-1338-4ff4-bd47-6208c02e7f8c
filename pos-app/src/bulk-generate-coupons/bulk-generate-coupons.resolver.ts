import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { BulkGenerateCouponsService } from './bulk-generate-coupons.service';
import {
  BulkGenerateCoupon,
  BulkGenerateCoupons,
} from './entities/bulk-generate-coupon.entity';
import { CreateBulkGenerateCouponInput } from './dto/create-bulk-generate-coupon.input';
import { UpdateBulkGenerateCouponInput } from './dto/update-bulk-generate-coupon.input';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { AdminGuard } from 'src/auth/roles.guard';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { CustomAuthGuard } from 'src/auth/auth.guard';

@Resolver(() => BulkGenerateCoupon)
export class BulkGenerateCouponsResolver {
  constructor(
    private readonly bulkGenerateCouponsService: BulkGenerateCouponsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => BulkGenerateCoupon)
  @UseGuards(CustomAuthGuard, AdminGuard)
  async createBulkGenerateCoupon(
    @Args('createBulkGenerateCouponsInput')
    bulkGenerateCouponsInput: CreateBulkGenerateCouponInput,
  ) {
    try {
      const data = await this.bulkGenerateCouponsService.create(
        bulkGenerateCouponsInput,
      );

      const response = this.successHandler.getSuccessResponse({
        data: data,
        code: 201,
      });
      console.log('response', response);

      return response;
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to generate coupons',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => BulkGenerateCoupons, { name: 'listBulkGenerateCoupons' })
  findAll() {
    try {
      const data = this.bulkGenerateCouponsService.findAll();
      const response = this.successHandler.getSuccessResponse({
        data: data,
        code: 200,
      });
      return response;
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list bulk coupons',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => BulkGenerateCoupon, { name: 'bulkGenerateCoupon' })
  findOne(@Args('id', { type: () => Int }) id: number) {
    return this.bulkGenerateCouponsService.findOne(id);
  }

  @Mutation(() => BulkGenerateCoupon)
  updateBulkGenerateCoupon(
    @Args('updateBulkGenerateCouponInput')
    updateBulkGenerateCouponInput: UpdateBulkGenerateCouponInput,
  ) {
    return this.bulkGenerateCouponsService.update(
      updateBulkGenerateCouponInput.id,
      updateBulkGenerateCouponInput,
    );
  }

  @Mutation(() => BulkGenerateCoupon)
  removeBulkGenerateCoupon(@Args('id', { type: () => Int }) id: number) {
    return this.bulkGenerateCouponsService.remove(id);
  }
}
