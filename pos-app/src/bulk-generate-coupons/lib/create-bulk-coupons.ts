import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import csv from 'csvtojson';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import {
  PutCommand,
  UpdateCommand,
  TransactWriteCommand,
} from '@aws-sdk/lib-dynamodb';
import { GetS3Object } from 'src/s3-uploader/lib/get-s3-object';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { CreateBulkGenerateCouponInput } from '../dto/create-bulk-generate-coupon.input';

interface CouponData {
  phone: string;
  code: string;
}

@Injectable()
export class CreateBulkCoupons {
  private readonly BATCH_SIZE = 100;

  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
  ) {}

  async processBulkCoupons(input: CreateBulkGenerateCouponInput) {
    const { filePath } = input;
    posLogger.info('Bulk Coupon Processing', 'processBulkCoupons', {
      filePath,
    });

    const BULK_COUPON_TABLE =
      await this.configParameters.getBulkCouponGenerationTableName();
    const CUSTOMER_COUPON_TABLE =
      await this.configParameters.getCustomerSpecificCouponTableName();
    const timestamp = new Date().toISOString();
    const id = uuidv4();

    const getHandler = new GetS3Object(this.configService, this.s3Client);
    const s3Key = filePath.includes('.amazonaws.com/')
      ? filePath.split('.amazonaws.com/')[1]
      : filePath;

    const tempFilePath = path.join('/tmp', path.basename(s3Key));
    const s3Object = await getHandler.getObject(s3Key);
    const csvData = await s3Object.Body.transformToString();
    fs.writeFileSync(tempFilePath, csvData);

    const couponData = await this.validateCSV(tempFilePath);
    try {
      const bulkCouponEntry = {
        id,
        filePath,
        status: 'INITIATED',
        createdAt: timestamp,
        updatedAt: timestamp,
        errorMessage: null,
      };

      await this.docClient.createItem(
        new PutCommand({ TableName: BULK_COUPON_TABLE, Item: bulkCouponEntry }),
      );

      this.processInBatches(
        couponData,
        CUSTOMER_COUPON_TABLE,
        timestamp,
        BULK_COUPON_TABLE,
        id,
      );

      fs.unlinkSync(tempFilePath);
      return bulkCouponEntry;
    } catch (error) {
      posLogger.error('Bulk Coupon Processing', 'processBulkCoupons', {
        error,
      });

      await this.docClient.updateItem(
        new UpdateCommand({
          TableName: BULK_COUPON_TABLE,
          Key: { id },
          UpdateExpression:
            'SET #status = :status, #updatedAt = :updatedAt, #errorMessage = :errorMessage',
          ExpressionAttributeNames: {
            '#status': 'status',
            '#updatedAt': 'updatedAt',
            '#errorMessage': 'errorMessage',
          },
          ConditionExpression: 'attribute_exists(id)',
          ExpressionAttributeValues: {
            ':status': 'FAILED',
            ':updatedAt': new Date().toISOString(),
            ':errorMessage': error.message,
          },
        }),
      );

      throw error;
    }
  }

  private async processInBatches(
    couponData: CouponData[],
    tableName: string,
    timestamp: string,
    bulkTableName: string,
    id: string,
  ) {
    try {
      for (let i = 0; i < couponData.length; i += this.BATCH_SIZE) {
        const batch = couponData.slice(i, i + this.BATCH_SIZE);
        const transactItems = batch.map((item) => ({
          Put: {
            TableName: tableName,
            Item: { phone: item.phone, code: item.code, createdAt: timestamp },
            ConditionExpression: 'attribute_not_exists(#code)',
            ExpressionAttributeNames: { '#code': 'code' },
          },
        }));

        await this.docClient.transactWriteItems(
          new TransactWriteCommand({ TransactItems: transactItems }),
        );
        posLogger.info('Batch Processing', 'processBatch', {
          batchStart: i,
          batchEnd: i + batch.length,
          batchSize: batch.length,
        });
      }

      await this.docClient.updateItem(
        new UpdateCommand({
          TableName: bulkTableName,
          Key: { id },
          UpdateExpression: 'SET #status = :status, #updatedAt = :updatedAt',
          ExpressionAttributeNames: {
            '#status': 'status',
            '#updatedAt': 'updatedAt',
          },
          ConditionExpression: 'attribute_exists(id)',
          ExpressionAttributeValues: {
            ':status': 'COMPLETED',
            ':updatedAt': new Date().toISOString(),
          },
        }),
      );
    } catch (batchError) {
      posLogger.error('Batch Processing Error', 'processBatch', {
        error: batchError,
      });
      await this.docClient.updateItem(
        new UpdateCommand({
          TableName: bulkTableName,
          Key: { id },
          UpdateExpression:
            'SET #status = :status, #updatedAt = :updatedAt, #errorMessage = :errorMessage',
          ExpressionAttributeNames: {
            '#status': 'status',
            '#updatedAt': 'updatedAt',
            '#errorMessage': 'errorMessage',
          },
          ConditionExpression: 'attribute_exists(id)',
          ExpressionAttributeValues: {
            ':status': 'FAILED',
            ':updatedAt': new Date().toISOString(),
            ':errorMessage': batchError.message,
          },
        }),
      );
    }
  }

  private async validateCSV(filePath: string): Promise<CouponData[]> {
    posLogger.info('CSV Validation', 'validateCSV', { filePath });

    try {
      const fileContent = fs.readFileSync(filePath, 'utf-8');
      const firstLine = fileContent.split('\n')[0].trim();

      const expectedHeaders = ['phone', 'code'];

      const headers = firstLine.split(',').map((header) => header.trim()); // Assuming CSV format

      if (headers.length < 2) {
        throw new CustomError(
          `Invalid CSV format: Expected at least 2 headers, but found ${headers.length}.`,
          400,
        );
      }

      if (headers[0] !== expectedHeaders[0]) {
        throw new CustomError(
          `Invalid CSV format: First header must be '${expectedHeaders[0]}', but found '${headers[0]}'.`,
          400,
        );
      }

      if (headers[1] !== expectedHeaders[1]) {
        throw new CustomError(
          `Invalid CSV format: Second header must be '${expectedHeaders[1]}', but found '${headers[1]}'.`,
          400,
        );
      }

      const jsonArray = await csv({
        noheader: false,
        headers: ['phone', 'code'],
        trim: true,
      }).fromFile(filePath);

      if (jsonArray.length === 0) {
        throw new CustomError('CSV file is empty.', 400);
      }

      const firstRow = jsonArray[0];
      if (!firstRow.hasOwnProperty('phone')) {
        throw new CustomError('Missing "phone" column in CSV file.', 400);
      }

      if (!firstRow.hasOwnProperty('code')) {
        throw new CustomError('Missing "code" column in CSV file.', 400);
      }
      console.log('jsonArray', jsonArray);

      const validatedData = jsonArray.map((row, index) => {
        if (row.phone === null || row.phone === undefined) {
          throw new CustomError(
            `Null or undefined phone number at row ${index + 2}.`,
            400,
          );
        }

        if (row.code === null || row.code === undefined) {
          throw new CustomError(
            `Null or undefined coupon code at row ${index + 2}.`,
            400,
          );
        }

        const phoneString = row.phone.toString().trim();
        if (!/^\d{10}$/.test(phoneString)) {
          throw new CustomError(
            `Invalid phone number format at row ${index + 2}: ${phoneString}. Must be exactly 10 digits.`,
            400,
          );
        }

        const codeString = row.code.toString().trim();
        if (codeString === '') {
          throw new CustomError(
            `Empty coupon code found at row ${index + 2} for phone number: ${phoneString}`,
            400,
          );
        }

        return {
          phone: phoneString,
          code: codeString,
        };
      });

      return validatedData;
    } catch (error: any) {
      if (error.message.includes('Unexpected token')) {
        throw new CustomError(
          'Invalid CSV format. Please check the file structure.',
          400,
        );
      }

      throw new CustomError(error.message || 'Invalid CSV file.', 400);
    }
  }
}
