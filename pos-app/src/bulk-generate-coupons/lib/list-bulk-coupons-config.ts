import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { ScanDB } from 'src/common/helper/scan-table';
import { BulkGenerateCouponData } from '../entities/bulk-generate-coupon.entity';

export class GetBulkCoupons {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async listBulkCoupons(): Promise<BulkGenerateCouponData[]> {
    posLogger.info('BulkCoupons', 'listBulkCoupons', '');
    try {
      const BULK_COUPON_TABLE =
        await this.configParameters.getBulkCouponGenerationTableName();

      const scanDBHandler = new ScanDB(this.docClient);
      const bulkCoupons = await scanDBHandler.scanTable(BULK_COUPON_TABLE);
      bulkCoupons.sort(
        (a, b) =>
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
      );

      return bulkCoupons;
    } catch (e) {
      posLogger.error('BulkCoupons', 'listBulkCoupons', {
        error: e,
      });
      throw e;
    }
  }
}
