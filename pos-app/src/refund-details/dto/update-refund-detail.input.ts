import { InputType, Field } from '@nestjs/graphql';
import { IsNotEmpty, IsString, IsOptional, IsBoolean, IsNumber } from 'class-validator';

@InputType()
export class UpdateRefundDetailInput {
  @Field(() => String)
  @IsNotEmpty()
  @IsString()
  id: string;

  @Field(() => String)
  @IsNotEmpty()
  @IsString()
  shopifyOrderId: string;

  // Transaction update fields (optional)
  @Field(() => String, { nullable: true, description: 'Transaction ID for the refund' })
  @IsOptional()
  @IsString()
  transactionId?: string;

  @Field(() => Number, { nullable: true, description: 'Amount refunded ' })
  @IsOptional()
  @IsNumber()
  refundAmount?: number;

  @Field(() => String, { nullable: true, description: 'Date when refund was processed' })
  @IsOptional()
  @IsString()
  refundDate?: string;

  @Field(() => String, { nullable: true, description: 'Status of the refund' })
  @IsOptional()
  @IsString()
  status?: string;

  @Field(() => String, { nullable: true, description: 'sku for refund' })
  @IsOptional()
  @IsString()
  sku?: string;

  // Refund form fields (optional)
  @Field(() => Boolean, { nullable: true, description: 'Is Back To Source' })
  @IsOptional()
  @IsBoolean()
  isBackToSource?: boolean;

  @Field(() => String, { nullable: true, description: 'Account Holder Name' })
  @IsOptional()
  @IsString()
  accountHolderName?: string;

  @Field(() => String, { nullable: true, description: 'Bank Account Name' })
  @IsOptional()
  @IsString()
  bankAccountName?: string;

  @Field(() => String, { nullable: true, description: 'IFSC Code' })
  @IsOptional()
  @IsString()
  ifscCode?: string;

  @Field(() => String, { nullable: true, description: 'Account No' })
  @IsOptional()
  @IsString()
  accountNo?: string;

  @Field(() => String, { nullable: true, description: 'Cancelled Cheque Image S3 Key' })
  @IsOptional()
  @IsString()
  chequeImageKey?: string;

  @Field(() => String, { nullable: true, description: 'UPI ID' })
  @IsOptional()
  @IsString()
  upiId?: string;

  @Field(() => String, { nullable: true, description: 'UPI Name' })
  @IsOptional()
  @IsString()
  upiName?: string;

  @Field(() => Boolean, { nullable: true, description: 'Form submission status' })
  @IsOptional()
  @IsBoolean()
  isFormSubmitted?: boolean;

  @Field(() => String, { nullable: true, description: 'Order type (CANCELLATION, RETURN, REPLACEMENT)' })
  @IsOptional()
  @IsString()
  orderType?: string;
}