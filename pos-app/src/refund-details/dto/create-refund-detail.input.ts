import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CreateRefundDetailInput {
  @Field(() => String)
  id: string;

  @Field(() => String)
  shopifyOrderId: string;

  @Field(() => Boolean, { description: 'Is Back To Source' })
  isBackToSource: boolean;

  @Field(() => String, { nullable: true, description: 'Account Holder Name' })
  accountHolderName?: string;

  @Field(() => String, { nullable: true, description: 'Bank Account Name' })
  bankAccountName?: string;

  @Field(() => String, { nullable: true, description: 'IFSC Code' })
  ifscCode?: string;

  @Field(() => String, { nullable: true, description: 'Account No' })
  accountNo?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Cancelled Checkque Image S3 Key',
  })
  chequeImageKey?: string;

  @Field(() => String, { nullable: true })
  upiName?: string;

  @Field(() => String, { nullable: true })
  upiId?: string;

  @Field(() => Boolean, { nullable: true })
  isFormSubmitted?: boolean;

  

  @Field(()=>String,{nullable:true})
  requestId?:string
}

@InputType()
export class CreateCancellationRefundInput {
  @Field(() => String)
  id: string;

  @Field(() => String)
  shopifyOrderId: string;

  @Field(() => Boolean, { description: 'Is Back To Source' })
  isBackToSource: boolean;

  @Field(() => String, { nullable: true, description: 'Account Holder Name' })
  accountHolderName?: string;

  @Field(() => String, { nullable: true, description: 'Bank Account Name' })
  bankAccountName?: string;

  @Field(() => String, { nullable: true, description: 'IFSC Code' })
  ifscCode?: string;

  @Field(() => String, { nullable: true, description: 'Account No' })
  accountNo?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Cancelled Cheque Image S3 Key',
  })
  chequeImageKey?: string;

  @Field(() => String, { nullable: true, description: 'UPI ID' })
  upiId?: string;

  @Field(() => Boolean, { nullable: true })
  isFormSubmitted?: boolean;

  @Field(()=>String,{nullable:true})
  requestId?:string

  @Field(()=>String,{nullable:true})
  customerEmail?:string

  @Field(()=>String,{nullable:true})
  customerPhone?:string

  @Field(()=>String,{nullable:true})
  customerFirstName?:string

  @Field(()=>String,{nullable:true})
  customerLastName?:string
}
