import { Field, InputType } from '@nestjs/graphql';
import { IsBoolean, IsDate, IsNumber, IsOptional, IsString } from 'class-validator';

@InputType()
export class ListRefundDetailInput {
  @Field(() => Number, { nullable: true })
  @IsOptional()
  @IsNumber()
  page?: number;

  @Field(() => Number, { nullable: true })
  @IsOptional()
  @IsNumber()
  pageSize?: number;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  id?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  shopifyOrderId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  status?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isBackToSource?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  formSubmitted?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  transactionId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  fromDate?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  toDate?: string;

  // Add more filter fields as needed
}