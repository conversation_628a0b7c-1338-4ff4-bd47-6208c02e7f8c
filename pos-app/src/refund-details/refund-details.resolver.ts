import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { RefundDetailsService } from './refund-details.service';
import {
  RefundDetail,
  RefundDetails,
  RefundExportResponse,
  RefundSmsResponse,
} from './entities/refund-detail.entity';
import {
  CreateCancellationRefundInput,
  CreateRefundDetailInput,
} from './dto/create-refund-detail.input';
import { HttpStatus, UsePipes } from '@nestjs/common';
import { RefundDetailsValidatorPipe } from 'src/pipes/refund-details-validator/refund-details-validator.pipe';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { UpdateRefundDetailInput } from './dto/update-refund-detail.input';
import { ListRefundDetailInput } from './dto/list-refund-detail.input';

@Resolver(() => RefundDetail)
export class RefundDetailsResolver {
  constructor(
    private readonly refundDetailsService: RefundDetailsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => RefundDetail, { name: 'createRefundDetail' })
  @UsePipes(RefundDetailsValidatorPipe)
  async createRefundDetail(
    @Args('createRefundDetailInput')
    createRefundDetailInput: CreateRefundDetailInput,
  ) {
    try {
      const data = await this.refundDetailsService.create(
        createRefundDetailInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create refund detail',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => RefundDetail, { name: 'getRefundDetail' })
  async findOne(
    @Args('id', { type: () => String }) id: string,
    @Args('shopifyOrderId', { type: () => String }) shopifyOrderId: string,
  ) {
    try {
      const data = await this.refundDetailsService.findOne(id, shopifyOrderId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get refund detail',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => RefundDetails, { name: 'getAllRefundDetails' })
  async findAll(
    @Args('filter', { nullable: true }) filter?: ListRefundDetailInput,
  ) {
    try {
      console.log("filter in args of refund",filter)
      const { data = {}, count } =
        await this.refundDetailsService.findAll(filter);
      
      const successRes = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });

      return {
        ...successRes,
        count,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get refund details',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => RefundDetail, { name: 'updateRefundStatus' })
  async updateRefundStatus(
    @Args('updateRefundStatusInput')
    updateRefundStatusInput: UpdateRefundDetailInput,
  ) {
    try {
      const data = await this.refundDetailsService.updateRefundStatus(
        updateRefundStatusInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update refund status',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => RefundDetail, { name: 'createCancellationRefund' })
  async createCancellationRefund(
    @Args('createCancellationRefundInput')
    createCancellationRefundInput: CreateCancellationRefundInput,
  ) {
    try {
      const data = await this.refundDetailsService.createCancellationRefund(
        createCancellationRefundInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create cancellation refund',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => RefundExportResponse, { name: 'exportRefundDetails' })
  async exportRefundDetails(
    @Args('email', { type: () => String }) email: string,
    @Args('filter', { nullable: true }) filter?: ListRefundDetailInput,
  ) {
    try {
      await this.refundDetailsService.exportRefundDetailsCSV(filter, email);

      return this.successHandler.getSuccessResponse({
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to export refund details',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });

      return errorResponse;
    }
  }

  @Query(() => RefundSmsResponse, { name: 'sendRefundFormSms' })
  async sendRefundFormSms(
    @Args('id', { type: () => String }) id: string,
    @Args('shopifyOrderId', { type: () => String }) shopifyOrderId: string,
  ) {
    try {
      await this.refundDetailsService.sendRefundFormSms(id, shopifyOrderId);

      return this.successHandler.getSuccessResponse({
        code: 200,
      });
    } catch (error) {
      // Return error response
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to send refund form SMS',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });

      return errorResponse;
    }
  }
}
