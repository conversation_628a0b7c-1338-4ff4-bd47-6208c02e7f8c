import { Injectable } from '@nestjs/common';
import {
  CreateCancellationRefundInput,
  CreateRefundDetailInput,
} from './dto/create-refund-detail.input';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { CreateRefundDetail } from './lib/create-refund-detail';
import { GetRefundDetail } from './lib/get-refund-detail';
import { QueryRefundDetails } from './lib/get-all-refund-details';
import { UpdateRefundStatus } from './lib/update-refund-details';
import { UpdateRefundDetailInput } from './dto/update-refund-detail.input';
import { RefundExportService } from './lib/export-refund-detail';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { RefundSmsService } from './lib/send-refund-form';

@Injectable()
export class RefundDetailsService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
  ) {}
  async create(createRefundDetailInput: CreateRefundDetailInput) {
    const createHandler = new CreateRefundDetail(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
    );
    return await createHandler.createRefundDetail(createRefundDetailInput);
  }

  async findOne(id: string, shopifyOrderId: string) {
    const getHandler = new GetRefundDetail(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await getHandler.getRefundDetail(id, shopifyOrderId);
  }

  async findAll(filter) {
    const getAllHandler = new QueryRefundDetails(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return await getAllHandler.queryRefundDetails(filter);
  }

  async updateRefundStatus(updateRefundStatusInput: UpdateRefundDetailInput) {
    const updateHandler = new UpdateRefundStatus(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client
    );
    return await updateHandler.updateRefundDetail(updateRefundStatusInput);
  }

  async createCancellationRefund(
    createCancellationRefundInput: CreateCancellationRefundInput,
  ) {
    const createHandler = new CreateRefundDetail(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
    );
    return await createHandler.createCancellationRefundDetail(
      createCancellationRefundInput,
    );
  }

  async exportRefundDetailsCSV(filter: any, email: string) {
    const exportHandler = new RefundExportService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
    );
    return await exportHandler.exportRefundDetailsCSV(filter, email);
  }

  async sendRefundFormSms(id: string, shopifyOrderId: string) {
    const smsHandler = new RefundSmsService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.s3Client,
    );
    return await smsHandler.sendRefundFormSms(id, shopifyOrderId);
  }
}
