import { ObjectType, Field } from '@nestjs/graphql';
import { RaisedIssueData } from 'src/orders/entities/order.entity';

@ObjectType()
export class RefundDetailData {
  @Field(() => String)
  id: string;

  @Field(() => String)
  shopifyOrderId: string;

  @Field(() => Boolean, { description: 'Is Back To Source' })
  isBackToSource: boolean;

  @Field(() => String, { nullable: true, description: 'Account Holder Name' })
  accountHolderName?: string;

  @Field(() => String, { nullable: true, description: 'Bank Account Name' })
  bankAccountName?: string;

  @Field(() => String, { nullable: true, description: 'IFSC Code' })
  ifscCode?: string;

  @Field(() => String, { nullable: true, description: 'Account No' })
  accountNo?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Cancelled Checkque Image S3 Key',
  })
  chequeImageKey?: string;

  @Field(() => RaisedIssueData, {
    nullable: true,
    description: 'Cancelled Checkque Image S3 Key',
  })
  refundTicket?: RaisedIssueData;

  @Field(() => String, { nullable: true, description: 'Transaction ID' })
  transactionId?: string;

  @Field(() => String, { nullable: true, description: 'Refund Date' })
  refundedAt?: string;

  @Field(() => String, { nullable: true, description: 'Refund Status' })
  status?: string;

  @Field(() => String, { nullable: false, description: 'Created At' })
  createdAt: string;

  @Field(() => String, { nullable: false, description: 'Updated At' })
  updatedAt: string;

  @Field(() => String, { nullable: true, description: 'replacementOrderId' })
  replacementOrderId?: string;

  @Field(() => String, { nullable: true })
  sku?: string;

  @Field(() => String, { nullable: true })
  upiName?: string;

  @Field(() => String, { nullable: true })
  upiId?: string;

  @Field(() => String, { nullable: true })
  refundType?: string;

  @Field(() => Boolean, { nullable: true })
  isFormSubmitted?: boolean;

  @Field(() => Number, { nullable: true })
  refundedPrice?: number;
  
  @Field(() => Number, { nullable: true })
  priceAfterDiscount?:number

  @Field(()=>Number,{nullable:true})
  refundAmount?:number

  @Field(()=>String,{nullable:true})
  requestId?:string
}

@ObjectType()
export class RefundDetail {
  @Field(() => RefundDetailData, {
    nullable: true,
    description: 'Refund Detail',
  })
  data: RefundDetailData;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class RefundDetails {
  @Field(() => [RefundDetailData], {
    nullable: true,
    description: 'Refund Details Array',
  })
  data: RefundDetailData[];

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;

  @Field(() => Number, { nullable: true })
  count?: number;
}

@ObjectType()
export class RefundSmsResponse {
  @Field(() => String)
  status: string;

  @Field(() => Boolean)
  success: boolean;

  @Field(() => String, { nullable: true })
  message?: string;
}

// Define a specific type for export response
@ObjectType()
export class RefundExportResponse {
  @Field(() => String)
  status: string;

  @Field(() => Boolean)
  success: boolean;

  @Field(() => String, { nullable: true })
  message?: string;
}
