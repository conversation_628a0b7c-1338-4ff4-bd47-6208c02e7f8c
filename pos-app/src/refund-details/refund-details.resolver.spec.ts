import { Test, TestingModule } from '@nestjs/testing';
import { RefundDetailsResolver } from './refund-details.resolver';
import { RefundDetailsService } from './refund-details.service';

describe('RefundDetailsResolver', () => {
  let resolver: RefundDetailsResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RefundDetailsResolver, RefundDetailsService],
    }).compile();

    resolver = module.get<RefundDetailsResolver>(RefundDetailsResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
