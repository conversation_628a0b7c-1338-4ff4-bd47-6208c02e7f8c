// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { RefundDetailData } from '../entities/refund-detail.entity';
export class GetRefundDetail {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getRefundDetail(
    id: string,
    shopifyOrderId: string,
  ): Promise<RefundDetailData> {
    posLogger.info('refundDetail', 'getRefundDetail', {
      input: { shopifyOrderId, id },
    });
    try {
      const REFUND_DETAIL_TABLE =
        await this.configParameters.getRefundDetailTableName();

      const refundDetailCommand = new GetCommand({
        TableName: REFUND_DETAIL_TABLE,
        Key: {
          shopifyOrderId,
          id,
        },
      });

      const { Item: data }: { Item: RefundDetailData } =
        await this.docClient.getItem(refundDetailCommand);

      console.log('refund detail data', data);
      if (!data) {
        throw new CustomError(
          `Refund Detail for ${shopifyOrderId} not found`,
          404,
        );
      }

      return data;
    } catch (e) {
      posLogger.error('refundDetail', 'getRefundDetail', {
        error: e,
      });
      throw e;
    }
  }


}
