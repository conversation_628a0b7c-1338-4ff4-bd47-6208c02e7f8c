import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { CreateFinalOrder } from 'src/replacement-orders/lib/create-final-order';
import { GetReplacementOrder } from 'src/replacement-orders/lib/get-replacement-order';
import { GetRefundDetail } from './get-refund-detail';
import { AppS3Client } from 'src/common/s3-client/s3-client';

@Injectable()
export class RefundSmsService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
  ) {}

  async sendRefundFormSms(
    id: string,
    shopifyOrderId: string,
  ): Promise<{ success: boolean; message: string }> {
    posLogger.info('RefundSmsService', 'sendRefundFormSms', {
      message: 'Starting refund form SMS process',
      id,
      shopifyOrderId,
    });

    try {
      // Get refund detail to check if form is already submitted
      const getRefundDetailHandler = new GetRefundDetail(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const refundDetail = await getRefundDetailHandler.getRefundDetail(
        id,
        shopifyOrderId,
      );

      // Check if form is already submitted
      if (refundDetail.isFormSubmitted) {
        posLogger.info('RefundSmsService', 'sendRefundFormSms', {
          message: 'Refund form already submitted, skipping SMS',
          id,
          shopifyOrderId,
        });
        return {
          success: false,
          message: 'Refund form already submitted, SMS not sent',
        };
      }

      // Get customer details from replacement order
      const getReplacementOrderHandler = new GetReplacementOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const replacementOrder =
        await getReplacementOrderHandler.getReplacementOrder(
          id,
          shopifyOrderId,
        );

      if (!replacementOrder || !replacementOrder.customer) {
        throw new CustomError(
          `No customer information found for replacement order ${id}`,
          404,
        );
      }

      const { customer } = replacementOrder;
      const customerName =
        `${customer.firstName || ''} ${customer.lastName || ''}`.trim();
      const customerPhone = customer.phone || '';

      if (!customerPhone) {
        posLogger.error('RefundSmsService', 'sendRefundFormSms', {
          message: 'Customer phone number not available',
          id,
          shopifyOrderId,
        });
        return {
          success: false,
          message: 'Customer phone number not available, SMS not sent',
        };
      }

      // Build the SMS payload
      const SMSpayload = {
        template_attributes: {
          templateName: 'ReplacementRefund',
          phoneNo: '9913356834',
          customerName: customerName,
          refund_link: `https://refund-form-${this.configService.get('NODE_ENV')}.thesleepcompany.in/?shopifyOrderId=${shopifyOrderId}%26id=${id}`,
        },
      };

      posLogger.info('RefundSmsService', 'sendRefundFormSms', {
        message: 'Sending SMS with refund form link',
        SMSpayload,
      });

      // Send the SMS notification

      const smsHandler = new CreateFinalOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.s3Client,
      );
      const smsResponse = await smsHandler.sendSMSNotification(SMSpayload);

      posLogger.info('RefundSmsService', 'sendRefundFormSms', {
        message: 'SMS sent successfully',
        smsResponse,
        id,
        shopifyOrderId,
      });

      return {
        success: true,
        message: 'Refund form SMS sent successfully',
      };
    } catch (error) {
      posLogger.error('RefundSmsService', 'sendRefundFormSms', {
        message: 'Error sending refund form SMS',
        error: error.message || error,
        id,
        shopifyOrderId,
      });

      // If the error is because refund details were not found, handle it specifically
      if (error.statusCode === 404) {
        return {
          success: false,
          message: `${error.message || `Details not found for order ${shopifyOrderId}`}`,
        };
      }

      return {
        success: false,
        message: `Error sending refund form SMS: ${error.message || 'Unknown error'}`,
      };
    }
  }
}
