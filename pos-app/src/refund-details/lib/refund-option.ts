export const determineIsBackToSource = (originalPaymentMode= []) => {
  // Define supported direct refund payment modes
  const supportedDirectRefundModes = [
    'RAZORPAY',
    'RAZORPAY_POS',
    'PAYU',
    'SNAPMINT',
    'PINELABS',
    'M_SWIPE',
  ];

  // Default to false for empty or undefined payment modes
  if (!originalPaymentMode?.length) {
    return false;
  }

  // If it's a split payment (multiple payment modes), we can't refund back to source
  if (originalPaymentMode.length > 1) {
    return false;
  }

  // Check if the payment mode supports direct refunds back to source
  return supportedDirectRefundModes.includes(originalPaymentMode[0]);
};
