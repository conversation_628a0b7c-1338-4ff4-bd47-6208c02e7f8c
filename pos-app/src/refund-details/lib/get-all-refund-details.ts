import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { RefundDetail } from '../entities/refund-detail.entity';
import { AppConfigParameters } from 'src/config/config';
import { filterFormatter } from 'src/common/helper/filter-helper';
import { ListRefundDetailInput } from '../dto/list-refund-detail.input';

// Define filter constants
const sortingFilter = {
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  // Add other fields that you want to support for sorting
};

const searchingFilter = {
  id: 'id',
  shopifyOrderId: 'shopifyOrderId',
  status: 'status',
  isBackToSource: 'isBackToSource',
  formSubmitted: 'formSubmitted',
  transactionId: 'transactionId',
  // Add more searchable fields as needed
};

const sortingFilterType = {
  createdAt: 'date',
  updatedAt: 'date',
  // Add appropriate type for each sortable field
};

export class QueryRefundDetails {
  constructor(
    private readonly configService: ConfigService,
    private readonly ssmClient: AppSsmClient,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async queryRefundDetails(
    filter: ListRefundDetailInput,
  ): Promise<{ data: RefundDetail[]; count: number }> {
    posLogger.info('RefundDetail', 'queryRefundDetails', {
      message: 'Querying refund details with filters',
      filter,
    });

    try {
      const REFUND_DETAILS_INDEX =
        await this.configParameters.getRefundDetailTableName();
      const { fromDate = null, toDate = null } = filter || {};
      console.log('REFUND_DETAILS_INDEX', REFUND_DETAILS_INDEX);
      const searchArray: any = [];

      // Add status filter if provided
      if (filter?.status) {
        searchArray.push({
          match: {
            status: filter?.status,
          },
        });
      }

      if (filter?.shopifyOrderId) {
        searchArray.push({
          match: {
            shopifyOrderId: filter?.shopifyOrderId,
          },
        });
      }

      // Add isBackToSource filter if provided
      if (filter?.isBackToSource !== undefined) {
        searchArray.push({
          match: {
            isBackToSource: filter?.isBackToSource,
          },
        });
      }

      // Add formSubmitted filter if provided
      if (filter?.formSubmitted !== undefined) {
        searchArray.push({
          match: {
            formSubmitted: filter?.formSubmitted,
          },
        });
      }
      console.log('searchingFilter', searchingFilter, filter, searchArray);

      // Add date range filters
      if (fromDate && toDate) {
        searchArray.push({
          range: {
            createdAt: {
              gte: fromDate,
              lte: toDate,
            },
          },
        });
      } else if (fromDate) {
        searchArray.push({
          range: {
            createdAt: {
              gte: fromDate,
            },
          },
        });
      } else if (toDate) {
        searchArray.push({
          range: {
            createdAt: {
              lte: toDate,
            },
          },
        });
      }

      let countResult;

      const esHandler = new ElasticClient(this.configService, this.ssmClient);


      const { body: bodyRes } = await esHandler.count({
        index: REFUND_DETAILS_INDEX,
        body: {
          query: {
            bool: {
              must: [...searchArray],
            },
          },
        },
      });
      countResult = bodyRes?.count;

      console.log('filter', filter, filter?.page * filter?.pageSize);
      // Set pagination parameters
      // const size = Number(filter?.pageSize) || 10;
      const size = Number(bodyRes?.count);
      const from =
        Number(filter?.page || 1) === 1
          ? 0
          : Number((filter?.page || 1) * filter?.pageSize);
      console.log('from', from, 'size', size);
      // Initialize ES client
      
      // Define default sort order if none is provided
      // This ensures that by default, we sort by updatedAt in descending order (newest first)

      const defaultSortObject = { updatedAt: { order: 'desc' } };
      // const finalSortObject =
      //   Object.keys(sortObject || {}).length > 0
      //     ? sortObject
      //     : defaultSortObject;

      posLogger.info('RefundDetail', 'queryRefundDetails', {
        message: 'Elasticsearch query parameters',
        index: REFUND_DETAILS_INDEX,
        searchCriteria: searchArray,
        size,
        from,
        sortObject: defaultSortObject,
      });

      let response;

      // Build query based on search filters
      if (searchArray.length) {
        // Get count with filters

        // Get search results with filters
        response = await esHandler.search({
          index: REFUND_DETAILS_INDEX,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
              },
            },
            sort: [defaultSortObject],
          },
        });
      } else {
        // Get count of all records
        const { body: bodyRes } = await esHandler.count({
          index: REFUND_DETAILS_INDEX,
        });
        countResult = bodyRes?.count;

        // Get all results with pagination
        response = await esHandler.search({
          index: REFUND_DETAILS_INDEX,
          body: {
            size,
            from,
            query: {
              match_all: {},
            },
            sort: [defaultSortObject],
          },
        });
      }

      // Extract data from response
      const data: RefundDetail[] = response.body.hits.hits.map(
        (hit) => hit._source,
      );

      posLogger.info('RefundDetail', 'queryRefundDetails', {
        message: `Successfully retrieved ${data.length} refund details out of ${countResult} total matches`,
        totalMatches: countResult,
        retrievedCount: data.length,
      });

      return { data, count: countResult };
    } catch (error) {
      console.log('error in fetching refund details', error);
      posLogger.error('RefundDetail', 'queryRefundDetails', {
        message: 'Error querying refund details',
        error,
      });
      throw error;
    }
  }
}
