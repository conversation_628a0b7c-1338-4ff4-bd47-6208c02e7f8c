import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { RequestApprovalService } from 'src/replacement-orders/lib/update-replacement-request';
import { CreateRefundDetail } from './create-refund-detail';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { AppS3Client } from 'src/common/s3-client/s3-client';

@Injectable()
export class RequestRefundService {
  private readonly requestService: RequestApprovalService;
  private readonly refundService: CreateRefundDetail;

  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
    requestService?: RequestApprovalService,
    refundService?: CreateRefundDetail,
  ) {
    // Initialize the services properly
    this.requestService =
      requestService ||
      new RequestApprovalService(this.configService, this.ssmClient);
    this.refundService =
      refundService ||
      new CreateRefundDetail(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.s3Client,
      );
  }

  async processRefundAgainstARequest(
    replacementOrderId: string,
    orderName: string,
    requestType: string,
    omsRequestId: string,
    isBackToSource: boolean = false
  ) {
    //get the request details
    try {
      console.log('orderName>>', orderName);
      const requestDetails =
        await this.requestService.getRequestDetailsByOrderName(
          omsRequestId,
          requestType,
        );
      console.log('requestDetails', requestDetails);
      const { data: requestData = {} } = requestDetails;
      if (!requestData || Object.keys(requestData).length <= 0) {
        throw new Error('No refund details found');
      }
      const {
        _id: requestId,
        bank_details: bankDetails,
        upi_details: upiDetails,
        
      } = requestData;

      let data = {
        id: replacementOrderId,
        shopifyOrderId: orderName,
        isBackToSource,
        requestId,
      } as any;

      if (bankDetails && Object.keys(bankDetails).length) {
        const { bankName, accountNumber, ifscCode, accountHolderName } =
          bankDetails;

        data = {
          ...data,
          accountHolderName,
          bankAccountName: bankName,
          ifscCode,
          accountNo: accountNumber,
          isFormSubmitted: true,
        };
      } else if (upiDetails && Object.keys(upiDetails).length) {
        const { upiId, name } = upiDetails;

        data = {
          ...data,
          upiName: name,
          upiId,
          isFormSubmitted: true,
        };
      } 
      
      if (isBackToSource) {
        data = {
          ...data,
          isFormSubmitted: true,
        };
      }

      // entry into refund table and creating refund ticket

      let resFromRefund;
      if (requestType === 'CANCELLATION') {
        console.log('Processing cancellation refund');
        resFromRefund =
          await this.refundService.createCancellationRefundDetail(data);
      } else {
        console.log('Processing standard refund');
        resFromRefund = await this.refundService.createRefundDetail(data);
      }
      console.log('res from refund>>', resFromRefund);
      const { id: refundId } = resFromRefund;
      
      // console.log('requestId', requestId);
      // await this.requestService.updateRequestWithRefundId(requestId, refundId);
    
      return true;
    } catch (err) {
      console.error('refund-detail', 'processRefundAgainstARequest', err);
    }
  }
}
