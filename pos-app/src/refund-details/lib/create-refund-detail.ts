// modules
import { PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import {
  CreateCancellationRefundInput,
  CreateRefundDetailInput,
} from '../dto/create-refund-detail.input';
import { RefundDetailData } from '../entities/refund-detail.entity';
import { RaisedIssueData } from 'src/orders/entities/order.entity';
import { CreateIssueTicket } from 'src/orders/lib/create-issue-ticket';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { CreateIssueTicketInput } from 'src/orders/dto/create-issue-ticket.input';
import { GetReplacementOrder } from 'src/replacement-orders/lib/get-replacement-order';
import { ReplacementOrderData } from 'src/replacement-orders/entities/replacement-order.entity';
import { OrderType } from 'src/common/enum/order';
import { CreateFinalOrder } from 'src/replacement-orders/lib/create-final-order';
import { getPickupPriorityLabel } from 'src/replacement-orders/helper/get-pickup-priority';
import { CancelOrderService } from 'src/cancel-order/cancel-order.service';
import { OrderCancellationService } from 'src/cancel-order/lib/cancel-order';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { RequestApprovalService } from 'src/replacement-orders/lib/update-replacement-request';

export class CreateRefundDetail {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
  ) {}

  async createRefundTicket(
    createRefundTicketInput: CreateIssueTicketInput,
  ): Promise<RaisedIssueData> {
    posLogger.info('refund-detail', 'createRefundTicket', {
      createRefundTicketInput,
    });

    //CHANGED GROUP ID HERE
    const {
      phone,
      subject,
      description,
      name,
      email,
      orderId,
      status,
      priority,
      voc1,
      //  voc2,
      group_id = 84000192282,
      RRTicketId,
    } = createRefundTicketInput;
    const createdAt = moment().toISOString();

    const apiData = {
      RRTicketId,
      description,
      subject,
      email,
      name,
      phone,
      priority,
      status,
      type: 'Query',
      group_id: group_id || 84000192282,
      internal_group_id: 84000192282,
      internal_agent_id: 84007339185,
      custom_fields: {
        cf_order_id: orderId,
        cf_voc_level_1: voc1,
        //  cf_voc_level_2: 'Refund Details',
      },
    };
    console.log('api data to update ticket', apiData);
    try {
      const createTicketHandler = new CreateIssueTicket(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const responseData = await createTicketHandler.updateTicket(apiData);
      posLogger.info('refund-detail', 'createRefundTicket', responseData);
      let raisedTicket: RaisedIssueData = null;

      if (responseData?.id) {
        const { id: ticketId } = responseData;
        raisedTicket = {
          subject,
          description,
          createdAt,
          ticketId,
        };

        return raisedTicket;
      } else {
        throw new CustomError(`Failed to raise refund ticket`, 400);
      }
    } catch (error) {
      posLogger.error('refund-detail', 'createRefundTicket', error);
      throw error;
    }
  }

  async createRefundDetail(
    createRefundDetailInput: CreateRefundDetailInput,
  ): Promise<RefundDetailData> {
    posLogger.info('RefundDetail', 'createRefundDetail', {
      input: createRefundDetailInput,
    });
    const {
      id,
      shopifyOrderId,
      isBackToSource = false,
      accountNo,
      accountHolderName,
      ifscCode,
      chequeImageKey,
      bankAccountName,
      requestId,
    } = createRefundDetailInput;
    try {
      const REFUND_DETAIL_TABLE =
        await this.configParameters.getRefundDetailTableName();

      const getHandler = new GetReplacementOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const {
        replacedProduct,
        accessories,
        orderProducts,
        refundedPrice,
        // finalDiscountedAmount,
        eeRefNo,
        orderType,
        awbNumber,
        pickupPriority,
        customer: { email, firstName, lastName, phone },
        RRTicketId,
        creditNoteId,
        carrier,
        originalPaymentMode,
        locationKey,
        deliveryDate,
        minDeliveryDate,
        transactionIDs = [],
      }: ReplacementOrderData = await getHandler.getReplacementOrder(
        id,
        shopifyOrderId,
      );
      console.log('RRTicketId', RRTicketId);
      const combinedPorducts = [
        ...(orderProducts || []),
        ...(accessories || []),
      ];
      const newItems = combinedPorducts
        .map(
          (product) =>
            `${product.title || '-'} - Quantity - ${product.quantity || 0}`,
        )
        .join(', ');

      // const replacementBills = combinedPorducts
      //   .map((product) => product.price || 0)
      //   .join(', ');
      //Create a lead on freshdesk

      //TODO: TO FIX THE REFUND ISSUE FIX THIS .
      //CHANGED GROUP ID HERE

      //     const refundTicket = await this.createRefundTicket({
      //       RRTicketId,
      //       email,
      //       name: `${firstName} ${lastName || ''}`,
      //       phone,
      //       id,
      //       orderId: shopifyOrderId,
      //       status: 13,
      //       // group_id: orderType == OrderType.RETURN ? 84000192282 : ***********,
      //       group_id: 84000192282,
      //       voc1:
      //         orderType == OrderType.RETURN
      //           ? 'Booked Return Refund'
      //           : 'Booked Replacement Refund',
      //       //  voc2: 'Refund Details',
      //       priority: 1,
      //       description:
      //         orderType == OrderType.RETURN
      //           ? `<span>
      //                 Original Order ID: ${shopifyOrderId || '-'}<br/>
      //                 Return Order Date:${moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss') || '-'}<br/>
      //                 Items being returned: ${replacedProduct.title || '-'} <br/>
      //                 Return Quantity - ${replacedProduct.quantity || 0} <br/>
      //                 Original Item SKU ID: - ${replacedProduct.sku || 0}<br/>
      //                 Original Item Value: ${replacedProduct?.finalItemPrice - replacedProduct?.itemDiscount - replacedProduct?.bankDiscount || 0}<br/>
      //                 Amount to be refunded: ${refundedPrice || 0}<br/>
      //                 Return AWB Number: ${awbNumber || ''}<br/>
      //               Courier Partner Name: ${carrier || ''}<br/>
      //               Credit Note Id: ${creditNoteId || ''}<br/>
      //                 Warehouse: ${locationKey || ''}<br/>
      //               Original Payment Mode: ${originalPaymentMode.join(', ') || ''}<br/><br/>
      //       <br/>
      //       Refund Mode:<br/>
      //       1. Back to source: ${isBackToSource ? 'YES' : 'NO'}<br/>
      //       2. To Account: <br/>
      //       <ul>
      //         a.  Account Holder Name: ${accountHolderName || '-'}<br/>
      //         b.  Bank Account Name: ${bankAccountName}<br/>
      //         c.  IFSC Code: ${ifscCode}<br/>
      //         d.  Account Number: ${accountNo}<br/>
      //         e.  Canceled Cheque Image upload: <a href="${chequeImageKey}">${chequeImageKey || ''}</a><br/>
      //       </ul> </span>`
      //           : `<span>
      //   Original Order ID: ${shopifyOrderId || '-'}<br/>
      //               Replacement Order ID: ${eeRefNo || '-'}<br/>
      //               Replacement Order Date:${moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss') || '-'}<br/>
      //               Items being replaced: ${replacedProduct.title || '-'} <br/>
      //               Quantity - ${replacedProduct.quantity || 0} <br/>
      //               Original Item SKU ID: - ${replacedProduct.sku || 0}<br/>
      //               Original Item Value: ${replacedProduct?.finalItemPrice - replacedProduct?.itemDiscount - replacedProduct?.bankDiscount || 0}<br/>
      //               New items: ${newItems}<br/>
      //               Extra Amount Paid: ${0}<br/>
      //               Amount to be refunded: ${refundedPrice || 0}<br/>
      //             Replacement Type: ${getPickupPriorityLabel(pickupPriority) || ''}<br/>
      //               Return AWB Number: ${awbNumber || ''}<br/>
      //               Courier Partner Name: ${carrier || ''}<br/>
      //               Credit Note Id: ${creditNoteId || ''}<br/>
      //               Warehouse: ${locationKey || ''}<br/>
      //                 Original Payment Mode: ${originalPaymentMode.join(', ') || ''}<br/><br/>
      // </span>
      //      <br/><br/>
      //       Refund Mode:<br/>
      //       1. Back to source: ${isBackToSource ? 'YES' : 'NO'}<br/>
      //       2. To Account: <br/>
      //       <ul>
      //         a.  Account Holder Name: ${accountHolderName || '-'}<br/>
      //         b.  Bank Account Name: ${bankAccountName}<br/>
      //         c.  IFSC Code: ${ifscCode}<br/>
      //         d.  Account Number: ${accountNo}<br/>
      //         e.  Canceled Cheque Image upload: <a href="${chequeImageKey}">${chequeImageKey || ''}</a><br/>
      //       </ul> </span>`,
      //       subject: `Booked ${orderType == OrderType.RETURN ? 'Return' : 'Replacement'} - Arrange Pick-up & Process Refund`,
      //     });

      let refundTicket = {
        ticketId: RRTicketId,
      };

      const Item: RefundDetailData = {
        ...createRefundDetailInput,
        sku: replacedProduct?.sku,
        replacementOrderId: orderType === OrderType.REPLACEMENT ? eeRefNo : '',
        refundType: orderType,
        refundTicket,
        refundedPrice,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
        requestId,
      };

      const command = new PutCommand({
        TableName: REFUND_DETAIL_TABLE,
        Item,
      });

      await this.docClient.createItem(command);

      const CreateReplacementOrderHandler = new CreateFinalOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.s3Client,
      );
      console.log('Edd', minDeliveryDate, deliveryDate, transactionIDs);

      // const middlewarPaylod = {
      //   id: shopifyOrderId,
      //   orderType: orderType,
      //   sku: replacedProduct?.sku,
      //   quantity: replacedProduct?.quantity,
      //   newOrderId: eeRefNo,
      //   awbNumber: awbNumber?.toString() || '',
      //   createdAt: moment().toISOString(),
      //   refundedAmount: refundedPrice,
      //   dateRange: {
      //     start: moment(minDeliveryDate).format('DD-MM-YYYY'),
      //     end: moment(deliveryDate).format('DD-MM-YYYY'),
      //   },
      //   refund_from_status: true,
      //   refund_details_from_customer: `Back to source: ${isBackToSource ? 'YES' : 'NO'}  \n
      //     Account Holder Name: ${accountHolderName || '-'} \n
      //     Bank Account Name: ${bankAccountName}<\n
      //     IFSC Code: ${ifscCode}\n
      //     Account Number: ${accountNo}\n
      //     Cheque Image: ${chequeImageKey}\n
      //     `,
      // };
      //  console.log('middlewarPaylod', middlewarPaylod);

      // const response =
      //   await CreateReplacementOrderHandler.middleWareCall(middlewarPaylod);
      // console.log('middleware response', response);

      const newmiddlewarPaylod = {
        replacement_attribute: {
          order_id:
            orderType == OrderType.REPLACEMENT ? eeRefNo : shopifyOrderId,
          // original_order_qty: replacedProduct?.quantity || '',
          // original_product_name:
          //   replacedProduct?.title + ' - ' + replacedProduct?.variantTitle ||
          //   '',
          // refund_required_or_extra_payment: 'Refund',
          // refund_amount_or_extra_paid: refundedPrice || 0,
          // return_awb_number: awbNumber?.toString() || '',
          // return_awb_status: 'Pending',
          // original_item_amount_paid:
          //   (replacedProduct?.price || 0) * (replacedProduct?.quantity || 0) -
          //   (replacedProduct?.itemDiscount || 0),
          // original_item_transaction_id: `${transactionIDs?.join(', ') || ''}`,
          // original_item_payment_mode: `${originalPaymentMode.join(', ') || ''}`,
          // refundedAmount: refundedPrice || 0,
          // new_item_transaction_id: '',
          refund_from_status: true,
          refund_details_from_customer: `Back to source: ${isBackToSource ? 'YES' : 'NO'}  \n
          Account Holder Name: ${accountHolderName || '-'} \n
          Bank Account Name: ${bankAccountName}<\n
          IFSC Code: ${ifscCode}\n
          Account Number: ${accountNo}\n
          Cheque Image: ${chequeImageKey}\n
          `,
          // replacement_remark: 'replacement order',
          // dateRange: {},
        },
      };
      const newresponse =
        await CreateReplacementOrderHandler.newMiddleWareCall(
          newmiddlewarPaylod,
        );

      try {
        const requestService = new RequestApprovalService(
          this.configService,
          this.ssmClient,
        );
        await requestService.updateRequestWithRefundId(requestId, id);
      } catch {
        console.log('error in updating request with refund id');
      }
      console.log('new middleware response', newresponse);
      return Item;
    } catch (e) {
      posLogger.error('RefundDetail', 'createRefundDetail', {
        error: e,
      });
      throw e;
    }
  }

  /**
   * Creates a ticket for cancellation refund
   * @param createRefundTicketInput Input data for the refund ticket
   * @returns Created ticket information
   */
  async createCancellationRefundTicket(
    createRefundTicketInput: CreateIssueTicketInput,
  ): Promise<RaisedIssueData> {
    posLogger.info('refund-detail', 'createCancellationRefundTicket', {
      createRefundTicketInput,
    });

    const {
      phone,
      subject,
      description,
      name,
      email,
      orderId,
      status,
      priority,
      voc1,
      group_id = ***********,
      RRTicketId,
    } = createRefundTicketInput;
    const createdAt = moment().toISOString();

    const apiData = {
      RRTicketId,
      description,
      subject,
      email,
      name,
      phone,
      priority,
      status,
      type: 'Query',
      group_id: ***********,
      internal_agent_id: 84044206026,
      internal_group_id: ***********,
      custom_fields: {
        cf_voc_1: 'Cancellation Request',
        cf_voc_2: 'Cancellation',
        cf_voc_3: 'Before Dispatch',
      },
    };
    console.log('api data for cancellation refund ticket', apiData);
    try {
      const createTicketHandler = new CreateIssueTicket(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      // Decide whether to update an existing ticket or create a new one
      const responseData = RRTicketId
        ? await createTicketHandler.updateTicket(apiData)
        : await createTicketHandler.createTicket(apiData);

      posLogger.info(
        'refund-detail',
        'createCancellationRefundTicket',
        responseData,
      );

      if (responseData?.id) {
        const { id: ticketId } = responseData;
        const raisedTicket = {
          subject,
          description,
          createdAt,
          ticketId,
        };

        return raisedTicket;
      } else {
        throw new CustomError(
          `Failed to raise cancellation refund ticket`,
          400,
        );
      }
    } catch (error) {
      posLogger.error('refund-detail', 'createCancellationRefundTicket', error);
      throw error;
    }
  }

  async getCancelledOrder(id: string) {
    posLogger.info('OrderCancellationService', 'getCancelledOrder', {
      id,
    });

    try {
      const CANCEL_ORDER_TABLE =
        await this.configParameters.getCancelledOrderTableName();

      const queryCancelOrderById = new QueryCommand({
        TableName: CANCEL_ORDER_TABLE,
        KeyConditions: {
          pk: {
            ComparisonOperator: 'EQ',
            AttributeValueList: [id],
          },
        },
      });
      const result = await this.docClient.queryItems(queryCancelOrderById);
      return result?.Items[0] || {};
    } catch (err) {
      posLogger.error('OrderCancellationService', 'getCancelledOrder', {
        id,
        error: err.message || err,
      });
    }
  }
  /**
   * Process refund for a cancelled order
   * @param createCancellationRefundInput Input data for cancellation refund
   * @returns Refund data
   */
  async createCancellationRefundDetail(
    createCancellationRefundInput: CreateCancellationRefundInput,
  ): Promise<RefundDetailData> {
    posLogger.info('RefundDetail', 'createCancellationRefundDetail', {
      input: createCancellationRefundInput,
    });

    const {
      id,
      shopifyOrderId,
      isBackToSource,
      accountNo,
      accountHolderName,
      ifscCode,
      chequeImageKey,
      bankAccountName,
      upiId,
      isFormSubmitted = false,
      requestId,
      customerEmail,
      customerPhone,
      customerFirstName,
      customerLastName,
    } = createCancellationRefundInput;

    try {
      console.log('intializng creation of refund detial', id);
      // Get the cancelled order details
      const cancelledOrder = await this.getCancelledOrder(id);
      console.log('cancelledOrder', cancelledOrder);
      if (!cancelledOrder) {
        throw new CustomError(`Cancelled order not found for ID: ${id}`, 404);
      }

      const {
        reasonForCancellation,
        originalPaymentMode = [],
        transactionIDs = [],
        priceBeforeDiscount = 0,
        priceAfterDiscount = 0,
        bankDiscount = 0,
        refundTicketId,
        customer,
      } = cancelledOrder;

      // Store refund details in the refund table
      const REFUND_DETAIL_TABLE =
        await this.configParameters.getRefundDetailTableName();

      // Create refund ticket for cancellation
      const refundTicket = await this.createCancellationRefundTicket({
        id,
        RRTicketId: refundTicketId,
        email: customerEmail,
        name: `${customerFirstName} ${customerLastName}`,
        phone: customerPhone,
        orderId: shopifyOrderId,
        status: 12,
        group_id: ***********,
        priority: 1,
        description: `<span>
        Order ID: ${shopifyOrderId || '-'}<br/>
        Cancellation Date: ${moment(cancelledOrder.cancelled_at).format('DD-MM-YYYY HH:mm:ss') || '-'}<br/>
        Original Order Amount: ${priceBeforeDiscount || '0'}<br/>
        Final Order Amount: ${priceAfterDiscount || '0'}<br/>
        Bank Discount: ${bankDiscount || '0'}<br/>
        Reason for Cancellation: ${reasonForCancellation || '-'}<br/>
        Original Payment Mode: ${originalPaymentMode.join(', ') || ''}<br/>
        Original Transaction IDs: ${transactionIDs.join(', ') || ''}<br/>
        <br/><br/>
        Refund Mode:<br/>
        1. Back to source: ${isBackToSource ? 'YES' : 'NO'}<br/>
        ${
          !isBackToSource
            ? `2. To Account: <br/>
        <ul>
          ${accountHolderName ? `a. Account Holder Name: ${accountHolderName}<br/>` : ''}
          ${bankAccountName ? `b. Bank Account Name: ${bankAccountName}<br/>` : ''}
          ${ifscCode ? `c. IFSC Code: ${ifscCode}<br/>` : ''}
          ${accountNo ? `d. Account Number: ${accountNo}<br/>` : ''}
          ${chequeImageKey ? `e. Canceled Cheque Image upload: <a href="${chequeImageKey}">${chequeImageKey}</a><br/>` : ''}
          ${upiId ? `f. UPI ID: ${upiId}<br/>` : ''}
        </ul>`
            : ''
        }
      </span>`,
        subject: 'Order Cancellation - Process Refund',
      });

      console.log('after updating refund ticket', refundTicket);

      // Create the refund data
      const refundData: RefundDetailData = {
        id,
        shopifyOrderId,
        isBackToSource,
        accountNo,
        accountHolderName,
        ifscCode,
        chequeImageKey,
        bankAccountName,
        refundTicket,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
        refundType: 'CANCELLATION',
        isFormSubmitted,
        refundedPrice:priceAfterDiscount,
        requestId
      };

      // Store the refund data
      const refundCommand = new PutCommand({
        TableName: REFUND_DETAIL_TABLE,
        Item: refundData,
      });

      await this.docClient.createItem(refundCommand);

      // Call the middleware to process the refund
      const createFinalOrderHandler = new CreateFinalOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.s3Client,
      );

      const middlewarePayload = {
        replacement_attribute: {
          order_id: shopifyOrderId,
          refund_from_status: true,
          refund_details_from_customer: `Back to source: ${isBackToSource ? 'YES' : 'NO'}  \n
        ${accountHolderName ? `Account Holder Name: ${accountHolderName} \n` : ''}
        ${bankAccountName ? `Bank Account Name: ${bankAccountName} \n` : ''}
        ${ifscCode ? `IFSC Code: ${ifscCode} \n` : ''}
        ${accountNo ? `Account Number: ${accountNo} \n` : ''}
        ${chequeImageKey ? `Cheque Image: ${chequeImageKey} \n` : ''}
        ${upiId ? `UPI ID: ${upiId} \n` : ''}`,
        },
      };

      try {
        const middlewareResponse =
          await createFinalOrderHandler.newMiddleWareCall(middlewarePayload);
        posLogger.info(
          'refund-detail',
          'middlewareResponse',
          middlewareResponse,
        );
      } catch (middlewareError) {
        // Log error but continue execution
        posLogger.error('refund-detail', 'middleware-call-failed', {
          error: middlewareError.message || middlewareError,
        });
      }

      try {
        const requestService = new RequestApprovalService(
          this.configService,
          this.ssmClient,
        );
        await requestService.updateRequestWithRefundId(requestId, id);
      } catch {
        console.log('error in updating request with refund id');
      }

      return refundData;
    } catch (error) {
      posLogger.error('RefundDetail', 'createCancellationRefundDetail', {
        error: error.message || error,
      });
      throw error;
    }
  }
}
