import { UpdateRefundDetailInput } from '../dto/update-refund-detail.input';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { RefundDetail } from '../entities/refund-detail.entity';
import { posLogger } from 'src/common/logger';
import { UpdateCommand, GetCommand } from '@aws-sdk/lib-dynamodb';
import { NotFoundException } from '@nestjs/common';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { GetRefundDetail } from './get-refund-detail';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { CreateRefundDetail } from './create-refund-detail';
import { ReplacementOrderData } from 'src/replacement-orders/entities/replacement-order.entity';
import { GetReplacementOrder } from 'src/replacement-orders/lib/get-replacement-order';
import { OrderType } from 'src/common/enum/order';
import moment from 'moment';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { getPickupPriorityLabel } from 'src/replacement-orders/helper/get-pickup-priority';

export class UpdateRefundStatus {
  constructor(
    private readonly configService: ConfigService,
    private readonly docClient: AppDocumentClient,
    private readonly ssmClient: AppSsmClient,
    private readonly configParameters: AppConfigParameters,
    private readonly s3Client: AppS3Client,
  ) {}

  async updateRefundDetail(
    updateRefundDetailInput: UpdateRefundDetailInput,
  ): Promise<RefundDetail> {
    posLogger.info('RefundDetail', 'updateRefundDetail', {
      updateData: updateRefundDetailInput,
    });

    const { id, shopifyOrderId } = updateRefundDetailInput;

    try {
      // First check if the refund exists
      const getHandler = new GetRefundDetail(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const existingRefund = await getHandler.getRefundDetail(
        id,
        shopifyOrderId,
      );

      if (!existingRefund) {
        throw new NotFoundException(
          `Refund with ID ${id} and shopifyOrderId ${shopifyOrderId} not found`,
        );
      }

      const REFUND_DETAILS_TABLE_NAME =
        await this.configParameters.getRefundDetailTableName();

      // Determine update type based on input fields
      const isTransactionUpdate = !!updateRefundDetailInput.transactionId;
      const isFormSubmit = !!updateRefundDetailInput.isFormSubmitted;

      // Prepare update expression and attribute values
      let updateExpression = 'SET updatedAt = :updatedAt';
      const expressionAttributeValues: any = {
        ':updatedAt': new Date().toISOString(),
      };
      const expressionAttributeNames: any = {};

      // Handle transaction update
      if (isTransactionUpdate) {
        updateExpression +=
          ', #status = :status, transactionId = :transactionId, refundDate = :refundDate , refundAmount = :refundAmount'; ;
        expressionAttributeValues[':status'] = updateRefundDetailInput.status;
        expressionAttributeValues[':transactionId'] =
          updateRefundDetailInput.transactionId;
        expressionAttributeValues[':refundDate'] =
          updateRefundDetailInput.refundDate;
        expressionAttributeNames['#status'] = 'status'; 
        expressionAttributeValues[':refundAmount'] =
          updateRefundDetailInput.refundAmount;

        if (updateRefundDetailInput.sku) {
          updateExpression += ', sku = :sku';
          expressionAttributeValues[':sku'] = updateRefundDetailInput.sku;
        }
      }

      // Handle form submission update
      if (isFormSubmit) {
        updateExpression += ', isFormSubmitted = :isFormSubmitted';
        expressionAttributeValues[':isFormSubmitted'] =
          updateRefundDetailInput.isFormSubmitted;

        // Only add account details if they're provided
        if (updateRefundDetailInput.accountHolderName) {
          updateExpression += ', accountHolderName = :accountHolderName';
          expressionAttributeValues[':accountHolderName'] =
            updateRefundDetailInput.accountHolderName;
        }

        if (updateRefundDetailInput.bankAccountName) {
          updateExpression += ', bankAccountName = :bankAccountName';
          expressionAttributeValues[':bankAccountName'] =
            updateRefundDetailInput.bankAccountName;
        }

        if (updateRefundDetailInput.ifscCode) {
          updateExpression += ', ifscCode = :ifscCode';
          expressionAttributeValues[':ifscCode'] =
            updateRefundDetailInput.ifscCode;
        }

        if (updateRefundDetailInput.accountNo) {
          updateExpression += ', accountNo = :accountNo';
          expressionAttributeValues[':accountNo'] =
            updateRefundDetailInput.accountNo;
        }

        if (updateRefundDetailInput.chequeImageKey) {
          updateExpression += ', chequeImageKey = :chequeImageKey';
          expressionAttributeValues[':chequeImageKey'] =
            updateRefundDetailInput.chequeImageKey;
        }

        if (updateRefundDetailInput.upiId) {
          updateExpression += ', upiId = :upiId';
          expressionAttributeValues[':upiId'] = updateRefundDetailInput.upiId;
        }

        if (updateRefundDetailInput.upiName) {
          updateExpression += ', upiName = :upiName';
          expressionAttributeValues[':upiName'] =
            updateRefundDetailInput.upiName;
        }

        // Update orderType if provided
        if (updateRefundDetailInput.orderType) {
          updateExpression += ', orderType = :orderType';
          expressionAttributeValues[':orderType'] =
            updateRefundDetailInput.orderType;
        }

        const {
          isBackToSource,
          accountNo,
          ifscCode,
          accountHolderName,
          bankAccountName,
          chequeImageKey,
        } = updateRefundDetailInput;

        //TODO: UPDATE THE REFUND TICKET WITH THIS DETAILS
        try {
          const getHandler = new GetReplacementOrder(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
          );
          const {
            replacedProduct,
            refundedPrice,
            eeRefNo,
            orderType,
            awbNumber,
            pickupPriority,
            customer: { email, firstName, lastName, phone },
            RRTicketId,

            orderProducts,
            accessories,
            creditNoteId,
            carrier,
            originalPaymentMode,
            locationKey,
          }: ReplacementOrderData = await getHandler.getReplacementOrder(
            id,
            shopifyOrderId,
          );

          const createRefundTicketHandler = new CreateRefundDetail(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
            this.s3Client,
          );

          const combinedPorducts = [
            ...(orderProducts || []),
            ...(accessories || []),
          ];

          const newItems = combinedPorducts
            .map(
              (product) =>
                `${product.title || '-'} - Quantity - ${product.quantity || 0}`,
            )
            .join(', ');


          const refundTicket =
            await createRefundTicketHandler.createRefundTicket({
              RRTicketId,
              email,
              name: `${firstName} ${lastName || ''}`,
              phone,
              id,
              orderId: shopifyOrderId,
              status: 13,
              // group_id: orderType == OrderType.RETURN ? 84000192282 : 84000291687,
              group_id: 84000192282,
              voc1:
                orderType == OrderType.RETURN
                  ? 'Booked Return Refund'
                  : 'Booked Replacement Refund',
              //  voc2: 'Refund Details',
              priority: 1,
              description:
                orderType == OrderType.RETURN
                  ? `<span>
                      Original Order ID: ${shopifyOrderId || '-'}<br/>
                      Return Order Date:${moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss') || '-'}<br/>
                      Items being returned: ${replacedProduct.title || '-'} <br/>
                      Return Quantity - ${replacedProduct.quantity || 0} <br/>
                      Original Item SKU ID: - ${replacedProduct.sku || 0}<br/>
                      Original Item Value: ${replacedProduct?.finalItemPrice - replacedProduct?.itemDiscount - replacedProduct?.bankDiscount || 0}<br/>
                      Amount to be refunded: ${refundedPrice || 0}<br/>
                      Return AWB Number: ${awbNumber || ''}<br/>
                    Courier Partner Name: ${carrier || ''}<br/>
                    Credit Note Id: ${creditNoteId || ''}<br/>
                      Warehouse: ${locationKey || ''}<br/>
                    Original Payment Mode: ${originalPaymentMode.join(', ') || ''}<br/><br/>
            <br/>
            Refund Mode:<br/>
            1. Back to source: ${isBackToSource ? 'YES' : 'NO'}<br/>
            2. To Account: <br/>
            <ul>
              a.  Account Holder Name: ${accountHolderName || '-'}<br/>
              b.  Bank Account Name: ${bankAccountName}<br/>
              c.  IFSC Code: ${ifscCode}<br/>
              d.  Account Number: ${accountNo}<br/>
              e.  Canceled Cheque Image upload: <a href="${chequeImageKey}">${chequeImageKey || ''}</a><br/>
            </ul> </span>`
                  : `<span>
        Original Order ID: ${shopifyOrderId || '-'}<br/>
                    Replacement Order ID: ${eeRefNo || '-'}<br/>
                    Replacement Order Date:${moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss') || '-'}<br/>
                    Items being replaced: ${replacedProduct.title || '-'} <br/>
                    Quantity - ${replacedProduct.quantity || 0} <br/>
                    Original Item SKU ID: - ${replacedProduct.sku || 0}<br/>
                    Original Item Value: ${replacedProduct?.finalItemPrice - replacedProduct?.itemDiscount - replacedProduct?.bankDiscount || 0}<br/>
                    New items: ${newItems}<br/>
                    Extra Amount Paid: ${0}<br/>
                    Amount to be refunded: ${refundedPrice || 0}<br/>
                  Replacement Type: ${getPickupPriorityLabel(pickupPriority) || ''}<br/>
                    Return AWB Number: ${awbNumber || ''}<br/>
                    Courier Partner Name: ${carrier || ''}<br/>
                    Credit Note Id: ${creditNoteId || ''}<br/>
                    Warehouse: ${locationKey || ''}<br/>
                      Original Payment Mode: ${originalPaymentMode.join(', ') || ''}<br/><br/>
      </span>
           <br/><br/>
            Refund Mode:<br/>
            1. Back to source: ${isBackToSource ? 'YES' : 'NO'}<br/>
            2. To Account: <br/>
            <ul>
              a.  Account Holder Name: ${accountHolderName || '-'}<br/>
              b.  Bank Account Name: ${bankAccountName}<br/>
              c.  IFSC Code: ${ifscCode}<br/>
              d.  Account Number: ${accountNo}<br/>
              e.  Canceled Cheque Image upload: <a href="${chequeImageKey}">${chequeImageKey || ''}</a><br/>
            </ul> </span>`,
              subject: `Booked ${orderType == OrderType.RETURN ? 'Return' : 'Replacement'} - Arrange Pick-up & Process Refund`,
            });
          console.log('here refund ticket updated hereee.');
        } catch (err) {
          console.log('error in updating refund ticket', err);
        }
      }

      // Create update command
      const updateCommand = new UpdateCommand({
        TableName: REFUND_DETAILS_TABLE_NAME,
        Key: {
          id: id,
          shopifyOrderId: shopifyOrderId,
        },
        UpdateExpression: updateExpression,
        ExpressionAttributeValues: expressionAttributeValues,
        ExpressionAttributeNames:
          Object.keys(expressionAttributeNames).length > 0
            ? expressionAttributeNames
            : undefined,
        ReturnValues: 'ALL_NEW', // Return the updated item
      });

      // Execute update
      const result = await this.docClient.updateItem(updateCommand);

      // Only call external API if this is a transaction update
      if (isTransactionUpdate) {
        await this.callExternalRefundStatusAPI(shopifyOrderId, existingRefund, {
          transactionId: updateRefundDetailInput.transactionId,
          refundDate: updateRefundDetailInput.refundDate,
          status: updateRefundDetailInput.status,
          sku: updateRefundDetailInput.sku,
        });
      }

      posLogger.info('RefundDetail', 'updateRefundDetail', {
        message: 'Successfully updated refund details',
        result: result,
      });

      return result.Attributes as RefundDetail;
    } catch (error) {
      posLogger.error('RefundDetail', 'updateRefundDetail', {
        error,
      });

      return {
        data: null,
        success: false,
        message: `Error updating refund detail: ${error.message}`,
      };
    }
  }

  async callExternalRefundStatusAPI(
    shopifyOrderId: string,
    existingRefund: any,
    updateData: any,
    quantity = 1,
  ) {
    try {
      let isPartialRefund = true;
      const { refundType = '' } = existingRefund;
      if (refundType === 'CANCELLATION') {
        isPartialRefund = false;
      }
      // Get API key and URL from SSM parameters
      const {
        Parameter: { Value: apiKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/apiKey`,
          WithDecryption: true,
        }),
      );

      const {
        Parameter: { Value: baseURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/replacement/url`,
          WithDecryption: true,
        }),
      );

      posLogger.info('RefundDetail', 'callExternalRefundStatusAPI', {
        apiKey,
        baseURL,
      });

      // Format the date for the external API
      const refundDate = new Date(updateData.refundDate);
      const refundDateISO = refundDate.toISOString().replace('Z', '+05:30');

      // Prepare bank information based on refund type
      //   let bankInfo = '';
      //   if (existingRefund.isBackToSource) {
      //     bankInfo = 'Back to Source';
      //   } else {
      //     bankInfo = JSON.stringify({
      //       accountHolderName: existingRefund.accountHolderName || '',
      //       bankName: existingRefund.bankAccountName || '',
      //       accountNumber: existingRefund.accountNo || '',
      //       ifscCode: existingRefund.ifscCode || ''
      //     });
      //   }

      // Prepare the payload for the external API

      const payload = {
        orderId: shopifyOrderId,
        refundType: isPartialRefund ? 'PARTIAL_REFUND' : 'FULL_REFUND',

        refundDetail: {
          refundDateAndTime: refundDateISO,
          transaction_id: updateData.transactionId,
          sku: updateData.sku?.split('_')[0],
          qty: quantity,
        },
      };

      const url = `${baseURL}/pos/refund_status`;

      posLogger.info('RefundDetail', 'callExternalRefundStatusAPI', {
        message: 'Calling external refund status API',
        url,
        payload,
      });

      // Call the external API
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
        },
        body: JSON.stringify(payload),
      });

      const responseData = await response.json();

      posLogger.info(
        'RefundDetail serverless OMS',
        'callExternalRefundStatusAPI',
        {
          message: 'External API response',
          responseData,
        },
      );

      return responseData;
    } catch (error) {
      posLogger.error('RefundDetail', 'callExternalRefundStatusAPI', {
        error,
      });
      throw new CustomError(
        `Error updating external system with refund status`,
        400,
      );
    }
  }
}
