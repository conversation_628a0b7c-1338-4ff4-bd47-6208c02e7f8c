import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { ListRefundDetailInput } from '../dto/list-refund-detail.input';
import { json2csv } from 'json-2-csv';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { TemplateType } from 'src/common/enum/template-type';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { QueryRefundDetails } from './get-all-refund-details';

@Injectable()
export class RefundExportService {
  constructor(
    private readonly configService: ConfigService,
    private readonly docClient: AppDocumentClient,
    private readonly ssmClient: AppSsmClient,
    private readonly configParameters: AppConfigParameters,
    private readonly s3Client: AppS3Client,
  ) {}

  async exportRefundDetailsCSV(
    filter: ListRefundDetailInput,
    email: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      posLogger.info('RefundExportService', 'exportRefundDetailsCSV', {
        message: 'Starting refund details export',
        filter,
        email,
      });

      // Get date range for email subject
      const dateRange = this.formatDateRangeForSubject(
        filter.fromDate,
        filter.toDate,
      );

      const queryRefundDetails = new QueryRefundDetails(
        this.configService,
        this.ssmClient,
        this.configParameters,
      );

      // Query refund details based on filter
      const { data, count } =
        await queryRefundDetails.queryRefundDetails(filter);

      if (!data || data.length === 0) {
        posLogger.info('RefundExportService', 'exportRefundDetailsCSV', {
          message: 'No refund details found for export',
        });
        return {
          success: false,
          message: 'No refund details found for export',
        };
      }

      // Convert data to CSV
      const csvData = await this.convertToCSV(data);
      console.log("csv data", csvData);
      // Prepare attachment
      const attachment = {
        content: csvData,
        filename: `refund-details-export${dateRange}.csv`,
        name: 'Refund Details Export',
      };

      const mailService = new PdfService(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.s3Client,
        this.configParameters,
      );
      // Send email with CSV attachment
      await mailService.sendEmailWithFileAttachment(
        email,
        `SmartServe POS Export | Type: ${attachment.name}${dateRange} | The Sleep Company`,
        `Following is the ${attachment.name} ${TemplateType.REFUNDS}`,
        'text/csv',
        TemplateType.REFUNDS,
        'csv',
        [attachment],
      );

      posLogger.info('RefundExportService', 'exportRefundDetailsCSV', {
        message: 'Successfully exported and emailed refund details',
        recordCount: data.length,
      });

      return {
        success: true,
        message: `Successfully exported ${data.length} refund details and sent to ${email}`,
      };
    } catch (error) {
      posLogger.error('RefundExportService', 'exportRefundDetailsCSV', {
        message: 'Error exporting refund details',
        error,
      });
      return {
        success: false,
        message: `Error exporting refund details: ${error.message}`,
      };
    }
  }

  private async convertToCSV(data: any): Promise<string> {
    try {
      // Prepare data for export by cleaning/formatting fields if necessary
      const preparedData = data.map((refund) => {
        // Clone the object to avoid modifying the original
        const formatted = { ...refund };

        // Format dates
        if (formatted.createdAt) {
          formatted.createdAt = new Date(formatted.createdAt).toISOString();
        }
        if (formatted.updatedAt) {
          formatted.updatedAt = new Date(formatted.updatedAt).toISOString();
        }

        // Format boolean values for better readability
        if (formatted.isBackToSource !== undefined) {
          formatted.isBackToSource = formatted.isBackToSource ? 'Yes' : 'No';
        }
        if (formatted.formSubmitted !== undefined) {
          formatted.formSubmitted = formatted.formSubmitted ? 'Yes' : 'No';
        }

        return formatted;
      });

      // Convert to CSV
      return await json2csv(preparedData, {});
    } catch (error) {
      posLogger.error('RefundExportService', 'convertToCSV', {
        message: 'Error converting refund details to CSV',
        error,
      });
      throw new Error(
        `Failed to convert refund details to CSV: ${error.message}`,
      );
    }
  }

  private formatDateRangeForSubject(
    fromDate?: string,
    toDate?: string,
  ): string {
    if (!fromDate && !toDate) {
      return '';
    }

    const formatDate = (dateStr: string) => {
      const date = new Date(dateStr);
      return date.toISOString().split('T')[0];
    };

    if (fromDate && toDate) {
      return ` | ${formatDate(fromDate)} to ${formatDate(toDate)}`;
    } else if (fromDate) {
      return ` | From ${formatDate(fromDate)}`;
    } else {
      return ` | To ${formatDate(toDate)}`;
    }
  }
}
