import { Module } from '@nestjs/common';
import { RefundDetailsService } from './refund-details.service';
import { RefundDetailsResolver } from './refund-details.resolver';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { AppS3Client } from 'src/common/s3-client/s3-client';

@Module({
  providers: [
    RefundDetailsResolver,
    RefundDetailsService,
    SuccessHandler,
    ErrorHandler,
    AppS3Client,
  ],
  imports: [DocumentClientModule, SsmClientModule, ConfigParametersModule],
})
export class RefundDetailsModule {}
