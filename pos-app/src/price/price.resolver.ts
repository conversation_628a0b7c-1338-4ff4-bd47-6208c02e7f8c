import { Args, Mutation, Resolver, Query } from '@nestjs/graphql';
import { PriceService } from './price.service';
import { Price, PriceData } from './entities/price.entity';
import { CreatePriceInput, FindPriceInput } from './dto/create-price.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { HttpStatus } from '@nestjs/common';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';

@Resolver(() => Price)
export class PriceResolver {
  constructor(
    private readonly priceService: PriceService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: <PERSON>rrorHandler,
  ) {}

  @Mutation(() => Price)
  createPrice(@Args('createPriceInput') createPriceInput: CreatePriceInput) {
    return this.priceService.create(createPriceInput);
  }

  @Query(() => PriceData, { name: 'getVariantPriceOnDate' })
  async findPrice(
    @Args('getVariantPriceOnDate') getVariantPriceOnDate: FindPriceInput,
  ) {
    try {
      const { data } = await this.priceService.findOne(getVariantPriceOnDate);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return res;
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create order',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
