import { Module } from '@nestjs/common';
import { PriceService } from './price.service';
import { PriceResolver } from './price.resolver';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';

@Module({
  imports: [
    S3ClientModule,
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
  ],
  providers: [PriceResolver, PriceService, SuccessHandler, ErrorHandler],
})
export class PriceModule {}
