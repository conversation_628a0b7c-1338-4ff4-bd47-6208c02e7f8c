import { AppConfigParameters } from 'src/config/config';
import { CreatePriceInput } from '../dto/create-price.input';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { BatchWriteCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';

export class CreatePrice {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async create(createPriceInput: CreatePriceInput | CreatePriceInput[]) {
    try {
      const PRICE_TABLE = await this.configParameters.getPriceTableName();

      const items = Array.isArray(createPriceInput)
        ? createPriceInput
        : [createPriceInput];

      const BATCH_SIZE = 25;
      const date = new Date().toISOString();
      for (let i = 0; i < items.length; i += BATCH_SIZE) {
        const batch = items.slice(i, i + BATCH_SIZE);

        const putRequests = batch.map((item) => ({
          PutRequest: {
            Item: {
              pk: `${item.product_id}#${item.variant_id}`,
              sk: date,
              product_id: item.product_id,
              variant_id: item.variant_id,
              date,
              price: item.price,
              sku: item.sku,
              createdAt: date,
            },
          },
        }));

        const command = new BatchWriteCommand({
          RequestItems: {
            [PRICE_TABLE]: putRequests,
          },
        });

        await this.docClient.createItems(command);
      }
    } catch (error) {
      console.error('Error inserting price:', error);
      throw error;
    }
  }
}
