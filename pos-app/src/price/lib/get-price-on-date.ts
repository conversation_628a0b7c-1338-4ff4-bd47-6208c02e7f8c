import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { ConfigService } from '@nestjs/config';
import { FindPriceInput } from '../dto/create-price.input';

export class GetPriceOnDate {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getPriceOnDate(getPriceInput: FindPriceInput) {
    const PRICE_TABLE = await this.configParameters.getPriceTableName();

    const { product_id, variant_id, date } = getPriceInput;

    const command = new QueryCommand({
      TableName: PRICE_TABLE,
      KeyConditionExpression: 'pk = :pk AND sk <= :sk',
      ExpressionAttributeValues: {
        ':pk': `${product_id}#${variant_id}`,
        ':sk': date, // The provided ISO date
      },
      ScanIndexForward: false, // Get latest date first (descending order)
      Limit: 1, // Only get the latest record
    });

    try {
      const result = await this.docClient.queryItems(command);
      return { data: result.Items?.[0] || undefined };
    } catch (error) {
      console.error('Error fetching latest price:', error);
      throw error;
    }
  }
}
