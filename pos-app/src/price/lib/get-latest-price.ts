import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { GetPriceInput } from '../dto/get-price-input';
import { ConfigService } from '@nestjs/config';

export class GetLatestPrice {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getLatestPrice(getPriceInput: GetPriceInput) {
    const PRICE_TABLE = await this.configParameters.getPriceTableName();

    const { product_id, variant_id } = getPriceInput;

    const command = new QueryCommand({
      TableName: PRICE_TABLE,
      KeyConditionExpression: 'pk = :pk',
      ExpressionAttributeValues: {
        ':pk': `${product_id}#${variant_id}`,
      },
      ScanIndexForward: false, // Get latest date first (descending order)
      Limit: 1, // Only get the latest record
    });

    try {
      const result = await this.docClient.queryItems(command);
      return result.Items?.[0] || undefined; // Return the latest record or undefined if not found
    } catch (error) {
      console.error('Error fetching latest price:', error);
      throw error;
    }
  }
}
