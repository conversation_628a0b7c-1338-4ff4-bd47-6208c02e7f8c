import { Field, ID, InputType } from '@nestjs/graphql';

@InputType()
export class CreatePriceInput {
  @Field(() => ID, { description: 'Product ID' })
  product_id: string;

  @Field(() => ID, { description: 'Variant ID' })
  variant_id: string;

  @Field(() => String, { description: 'Price of product' })
  price: string;

  @Field(() => String, { description: 'SKU of product', nullable: true })
  sku?: string;
}

@InputType()
export class FindPriceInput {
  @Field(() => ID, { description: 'Product ID' })
  product_id: string;

  @Field(() => ID, { description: 'Variant ID' })
  variant_id: string;

  @Field(() => String, { description: 'Date' })
  date: string;
}
