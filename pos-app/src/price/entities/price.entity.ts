import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType()
export class Price {
  @Field(() => String, { description: 'Date of price' })
  date: string;

  @Field(() => ID, { description: 'Product ID' })
  product_id: string;

  @Field(() => ID, { description: 'Variant ID' })
  variant_id: string;

  @Field(() => String, { description: 'Price of product' })
  price: string;

  @Field(() => String, { description: 'SKU of product', nullable: true })
  sku: string;

  @Field(() => String, { description: 'Created at timestamp' })
  createdAt: string;
}

@ObjectType()
export class PriceData {
  @Field(() => Price, {
    nullable: true,
    description: 'Order Product Data',
  })
  data: Price;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
