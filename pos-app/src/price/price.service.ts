import { Injectable } from '@nestjs/common';
import { CreatePriceInput, FindPriceInput } from './dto/create-price.input';
// import { UpdatePriceInput } from './dto/update-price.input';
import { CreatePrice } from './lib/create-price';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { ConfigService } from '@nestjs/config';
import { GetPriceInput } from './dto/get-price-input';
import { GetLatestPrice } from './lib/get-latest-price';
import { GetPriceOnDate } from './lib/get-price-on-date';

@Injectable()
export class PriceService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  create(createPriceInput: CreatePriceInput | CreatePriceInput[]) {
    const handler = new CreatePrice(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return handler.create(createPriceInput);
  }

  // findAll() {
  //   return `This action returns all price`;
  // }

  findLatestOne(getPriceInput: GetPriceInput) {
    const handler = new GetLatestPrice(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return handler.getLatestPrice(getPriceInput);
  }

  async findOne(getVariantPriceOnDate: FindPriceInput) {
    const handler = new GetPriceOnDate(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return await handler.getPriceOnDate(getVariantPriceOnDate);
  }

  // update(id: number, updatePriceInput: UpdatePriceInput) {
  //   return `This action updates a #${id} price`;
  // }

  // remove(id: number) {
  //   return `This action removes a #${id} price`;
  // }
}
