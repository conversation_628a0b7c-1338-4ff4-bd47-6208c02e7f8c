// modules
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { SpinTheWheelData } from '../entities/spin-the-wheel';
import { ScanDB } from 'src/common/helper/scan-table';

export class ListSpinTheWheelCoupon {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async listSpinWheelCoupon(prefix: string): Promise<SpinTheWheelData[]> {
    posLogger.info('listSpinWheelCoupon', 'listSpinWheelCoupon', {
      input: prefix,
    });
    try {
      const SPIN_THE_WHEEL_TABLE =
        await this.configParameters.getSpinTheWheelCouponConfigTableName();
      console.log('SPIN_THE_WHEEL_TABLE', SPIN_THE_WHEEL_TABLE);
      const scanDBHandler = new ScanDB(this.docClient);
      const data = await scanDBHandler.scanTable(SPIN_THE_WHEEL_TABLE);
      console.log('data', data);
      if (!data) {
        throw new CustomError(`Coupons not found`, 404);
      }

      return data;
    } catch (e) {
      posLogger.error('listSpinWheelCoupon', 'listSpinWheelCoupon', {
        error: e,
      });
      throw e;
    }
  }
}
