import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { posLogger } from 'src/common/logger';
import { ClaimSpinWheelRewardInput } from '../dto/claim-spin-the-wheel-reward-input';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { GetCustomer } from 'src/customers/lib/get-customer';
import { AppShopify } from 'src/common/shopify/shopify';
import { ListSpinTheWheelCoupon } from './list-spin-the-wheel-coupon';
import moment from 'moment';
import { PutCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import {
  createDiscountCodePayload,
  createPriceRulePayload,
  getDate,
} from 'src/cron/helper/lib';
import { generateDiscountCode } from '../helper/generateCode';
export class CreateSpinWheelCoupon {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
    private shopifyClient: AppShopify,
  ) {}
  async sendWhatsAppMessage(
    templateName: string,
    couponCode: string,
    phoneNo: string,
  ) {
    try {
      const response = await fetch(
        'https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/sendwhatsapp',
        {
          method: 'POST',
          headers: {
            'x-api-key': 'UBQJgJrPzW67r32ydSn9H8APs1VcZSkN3LKjoukp',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            template_attributes: {
              templateName,
              couponCode,
              phoneNo,
            },
          }),
        },
      );
      const data = await response.json();

      return data;
    } catch (error) {
      posLogger.error('sendWhatsAppMessage', 'Error sending WhatsApp message', {
        error,
      });
      throw new CustomError('Failed to send WhatsApp message', 400);
    }
  }

  async sendSMSMessage(
    templateName: string,
    couponCode: string,
    phoneNo: string,
  ) {
    try {
      const response = await fetch(
        'https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/send_sms',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            template_attributes: {
              templateName,
              couponCode,
              phoneNo,
            },
          }),
        },
      );
      return await response.json();
    } catch (error) {
      posLogger.error('sendSMSMessage', 'Error sending SMS message', { error });
      throw new CustomError('Failed to send SMS message', 400);
    }
  }

  private formatShopifyGIDs(ids: string[], type: string) {
    return ids.slice(0, 5).map((id) => `gid://shopify/${type}/${id}`);
  }

  private buildPriceRulePayload(payload: any) {
    const start = getDate(0);
    const end = moment().add(1, 'day').toISOString();
    const entitledProductIds = payload?.entitled_product_ids?.length
      ? this.formatShopifyGIDs(payload.entitled_product_ids, 'Product')
      : [];
    const entitledVariantIds = payload?.entitled_variant_ids?.length
      ? this.formatShopifyGIDs(payload.entitled_variant_ids, 'ProductVariant')
      : [];
    const entitledCollectionIds = payload?.entitled_collection_ids?.length
      ? this.formatShopifyGIDs(payload.entitled_collection_ids, 'Collection')
      : [];
    const prerequisiteProductIds = payload?.prerequisite_product_ids?.length
      ? this.formatShopifyGIDs(payload.prerequisite_product_ids, 'Product')
      : [];
    const prerequisiteVariantIds = payload?.prerequisite_variant_ids?.length
      ? this.formatShopifyGIDs(
          payload.prerequisite_variant_ids,
          'ProductVariant',
        )
      : [];
    const prerequisiteCollectionIds = payload?.prerequisite_collection_ids
      ?.length
      ? this.formatShopifyGIDs(
          payload.prerequisite_collection_ids,
          'Collection',
        )
      : [];
    const customerIds = Array.isArray(payload.customerIds)
      ? this.formatShopifyGIDs(payload.customerIds, 'Customer')
      : [
          payload.customerIds?.id
            ? `gid://shopify/Customer/${payload.customerIds.id}`
            : null,
        ].filter(Boolean);

    const value =
      payload.value_type === 'percentage'
        ? { percentageValue: parseFloat(payload.value) }
        : { fixedAmountValue: -parseFloat(payload.value) };

    console.log('======customerIds are======', customerIds);

    return {
      title: `SPIN-THE-WHEEL-${payload.title}`,
      allocationMethod: 'ACROSS',
      allocationLimit: null,
      customerSelection: {
        customerIdsToAdd: customerIds.length ? customerIds : null,
        forAllCustomers: !customerIds.length,
      },
      itemEntitlements: {
        productIds: entitledProductIds,
        productVariantIds: entitledVariantIds, // Changed from variants to productVariantIds
        collectionIds: entitledCollectionIds, // Changed from collections to collectionIds
        targetAllLineItems:
          !entitledProductIds.length &&
          !entitledVariantIds.length &&
          !entitledCollectionIds.length,
      },
      itemPrerequisites: {
        productIds: prerequisiteProductIds,
        productVariantIds: prerequisiteVariantIds,
        collectionIds: prerequisiteCollectionIds,
      },
      target: 'LINE_ITEM',
      validityPeriod: {
        start,
        end,
      },
      value,
      prerequisiteSubtotalRange: payload?.prerequisite_subtotal_range
        ? {
            greaterThanOrEqualTo:
              payload.prerequisite_subtotal_range.toString(),
          }
        : null,
      prerequisiteQuantityRange: payload?.prerequisite_quantity_range
        ? {
            greaterThanOrEqualTo: parseInt(
              payload.prerequisite_quantity_range,
              10,
            ),
          }
        : null,
      prerequisiteShippingPriceRange: payload?.prerequisite_shipping_price_range
        ? {
            greaterThanOrEqualTo:
              payload.prerequisite_shipping_price_range.toString(),
          }
        : null,
      prerequisiteToEntitlementQuantityRatio:
        payload?.prerequisite_to_entitlement_quantity_ratio
          ? {
              prerequisiteQuantity:
                parseInt(
                  payload.prerequisite_to_entitlement_quantity_ratio
                    .prerequisite_quantity,
                  10,
                ) || null,
              entitlementQuantity:
                parseInt(
                  payload.prerequisite_to_entitlement_quantity_ratio
                    .entitled_quantity,
                  10,
                ) || null,
            }
          : { prerequisiteQuantity: null, entitlementQuantity: null },
      usageLimit: 1,
      oncePerCustomer: true,
    };
  }

  private async createShopifyCoupon(payload: any) {
    try {
      const [COUPONS_TABLE, SHOPIFY_ACCESS_TOKEN, SHOPIFY_ADMIN_BASE_URL] =
        await Promise.all([
          this.configParameters.getCouponTableName(),
          this.shopifyClient.getShopifyAccessToken(),
          this.shopifyClient.getShopifyAdminBaseUrl(),
        ]);

      const code = generateDiscountCode(payload?.prefix);
      const priceRulePayload = this.buildPriceRulePayload(payload);
      const discountCodePayload = { code };

      const mutation = `
      mutation priceRuleCreate($priceRule: PriceRuleInput!, $priceRuleDiscountCode: PriceRuleDiscountCodeInput) {
        priceRuleCreate(priceRule: $priceRule, priceRuleDiscountCode: $priceRuleDiscountCode) {
          priceRule {
            id
            title
            allocationMethod
            allocationLimit
            customerSelection {
              forAllCustomers,
              customers(first: 5) { edges { node { id } } }
            }
            itemEntitlements {
              products(first: 5) { edges { node { id } } }
              productVariants(first: 5) { edges { node { id } } }
              collections(first: 5) { edges { node { id } } }
              targetAllLineItems
            }         
            target
            validityPeriod {
              start
              end
            }
            valueV2 {
              ... on MoneyV2 {
                amount
                currencyCode
              }
              ... on PricingPercentageValue {
                percentage
              }
            }
            itemPrerequisites {
              collections(first: 50) { edges { node { id } } }
              products(first: 50) { edges { node { id } } }
              productVariants(first: 50) { edges { node { id } } }
            }
            prerequisiteSubtotalRange { greaterThanOrEqualTo }
            prerequisiteQuantityRange { greaterThanOrEqualTo }
            prerequisiteShippingPriceRange { greaterThanOrEqualTo }
            prerequisiteToEntitlementQuantityRatio {
              prerequisiteQuantity
              entitlementQuantity
            }
            usageLimit
            oncePerCustomer
          }
          priceRuleDiscountCode {
            id
            code
          }
          priceRuleUserErrors {
            field
            message
          }
        }
      }
    `;

      const response = await fetch(`${SHOPIFY_ADMIN_BASE_URL}/graphql.json`, {
        method: 'POST',
        headers: {
          'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: mutation,
          variables: {
            priceRule: priceRulePayload,
            priceRuleDiscountCode: discountCodePayload,
          },
        }),
      });

      const { data, errors } = await response.json();
      if (errors || data?.priceRuleCreate?.priceRuleUserErrors?.length) {
        throw new Error(
          JSON.stringify(errors || data.priceRuleCreate.priceRuleUserErrors),
        );
      }

      const priceRule = data.priceRuleCreate.priceRule;
      const discountCode = data.priceRuleCreate.priceRuleDiscountCode;
      const priceRuleNumericId = parseInt(priceRule.id.split('/').pop(), 10);
      const discountCodeNumericId = parseInt(
        discountCode.id.split('/').pop(),
        10,
      );
      const timestamp = moment()
        .utcOffset('+05:30')
        .format('YYYY-MM-DDTHH:mm:ssZ');

      const priceRuleDynamoPayload = createPriceRulePayload({
        id: priceRuleNumericId,
        admin_graphql_api_id: priceRule?.id,
        title: priceRule?.title,
        allocation_limit: priceRule?.allocationLimit,
        allocation_method: priceRule?.allocationMethod?.toLowerCase(),
        customer_selection: priceRule?.customerSelection?.forAllCustomers
          ? 'all'
          : 'prerequisite',
        target_type: priceRule?.target?.toLowerCase(),
        target_selection: priceRule?.itemEntitlements?.targetAllLineItems
          ? 'all'
          : 'entitled',
        entitled_collection_ids:
          priceRule?.itemEntitlements?.collections?.edges?.map((edge: any) =>
            parseInt(edge?.node?.id?.split('/')?.pop(), 10),
          ) || [],
        entitled_product_ids: priceRule?.itemEntitlements?.products?.edges?.map(
          (edge: any) => parseInt(edge?.node?.id?.split('/')?.pop(), 10),
        ),
        entitled_variant_ids:
          priceRule?.itemEntitlements?.productVariants?.edges?.map(
            (edge: any) =>
              parseInt(edge?.node?.id?.split('/')?.pop(), 10) || [],
          ),
        prerequisite_collection_ids:
          priceRule?.itemPrerequisites?.collections?.edges?.map((edge: any) =>
            parseInt(edge?.node?.id?.split('/')?.pop(), 10),
          ) || [],
        prerequisite_product_ids:
          priceRule?.itemPrerequisites?.products?.edges?.map(
            (edge: any) =>
              parseInt(edge?.node?.id?.split('/')?.pop(), 10) || [],
          ),
        prerequisite_variant_ids:
          priceRule?.itemPrerequisites?.productVariants?.edges?.map(
            (edge: any) =>
              parseInt(edge?.node?.id?.split('/')?.pop(), 10) || [],
          ),
        value: priceRule?.valueV2?.amount
          ? priceRule?.valueV2?.amount
          : -(priceRule?.valueV2?.percentage * 100),
        value_type: priceRule?.valueV2?.amount ? 'fixed_amount' : 'percentage',
        created_at: timestamp,
        updated_at: timestamp,
        starts_at: priceRule?.validityPeriod?.start,
        ends_at: priceRule?.validityPeriod?.end,
        usage_limit: priceRule?.usageLimit,
        once_per_customer: priceRule?.oncePerCustomer,
        prerequisite_subtotal_range: priceRule?.prerequisiteSubtotalRange
          ? {
              greater_than_or_equal_to:
                priceRule?.prerequisiteSubtotalRange?.greaterThanOrEqualTo,
            }
          : null,
        prerequisite_quantity_range: priceRule?.prerequisiteQuantityRange
          ? {
              greater_than_or_equal_to:
                priceRule?.prerequisiteQuantityRange?.greaterThanOrEqualTo,
            }
          : null,
        prerequisite_shipping_price_range:
          priceRule?.prerequisiteShippingPriceRange
            ? {
                greater_than_or_equal_to:
                  priceRule?.prerequisiteShippingPriceRange
                    ?.greaterThanOrEqualTo,
              }
            : null,
        prerequisite_to_entitlement_quantity_ratio: {
          prerequisite_quantity:
            priceRule?.prerequisiteToEntitlementQuantityRatio
              ?.prerequisiteQuantity || null,
          entitled_quantity:
            priceRule?.prerequisiteToEntitlementQuantityRatio
              ?.entitlementQuantity || null,
        },
        entitled_country_ids: [],
        customer_segment_prerequisite_ids: [],
        prerequisite_customer_ids: priceRule?.customerSelection?.customers
          ? priceRule?.customerSelection?.customers?.edges?.map((edge: any) =>
              parseInt(edge?.node?.id?.split('/')?.pop(), 10),
            )
          : [],
        prerequisite_to_entitlement_purchase: { prerequisite_amount: null },
      });

      await this.docClient.createItem(
        new PutCommand({
          TableName: COUPONS_TABLE,
          Item: priceRuleDynamoPayload,
        }),
      );

      const discountDynamoPayload = createDiscountCodePayload({
        code: discountCode.code,
        id: discountCodeNumericId,
        price_rule_id: priceRuleNumericId,
        created_at: timestamp,
        updated_at: timestamp,
        usage_count: 0,
      });

      await this.docClient.createItem(
        new PutCommand({
          TableName: COUPONS_TABLE,
          Item: discountDynamoPayload,
        }),
      );

      return {
        code: discountCode.code,
        discountCode,
        priceRuleId: priceRule.id,
      };
    } catch (error) {
      console.error('Error creating Shopify Coupon:', error);
      throw new Error(
        `createShopifyCoupon failed: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
      );
    }
  }

  async callLeadSquare(payload: any) {
    const { customerPhone, storeId, code, codeLabel } = payload;
    // const requestBody = {
    //   LeadDetails: [
    //     {
    //       Attribute: 'Phone',
    //       Value: `${customerPhone}`,
    //     },
    //     {
    //       Attribute: 'Source',
    //       Value: 'Spin the Wheel - POS',
    //     },
    //     {
    //       Attribute: 'mx_Store_ID',
    //       Value: `${storeId}`,
    //     },
    //     {
    //       Attribute: 'mx_Lead_State',
    //       Value: `${storeState}`,
    //     },
    //     {
    //       Attribute: 'mx_Lead_City',
    //       Value: `${storeCity}`,
    //     },
    //     {
    //       Attribute: 'mx_Lead_Status',
    //       Value: 'Store',
    //     },
    //     {
    //       Attribute: 'mx_Zip',
    //       Value: `${storePinCode}`,
    //     },
    //     {
    //       Attribute: 'SearchBy',
    //       Value: 'Phone',
    //     },
    //   ],
    //   Activity: {
    //     ActivityEvent: 208,
    //     ActivityNote: 'Any notes realted to capture activity',
    //     ActivityDateTime: new Date()
    //       .toISOString()
    //       .replace('T', ' ')
    //       .split('.')[0],
    //     Fields: [
    //       {
    //         SchemaName: 'mx_Custom_1',
    //         Value: '',
    //       },
    //       {
    //         SchemaName: 'mx_Custom_2',
    //         Value: '',
    //       },
    //       {
    //         SchemaName: 'mx_Custom_3',
    //         Value: '',
    //       },
    //       {
    //         SchemaName: 'Status',
    //         Value: '',
    //       },
    //       {
    //         SchemaName: 'mx_Custom_4',
    //         Value: '',
    //       },
    //       {
    //         SchemaName: 'mx_Custom_5',
    //         Value: 'Spin the Wheel - POS',
    //       },
    //       {
    //         SchemaName: 'mx_Custom_6',
    //         Value: '',
    //       },
    //       {
    //         SchemaName: 'mx_Custom_7',
    //         Value: `${code + ':' + codeLabel}`,
    //       },
    //       {
    //         SchemaName: 'mx_Custom_8',
    //         Value: '',
    //       },
    //       {
    //         SchemaName: 'mx_Custom_9',
    //         Value: '',
    //       },
    //       {
    //         SchemaName: 'mx_Custom_10',
    //         Value: '',
    //       },
    //       {
    //         SchemaName: 'mx_Custom_11',
    //         Value: '',
    //       },
    //       {
    //         SchemaName: 'mx_Custom_13',
    //         Value: '',
    //       },
    //     ],
    //   },
    // };

    const requestBody = {
      phone: customerPhone,
      source: 'Spin the Wheel - POS',
      product_type: '',
      product_name: '',
      lead_status: 'Tier 3',
      store_id: storeId,
      activity_source: 'Spin the Wheel - POS',
      mx_custom_2: '',
      mx_custom_3: '',
      mx_custom_6: '',
      mx_custom_5: 'Spin the Wheel - POS',
      mx_custom_7: `${code + ':' + codeLabel}`,
      mx_custom_9: '',
      mx_custom_26: storeId,
      is_web_engage_event: false,
    };

    console.log('requestBody api calling', JSON.stringify(requestBody));
    const leadResponse = await fetch(
      // 'https://api-in21.leadsquared.com/v2/ProspectActivity.svc/CreateCustom?accessKey=' +
      //   'u%24r3f8f6b578c93f2d348094733191527e8' +
      //   '&secretKey=' +
      //   '524000eef0227ce5479ef4383d98e07ed283923d',
      'https://apis.thesleepcompany.in/lead/datafeed',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      },
    );

    const responseBody = await leadResponse.text();
    console.log('Status Code:', leadResponse.status);
    console.log('Response Body:', responseBody);
  }
  async createSpinTheWheelCoupon(
    claimSpinWheelRewardInput: ClaimSpinWheelRewardInput,
  ) {
    try {
      console.log('payload is', claimSpinWheelRewardInput);

      const SPIN_THE_WHEEL_TABLE =
        await this.configParameters.getSpinTheWheelCouponConfigTableName();

      const queryHandler = new ListSpinTheWheelCoupon(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const allConfigs = await queryHandler.listSpinWheelCoupon('coupon');
      console.log('allConfigs', allConfigs);

      const phoneExists = allConfigs.some((item) =>
        item?.createdCodes?.some(
          (phoneObj) =>
            phoneObj.phone === claimSpinWheelRewardInput?.customerPhone,
        ),
      );

      if (phoneExists) {
        throw new CustomError('Reward Already Claimed', 400);
      }

      const couponConfigs = allConfigs.find(
        (item) => claimSpinWheelRewardInput.codePrefix == item?.prefix,
      );
      console.log('couponConfigs', couponConfigs);

      if (!couponConfigs?.status || couponConfigs?.status != 'ACTIVE') {
        throw new CustomError('Coupon not active', 400);
      }
      if (couponConfigs?.currentCount >= couponConfigs?.limit) {
        throw new CustomError('Coupon limit reached', 400);
      }

      let response;
      if (claimSpinWheelRewardInput.codePrefix !== 'HL') {
        const getHandler = new GetCustomer(
          this.configService,
          this.ssmClient,
          this.shopifyClient,
          this.configParameters,
          this.docClient,
        );
        const customer = await getHandler.getCustomer(
          claimSpinWheelRewardInput?.customerPhone,
          'API',
          true,
        );
        console.log('customer is', customer);
        const payload = { ...couponConfigs, customerIds: customer };
        console.log('=======payload is=======', payload);
        if (!couponConfigs?.value_type) {
          throw new CustomError('Coupon Configs not defined', 400);
        }
        response = await this.createShopifyCoupon(payload);
        console.log('final res', response);
      }

      const updateExpressionString = `
        SET #updatedAt = :updatedAt,
            #currentCount = if_not_exists(#currentCount, :zero) + :increment,
            #createdCodes = list_append(if_not_exists(#createdCodes, :emptyList), :newCreatedCode)
      `;

      const updateExpressionAttributeNames = {
        '#updatedAt': 'updatedAt',
        '#currentCount': 'currentCount',
        '#createdCodes': 'createdCodes',
      };

      const expressionAttributesValues = {
        ':updatedAt': moment().toISOString(),
        ':increment': 1,
        ':zero': 0,
        ':newCreatedCode': [
          {
            phone: claimSpinWheelRewardInput.customerPhone,
            code: response?.code || ' ',
          },
        ],
        ':emptyList': [],
      };

      const command = new UpdateCommand({
        TableName: SPIN_THE_WHEEL_TABLE,
        Key: {
          prefix: claimSpinWheelRewardInput.codePrefix,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: updateExpressionAttributeNames,
        ExpressionAttributeValues: expressionAttributesValues,
        ConditionExpression: 'attribute_exists(prefix)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: any } =
        await this.docClient.updateItem(command);
      console.log('Attributes', Attributes);
      const leadPayload = {
        customerPhone: claimSpinWheelRewardInput?.customerPhone,
        storeId: claimSpinWheelRewardInput?.storeId,
        storeState: claimSpinWheelRewardInput?.storeState,
        storeCity: claimSpinWheelRewardInput?.storeCity,
        storePinCode: claimSpinWheelRewardInput?.storePinCode,
        code: response?.code || ' ',
        codeLabel: couponConfigs?.title,
      };
      this.callLeadSquare(leadPayload);
      this.sendSMSMessage(
        'spinTheWheel',
        response?.code,
        claimSpinWheelRewardInput?.customerPhone,
      );
      this.sendWhatsAppMessage(
        'spinTheWheel',
        response?.code,
        claimSpinWheelRewardInput?.customerPhone,
      );

      return { code: response?.code || ' ' };
    } catch (error) {
      posLogger.error(
        'claimSpinWheelRewardInput',
        'claimSpinWheelRewardInput',
        error,
      );
      throw error;
    }
  }
}
