import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class ClaimSpinWheelRewardInput {
  @Field(() => String, { description: 'number' })
  customerPhone: string;

  @Field(() => String, { description: 'codePrefix' })
  codePrefix: string;

  @Field(() => String, { description: 'storeId' })
  storeId: string;

  @Field(() => String, { description: 'storeId' })
  storeState: string;

  @Field(() => String, { description: 'storeId' })
  storeCity: string;

  @Field(() => String, { description: 'storeId' })
  storePinCode: string;
}
