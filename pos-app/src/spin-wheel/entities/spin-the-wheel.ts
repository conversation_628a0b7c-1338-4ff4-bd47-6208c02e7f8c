import { ObjectType, Field, Float } from '@nestjs/graphql';

@ObjectType()
export class GeneratedCode {
  @Field(() => String, { description: 'customerPhone' })
  phone: string;

  @Field(() => String, { description: 'code' })
  code: string;
}

@ObjectType()
export class SpinTheWheelData {
  @Field(() => String, { description: 'prefix' })
  prefix: string;

  @Field(() => String, { description: 'prefix' })
  label: string;

  @Field(() => String, { description: 'title' })
  title: string;

  @Field(() => Number, { description: 'currentCount' })
  currentCount: number;

  @Field(() => Number, { description: 'limit' })
  limit: number;

  @Field(() => String, { description: 'priceruleId' })
  priceruleId: string;

  @Field(() => Float, { description: 'probability' })
  probability: number;

  @Field(() => String, { description: 'status' })
  status: string;

  @Field(() => [GeneratedCode], {
    description: 'created codes',
    nullable: true,
  })
  createdCodes?: GeneratedCode[];

  @Field(() => String, { description: 'value_type' })
  value_type: string;
}

@ObjectType()
export class SpinTheWheel {
  @Field(() => [SpinTheWheelData], { nullable: true })
  data?: SpinTheWheelData[];

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class ClaimedCode {
  @Field(() => String, { nullable: true })
  code?: string;
}

@ObjectType()
export class ClaimSpinTheWheel {
  @Field(() => ClaimedCode, { nullable: true })
  data?: ClaimedCode;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
