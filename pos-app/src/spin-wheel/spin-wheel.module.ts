import { Module } from '@nestjs/common';
import { SpinWheelService } from './spin-wheel.service';
import { SpinWheelResolver } from './spin-wheel.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ShopifyModule } from 'src/common/shopify/shopify.module';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    S3ClientModule,
    ConfigParametersModule,
    ShopifyModule,
  ],
  providers: [
    SpinWheelResolver,
    SuccessHandler,
    ErrorHandler,
    SpinWheelService,
  ],
})
export class SpinWheelModule {}
