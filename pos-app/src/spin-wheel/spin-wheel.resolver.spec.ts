import { Test, TestingModule } from '@nestjs/testing';
import { SpinWheelResolver } from './spin-wheel.resolver';
import { SpinWheelService } from './spin-wheel.service';

describe('SpinWheelResolver', () => {
  let resolver: SpinWheelResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SpinWheelResolver, SpinWheelService],
    }).compile();

    resolver = module.get<SpinWheelResolver>(SpinWheelResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
