export function generateDiscountCode(prefix, randomCharCount = 3) {
  const digitArray = new Uint8Array(1);
  crypto.getRandomValues(digitArray);
  const digit = digitArray[0] % 10;
  const randomArray = new Uint8Array(randomCharCount);
  crypto.getRandomValues(randomArray);
  const chars = [];
  for (let i = 0; i < randomCharCount; i++) {
    const rand = randomArray[i] % 36;
    if (rand < 10) {
      chars.push(rand.toString());
    } else {
      chars.push(String.fromCharCode(65 + (rand - 10)));
    }
  }
  return `${prefix}${digit}${chars.join('')}`;
}
