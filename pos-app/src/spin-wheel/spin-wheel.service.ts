import { Injectable } from '@nestjs/common';
import { CreateSpinWheelCoupon } from './lib/claim-spin-the-wheel-reward';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
import { ListSpinTheWheelCoupon } from './lib/list-spin-the-wheel-coupon';
import { ClaimSpinWheelRewardInput } from './dto/claim-spin-the-wheel-reward-input';
import { AppShopify } from 'src/common/shopify/shopify';

@Injectable()
export class SpinWheelService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
    private shopifyClient: AppShopify,
  ) {}
  async createCoupon(claimSpinWheelRewardInputt: ClaimSpinWheelRewardInput) {
    try {
      const createCouponHandler = new CreateSpinWheelCoupon(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.s3Client,
        this.shopifyClient,
      );
      return await createCouponHandler.createSpinTheWheelCoupon(
        claimSpinWheelRewardInputt,
      );
    } catch (error) {
      throw error;
    }
  }

  async findAll(type?: any) {
    const queryHandler = new ListSpinTheWheelCoupon(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return queryHandler.listSpinWheelCoupon(type);
  }
}
