import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { SpinWheelService } from './spin-wheel.service';
import { ClaimSpinTheWheel, SpinTheWheel } from './entities/spin-the-wheel';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from 'src/common/response/errorHandler/error.handler';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CRMGuard } from 'src/auth/roles.guard';
import { ClaimSpinWheelRewardInput } from './dto/claim-spin-the-wheel-reward-input';

@Resolver(() => SpinTheWheel)
@UseGuards(CustomAuthGuard, CRMGuard)
export class SpinWheelResolver {
  constructor(
    private readonly SpinTheWheelService: SpinWheelService,
    private readonly successHandler: SuccessHand<PERSON>,
    private readonly errorHandler: <PERSON>rror<PERSON>and<PERSON>,
  ) {}

  @Mutation(() => ClaimSpinTheWheel, { name: 'claimSpinWheelReward' })
  async createSpinTheWheelCoupon(
    @Args('claimSpinWheelRewardInput')
    claimSpinWheelRewardInput: ClaimSpinWheelRewardInput,
  ) {
    try {
      const data = await this.SpinTheWheelService.createCoupon(
        claimSpinWheelRewardInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to bulk update price master',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => SpinTheWheel, { name: 'listSpinTheWheelCoupon' })
  async findAll(@Args('type', { type: () => String }) type: string) {
    try {
      const data = await this.SpinTheWheelService.findAll(type);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get coupon',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
