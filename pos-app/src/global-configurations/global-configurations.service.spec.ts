import { Test, TestingModule } from '@nestjs/testing';
import { GlobalConfigurationsService } from './global-configurations.service';

describe('GlobalConfigurationsService', () => {
  let service: GlobalConfigurationsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GlobalConfigurationsService],
    }).compile();

    service = module.get<GlobalConfigurationsService>(
      GlobalConfigurationsService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
