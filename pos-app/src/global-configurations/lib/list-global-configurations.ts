import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GlobalConfigurationData } from '../entities/global-configuration.entity';
import { AppConfigParameters } from 'src/config/config';
import { ScanDB } from 'src/common/helper/scan-table';

export class QueryGlobalConfiguration {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryGlobalConfiguration(): Promise<GlobalConfigurationData[]> {
    posLogger.info('GlobalConfiguration', 'listGlobalConfiguration', '');
    try {
      const GLOBAL_CONFIGURATION_TABLE =
        await this.configParameters.getGlobalConfigurationName();

      const scanDBHandler = new ScanDB(this.docClient);
      const configurations = await scanDBHandler.scanTable(
        GLOBAL_CONFIGURATION_TABLE,
      );

      return configurations;
    } catch (e) {
      posLogger.error('GlobalConfiguration', 'listGlobalConfiguration', {
        error: e,
      });
      throw e;
    }
  }
}
