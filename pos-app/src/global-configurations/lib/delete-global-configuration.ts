// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GlobalConfigurationData } from '../entities/global-configuration.entity';
import moment from 'moment';
import { AppConfigParameters } from 'src/config/config';
export class RemoveGlobalConfiguration {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async removeGlobalConfiguration(
    key: string,
    updatedBy: string,
  ): Promise<GlobalConfigurationData> {
    posLogger.info('globalConfiguration', 'removeGlobalConfiguration', {
      input: { key },
    });
    try {
      const GLOBAL_CONFIGURATION_TABLE =
        await this.configParameters.getGlobalConfigurationName();

      const param = new UpdateCommand({
        TableName: GLOBAL_CONFIGURATION_TABLE,
        Key: {
          key,
        },
        UpdateExpression:
          'SET updatedBy = :updatedBy, isDeleted = :isDeleted, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
          ':updatedBy': updatedBy,
          ':isDeleted': true,
          ':updatedAt': moment().toISOString(),
        },
        ReturnValues: 'ALL_NEW',
        ConditionExpression: 'attribute_exists(key)',
      });

      const { Attributes }: { Attributes: GlobalConfigurationData } =
        await this.docClient.updateItem(param);

      return Attributes;
    } catch (error) {
      posLogger.error('globalConfiguration', 'removeGlobalConfiguration', {
        error,
      });
      throw error;
    }
  }
}
