// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GlobalConfigurationData } from '../entities/global-configuration.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';

export class GetGlobalConfiguration {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getGlobalConfiguration(key: string): Promise<GlobalConfigurationData> {
    posLogger.info('GlobalConfiguration', 'getGlobalConfiguration', {
      input: key,
    });
    try {
      const GLOBAL_CONFIGURATION_TABLE =
        await this.configParameters.getGlobalConfigurationName();

      const command = new GetCommand({
        TableName: GLOBAL_CONFIGURATION_TABLE,
        Key: { key },
      });

      const { Item } = await this.docClient.getItem(command);

      if (!Item || Item.isDeleted) {
        throw new CustomError(`${key} configuration not found!`, 404);
      }
      return Item;
    } catch (e) {
      posLogger.error('GlobalConfiguration', 'getGlobalConfiguration', {
        error: e,
      });
      throw e;
    }
  }
}
