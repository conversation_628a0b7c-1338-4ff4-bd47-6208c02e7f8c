// modules
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GlobalConfigurationData } from '../entities/global-configuration.entity';
import { CreateGlobalConfigurationInput } from '../dto/create-global-configuration.input';
import { AppConfigParameters } from 'src/config/config';

export class CreateGlobalConfiguration {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createGlobalConfiguration(
    configuration: CreateGlobalConfigurationInput,
  ): Promise<GlobalConfigurationData> {
    posLogger.info('GlobalConfiguration', 'createGlobalConfiguration', {
      input: configuration,
    });
    try {
      const GLOBAL_CONFIGURATION_TABLE =
        await this.configParameters.getGlobalConfigurationName();

      const Item: GlobalConfigurationData = {
        ...configuration,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      };

      const command = new PutCommand({
        TableName: GLOBAL_CONFIGURATION_TABLE,
        Item,
      });

      await this.docClient.createItem(command);
      return Item;
    } catch (e) {
      posLogger.error('GlobalConfiguration', 'createGlobalConfiguration', {
        error: e,
      });
      throw e;
    }
  }
}
