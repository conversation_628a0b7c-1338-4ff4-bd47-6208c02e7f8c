// modules
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';

import { GlobalConfigurationData } from '../entities/global-configuration.entity';
import { CreateGlobalConfigurationInput } from '../dto/create-global-configuration.input';
import { AppConfigParameters } from 'src/config/config';

export class UpdateGlobalConfiguration {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateGlobalConfiguration(
    key: string = '',
    {
      key: {},
      ...updateGlobalConfigurationInput
    }: CreateGlobalConfigurationInput,
  ): Promise<GlobalConfigurationData> {
    posLogger.info('GlobalConfiguration', 'updateGlobalConfiguration', {
      input: { key, updateGlobalConfigurationInput },
    });
    try {
      const GLOBAL_CONFIGURATION_TABLE =
        await this.configParameters.getGlobalConfigurationName();

      let updateExpressionString: string = 'Set updatedAt = :updatedAt, ';
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributesValues: Record<string, any> = {
        ':updatedAt': moment().toISOString(),
      };

      Object.keys(updateGlobalConfigurationInput).map((key) => {
        updateExpressionString += `#${key} = :${key}, `;
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributesValues[`:${key}`] =
          updateGlobalConfigurationInput[key];
      });

      updateExpressionString = updateExpressionString.substring(
        0,
        updateExpressionString.length - 2,
      );

      // Fire the update command
      const command = new UpdateCommand({
        TableName: GLOBAL_CONFIGURATION_TABLE,
        Key: {
          key,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributesValues,
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: GlobalConfigurationData } =
        await this.docClient.updateItem(command);

      return Attributes;
    } catch (e) {
      posLogger.error('GlobalConfiguration', 'updateGlobalConfiguration', {
        error: e,
      });
      throw e;
    }
  }
}
