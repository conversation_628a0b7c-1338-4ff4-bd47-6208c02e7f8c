import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class GlobalConfigurationData {
  @Field(() => String, { description: 'Key' })
  key: string;

  @Field(() => String, { description: 'Value' })
  value: string;

  @Field(() => String, { nullable: true, description: 'Updated By' })
  updatedBy?: string;

  @Field(() => String, { description: 'CreatedAt', nullable: true })
  createdAt: string;

  @Field(() => String, { description: 'UpdatedAt' })
  updatedAt: string;
}

@ObjectType()
export class GlobalConfiguration {
  @Field(() => GlobalConfigurationData, {
    nullable: true,
    description: 'Global Configuration',
  })
  data: GlobalConfigurationData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class GlobalConfigurations {
  @Field(() => [GlobalConfigurationData], {
    nullable: true,
    description: 'Global Configuration',
  })
  data: GlobalConfigurationData[];

  @Field(() => Number, { description: 'Count of Total data', nullable: true })
  count: number;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
