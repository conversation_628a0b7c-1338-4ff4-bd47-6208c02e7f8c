import { Test, TestingModule } from '@nestjs/testing';
import { GlobalConfigurationsResolver } from './global-configurations.resolver';
import { GlobalConfigurationsService } from './global-configurations.service';

describe('GlobalConfigurationsResolver', () => {
  let resolver: GlobalConfigurationsResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GlobalConfigurationsResolver, GlobalConfigurationsService],
    }).compile();

    resolver = module.get<GlobalConfigurationsResolver>(
      GlobalConfigurationsResolver,
    );
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
