import { Module } from '@nestjs/common';
import { GlobalConfigurationsService } from './global-configurations.service';
import { GlobalConfigurationsResolver } from './global-configurations.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { ConfigParametersModule } from 'src/config/config.module';

@Module({
  imports: [DocumentClientModule, SsmClientModule, ConfigParametersModule],
  providers: [
    GlobalConfigurationsResolver,
    GlobalConfigurationsService,
    SuccessHandler,
    ErrorHandler,
  ],
})
export class GlobalConfigurationsModule {}
