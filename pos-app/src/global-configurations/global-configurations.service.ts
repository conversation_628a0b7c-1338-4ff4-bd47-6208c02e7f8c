import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateGlobalConfigurationInput } from './dto/create-global-configuration.input';
import { CreateGlobalConfiguration } from './lib/create-global-configuration';
import { QueryGlobalConfiguration } from './lib/list-global-configurations';
import { GetGlobalConfiguration } from './lib/get-global-configuration';
import { RemoveGlobalConfiguration } from './lib/delete-global-configuration';
import { UpdateGlobalConfiguration } from './lib/update-global-configurations';
import { AppConfigParameters } from 'src/config/config';

@Injectable()
export class GlobalConfigurationsService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}
  async create(createGlobalConfigurationInput: CreateGlobalConfigurationInput) {
    const createHandler = new CreateGlobalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return createHandler.createGlobalConfiguration(
      createGlobalConfigurationInput,
    );
  }

  findAll() {
    const queryHandler = new QueryGlobalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return queryHandler.queryGlobalConfiguration();
  }

  findOne(key: string) {
    const getHandler = new GetGlobalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.getGlobalConfiguration(key);
  }

  async update(
    key: string,
    updateGlobalConfigurationInput: CreateGlobalConfigurationInput,
  ) {
    const updateHandler = new UpdateGlobalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return updateHandler.updateGlobalConfiguration(
      key,
      updateGlobalConfigurationInput,
    );
  }

  remove(key: string, updatedBy: string) {
    const removeHandler = new RemoveGlobalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return removeHandler.removeGlobalConfiguration(key, updatedBy);
  }
}
