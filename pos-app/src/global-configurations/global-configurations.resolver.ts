import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { GlobalConfigurationsService } from './global-configurations.service';
import {
  GlobalConfiguration,
  GlobalConfigurations,
} from './entities/global-configuration.entity';
import { CreateGlobalConfigurationInput } from './dto/create-global-configuration.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { AdminGuard, CRMGuard, StaffGuard } from 'src/auth/roles.guard';

@Resolver(() => GlobalConfiguration)
@UseGuards(CustomAuthGuard, CRMGuard)
export class GlobalConfigurationsResolver {
  constructor(
    private readonly globalConfigurationsService: GlobalConfigurationsService,
    private readonly successHandler: SuccessHand<PERSON>,
    private readonly errorHandler: <PERSON>rror<PERSON><PERSON><PERSON>,
  ) {}

  @Mutation(() => GlobalConfiguration)
  @UseGuards(AdminGuard)
  async createGlobalConfiguration(
    @Args('createGlobalConfigurationInput')
    createGlobalConfigurationInput: CreateGlobalConfigurationInput,
  ) {
    try {
      const data = await this.globalConfigurationsService.create(
        createGlobalConfigurationInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create global configuration',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => GlobalConfigurations, { name: 'listGlobalConfigurations' })
  @UseGuards(StaffGuard)
  async findAll() {
    try {
      const data = await this.globalConfigurationsService.findAll();
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count: data.length };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list global configuration',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => GlobalConfiguration, { name: 'getGlobalConfiguration' })
  @UseGuards(StaffGuard)
  async findOne(@Args('key', { type: () => String }) key: string) {
    try {
      const data = await this.globalConfigurationsService.findOne(key);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get global configuration',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => GlobalConfiguration, { name: 'updateGlobalConfiguration' })
  @UseGuards(AdminGuard)
  async updateGlobalConfiguration(
    @Args('updateGlobalConfigurationInput')
    updateGlobalConfigurationInput: CreateGlobalConfigurationInput,
  ) {
    try {
      const data = await this.globalConfigurationsService.update(
        updateGlobalConfigurationInput.key,
        updateGlobalConfigurationInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update global configuration',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => GlobalConfiguration, { name: 'deleteGlobalConfiguration' })
  @UseGuards(AdminGuard)
  async removeGlobalConfiguration(
    @Args('key', { type: () => String }) key: string,
    @Args('updatedBy', { type: () => String }) updatedBy: string,
  ) {
    try {
      const data = await this.globalConfigurationsService.remove(
        key,
        updatedBy,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to delete global configuration',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
