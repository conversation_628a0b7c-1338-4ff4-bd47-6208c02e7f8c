import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { CustomDiscountVerificationService } from './custom-discount-verification.service';
import {
  CustomDiscountVerification,
  CustomDiscountVerificationData,
  VerificationResponse,
} from './entities/custom-discount-verification.entity';
import { CreateCustomDiscountVerificationInput } from './dto/create-custom-discount-verification.input';
import { HttpStatus } from '@nestjs/common';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';

import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ExportResponse } from 'src/stn/entity/stn.entity';
import { ListCustomDiscountVerificationInput } from './dto/list-custom-discount-verification.input';

@Resolver(() => CustomDiscountVerificationData)
export class CustomDiscountVerificationResolver {
  constructor(
    private readonly customDiscountVerificationService: CustomDiscountVerificationService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => CustomDiscountVerification)
  async createCustomDiscountVerification(
    @Args('createCustomDiscountVerificationInput')
    createCustomDiscountVerificationInput: CreateCustomDiscountVerificationInput,
  ) {
    try {
      const data = await this.customDiscountVerificationService.create(
        createCustomDiscountVerificationInput,
      );

      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create request',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => [CustomDiscountVerificationData], {
    name: 'customDiscountVerification',
  })
  findAll() {
    return this.customDiscountVerificationService.findAll();
  }

  @Query(() => CustomDiscountVerification, {
    name: 'customDiscountVerification',
  })
  async findOne(
    @Args('quotationId', { type: () => String }) quotationId: string,
    @Args('id', { type: () => String }) id: string,
  ) {
    try {
      const data = await this.customDiscountVerificationService.findOne(
        quotationId,
        id,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to fetch the record',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => ExportResponse, { name: 'exportCustomDiscountVerification' })
  async exportCustomDiscountVerification(
    @Args('email', { type: () => String }) email: string,
    @Args('listCustomDiscountVerificationInput')
    listCustomDiscountVerificationInput: ListCustomDiscountVerificationInput,
  ) {
    try {
      const { message } =
        await this.customDiscountVerificationService.exportCustomDiscountVerification(
          email,
          listCustomDiscountVerificationInput,
        );
      return {
        message,
        success: true,
        status: 200,
      };
    } catch (error) {
      const errorResponse = {
        success: false,
        status: error.statusCode || 500,
        message:
          error.message || 'Failed to export Custom Discount Verification data',
      };
      return errorResponse;
    }
  }

  @Mutation(() => VerificationResponse, {
    name: 'verifyCustomDiscountOTP',
  })
  async verifyOTP(
    @Args('quotationId', { type: () => String }) quotationId: string,
    @Args('otp', { type: () => String }) otp: string,
  ) {
    try {
      const data = await this.customDiscountVerificationService.verifyOTP(
        quotationId,
        otp,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to verify OTP',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => CustomDiscountVerificationData)
  removeCustomDiscountVerification(
    @Args('id', { type: () => Int }) id: number,
  ) {
    return this.customDiscountVerificationService.remove(id);
  }
}
