import { Injectable } from '@nestjs/common';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { GetCustomDiscountVerification } from '../lib/get-custom-discount-request';
import { posLogger } from 'src/common/logger';
import { CustomDiscountVerificationDetails } from 'src/quotations/entities/quotation.entity';

@Injectable()
export class UpdateQuotationCustomDiscountVerification {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async updateQuotationCustomDiscountVerification(
    quotationId: string,
    id: string,
  ): Promise<void> {
    posLogger.info(
      'UpdateQuotationCustomDiscountVerification',
      'updateQuotationCustomDiscountVerification',
      { quotationId, id },
    );

    try {
      // Fetch Custom Discount Verification Details
      const getCustomDiscountVerification = new GetCustomDiscountVerification(
        this.docClient,
        this.configParameters,
      );
      const {
        customDiscountAmount,
        approverDetails,
        customDiscountApprovalStatus,
        discountExpiredTimestamp,
        customDiscountValue,
        customDiscountType,
        initialCartDiscountedAmount,
        cartTotalAmount,
        finalCartDiscountedAmount,
        campaignDiscountAmount,
        promotionalDiscountAmount,
        additionalDiscountPercentage,
      } = await getCustomDiscountVerification.getCustomDiscountRequest(
        quotationId,
        id,
      );

      const customDiscountVerificationDetails: CustomDiscountVerificationDetails =
        {
          id,
          approverDetails,
          additionalDiscountPercentage,
          customDiscountAmount,
          customDiscountApprovalStatus,
          discountExpiredTimestamp,
          customDiscountValue,
          customDiscountType,
          initialCartDiscountedAmount,
          finalCartDiscountedAmount,
          cartTotalAmount,
          campaignDiscountAmount,
          promotionalDiscountAmount,
        };

      const QUOTATION_TABLE =
        await this.configParameters.getQuotationTableName();

      // Default update expression and values
      let updateExpression = `
        SET customDiscountVerificationDetails = :customDiscountVerificationDetails
      `;
      const expressionAttributeValues: any = {
        ':customDiscountVerificationDetails': customDiscountVerificationDetails,
      };

      if (customDiscountApprovalStatus === 'APPROVED') {
        const customCode = {
          value: customDiscountValue,
          value_type: customDiscountType,
          approver: approverDetails.name,
        };

        updateExpression += `,
          customCode = :customCode,
          customDiscountAmount = :customDiscountAmount
        `;
        expressionAttributeValues[':customCode'] = customCode;
        expressionAttributeValues[':customDiscountAmount'] =
          customDiscountAmount;
      }

      // Prepare Update Command
      const updateCommand = new UpdateCommand({
        TableName: QUOTATION_TABLE,
        Key: { id: quotationId },
        UpdateExpression: updateExpression,
        ExpressionAttributeValues: expressionAttributeValues,
      });

      // Execute Update Command
      await this.docClient.updateItem(updateCommand);

      posLogger.info(
        'UpdateQuotationCustomDiscountVerification',
        'updateQuotationCustomDiscountVerification',
        {
          message: 'Quotation updated successfully',
          quotationId,
          status: customDiscountApprovalStatus,
        },
      );
    } catch (error) {
      posLogger.error(
        'UpdateQuotationCustomDiscountVerification',
        'updateQuotationCustomDiscountVerification',
        { error },
      );
      throw new CustomError(
        `Failed to update quotation with ID ${quotationId}: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }
}
