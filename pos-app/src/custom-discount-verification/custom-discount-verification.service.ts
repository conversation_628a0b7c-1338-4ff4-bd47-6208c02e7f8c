import { Injectable } from '@nestjs/common';
import { CreateCustomDiscountVerificationInput } from './dto/create-custom-discount-verification.input';
import { ConfigService } from '@nestjs/config';

import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetCustomDiscountVerification } from './lib/get-custom-discount-request';
import { CreateCustomDiscountRequest } from './lib/create-custom-discount-request';
import { VerifyCustomDiscountRequest } from './lib/verify-custom-discount-request';
import { ListCustomDiscountVerificationInput } from './dto/list-custom-discount-verification.input';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { ExportCustomDiscountVerification } from './lib/export-custom-discount-verification';

@Injectable()
export class CustomDiscountVerificationService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private ssmClient: AppSsmClient,
    private readonly s3Client: AppS3Client,
  ) {}

  async create(
    createCustomDiscountVerificationInput: CreateCustomDiscountVerificationInput,
  ) {
    const createHandler = new CreateCustomDiscountRequest(
      this.docClient,
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return createHandler.createDiscountRequest(
      createCustomDiscountVerificationInput,
    );
  }

  async verifyOTP(quotationId: string, otp: string) {
    const createHandler = new VerifyCustomDiscountRequest(
      this.docClient,
      this.configParameters,
      this.configService,
      this.ssmClient,
    );
    return createHandler.verifyCustomDiscountRequest(quotationId, otp);
  }

  findAll() {
    return `This action returns all customDiscountVerification`;
  }

  async exportCustomDiscountVerification(
    email: string,
    listCustomDiscountVerificationInput: ListCustomDiscountVerificationInput,
  ) {
    const exportHandler = new ExportCustomDiscountVerification(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    return await exportHandler.export(
      email,
      listCustomDiscountVerificationInput,
    );
  }

  findOne(quotationId: string, id: string) {
    const createHandler = new GetCustomDiscountVerification(
      this.docClient,
      this.configParameters,
    );
    return createHandler.getCustomDiscountRequest(quotationId, id);
  }

  update(id: number) {
    return `This action updates a #${id} customDiscountVerification`;
  }

  remove(id: number) {
    return `This action removes a #${id} customDiscountVerification`;
  }
}
