import { CustomDiscountApprovalStatus } from '../entities/custom-discount-verification.entity';
import { CreateCustomDiscountVerificationInput } from './create-custom-discount-verification.input';
import { InputType, Field, PartialType } from '@nestjs/graphql';

@InputType()
export class UpdateCustomDiscountVerificationInput extends PartialType(
  CreateCustomDiscountVerificationInput,
) {
  @Field(() => String)
  id: string;

  @Field(() => String)
  quotationId: string;

  @Field(() => CustomDiscountApprovalStatus)
  approvalStatus: CustomDiscountApprovalStatus;
}
