import { InputType, Field } from '@nestjs/graphql';
import { CouponType } from 'src/common/enum/coupons';

@InputType()
export class ApproverDetailsInput {
  @Field(() => String, {
    description:
      'The unique identifier of the store requesting the discount approval.',
  })
  name: string;

  @Field(() => String, {
    description:
      'The unique identifier of the store requesting the discount approval.',
  })
  email: string;

  @Field(() => String, {
    description:
      'The unique identifier of the store requesting the discount approval.',
  })
  phone: string;
}
@InputType()
export class CreateCustomDiscountVerificationInput {
  @Field(() => String, { description: 'Quotation Id' })
  quotationId: string;

  @Field(() => ApproverDetailsInput, {
    description: 'Mobile number of the approver',
  })
  approverDetails: ApproverDetailsInput;

  @Field(() => CouponType, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountType: CouponType;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountValue: number;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountAmount: number;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  additionalDiscountPercentage: number;
}
