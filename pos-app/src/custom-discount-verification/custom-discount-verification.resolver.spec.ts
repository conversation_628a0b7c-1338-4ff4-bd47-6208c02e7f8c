import { Test, TestingModule } from '@nestjs/testing';
import { CustomDiscountVerificationResolver } from './custom-discount-verification.resolver';
import { CustomDiscountVerificationService } from './custom-discount-verification.service';

describe('CustomDiscountVerificationResolver', () => {
  let resolver: CustomDiscountVerificationResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomDiscountVerificationResolver,
        CustomDiscountVerificationService,
      ],
    }).compile();

    resolver = module.get<CustomDiscountVerificationResolver>(
      CustomDiscountVerificationResolver,
    );
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
