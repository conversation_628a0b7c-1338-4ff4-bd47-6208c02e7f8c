import { ObjectType, Field, registerEnumType } from '@nestjs/graphql';
import { CouponType } from 'src/common/enum/coupons';

// Define an enum for the discount approval status with all uppercase values
export enum CustomDiscountApprovalStatus {
  REQUESTED = 'REQUESTED',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  APPLIED = 'APPLIED',
  EXPIRED = 'EXPIRED',
}
registerEnumType(CustomDiscountApprovalStatus, {
  name: 'CustomDiscountApprovalStatus',
});

@ObjectType()
export class ApproverDetails {
  @Field(() => String, {
    description:
      'The unique identifier of the store requesting the discount approval.',
  })
  name: string;

  @Field(() => String, {
    description:
      'The unique identifier of the store requesting the discount approval.',
  })
  email: string;

  @Field(() => String, {
    description:
      'The unique identifier of the store requesting the discount approval.',
  })
  phone: string;
}

@ObjectType()
export class VerificationResponseData {
  @Field(() => CustomDiscountApprovalStatus, {
    description:
      'The status of the discount approval request. The options are PENDING, REJECTED, APPROVED, APPLIED, EXPIRED.',
  })
  customDiscountApprovalStatus: CustomDiscountApprovalStatus;
}
@ObjectType()
export class VerificationResponse {
  @Field(() => VerificationResponseData, { nullable: true })
  data: VerificationResponseData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}

@ObjectType()
export class CustomDiscountVerificationData {
  @Field(() => String, {
    description:
      'The unique identifier of the store requesting the discount approval.',
  })
  storeId: string;

  @Field(() => String, {
    description: 'Quotation ID',
  })
  quotationId: string;

  @Field(() => String, {
    description: 'Quotation ID',
  })
  id: string;

  @Field(() => String, {
    description: 'The staff ID of the person requesting the discount approval.',
  })
  employeeId: string;

  @Field(() => String, {
    description:
      'The customer’s unique number associated with the discount request.',
  })
  customerNumber: string;

  @Field(() => Number, {
    description: 'The initial cart amount before applying the discount.',
  })
  initialCartDiscountedAmount: number;

  @Field(() => Number, {
    description: 'The initial cart amount before applying the discount.',
  })
  cartTotalAmount: number;

  @Field(() => Number, {
    nullable: true,
    description: 'The final cart amount after applying the discount.',
  })
  finalCartDiscountedAmount: number;

  @Field(() => String, {
    nullable: true,
    description:
      'The unique identifier for the order associated with the discount request.',
  })
  orderId?: string;

  @Field(() => CouponType, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountType: CouponType;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountValue: number;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  customDiscountAmount: number;

  @Field(() => ApproverDetails, {
    description: 'The mobile number of the customer requesting the discount.',
  })
  approverDetails: ApproverDetails;

  @Field(() => String, {
    description: 'The OTP to whom the discount approval request is made.',
  })
  otp: string;

  @Field(() => CustomDiscountApprovalStatus, {
    description:
      'The status of the discount approval process (REQUESTED, REJECTED, APPROVED, APPLIED, EXPIRED).',
  })
  customDiscountApprovalStatus: CustomDiscountApprovalStatus;

  @Field(() => String, {
    description: 'The timestamp when the discount approval request was made.',
  })
  approvalRequestTimestamp: string;

  @Field(() => String, {
    nullable: true,
    description:
      'The timestamp when the discount approval was either approved or rejected.',
  })
  approvalApprovedOrRejectedTimestamp?: string;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  promotionalDiscountAmount: number;

  @Field(() => String, {
    nullable: true,
    description: 'The promotional code applied to the order.',
  })
  promotionalCode?: string;

  @Field(() => Number, {
    nullable: false,
    description: 'Final Amount',
  })
  campaignDiscountAmount: number;

  @Field(() => String, {
    nullable: true,
    description: 'The campaign code applied to the order.',
  })
  campaignCode?: string;

  @Field(() => String, {
    nullable: true,
    description: 'The timestamp when the discount was applied to the order.',
  })
  discountAppliedTimestamp?: string;

  @Field(() => String, {
    description: 'The timestamp when the discount expired.',
  })
  discountExpiredTimestamp: string;

  @Field(() => Number, {
    description: 'The discount amount requested for the order.',
  })
  additionalDiscountPercentage: number;
}
@ObjectType()
export class CustomDiscountVerification {
  @Field(() => CustomDiscountVerificationData, { nullable: true })
  data: CustomDiscountVerificationData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
