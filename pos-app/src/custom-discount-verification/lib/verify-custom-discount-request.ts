import { Injectable } from '@nestjs/common';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { GetQuotation } from 'src/quotations/lib/get-quotation';
import { GetCustomDiscountVerification } from './get-custom-discount-request';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import {
  CustomDiscountApprovalStatus,
  VerificationResponseData,
} from '../entities/custom-discount-verification.entity';
import { UpdateCustomDiscountStatus } from './update-custom-discount-status';

@Injectable()
export class VerifyCustomDiscountRequest {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configParameters: AppConfigParameters,
    private readonly configService: ConfigService,
    private readonly ssmClient: AppSsmClient,
  ) {}

  async verifyCustomDiscountRequest(
    quotationId: string,
    otp: string,
  ): Promise<VerificationResponseData> {
    posLogger.info(
      'VerifyCustomDiscountRequest',
      'verifyCustomDiscountRequest',
      { quotationId, otp },
    );

    try {
      // Initialize the required handlers within the function
      const getQuotationHandler = new GetQuotation(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const getCustomDiscountVerificationHandler =
        new GetCustomDiscountVerification(
          this.docClient,
          this.configParameters,
        );

      const quotation = await getQuotationHandler.getQuotation(quotationId);

      if (!quotation || !quotation.customDiscountVerificationDetails) {
        throw new CustomError(
          `Custom Discount Verification details not found for Quotation ID ${quotationId}`,
          404,
        );
      }

      if (
        quotation.customDiscountVerificationDetails
          .customDiscountApprovalStatus ===
        CustomDiscountApprovalStatus.APPROVED
      ) {
        return {
          customDiscountApprovalStatus: CustomDiscountApprovalStatus.APPROVED,
        };
      }

      const { id, discountExpiredTimestamp } =
        quotation.customDiscountVerificationDetails;

      const currentTimestamp = Date.now();
      const discountExpiredTimestampAsNumber = new Date(
        discountExpiredTimestamp,
      ).getTime();

      const updateCustomDiscountStatusHandler = new UpdateCustomDiscountStatus(
        this.docClient,
        this.configParameters,
      );

      if (
        discountExpiredTimestamp &&
        currentTimestamp > discountExpiredTimestampAsNumber
      ) {
        await updateCustomDiscountStatusHandler.updateCustomDiscountStatus(
          quotationId,
          id,
          CustomDiscountApprovalStatus.EXPIRED,
        );

        posLogger.info(
          'VerifyCustomDiscountRequest',
          'verifyCustomDiscountRequest',
          {
            message:
              'Custom Discount Verification status updated to EXPIRED due to expiration',
            quotationId,
            id,
          },
        );

        throw new CustomError('OTP is Expired!', 400);
      }

      const customDiscountVerification =
        await getCustomDiscountVerificationHandler.getCustomDiscountRequest(
          quotationId,
          id,
        );
      const { totalAmount } = quotation;

      if (customDiscountVerification.cartTotalAmount > totalAmount) {
        throw new CustomError(
          `Verification failed: The cart's total amount (${totalAmount}) is now less than the required total amount (${customDiscountVerification.cartTotalAmount}) that was present while creating the request.`,
          400,
        );
      }
      if (
        customDiscountVerification.customDiscountApprovalStatus !==
        CustomDiscountApprovalStatus.REQUESTED
      ) {
      }

      if (customDiscountVerification.otp !== otp) {
        throw new CustomError('Invalid OTP provided', 400);
      }

      // Step 4: Approve the discount
      await updateCustomDiscountStatusHandler.updateCustomDiscountStatus(
        quotationId,
        id,
        CustomDiscountApprovalStatus.APPROVED,
      );

      posLogger.info(
        'VerifyCustomDiscountRequest',
        'verifyCustomDiscountRequest',
        {
          message: 'Custom Discount Verification status updated to APPROVED',
          quotationId,
          id,
        },
      );

      return {
        customDiscountApprovalStatus: CustomDiscountApprovalStatus.APPROVED,
      };
    } catch (error) {
      posLogger.error(
        'VerifyCustomDiscountRequest',
        'verifyCustomDiscountRequest',
        { error },
      );

      const errorMessage =
        error instanceof CustomError
          ? error.message
          : `Failed to verify custom discount request: ${error.message}`;
      const statusCode = error instanceof CustomError ? error.statusCode : 500;

      throw new CustomError(errorMessage, statusCode);
    }
  }
}
