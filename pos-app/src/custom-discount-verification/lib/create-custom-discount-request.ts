import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import {
  CustomDiscountVerificationData,
  CustomDiscountApprovalStatus,
} from '../entities/custom-discount-verification.entity';
import moment from 'moment';
import { GetQuotation } from 'src/quotations/lib/get-quotation';
import { CreateCustomDiscountVerificationInput } from '../dto/create-custom-discount-verification.input';
import { generateOtp } from 'src/common/helper/generate-otp';
import { v4 as uuid } from 'uuid';
import { QuotationStatus } from 'src/common/enum/quotations';
import { UpdateCustomDiscountStatus } from './update-custom-discount-status';
import { UpdateQuotationCustomDiscountVerification } from '../helper/update-quotation-custom-discount-verification-details';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { GetEmployee } from 'src/employees/lib/get-employee';
import { GetParameterCommand } from '@aws-sdk/client-ssm';

export class CreateCustomDiscountRequest {
  constructor(
    private docClient: AppDocumentClient,
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createDiscountRequest(
    input: CreateCustomDiscountVerificationInput,
  ): Promise<CustomDiscountVerificationData> {
    try {
      console.log('CreateCustomDiscountRequest - Input:', input);
      const {
        quotationId,
        approverDetails: { phone: approverMobileNumber },
      } = input;

      const getQuotationHandler = new GetQuotation(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const {
        storeId,
        employeeId,
        finalDiscountedAmount,
        totalAmount,
        promotionalDiscountAmount,
        campaignDiscountAmount,
        customer,
        status,
        customDiscountVerificationDetails,
        campaignCode,
        promotionalCode,
      } = await getQuotationHandler.getQuotation(quotationId);

      if (status !== QuotationStatus.ACTIVE) {
        throw new CustomError('Quotation is not active', 403);
      }

      if (customDiscountVerificationDetails) {
        const { id, customDiscountApprovalStatus } =
          customDiscountVerificationDetails;
        if (
          customDiscountApprovalStatus ===
            CustomDiscountApprovalStatus.APPROVED ||
          customDiscountApprovalStatus === CustomDiscountApprovalStatus.APPLIED
        ) {
          throw new CustomError(
            'Discount already approved for this quotation',
            403,
          );
        }
        const updateStatusHandler = new UpdateCustomDiscountStatus(
          this.docClient,
          this.configParameters,
        );

        await updateStatusHandler.updateCustomDiscountStatus(
          quotationId,
          id,
          CustomDiscountApprovalStatus.EXPIRED,
        );

        console.log(
          `Existing discount request for ID ${id} marked as REJECTED.`,
        );
      }

      // Step 3: Generate a new OTP and create a new discount verification request
      const otp = generateOtp();
      console.log('Generated OTP:', otp);
      const getStoreHandler = new GetStore(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const { sapLocationCode } = await getStoreHandler.getStore(storeId);
      const getEmployeeHandler = new GetEmployee(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const employee = await getEmployeeHandler.getEmployee(employeeId);
      const { firstName } = employee;
      const { phone: customerNumber } = customer;
      const { customDiscountAmount, additionalDiscountPercentage } = input;
      const discountDetails = {
        storeName: sapLocationCode,
        quotationId,
        requestingStaffId: employeeId,
        requestingStaffName: firstName,
        customerNumber,
        orderValue: totalAmount,
        discountAmount: customDiscountAmount,
        campaignDiscountAmount,
        promotionalDiscountAmount,
        additionalDiscountPercentage,
        finalDiscountedAmount,
      };
      // await this.sendOtpWithDetails(approverMobileNumber, otp, discountDetails);
      // await this.sendOtp(approverMobileNumber, otp);
      await this.sendOTPViaSMS(approverMobileNumber, otp, discountDetails);
      const id = uuid();

      const tableName =
        await this.configParameters.getCustomDiscountVerificationTableName(); // Fetch table name dynamically
      const item: CustomDiscountVerificationData = {
        ...input,
        id,
        employeeId,
        storeId,
        quotationId,
        customerNumber: customer?.phone,
        cartTotalAmount: totalAmount,
        promotionalDiscountAmount,
        campaignDiscountAmount,
        otp,
        initialCartDiscountedAmount: finalDiscountedAmount,
        finalCartDiscountedAmount:
          finalDiscountedAmount - input.customDiscountAmount,
        customDiscountApprovalStatus: CustomDiscountApprovalStatus.REQUESTED,
        approvalRequestTimestamp: moment().toISOString(),
        approvalApprovedOrRejectedTimestamp: null,
        discountAppliedTimestamp: null,
        discountExpiredTimestamp: moment().add(1, 'hour').toISOString(),
        campaignCode,
        promotionalCode,
      };

      const command = new PutCommand({
        TableName: tableName,
        Item: item,
      });

      await this.docClient.createItem(command);

      const {
        Parameter: { Value: otpBaseURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/otp/url`,
          WithDecryption: true,
        }),
      );

      try {
        fetch(`${otpBaseURL}/sendwhatsapp`, {
          method: 'POST',
          headers: {
            'x-api-key': 'UBQJgJrPzW67r32ydSn9H8APs1VcZSkN3LKjoukp',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            template_attributes: {
              templateName: 'custom_discount_approval',
              phoneNo: approverMobileNumber,
              storeName: sapLocationCode,
              staffName: firstName,
              staffId: employeeId,
              customerNumber: customerNumber,
              quotationNumber: quotationId,
              orderValue: totalAmount,
              promotionCoupon: promotionalCode || '-',
              campaignCoupon: campaignCode || '-',
              afterDiscountAmount: finalDiscountedAmount - customDiscountAmount,
              reqDiscountAmount: customDiscountAmount,
              addDiscountPercentage: additionalDiscountPercentage,
              otpNamespace: 'OTP',
              OTP: otp,
            },
          }),
        })
          .then((res: any) => res.json())
          .then((res: any) => console.log(res))
          .catch((error: any) => {
            console.error(error);
            throw error;
          });
      } catch (error) {
        console.error(
          'Error sending OTP via WhatsApp:',
          error.message || error,
        );
        // throw new CustomError('Failed to send OTP via WhatsApp', error);
      }
      const updateQuotationHandler =
        new UpdateQuotationCustomDiscountVerification(
          this.docClient,
          this.configParameters,
        );

      await updateQuotationHandler.updateQuotationCustomDiscountVerification(
        quotationId,
        id,
      );

      return item;
    } catch (error) {
      console.error('CreateCustomDiscountRequest - Error:', error);
      throw new CustomError('Failed to create custom discount request', error);
    }
  }

  private async sendOtpWithDetails(
    phoneNumber: string,
    otp: string,
    customDiscountDetails: {
      storeName: string;
      requestingStaffId: string;
      customerNumber: string;
      orderValue: number;
      discountAmount: number;
    },
  ): Promise<void> {
    try {
      const userId = '2000232054';
      const password = 'uaMdCSc5';
      const {
        storeName,
        requestingStaffId,
        customerNumber,
        orderValue,
        discountAmount,
      } = customDiscountDetails;

      const url =
        `https://media.smsgupshup.com/GatewayAPI/rest?` +
        `userid=${encodeURIComponent(userId)}&` +
        `password=${encodeURIComponent(password)}&` +
        `send_to=${encodeURIComponent(phoneNumber)}&` +
        `v=1.1&format=json&msg_type=TEXT&method=SENDMESSAGE&` +
        `msg=${encodeURIComponent(
          `Custom Discount requested by ${storeName} Store for 
          Requesting Store: ${storeName}
          Requesting Staff ID: ${requestingStaffId}
          Customer number: ${customerNumber}
          Order Value: ${orderValue}
          Discount amount: ${discountAmount}
          OTP: ${otp}`,
        )}`;

      const response = await fetch(url, { method: 'GET' });
      const responseData = await response.json();

      const {
        response: { status },
      } = responseData;
      if (status != 'success') {
        throw new CustomError(`Failed to send OTP`, 400);
      }
      if (!response.ok) {
        const errorDetails = await response.text();
        console.error('Failed to send OTP:', errorDetails);
        throw new CustomError('Failed to send OTP', 400);
      }
      console.log('OTP sent successfully:', otp);
    } catch (error) {
      console.error('Error in sendOtpWithDetails:', error);
      throw new CustomError('Failed to send OTP', error);
    }
  }
  private async sendOtp(approverNumber: string, otp: string): Promise<void> {
    try {
      const userId = '2000232054';
      const password = 'uaMdCSc5';

      const url =
        `https://media.smsgupshup.com/GatewayAPI/rest?` +
        `userid=${encodeURIComponent(userId)}&` +
        `password=${encodeURIComponent(password)}&` +
        `send_to=${encodeURIComponent(approverNumber)}&` +
        `v=1.1&format=json&msg_type=TEXT&method=SENDMESSAGE&` +
        `msg=${encodeURIComponent(`${otp} is your verification code. For your security, do not share this code.`)}&` +
        `isTemplate=true&footer=This code expires in 10 minutes.`;

      // Send GET request to Gupshup API
      const response = await fetch(url, { method: 'GET' });
      const responseData = await response.json();
      const {
        response: { status },
      } = responseData;
      if (status != 'success') {
        throw new CustomError(`Failed to send OTP`, 400);
      }

      if (!response.ok) {
        const errorDetails = await response.text();
        const {
          response: { status },
        } = responseData;

        if (status !== 'success') {
          throw new CustomError(`Failed to send OTP: ${errorDetails}`, 400);
        }
        console.error('Failed to send OTP:', errorDetails);
        throw new CustomError('Failed to send OTP', 400);
      }

      console.log('OTP sent successfully:', otp);
    } catch (error) {
      console.error('Error in sendOnlyOtp:', error);
      throw new CustomError('Failed to send OTP', error);
    }
  }

  private async sendOTPViaSMS(
    phoneNumber: string,
    otp: string,
    customDiscountDetails: {
      storeName: string;
      requestingStaffName: string;
      requestingStaffId: string;
      customerNumber: string;
      quotationId: string;
      orderValue: number;
      discountAmount: number;
      promotionalDiscountAmount: number;
      campaignDiscountAmount: number;
      additionalDiscountPercentage: number;
      finalDiscountedAmount: number;
    },
  ): Promise<void> {
    const url =
      'https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/send_sms';
    const {
      storeName,
      requestingStaffName: staffName,
      requestingStaffId: staffId,
      quotationId: quotationNo,
      customerNumber,
      orderValue,
      discountAmount,
      promotionalDiscountAmount,
      campaignDiscountAmount,
      additionalDiscountPercentage,
      finalDiscountedAmount,
    } = customDiscountDetails;

    const payload = {
      template_attributes: {
        phoneNo: phoneNumber,

        templateName: 'POSOTP',
        customerNo: customerNumber,

        storeName,
        staffName,
        staffId,
        quotationNo,
        orderValue,
        promotionalDiscountAmount,
        campaignDiscountAmount,
        finalDiscountedAmount,
        requestedDiscountAmount: discountAmount,
        additionalDiscountPercentage,
        OTP: otp,
      },
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new CustomError(
          `Failed to send OTP. API Response: ${errorText}`,
          400,
        );
      }

      const result = await response.json();
      console.log('OTP sent successfully:', result);
    } catch (error) {
      console.error('Error sending OTP via SMS:', error.message || error);
      throw new CustomError('Failed to send OTP via SMS', error);
    }
  }
}
