import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { posLogger } from 'src/common/logger';

import { CustomDiscountApprovalStatus } from '../entities/custom-discount-verification.entity';
import { UpdateQuotationCustomDiscountVerification } from '../helper/update-quotation-custom-discount-verification-details';

export class UpdateCustomDiscountStatus {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async updateCustomDiscountStatus(
    quotationId: string,
    id: string,
    customDiscountApprovalStatus: CustomDiscountApprovalStatus,
    shopifyOrderId?: string,
  ): Promise<void> {
    const CUSTOM_DISCOUNT_VERIFICATION_TABLE =
      await this.configParameters.getCustomDiscountVerificationTableName();

    const updateExpressionParts = ['#customDiscountApprovalStatus = :status'];
    const expressionAttributeNames: Record<string, string> = {
      '#customDiscountApprovalStatus': 'customDiscountApprovalStatus',
    };
    const expressionAttributeValues: Record<string, any> = {
      ':status': customDiscountApprovalStatus,
    };

    const currentTimestamp = new Date().toISOString();

    if (
      customDiscountApprovalStatus === CustomDiscountApprovalStatus.APPROVED ||
      customDiscountApprovalStatus === CustomDiscountApprovalStatus.REJECTED
    ) {
      updateExpressionParts.push(
        '#approvalApprovedOrRejectedTimestamp = :approvalTimestamp',
      );
      expressionAttributeNames['#approvalApprovedOrRejectedTimestamp'] =
        'approvalApprovedOrRejectedTimestamp';
      expressionAttributeValues[':approvalTimestamp'] = currentTimestamp;
    } else if (
      customDiscountApprovalStatus === CustomDiscountApprovalStatus.APPLIED
    ) {
      updateExpressionParts.push(
        '#discountAppliedTimestamp = :appliedTimestamp',
      );
      updateExpressionParts.push('#orderId=:orderId');
      expressionAttributeNames['#discountAppliedTimestamp'] =
        'discountAppliedTimestamp';

      expressionAttributeNames['#orderId'] = 'orderId';
      expressionAttributeValues[':orderId'] = shopifyOrderId;

      expressionAttributeValues[':appliedTimestamp'] = currentTimestamp;
    }

    const updateCommand = new UpdateCommand({
      TableName: CUSTOM_DISCOUNT_VERIFICATION_TABLE,
      Key: { quotationId, id },
      UpdateExpression: `SET ${updateExpressionParts.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    });

    try {
      await this.docClient.updateItem(updateCommand);
      posLogger.info(
        'UpdateCustomDiscountStatusHandler',
        'updateCustomDiscountStatus',
        {
          message: 'Custom Discount Verification status updated',
          quotationId,
          id,
          customDiscountApprovalStatus,
        },
      );

      const updateQuotationHandler =
        new UpdateQuotationCustomDiscountVerification(
          this.docClient,
          this.configParameters,
        );

      await updateQuotationHandler.updateQuotationCustomDiscountVerification(
        quotationId,
        id,
      );

      posLogger.info(
        'UpdateCustomDiscountStatusHandler',
        'updateQuotationCustomDiscountVerification',
        {
          message:
            'Quotation table updated with custom discount verification details',
          quotationId,
          id,
        },
      );
    } catch (error) {
      posLogger.error(
        'UpdateCustomDiscountStatusHandler',
        'updateCustomDiscountStatus',
        {
          error,
          quotationId,
          id,
          customDiscountApprovalStatus,
        },
      );
      throw error;
    }
  }
}
