import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
import { json2csv } from 'json-2-csv';
import moment from 'moment';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { TemplateType } from 'src/common/enum/template-type';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { QueryHandlerService } from 'src/common/query-handler/query-handler';
import {
  CustomDiscountApprovalStatus,
  CustomDiscountVerificationData,
} from '../entities/custom-discount-verification.entity';

export class ExportCustomDiscountVerification {
  constructor(
    private readonly configService: ConfigService,
    private readonly docClient: AppDocumentClient,
    private readonly ssmClient: AppSsmClient,
    private readonly s3Client: AppS3Client,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async exportCSV(data: CustomDiscountVerificationData[]) {
    const formattedData = data.map((item) => {
      const currentTimestamp = Date.now();
      const discountExpiredTimestampAsNumber = new Date(
        item.discountExpiredTimestamp,
      ).getTime();
      const finalStatus = [
        CustomDiscountApprovalStatus.APPROVED,
        CustomDiscountApprovalStatus.APPLIED,
      ];

      const approvalStatus =
        item.discountExpiredTimestamp &&
        currentTimestamp > discountExpiredTimestampAsNumber &&
        !finalStatus.includes(item.customDiscountApprovalStatus)
          ? CustomDiscountApprovalStatus.EXPIRED
          : item.customDiscountApprovalStatus;

      return {
        'Store ID': item.storeId,
        'Quotation ID': item.quotationId,
        ID: item.id,
        'Employee ID': item.employeeId,
        'Customer Number': item.customerNumber,
        'Initial Cart Discounted Amount': item.initialCartDiscountedAmount,
        'Cart Total Amount': item.cartTotalAmount,
        'Final Cart Discounted Amount': item.finalCartDiscountedAmount || '-',
        'Order ID': item.orderId || '-',
        'Promotional Discount Amount': item?.promotionalDiscountAmount || '-',
        'Promotional Code': item?.promotionalCode || '-',
        'Campaign Discount Amount': item?.campaignDiscountAmount || '-',
        'Campaign Code': item?.campaignCode || '-',
        'Custom Discount Type': item.customDiscountType,
        'Custom Discount Value': item.customDiscountValue,
        'Custom Discount Amount': item.customDiscountAmount,
        'Approver Name': item.approverDetails?.name || '-',
        'Approver Email': item.approverDetails?.email || '-',
        'Approver Phone': item.approverDetails?.phone || '-',
        'Approval Status': approvalStatus,
        'Approval Request Timestamp': moment(item.approvalRequestTimestamp)
          .utcOffset('+05:30')
          .format('DD/MM/yyyy HH:mm'),
        'Approval Approved/Rejected Timestamp':
          item.approvalApprovedOrRejectedTimestamp
            ? moment(item.approvalApprovedOrRejectedTimestamp)
                .utcOffset('+05:30')
                .format('DD/MM/yyyy HH:mm')
            : '-',
        'Discount Applied Timestamp': item.discountAppliedTimestamp
          ? moment(item.discountAppliedTimestamp)
              .utcOffset('+05:30')
              .format('DD/MM/yyyy HH:mm')
          : '-',
        'Discount Expired Timestamp': moment(item.discountExpiredTimestamp)
          .utcOffset('+05:30')
          .format('DD/MM/yyyy HH:mm'),
      };
    });

    return await json2csv(formattedData, {});
  }

  async export(email: string, listCustomDiscountVerificationInput: any) {
    const TABLE_NAME =
      await this.configParameters.getCustomDiscountVerificationTableName();

    const { fromDate, toDate } = listCustomDiscountVerificationInput;

    const filters = [];
    if (fromDate && toDate) {
      filters.push({
        range: {
          approvalRequestTimestamp: {
            gte: fromDate,
            lte: toDate,
          },
        },
      });
    }

    const esHandler = new ElasticClient(this.configService, this.ssmClient);
    const queryHandler = new QueryHandlerService(esHandler);
    const sortItems = [{ approvalRequestTimestamp: { order: 'asc' } }];

    const data = await queryHandler.queryAll({
      index: TABLE_NAME,
      sortItems,
      filter: {
        bool: {
          must: [...filters],
        },
      },
      nextToken: null,
    });

    const csv = await this.exportCSV(data);

    const mailService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    await mailService.sendEmailWithFileAttachment(
      email,
      'Custom Discount Verification Export',
      'Here is your Custom Discount Verification export file',
      'text/csv',
      TemplateType.REPORT,
      'csv',
      [{ name: 'CustomDiscountVerification', content: csv }],
    );

    return { message: 'Email has been sent' };
  }
}
