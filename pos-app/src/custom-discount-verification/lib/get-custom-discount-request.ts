import { Injectable } from '@nestjs/common';
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { CustomDiscountVerificationData } from '../entities/custom-discount-verification.entity';
import { posLogger } from 'src/common/logger';

@Injectable()
export class GetCustomDiscountVerification {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async getCustomDiscountRequest(
    quotationId: string,
    id: string,
  ): Promise<CustomDiscountVerificationData> {
    posLogger.info(
      'CustomDiscountVerificationData',
      'getCustomDiscountRequest',
      {
        quotationId,
        id,
      },
    );

    try {
      const CUSTOM_DISCOUNT_VERIFICATION_TABLE =
        await this.configParameters.getCustomDiscountVerificationTableName();
      const command = new GetCommand({
        TableName: CUSTOM_DISCOUNT_VERIFICATION_TABLE,
        Key: {
          quotationId,
          id,
        },
      });

      const { Item }: { Item: CustomDiscountVerificationData } =
        await this.docClient.getItem(command);
      if (!Item) {
        throw new CustomError(
          `Custom Discount Request with ID ${id} for Quotation ${quotationId} not found`,
          404,
        );
      }

      return Item;
    } catch (e) {
      posLogger.error(
        'CustomDiscountVerificationData',
        'getCustomDiscountRequest',
        { error: e },
      );
      throw e;
    }
  }
}
