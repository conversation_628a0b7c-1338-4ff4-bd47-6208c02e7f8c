import { Injectable } from '@nestjs/common';
import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { CustomDiscountVerificationData } from '../entities/custom-discount-verification.entity';
import { posLogger } from 'src/common/logger';

@Injectable()
export class ListCustomDiscountVerifications {
  constructor(
    private readonly docClient: AppDocumentClient,
    private readonly configParameters: AppConfigParameters,
  ) {}

  async listCustomDiscountRequests(
    quotationId: string,
  ): Promise<CustomDiscountVerificationData[]> {
    posLogger.info(
      'CustomDiscountVerificationData',
      'listCustomDiscountRequests',
      {
        quotationId,
      },
    );

    try {
      const CUSTOM_DISCOUNT_TABLE =
        await this.configParameters.getCustomDiscountVerificationTableName();
      const command = new QueryCommand({
        TableName: CUSTOM_DISCOUNT_TABLE,
        KeyConditionExpression: 'quotationId = :quotationId',
        ExpressionAttributeValues: {
          ':quotationId': quotationId,
        },
      });

      const { Items }: { Items: CustomDiscountVerificationData[] } =
        await this.docClient.queryItems(command);

      if (!Items || Items.length === 0) {
        throw new CustomError(
          `No Custom Discount Requests found for Quotation ${quotationId}`,
          404,
        );
      }

      return Items;
    } catch (e) {
      posLogger.error(
        'CustomDiscountVerificationData',
        'listCustomDiscountRequests',
        { error: e },
      );
      throw e;
    }
  }
}
