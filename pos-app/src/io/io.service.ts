import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { ListIOInput } from './dto/list-io.input';
import { ExportIO } from './lib/export-io';

@Injectable()
export class IoService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
  ) {}
  async exportIO(email: string, listIOInput: ListIOInput) {
    const exportHandler = new ExportIO(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    exportHandler.exportIO(email, listIOInput).catch((error) => {
      console.error('IO', 'exportIO', error);
    });
    return { message: 'File will be sent on mail' };
  }
}
