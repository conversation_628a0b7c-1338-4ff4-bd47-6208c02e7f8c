import { <PERSON>du<PERSON> } from '@nestjs/common';
import { IoService } from './io.service';
import { IoResolver } from './io.resolver';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from 'src/common/response/errorHandler/error.handler';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { S3ClientModule } from 'src/common/s3-client/s3-client.module';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
    S3ClientModule,
  ],
  providers: [IoResolver, IoService, SuccessHandler, ErrorHandler],
})
export class IoModule {}
