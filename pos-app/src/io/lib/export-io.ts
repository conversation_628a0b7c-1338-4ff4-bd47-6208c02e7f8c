import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppS3Client } from 'src/common/s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';
import { json2csv } from 'json-2-csv';
import moment from 'moment';
import { IOData } from '../entities/io.entity';
import { PdfService } from 'src/common/ejs/nodemailer.service';
import { TemplateType } from 'src/common/enum/template-type';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { QueryHandlerService } from 'src/common/query-handler/query-handler';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { filterFormatter } from 'src/common/helper/filter-helper';

export class ExportIO {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
  ) {}

  async exportIOCSV(ioData: IOData[]) {
    const data = ioData.map((io) => {
      const {
        id,
        storeId,
        finalQuantity,
        quantity,
        sku,
        sourceId,
        sourceType,
        tag,
        updatedAt,
        inventoryFrom,
        inventoryTo,
      } = io;

      const formattedUpdatedAt = moment(updatedAt)
        .utcOffset('+05:30')
        .format('DD/MM/yyyy HH:mm');

      return {
        'Store ID': storeId,
        'IO ID': id,
        'Source ID': sourceId,
        'Source Type': sourceType,
        'Inventory From': inventoryFrom,
        'Inventory To': inventoryTo,
        SKU: sku,
        'Movment Quantity': quantity,
        'Final Quantity': finalQuantity,
        Tag: tag,
        'Inventory Transaction Completed Date': formattedUpdatedAt,
      };
    });

    return await json2csv(data, {});
  }

  async exportIO(email: string, filter: any) {
    const IO_TABLE = await this.configParameters.getIOTableName();
    console.log('export');

    const searchArray = [];

    const { searchArray: filteredSearch } = await filterFormatter(
      sortingFilter,
      searchingFilter,
      sortingFilterType,
      filter,
    );

    searchArray.push(...filteredSearch);
    const { fromDate, toDate } = filter || {};
    if (fromDate && toDate) {
      searchArray.push({
        range: {
          updatedAt: {
            gte: fromDate,
            lte: toDate,
          },
        },
      });
    }
    const esHandler = new ElasticClient(this.configService, this.ssmClient);
    const queryHandler = new QueryHandlerService(esHandler);
    const sortItems = [{ updatedAt: { order: 'asc' } }];
    const ioData = await queryHandler.queryAll({
      index: IO_TABLE,
      filter: {
        bool: {
          must: [...searchArray],
        },
      },
      nextToken: null,
      sortItems,
    });
    console.log('ioData', ioData);

    const ioCSV = await this.exportIOCSV(ioData);

    const mailService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
    );

    await mailService.sendEmailWithFileAttachment(
      email,
      'IO Export',
      'Here is your IO export file',
      'text/csv',
      TemplateType.REPORT,
      'csv',
      [{ name: 'IOs', content: ioCSV }],
    );

    return { message: 'Email has been sent' };
  }
}
