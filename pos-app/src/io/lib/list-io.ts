// modules

import { ConfigService } from '@nestjs/config';
import { filterFormatter } from 'src/common/helper/filter-helper';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { AppConfigParameters } from 'src/config/config';
import { ListIOInput } from '../dto/list-io.input';
import { IOData, IOs } from '../entities/io.entity';

export class QueryIOs {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryIOs(filter?: ListIOInput): Promise<IOs> {
    posLogger.info('IO', 'queryIO', { input: filter });
    try {
      const IO_TABLE = await this.configParameters.getIOTableName();
      const searchArray = [];

      const { searchArray: filteredSearch, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );

      searchArray.push(...filteredSearch);
      const { fromDate, toDate } = filter || {};

      if (fromDate && toDate) {
        searchArray.push({
          range: {
            updatedAt: {
              gte: fromDate,
              lte: toDate,
            },
          },
        });
      }

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: IO_TABLE,
        });

        size = bodyRes?.count;
      }

      if (searchArray.length) {
        const { body: bodyRes } = await esHandler.count({
          index: IO_TABLE,
          body: {
            query: {
              bool: {
                must: [...searchArray],
              },
            },
          },
        });

        const response = await esHandler.search({
          index: IO_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data: IOData[] = response.body.hits.hits.map(
          (hit) => hit._source,
        );

        return { data, count: bodyRes?.count };
      }

      const response = await esHandler.search({
        index: IO_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });

      const data: IOData[] = response.body.hits.hits.map((hit) => hit._source);

      const { body: bodyRes } = await esHandler.count({
        index: IO_TABLE,
      });

      return { data, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('IO', 'queryIOs', { error: e });
      throw e;
    }
  }
}
