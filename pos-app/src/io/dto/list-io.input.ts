import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class IOTextSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  id?: string;
}
@InputType()
export class IOTermSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  storeId?: string;
}

@InputType()
export class IOSortingFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class ListIOInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size?: string;

  @Field(() => IOTextSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  textSearchFields?: IOTextSearchFieldsInput;

  @Field(() => IOTermSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  termSearchFields?: IOTermSearchFieldsInput;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  fromDate?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  toDate?: string;

  @Field(() => IOSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { updatedAt: 'desc' },
  })
  sortBy?: IOSortingFieldsInput;
}
