import { Test, TestingModule } from '@nestjs/testing';
import { IoResolver } from './io.resolver';
import { IoService } from './io.service';

describe('IoResolver', () => {
  let resolver: IoResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [IoResolver, IoService],
    }).compile();

    resolver = module.get<IoResolver>(IoResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
