import { ObjectType, Field, Int } from '@nestjs/graphql';

@ObjectType()
export class IOData {
  @Field(() => String, {
    description: 'Unique identifier for the inventory operation',
  })
  id: string;

  @Field(() => String, {
    description: 'Identifier for the store involved in the operation',
  })
  storeId: string;

  @Field(() => String, {
    description: 'Final quantity after the inventory operation',
  })
  finalQuantity: string;

  @Field(() => String, {
    description: 'Initial quantity before the inventory operation',
  })
  quantity: string;

  @Field(() => String, {
    description: 'Stock Keeping Unit (SKU) associated with the product',
  })
  sku: string;

  @Field(() => String, {
    description: 'ID of the source from where the inventory is transferred',
  })
  sourceId: string;

  @Field(() => String, {
    description: 'Type of the source, e.g., warehouse, supplier',
  })
  sourceType: string;

  @Field(() => String, {
    description: 'Type of the source, e.g., warehouse, supplier',
  })
  inventoryFrom: string;

  @Field(() => String, {
    description: 'Type of the source, e.g., warehouse, supplier',
  })
  inventoryTo: string;

  @Field(() => String, {
    description:
      'Tag or label associated with the operation for categorization',
  })
  tag: string;

  @Field(() => String, {
    description: 'Timestamp of when the operation was last updated',
  })
  updatedAt: string;
}

@ObjectType()
export class IO {
  @Field(() => IOData, { nullable: true })
  data?: IOData;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class IOs {
  @Field(() => [IOData], { nullable: true })
  data?: IOData[];

  @Field(() => Int, { nullable: true, description: 'count' })
  count?: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}
