import { Resolver, Query, Args } from '@nestjs/graphql';
import { IoService } from './io.service';
import { IO } from './entities/io.entity';
import { ExportResponse } from 'src/stn/entity/stn.entity';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus } from '@nestjs/common';
import { ListIOInput } from './dto/list-io.input';

@Resolver(() => IO)
export class IoResolver {
  constructor(
    private readonly ioService: IoService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Query(() => ExportResponse, { name: 'exportIO' })
  async exportIO(
    @Args('email', { type: () => String }) email: string,
    @Args('listIOInput', { nullable: true }) listIOInput: ListIOInput,
  ) {
    try {
      const { message } = await this.ioService.exportIO(email, listIOInput);
      return {
        message,
        success: true,
        status: 200,
      };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to export STNs',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
