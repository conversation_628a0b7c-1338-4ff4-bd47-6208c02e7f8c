import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { AppConfigParameters } from 'src/config/config';
import moment from 'moment';
import { DeliveryType } from 'src/common/enum/delivery';
import { posLogger } from 'src/common/logger';
import { ListSpinTheWheelCoupon } from 'src/spin-wheel/lib/list-spin-the-wheel-coupon';
import { AppDocumentClient } from 'src/common/document-client/document-client';

@Injectable()
export class SapService {
  private esHandler: ElasticClient;
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private docClient: AppDocumentClient,
  ) {
    this.esHandler = new ElasticClient(this.configService, this.ssmClient);
  }

  async query({
    index,
    limit: size = 100,
    page = 1,
    filter,
    sort,
    nextToken: nt,
  }) {
    let searchAfter;
    if (nt) {
      searchAfter = nt
        ? JSON.parse(Buffer.from(nt, 'base64').toString('ascii'))
        : undefined;
    }

    console.log('searchAfter :>> ', searchAfter);

    //Building search request
    const searchParams = {
      index,
      size,
      from: (page - 1) * size,
      body: {
        version: false,
        track_total_hits: true,
        search_after: searchAfter,
        query: filter,
        sort: [...sort],
      },
    };

    // Executing the OpenSearch request
    const { body } = await this.esHandler.search(searchParams);

    const { hits } = body;
    const { hits: results = [], total } = hits;
    const lastResult = results[results.length - 1];
    const nextToken =
      lastResult && lastResult.sort
        ? Buffer.from(JSON.stringify(lastResult.sort), 'ascii').toString(
            'base64',
          )
        : null;

    return {
      page,
      pageSize: size,
      totalPages: Math.ceil(total.value / size),
      total: total.value,
      items: results.map(({ _source }) => _source),
      nextToken: nextToken,
    };
  }

  async queryAll({ index, filter, nextToken: nT }) {
    console.log('index :>> ', index);
    const sortItems = [{ 'id.keyword': { order: 'desc' } }];

    const { items, nextToken } = await this.query({
      index,
      filter,
      sort: sortItems,
      nextToken: nT,
      limit: 9999,
    });

    if (nextToken) {
      const nextItems = await this.queryAll({
        index,
        filter,
        nextToken,
      });
      return [...items, ...nextItems];
    }

    return items;
  }

  async findAll(From, To) {
    posLogger.info('SAP', 'findAll', { From, To });

    const must: any = [
      {
        term: {
          'status.keyword': {
            value: 'SENT_TO_SHOPIFY',
          },
        },
      },
    ];
    // const mustNot: any = [
    //   {
    //     term: {
    //       isCreatedOnEE: true,
    //     },
    //   },
    // ];

    if (From) {
      must.push({
        range: {
          shopifyCreatedAt: {
            gte: moment(From).toISOString(),
          },
        },
      });
    }

    if (To) {
      must.push({
        range: {
          shopifyCreatedAt: {
            lte: moment(To).toISOString(),
          },
        },
      });
    }

    const ORDER_TABLE = await this.configParameters.getOrderTableName();

    const data = await this.queryAll({
      index: ORDER_TABLE,
      filter: {
        bool: {
          must,
          // must_not: mustNot,
        },
      },
      nextToken: null,
    });

    const finalOrders = data.filter(
      (d) =>
        d?.storeId.toUpperCase() !== 'TEST-STORE' &&
        (d?.orderProducts
          ? (d?.orderProducts).every(
              (p) => p.deliveryStatus === DeliveryType.CASH_AND_CARRY,
            )
          : false),
    );

    return finalOrders.map((d) => {
      let count = 0;

      const shopifyproduct = d.orderProducts.map((item) => {
        const product = {
          LineID: count,
          id: item.productId,
          variant_id: item.variantId,
          sku: item.sku,
          quantity: item.quantity,
          title: item.title,
          price: item.price,
          total: item.price * item.quantity,
          lineItemFinal: item.finalItemPrice,
          hsnentry: item.hsn,
          gsttax: item.gstRate,
          discountwithouttax: item.finalItemPriceWithoutGst,
        };

        count += 1;
        return product;
      });

      return {
        orderid: d.shopifyOrderId,
        ordernumber: d.shopifyOrderName,
        invoiceno: d.id,
        invoicedate: moment(d.createdAt).format('yyyy-MM-DD HH:mm:ss'),
        customerfirstname: d.customer.firstName,
        customerlastname: d.customer.lastName,
        ordercreatedate: moment(d.shopifyCreatedAt)
          .utcOffset('+05:30')
          .format('yyyy-MM-DD HH:mm:ss'),
        discounttype: d.shopifyDiscountType || '',
        discountamount: d.totalAmount - d.finalDiscountedAmount || '',
        shippingaddress1: d.shippingAddress.line1,
        shippingaddress2: d.shippingAddress.line2 || '',
        shippingcity: d.shippingAddress.city,
        shippingstate: d.shippingAddress.state,
        shippingpincode: d.shippingAddress.pinCode,
        billingaddress1: d.billingAddress.line1,
        billingaddress2: d.billingAddress.line2 || '',
        billingcity: d.billingAddress.city,
        billingpincode: d.billingAddress.pinCode,
        billingstate: d.billingAddress.state,
        gstnumber: d.gstDetails.gstNumber,
        companyname: d.gstDetails.companyName,
        loggedusername: d.loggedusername, //sap store code
        series: d.series, //financial year
        requestorderamount: d.finalDiscountedAmount,
        discountedAmount: `${d.totalAmount - d.finalDiscountedAmount}`,
        totalValue: d.totalAmount, //without discount
        acctcode: d.acctcode,
        whscode: d.whscode,
        cardcode: d.cardcode,
        shopifyproduct,
      };
    });
  }
  async findOne(code: string) {
    const queryHandler = new ListSpinTheWheelCoupon(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const response = await queryHandler.listSpinWheelCoupon(code);
    const isExists = response.some((item) =>
      item?.createdCodes?.some((phoneObj) => phoneObj.code == code),
    );

    return { createdForSpinWheel: isExists };
  }
}
