import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { SapService } from './sap.service';
import { SAPCustomAuthGuard } from 'src/auth/sap.guard';

@Controller('sap')
export class SapController {
  constructor(private readonly sapService: SapService) {}

  @Get('/takeaways')
  @UseGuards(SAPCustomAuthGuard)
  async findAll(@Query('From') From?: string, @Query('To') To?: string) {
    return await this.sapService.findAll(From, To);
  }

  @Get('/validateCoupon')
  @UseGuards(SAPCustomAuthGuard)
  async findOne(@Query('code') code?: string) {
    return await this.sapService.findOne(code);
  }
}
