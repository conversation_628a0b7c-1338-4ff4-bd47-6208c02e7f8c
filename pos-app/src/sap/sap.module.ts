import { Module } from '@nestjs/common';
import { SapService } from './sap.service';
import { <PERSON>p<PERSON>ontroller } from './sap.controller';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';

@Module({
  imports: [SsmClientModule, ConfigParametersModule, DocumentClientModule],
  controllers: [SapController],
  providers: [SapService],
})
export class SapModule {}
