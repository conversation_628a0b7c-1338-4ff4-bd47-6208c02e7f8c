import { Controller, Get, Query, BadRequestException } from '@nestjs/common';
import { StoreOrdersDataService } from './store-orders-data.service';
import { posLogger } from 'src/common/logger'; // Assuming you have a logger module for logging

@Controller('store-orders-data')
export class StoreOrdersDataController {
  constructor(
    private readonly storeOrdersDataService: StoreOrdersDataService,
  ) {}

  @Get('sales')
  async getStoreSalesData(
    @Query('from') from: string,
    @Query('to') to: string,
    @Query('storeId') storeId: string,
    @Query('apiKey') apiKey: string,
  ) {
    posLogger.info('Store sales', 'Store sales data requested', {
      from,
      to,
      storeId,
    });

    if (!from || !to || !storeId) {
      throw new BadRequestException(
        'Missing required query parameters: from, to, or storeId',
      );
    }

    if (!apiKey) {
      throw new BadRequestException('Missing required query parameter: apiKey');
    }

    if (!this.isValidDate(from) || !this.isValidDate(to)) {
      throw new BadRequestException('Invalid date format. Use YYYY-MM-DD');
    }

    try {
      const orders = await this.storeOrdersDataService.findOrders({
        from,
        to,
        storeId,
        apiKey,
      });
      return orders;
    } catch (error) {
      posLogger.error('Store sales', 'Error fetching store sales data', {
        error,
      });
      throw new BadRequestException(
        error?.response?.message || 'Failed to fetch store sales data',
      );
    }
  }

  private isValidDate(date: string): boolean {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    return regex.test(date);
  }
}
