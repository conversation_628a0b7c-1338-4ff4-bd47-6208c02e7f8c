import { Test, TestingModule } from '@nestjs/testing';
import { StoreOrdersDataController } from './store-orders-data.controller';
import { StoreOrdersDataService } from './store-orders-data.service';

describe('StoreOrdersDataController', () => {
  let controller: StoreOrdersDataController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StoreOrdersDataController],
      providers: [StoreOrdersDataService],
    }).compile();

    controller = module.get<StoreOrdersDataController>(
      StoreOrdersDataController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
