import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { posLogger } from 'src/common/logger';
import { QueryHandlerService } from 'src/common/query-handler/query-handler';
import moment from 'moment-timezone';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ListStoreOrdersApiKeysHandler } from 'src/store-orders-api-key/lib/create-store-orders-api-key copy';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { OrderData } from 'src/orders/entities/order.entity';
import { OrderType } from 'src/common/enum/order';

interface QueryParameters {
  from: string;
  to: string;
  storeId: string;
  apiKey: string;
}

@Injectable()
export class StoreOrdersDataService {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private queryHandlerService: QueryHandlerService,
    private docClient: AppDocumentClient,
  ) {}

  private async fetchOrders({ from, to, storeId }: QueryParameters) {
    const ORDER_TABLE = await this.configParameters.getOrderTableName();

    const filter = {
      bool: {
        must: [
          { range: { shopifyCreatedAt: { gte: from, lte: to } } },
          { term: { 'storeId.keyword': storeId } },
        ],
      },
    };
    const sort = [{ shopifyCreatedAt: { order: 'asc' } }];
    const esHandler = new ElasticClient(this.configService, this.ssmClient);
    const queryHandler = new QueryHandlerService(esHandler);

    try {
      const orders = await queryHandler.queryAll({
        index: ORDER_TABLE,
        filter,
        sortItems: sort,
        nextToken: null,
      });
      return orders;
    } catch (error) {
      posLogger.error('ordersdataa', 'Error fetching orders', {
        error: error.message,
        storeId,
        from,
        to,
      });
      throw new Error(`Error fetching orders: ${error.message}`);
    }
  }

  async findOrders({
    from,
    to,
    storeId,
    apiKey,
  }: QueryParameters): Promise<any> {
    posLogger.info('Store sales', 'Fetching store sales orders', {
      from,
      to,
      storeId,
    });
    const listHandler = new ListStoreOrdersApiKeysHandler(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const apiKeys = await listHandler.listStoreOrdersApiKeys();

    if (apiKeys.length === 0) {
      throw new BadRequestException('No API keys found!');
    }
    const requestedAPIkeyData = apiKeys.find((key) => key.apiKey === apiKey);
    if (!requestedAPIkeyData) {
      throw new BadRequestException(`API key ${apiKey} not found!`);
    }
    const isStoreAllowed = requestedAPIkeyData.storeIds.includes(storeId);

    if (!isStoreAllowed) {
      throw new BadRequestException(
        `API key ${apiKey} not allowed for this store!`,
      );
    }

    const orders = await this.fetchOrders({ from, to, storeId, apiKey });

    if (orders.length === 0) {
      throw new BadRequestException(
        'No orders found for the given store within the specified date range',
      );
    }

    const groupedOrders = orders.reduce((acc, order: OrderData) => {
      const orderDate = moment
        .utc(order.shopifyCreatedAt)
        .tz('Asia/Kolkata')
        .format('YYYY-MM-DD');
      const formattedOrder = {
        orderId: order?.shopifyOrderName || '-',
        invoiceNo: order.id || '-',
        orderDateTime: moment
          .utc(order.shopifyCreatedAt)
          .tz('Asia/Kolkata')
          .toISOString(),
        orderAmountTotal: order.totalAmount,
        discount:
          order.customDiscountAmount +
          order.campaignDiscountAmount +
          order.promotionalDiscountAmount,
        orderAmountPostDiscount: order.finalDiscountedAmount,
        orderTaxAmount: order.orderProducts?.reduce(
          (sum, product) => sum + product?.gstAmount || 0,
          0,
        ),
        transactionType:
          order.orderType === OrderType.POS ? 'SALE' : order.orderType,
      };
      formattedOrder['netOrderValue'] =
        formattedOrder.orderAmountPostDiscount + formattedOrder.orderTaxAmount;

      if (!acc[storeId]) {
        acc[storeId] = [];
      }

      const dateGroup = acc[storeId].find(
        (group) => Object.keys(group)[0] === orderDate,
      );
      if (dateGroup) {
        dateGroup[orderDate].push(formattedOrder);
      } else {
        acc[storeId].push({ [orderDate]: [formattedOrder] });
      }

      return acc;
    }, {});

    return groupedOrders;
  }
}
