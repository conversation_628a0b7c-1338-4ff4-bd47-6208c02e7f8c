import { Module } from '@nestjs/common';
import { StoreOrdersDataService } from './store-orders-data.service';
import { StoreOrdersDataController } from './store-orders-data.controller';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { QueryHandlerModule } from 'src/common/query-handler/query-handler.module'; // Import the QueryHandlerModule
import { DocumentClientModule } from 'src/common/document-client/document-client.module';

@Module({
  imports: [
    SsmClientModule,
    ConfigParametersModule,
    QueryHandlerModule,
    DocumentClientModule,
  ], // Add it here
  exports: [StoreOrdersDataService],
  controllers: [StoreOrdersDataController],
  providers: [StoreOrdersDataService],
})
export class StoreOrdersDataModule {}
