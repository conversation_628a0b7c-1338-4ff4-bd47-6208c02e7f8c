import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { WhatsAppService } from './whatsapp.service';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CRMGuard } from 'src/auth/roles.guard';
import { SendWhatsAppMessageInput } from './dto/whatsapp.dto';
import { SendWhatsAppSuccess } from './entity/whatsapp.entity';

@Resolver(() => SendWhatsAppSuccess)
@UseGuards(CustomAuthGuard, CRMGuard)
export class WhatsAppResolver {
  constructor(
    private readonly whatsAppService: WhatsAppService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: <PERSON>rror<PERSON>and<PERSON>,
  ) {}

  @Mutation(() => SendWhatsAppSuccess, { name: 'sendWhatsAppMessage' })
  async sendWhatsAppMessage(
    @Args('sendWhatsAppMessage') sendWhatsAppMessage: SendWhatsAppMessageInput,
  ) {
    try {
      await this.whatsAppService.sendWhatsappMessage(sendWhatsAppMessage);
      return this.successHandler.getSuccessResponse({
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to send whatsapp message',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
