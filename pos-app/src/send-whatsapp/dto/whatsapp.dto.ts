import { Field, InputType } from '@nestjs/graphql';

@InputType()
class DataObject {
  @Field(() => String, { description: 'Order Tracking link', nullable: true })
  trackURL: string;

  @Field(() => String, { description: 'Order Tracking link', nullable: true })
  tracklink: string;

  @Field(() => String, { description: 'Order ID', nullable: true })
  order_id: string;

  @Field(() => String, { description: 'Product Name', nullable: true })
  productName: string;
}

@InputType()
export class SendWhatsAppMessageInput {
  @Field(() => String, { description: 'Phone number', nullable: false })
  phone: string;

  @Field(() => String, { description: 'Message Type', nullable: false })
  type: string;

  @Field(() => DataObject, { description: 'Data', nullable: true })
  data: DataObject;

  @Field(() => [String], { description: 'Data', nullable: true })
  scope: [string];
}
