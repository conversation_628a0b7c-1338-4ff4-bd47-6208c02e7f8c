import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { SendWhatsAppMessageInput } from './dto/whatsapp.dto';
import { sendMessageOnWhatsApp } from 'src/common/helper/send-whatapp-message';
import { sendSms } from 'src/common/helper/send-sms';

const smsTemplates = [
  'AiMattressRecommendationsCustomer',
  'InternalOrderTrackingLink',
];
const whatsAppToSMSMapper = {
  InternalOrderTrackingLink: 'CustomerTracking',
};

@Injectable()
export class WhatsAppService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async sendWhatsappMessage(sendWhatsAppMessage: SendWhatsAppMessageInput) {
    try {
      const { phone, type, data, scope } = sendWhatsAppMessage;

      const payload: any = {
        phoneNo: phone,
        templateName: type,
        ...data,
      };

      console.log(payload, '::::: payload');
      if (smsTemplates.includes(type) && scope.includes('sms')) {
        try {
          const res = await sendSms({
            ...payload,
            templateName: whatsAppToSMSMapper[type] || type,
          });
          console.log(res, '::::: res sms');
        } catch (error) {
          console.error('Error sending recommendation to customer:', error);
        }
      }

      if (scope.includes('whatsapp')) {
        const res = await sendMessageOnWhatsApp(payload);
        console.log(res, ':::::: sendWhatsappMessage');
      }

      return true;
    } catch (error) {
      console.log(error, ':::::: error');
      throw error;
    }
  }
}
