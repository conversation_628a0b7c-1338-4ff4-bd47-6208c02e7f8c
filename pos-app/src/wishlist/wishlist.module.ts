import { Modu<PERSON> } from '@nestjs/common';
import { WishlistService } from './wishlist.service';
import { WishlistResolver } from './wishlist.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { ShopifyModule } from 'src/common/shopify/shopify.module';

@Module({
  imports: [
    DocumentClientModule,
    SsmClientModule,
    ConfigParametersModule,
    ShopifyModule,
  ],
  providers: [WishlistResolver, WishlistService],
})
export class WishlistModule {}
