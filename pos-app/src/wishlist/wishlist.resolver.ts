import { Args, Resolver, Query } from '@nestjs/graphql';
import { GetWishlistInput } from './dto/get-wishlist.input';
import { Wishlist } from './entities/wishlist.entity';
import { WishlistService } from './wishlist.service';
import { UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CRMGuard } from 'src/auth/roles.guard';

@Resolver(() => Wishlist)
@UseGuards(CustomAuthGuard, CRMGuard)
export class WishlistResolver {
  constructor(private readonly wishlistService: WishlistService) {}

  @Query(() => Wishlist)
  getWishlist(
    @Args('getWishlistInput')
    getWishlistInput: GetWishlistInput,
  ) {
    return this.wishlistService.getWishlist(getWishlistInput);
  }
}
