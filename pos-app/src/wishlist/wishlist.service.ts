import { Injectable } from '@nestjs/common';
import { GetWishList } from './lib/get-wishlist';
import { GetWishlistInput } from './dto/get-wishlist.input';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';

@Injectable()
export class WishlistService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}

  async getWishlist(getWishlistInput: GetWishlistInput) {
    const getWishListHandler = new GetWishList(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
      this.shopifyClient,
    );
    return getWishListHandler.getWishList(getWishlistInput);
  }
}
