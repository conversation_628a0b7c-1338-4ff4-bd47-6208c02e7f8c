import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { ShopifyWishList } from 'src/common/helper/get-wishlist-api';
import { posLogger } from 'src/common/logger';
import { AppShopify } from 'src/common/shopify/shopify';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetProduct } from 'src/products/lib/get-product';
import { GetVariant } from 'src/products/lib/get-variant';

export class GetWishList {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}

  async getWishList(getUpdatedWishlistInput) {
    posLogger.info('wishlist', 'getWishList', {
      input: getUpdatedWishlistInput,
    });
    try {
      const { customerId } = getUpdatedWishlistInput;
      const wishListHandler = new ShopifyWishList(
        this.configService,
        this.ssmClient,
        this.shopifyClient,
      );
      const wishListData =
        await wishListHandler.fetchWishlistFromShopify(customerId);

      if (!!wishListData.length) {
        const enhancedWishlistData = await Promise.all(
          wishListData.map(async (item) => {
            const getProductHandler = new GetProduct(
              this.configService,
              this.docClient,
              this.ssmClient,
              this.configParameters,
            );
            try {
              const product = await getProductHandler.getProductById(
                item.productId,
              );

              let variant = null;
              if (item.variantId) {
                const getVariantHandler = new GetVariant(
                  this.configService,
                  this.docClient,
                  this.ssmClient,
                  this.configParameters,
                );
                variant = await getVariantHandler.getVariantById(
                  item.variantId,
                  item.productId,
                );
              }

              if (product.status === 'active' && !!product.published_at) {
                return { ...item, product, variant };
              }
              return { ...item };
            } catch (err) {
              posLogger.error('WishList helper', 'getWishList', err);
            }
          }),
        );

        return { data: enhancedWishlistData.filter(Boolean) };
      }
      return { data: [] };
    } catch (e) {
      posLogger.error('wishlist', 'getWishList', { error: e });
      throw e;
    }
  }
}
