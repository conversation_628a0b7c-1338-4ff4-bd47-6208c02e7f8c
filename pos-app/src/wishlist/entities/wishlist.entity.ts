import { ObjectType, Field } from '@nestjs/graphql';
import { ProductData } from 'src/products/entities/product.entity';
import { VariantData } from 'src/products/entities/variant.entity';

@ObjectType()
export class WishlistData {
  @Field(() => String, { nullable: true })
  productId?: string;

  @Field(() => String, { nullable: true })
  productUrl?: string;

  @Field(() => String, { nullable: true })
  productType?: string;

  @Field(() => String, { nullable: true })
  productAvailability?: string;

  @Field(() => String, { nullable: true })
  createdAt?: string;

  @Field(() => String, { nullable: true })
  variantId?: string;

  @Field(() => ProductData, { nullable: true })
  product?: ProductData;

  @Field(() => VariantData, { nullable: true })
  variant?: VariantData;
}

@ObjectType()
export class Wishlist {
  @Field(() => [WishlistData], { nullable: true })
  data?: WishlistData[];
}
