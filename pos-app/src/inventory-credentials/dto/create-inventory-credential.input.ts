import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CreateInventoryCredentialInput {
  @Field(() => String, { description: 'Unique identifier for the credential' })
  id: string;

  @Field(() => String, { description: 'Location key for the inventory system' })
  locationKey: string;

  @Field(() => String, { description: 'Secret key for the inventory system' })
  secretKey: string;

  @Field(() => String, { description: 'Secret key for the inventory system' })
  state: string;
}
