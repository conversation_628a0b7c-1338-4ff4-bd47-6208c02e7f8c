import { Module } from '@nestjs/common';
import { InventoryCredentialsService } from './inventory-credentials.service';
import { InventoryCredentialsResolver } from './inventory-credentials.resolver';
import { DocumentClientModule } from 'src/common/document-client/document-client.module';
import { SsmClientModule } from 'src/common/ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';

@Module({
  imports: [DocumentClientModule, SsmClientModule, ConfigParametersModule],
  providers: [
    InventoryCredentialsResolver,
    InventoryCredentialsService,
    SuccessHandler,
    ErrorHandler,
  ],
})
export class InventoryCredentialsModule {}
