import { Resolver, Mutation, Query, Args } from '@nestjs/graphql';
import { InventoryCredentialsService } from './inventory-credentials.service';
import { CreateInventoryCredentialInput } from './dto/create-inventory-credential.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus } from '@nestjs/common';
import {
  InventoryCredentials,
  InventoryCredentialsData,
} from './entities/inventory-credential.entity';

@Resolver(() => InventoryCredentialsData)
export class InventoryCredentialsResolver {
  constructor(
    private readonly inventoryService: InventoryCredentialsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => InventoryCredentials)
  async createInventoryCredential(
    @Args('createInventoryCredentialInput')
    createInventoryCredentialInput: CreateInventoryCredentialInput,
  ) {
    try {
      const data = await this.inventoryService.createInventoryCredential(
        createInventoryCredentialInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create inventory credential',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => InventoryCredentials)
  async getInventoryCredentialById(@Args('id') id: string) {
    try {
      const data = await this.inventoryService.getInventoryCredentialById(id);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to retrieve inventory credential',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  

}
