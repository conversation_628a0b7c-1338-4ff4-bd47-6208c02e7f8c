import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateInventoryCredentialInput } from '../dto/create-inventory-credential.input';
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { InventoryCredentialsData } from '../entities/inventory-credential.entity';

export class CreateInventoryCredential {
  constructor(
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private ssmClient: AppSsmClient,
  ) {}

  async createInventoryCredential(
    input: CreateInventoryCredentialInput,
  ): Promise<InventoryCredentialsData> {
    console.log('input :>> ', input);

    const INVENTORY_CREDENTIALS_TABLE_NAME =
      await this.configParameters.getInventoryCredentialsTableName();
    console.log(
      'INVENTORY_CREDENTIALS_TABLE_NAME :>> ',
      INVENTORY_CREDENTIALS_TABLE_NAME,
    );

    const command = new PutCommand({
      TableName: INVENTORY_CREDENTIALS_TABLE_NAME,
      Item: input,
      ConditionExpression: 'attribute_not_exists(id)',
    });

    await this.docClient.createItem(command);
    return input;
  }
}
