// src/inventory-credentials/get-inventory-credential.ts

import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { InventoryCredentialsData } from '../entities/inventory-credential.entity';
import { posLogger } from 'src/common/logger';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
const { ScanCommand } = require('@aws-sdk/lib-dynamodb');
export class GetInventoryCredential {
  constructor(
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getInventoryCredential(id: string): Promise<InventoryCredentialsData> {
    const INVENTORY_CREDENTIALS_TABLE_NAME =
      await this.configParameters.getInventoryCredentialsTableName();

    const command = new GetCommand({
      TableName: INVENTORY_CREDENTIALS_TABLE_NAME,
      Key: { id },
    });

    const result = await this.docClient.getItem(command);
    posLogger.info('Inventory', 'GetInventoryCredential', { result });

    if (!result.Item) {
      posLogger.error('Inventory', 'GetInventoryCredential', { result });
      throw new CustomError(
        `Inventory Credential with ID ${id} not found`,
        400,
      );
    }

    return result.Item as InventoryCredentialsData;
  }

  
  async getInventoryCredentialBySecretKey(secretKey: string): Promise<InventoryCredentialsData> {
    const INVENTORY_CREDENTIALS_TABLE_NAME =
      await this.configParameters.getInventoryCredentialsTableName();
  
    // Use ScanCommand to find the item by secretKey
  
    
    const command = new ScanCommand({
      TableName: INVENTORY_CREDENTIALS_TABLE_NAME,
      FilterExpression: 'secretKey = :secretKey',
      ExpressionAttributeValues: {
        ':secretKey': secretKey,
      },
    });
  
    const result = await this.docClient.scanItems(command);
    posLogger.info('Inventory', 'GetInventoryCredentialBySecretKey', { 
      itemCount: result.Items?.length 
    });
  
    if (!result.Items || result.Items.length === 0) {
      posLogger.error('Inventory', 'GetInventoryCredentialBySecretKey', { 
        error: 'No items found' 
      });
    
    }
  
    // Return the first matching item
    return result?.Items[0] as InventoryCredentialsData;
  }
}
