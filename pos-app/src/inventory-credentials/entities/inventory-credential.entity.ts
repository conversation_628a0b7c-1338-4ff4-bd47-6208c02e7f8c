import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class InventoryCredentialsData {
  @Field(() => String, { description: 'Unique identifier for the credential' })
  id: string;

  @Field(() => String, { description: 'Location key for the inventory system' })
  locationKey: string;

  @Field(() => String, { description: 'Secret key for the inventory system' })
  secretKey: string;

  @Field(() => String, { description: 'State for the inventory system' })
  state: string;
}
@ObjectType()
export class InventoryCredentials {
  @Field(() => InventoryCredentialsData)
  data: InventoryCredentialsData;
  @Field(() => String, { nullable: true })
  message: string;
  @Field(() => Number, { nullable: true })
  status: number;
  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
