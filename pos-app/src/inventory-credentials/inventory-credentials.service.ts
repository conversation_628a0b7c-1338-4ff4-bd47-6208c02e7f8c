import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateInventoryCredentialInput } from './dto/create-inventory-credential.input';
import { CreateInventoryCredential } from './lib/create-inventory-credential';
import { InventoryCredentialsData } from './entities/inventory-credential.entity';
import { GetInventoryCredential } from './lib/get-inventory-credential';

@Injectable()
export class InventoryCredentialsService {
  private createHandler: CreateInventoryCredential;
  private getHandler: GetInventoryCredential;

  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private ssmClient: AppSsmClient,
  ) {
    this.createHandler = new CreateInventoryCredential(
      this.docClient,
      this.configParameters,
      this.ssmClient,
    );
    this.getHandler = new GetInventoryCredential(
      this.docClient,
      this.configParameters,
    );
  }

  async createInventoryCredential(
    input: CreateInventoryCredentialInput,
  ): Promise<InventoryCredentialsData> {
    return this.createHandler.createInventoryCredential(input);
  }

  async getInventoryCredentialById(
    id: string,
  ): Promise<InventoryCredentialsData> {
    return this.getHandler.getInventoryCredential(id);
  }

  async getInventoryCredentialBySecretKey(
    secretKey: string,
  ): Promise<InventoryCredentialsData> {
    return this.getHandler.getInventoryCredentialBySecretKey(secretKey);
  }
}
