import { ConfigService } from '@nestjs/config';
import { Injectable } from '@nestjs/common';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { posLogger } from '../logger';
import { OrderProductData } from '../../orders/entities/order.entity';
import { CustomError } from '../response/errorHandler/error.handler';
import { generateSHA512Hash } from '../helper/hash-generator';

@Injectable()
export class AppSnapmint {
  private accessToken;
  private appURL;
  private appRequestURL;
  private snapmintMerchantId;
  private successUrl;
  private failureUrl;
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {}

  async init() {
    if (!this.appURL) {
      const {
        Parameter: { Value: appURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/snapmint/app-url`,
          WithDecryption: true,
        }),
      );
      this.appURL = appURL;
    }
    if (!this.appRequestURL) {
      const {
        Parameter: { Value: appRequestURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/snapmint/request-app-url`,
          WithDecryption: true,
        }),
      );
      this.appRequestURL = appRequestURL;
    }
    if (!this.successUrl) {
      const {
        Parameter: { Value: SnapmintSuccessUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/snapmint/success-edge-url`,
          WithDecryption: true,
        }),
      );
      this.successUrl = SnapmintSuccessUrl;
    }
    if (!this.failureUrl) {
      const {
        Parameter: { Value: SnapmintFailureUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/snapmint/failure-url`,
          WithDecryption: true,
        }),
      );
      this.failureUrl = SnapmintFailureUrl;
    }
  }

  async fetchTransactionStatus(transactionId: string, accessToken: string) {
    posLogger.info('AppSnapmint', 'fetchTransactionStatus', {
      input: { transactionId },
    });
    try {
      await this.init();
      const response = await fetch(
        `${this.appURL}/v1/merchants/orders/order_status?order_id=${transactionId}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          method: 'GET',
        },
      ).then(async (response) => {
        return response.json();
      });
      return response;
    } catch (e) {
      posLogger.error('AppSnapmint', 'fetchTransactionStatus', {
        error: { e },
      });
      throw e;
    }
  }
  async createSnapmintPaymentLink(
    order,
    transactionId,
    transactionAmount,
    phone,
    snapmintData,
  ) {
    await this.init();
    try {
      const fullName = `${order?.customer?.firstName} ${order?.customer?.lastName}`;

      const text = `${snapmintData?.merchantKey}|${transactionId}|${transactionAmount}|${fullName}|${order.customer.email}|${snapmintData?.accessToken}`;

      const checksum = generateSHA512Hash(text);

      const orderProducts = order?.orderProducts?.map(
        (product: OrderProductData) => {
          return {
            sku: product.sku,
            name: product.title,
            quantity: product.quantity,
            unit_price: product.price,
          };
        },
      );
      const encodedQuery = btoa(
        JSON.stringify({
          id: order?.id,
          isFirstTimeVisit: order?.isFirstTimeVisit,
        }),
      );

      const paymentLinkRequest = {
        merchant_id: `${snapmintData?.merchantId}`,
        token: `${snapmintData?.accessToken}`,
        merchant_confirmation_url: `${this.successUrl}?q=${encodedQuery}`,
        merchant_failure_url: `${this.failureUrl}`,
        checksum_hash: checksum,
        order_id: transactionId,
        order_value: transactionAmount,
        first_name: order?.customer?.firstName,
        last_name: order?.customer?.lastName,
        full_name: fullName,
        email: order?.customer?.email,
        mobile: phone,
        products: orderProducts,
        billing_first_name: order?.customer?.firstName,
        billing_last_name: order?.customer?.lastName,
        billing_address_line1: order?.billingAddress?.line1,
        billing_city: order?.billingAddress?.city,
        billing_zip: order?.billingAddress?.pinCode,
        shipping_first_name: order?.customer?.firstName,
        shipping_last_name: order?.customer?.lastName,
        shipping_address_line1: order?.shippingAddress?.line1,
        shipping_city: order?.shippingAddress?.city,
        shipping_zip: order?.shippingAddress?.pinCode,
      };
      posLogger.info('Snapmint', 'SnapmintPaymentLink', {
        msg: 'Snapmint create payment link request payload',
        payload: paymentLinkRequest,
      });

      const linkResult = await fetch(
        `${this.appRequestURL}/api/v1/public/s2s_online_checkout`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          method: 'POST',
          body: JSON.stringify({
            ...paymentLinkRequest,
          }),
        },
      ).then((res) => res.json());

      posLogger.info('Snapmint', 'SnapmintPaymentLink', {
        msg: 'Snapmint pay create payment response',
        response: linkResult,
      });

      if (!linkResult) throw new Error('Snapmint request is failed.');

      if (linkResult.status === 'Error') {
        throw new CustomError(linkResult.message, 400);
      }

      return linkResult;
    } catch (e) {
      posLogger.error('Snapmint', 'SnapmintPaymentLink', {
        error: e,
      });
      throw e;
    }
  }
}
