<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotation</title>
    <style>
        body {
            /* background: #fff; */
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f7f7f7;
            zoom:0.80;
            color: #1D2F5A;
        }
        

        .container {
            width: 90%;
            margin: 20px auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }


        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .header img {
            height: 50px;
        }

        .header div {
            text-align: right;
        }

        .quotation-info {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
            border-top: 2px solid black;
            padding-top: 20px;
        }

        .quotation-info>div {
            background: #fff;
            padding: 10px;
            border-radius: 10px;
            border: 1px solid rgb(145, 136, 136);
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
        }

        .seller-info {
            display: grid;
            grid-template-columns: 0.4fr 2fr;
            gap: 10px;
        }
        .seller-info .company-name{
            font-size: small;
            font-weight: 510;
            grid-column: span 2;
        }

        .seller-info div {
            margin-bottom: 5px;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-bottom: 20px;
            border-radius: 10px;
            border: 1px solid rgb(145, 136, 136);
            overflow: hidden;
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
        }

        tbody tr {
            font-weight: 450;
        
            font-size: small;
        }

        table th,
        table td {
            border: none;
        }

        th,
        td {
            padding: 10px;
            text-align: left;
        }

        th {
            font-size: small;
            background-color: #F1F3F9;
        }

        tbody tr:not(:last-child) {
            border-bottom: 1px solid #ddd;
        }

        .total-container {
            display: flex;
            justify-content: flex-end;
        }

        .total {
            width: 30%;
            background: #fff;
            padding: 10px;
            border-radius: 10px;
            border: 1px solid rgb(145, 136, 136);
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
            margin-top: 20px;
        }

        .total div {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .total div p {
            margin: 0;
        }

        .total hr {
            border: none;
            border-top: 1px solid #ddd;
            margin: 5px 0;
        }

        .item-key {
            font-size: small;
            font-weight: 510;
        }

        .item-val {
            font-weight: normal;
            /* color: #605e5e; */
        }
       .quotation-for .item-val{
font-size: small;
       }

        .seller-info .item-val {
            font-size: small;
        }
        .seller-info .item-key {
            font-size: small;
        }

        .key-value-pair {
            gap: 10px;
            font-size: small;
            display: flex;
        }
        .tnc .item-val {
            font-size: small;
            
        }
        .item-container {
            font-size: small;
    display: flex;
    flex-wrap: wrap;
}

.item-row {
    display: flex;
    margin-right: 20px; /* Adjust spacing between key and value */
    margin-bottom: 10px; /* Adjust vertical spacing between rows */
}
        .queries {
    background-color: #f1f1f1; /* Light grey background */
    text-align: center; /* Center aligned text */
    padding: 10px; /* Padding for space around the text */
    font-size: medium; /* Adjust font size as needed */
    margin-top: 40px; /* Space above the queries section */
    border-radius: 8px; /* Optional: rounded corners */
}
.blue-text {
    color: blue; /* Blue text color */
    text-decoration: underline; /* Adds underline */

}
.title{

    font-weight: bold;
}
.key-value-pair .item-key{
    font-weight: 550;

}
.key-value-pair .item-val{
    font-weight: 400;
}

    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <img src="<%= quotation.logo %>" alt="<%= quotation.companyName %>">
            <div class="title" style="font-size: 20px;">Quotation</div>
            <div>
                <div class="key-value-pair">
                    <span class="item-key">Quotation Date:</span>
                    <span class="item-val"><%= quotation.invoiceDate %></span>
                </div>
                <div class="key-value-pair" style="margin-top: 2px;">
                    <span class="item-key">Quotation No:</span>
                    <span class="item-val"><%= quotation.invoiceNumber %></span>
                </div>
            </div>
        </div>
        <div class="quotation-info">
            <div>
                <h3>Seller</h3>
                <div class="seller-info">
        
                    <div class="company-name"><%= quotation.companyName %></div>
                    <div class="item-key">Office:</div>
                    <div class="item-val"><%= quotation.companyAddress %></div>
                    <div class="item-key">Account:</div>
                    <div class="item-val">************ / ICIC0001206 / COMFORT GRID TECHNOLOGIES PRIVATE LIMITED</div>
                    <div class="item-key">Email:</div>
                    <div class="item-val"><EMAIL></div>
                </div>


                    <div class="item-container" >
                        <div class="item-row" style="margin-top: 20px;" >
                            <div style="width: 300px;">
                                <span  class="item-key">CIN:</span>
                                <span class="item-val"><%= quotation.cin %></span>
                            </div>
                            <div >

                                <span class="item-key">Store ID:</span>
                                <span class="item-val"><%= quotation.storeId %></span>
                            </div>
                
                        </div>
                        <div class="item-row" style="margin-top: 10px;">
                            <div style="width: 300px;">
                                <span class="item-key">GST:</span>
                                <span class="item-val"><%= quotation.gst %></span>
            
                            </div>
                            <div>
                                <span class="item-key">PAN :</span>
                                <span class="item-val"><%= quotation.pan %></span>
                            </div>
                        
                        
                        </div>
                    </div>

                    
            </div>
            <div>
                <h3 class="quotation-for">Quotation For</h3>
                <% if (quotation.customerName) { %>
                    <p>
                      <span class="item-key">Name:</span>
                      <span style="font-size: small;" class="item-val"><%= quotation.customerName %></span>
                    </p>
                  <% } %>
                <p><span class="item-key">Ph No:</span> <span style="font-size: small;" class="item-val"><%= quotation.customerNumber %></span></p>
                <!-- Add more customer details as needed -->
            </div>
        </div>
        <table>
            <thead>
                <tr>
                    <th>Sr No</th>
                    <th>Item Name</th>
                    <th>SKU</th>
                    <% if (quotation.items[0].mrp !== 0) { %>
                        <th>MRP</th>
                        <th>Item Discount</th>
                    <% } %>
                    <th>Selling Price</th>
                    <th>Additional Discount</th>
                    <th>Total Discount</th>
                    <th>Quantity</th>
                    <th>Status</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <% quotation.items.forEach((item, index) => { %>
                    <tr>
                        <td><%= index + 1 %></td> <!-- Auto-generated Sr No starting from 1 -->
                        <td><%= item.name %></td>
                        <td><%= item.sku %></td>
                        <% if (quotation.items[0].mrp !== 0) { %>
                            <td><%= item.mrp %></td>
                            <td><%= item.mrpDiscount %></td>
                        <% } %>
                        <td><%= item.itemPrice %></td>
                        <td><%= item.discount %></td>
                        <td><%= item.itemTotalDiscount %></td>
                        <td><%= item.quantity %></td>
                        <td></td>
                        <td><%= item.total %></td>
                    </tr>
                <% }); %>
                <% if (quotation.type === 'BNPL') { %>
                    <tr>
                        <td></td>
                        <!-- <td colspan="<%= (quotation.items[0].mrp !== 0 ? 8 : 6) %>" style="text-align: left;">Booking Amount</td> -->
                        <td>Booking Amount</td>
                        <td><%= quotation.bnplProduct.sku %></td>
                        <% if (quotation.items[0].mrp !== 0) { %>
                            <td><%= quotation.bnplProduct.price %></td>
                            <td>0</td>
                        <% } %>
                        <td><%= quotation.bnplProduct.price %></td>
                        <td>0</td>
                        <td>0</td>
                        <td><%= quotation.bnplProduct.quantity %></td>
                        <td style="text-align: left;"><%= quotation.bookingAmountStatus %></td>
                        <td><%= quotation.bookingAmount %></td>
                    </tr>
                <% } %>                
            </tbody>
        </table>
        
        
        <div class="total-container">
            <div class="total">
                <div>
                    <p style="text-align: left;">Total:</p>
                    <p style="text-align: right;">₹<%= quotation.totalAmountTaxable %></p>
                </div>
                <hr>
                <div>
                    <p style="text-align: left;">Total Discount:</p>
                    <p style="text-align: right;">₹<%= quotation.totalDiscount %></p>
                </div>
                <% if (quotation.deliveryCharge > 0) { %>
                    <hr>
                    <div>
                        <p style="text-align: left;">Delivery Charge:</p>
                        <p style="text-align: right;">₹<%= quotation.deliveryCharge %></p>
                    </div>
                <% } %>
                <hr>
                <div>
                    <p style="text-align: left;">Quotation Value:</p>
                    <p style="text-align: right;">₹<%= quotation.payableAmount %></p>
                </div>
            </div>
        </div>
        <hr style="margin-top: 20px;">
        <div class="tnc">
            <p class="title" style="font-size: medium;">TERMS OF QUOTATION</p>
            <p class="item-val">1. I/We agree that the prices stated herein is/are tentative and will be charged as
                ruling at the time of order placement.</p>
            <p class="item-val">2. I/We agree to the details of the product mentioned overleaf.</p>
            <% if (quotation.type === 'BNPL') { %>
                <p class="item-val">3. The booking amount remains valid for one year from the booking date and is strictly non-refundable.</p>
                <p class="item-val">4. This amount cannot be adjusted against the final purchase price of the product.</p>
                <p class="item-val">5. If the customer decides to switch to a different product, the booking amount will not be refunded. However, they may change the size or variant of the originally booked product while retaining the locked price.</p>
                <p class="item-val">6. If the purchase is not completed within the validity period, the booking amount will be forfeited.</p>
            <% } %>
            <p class="item-val">&nbsp;&nbsp;&nbsp;Note : Invoice can be generated from the State other than the State where Order was taken as per the availability of Stock</p>
        </div>
        <div class="queries">
            For Any Queries, reach out at <span class="blue-text"><%= quotation.storeNumber %></span> / <span class="blue-text"><%= quotation.storeEmail %></span>
        </div>

    </div>
</body>

</html>
