import ejs from 'ejs';
import fs from 'fs';
import nodemailer from 'nodemailer';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from '../document-client/document-client';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { StoreData } from 'src/stores/entities/store.entity';
import moment from 'moment';
import { S3Interactions } from '../helper/interaction-to-s3';
import path from 'path';
import { TemplateType } from '../enum/template-type';
import { posLogger } from '../logger';
import { CustomError } from '../response/errorHandler/error.handler';
import { AppS3Client } from '../s3-client/s3-client';
import puppeteer from 'puppeteer';
import { AppConfigParameters } from 'src/config/config';
import { DeliveryType } from '../enum/delivery';
import { ToWords } from 'to-words';
import { ReplacementType } from '../enum/replacement';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import QRCode from 'qrcode';
import { sendMessageOnWhatsApp } from '../helper/send-whatapp-message';
import { getHighestGST, splitDeliveryCharges } from '../helper/gst-helper';
import { QuotationType } from '../enum/quotations';
import { SubOrderType } from '../enum/order';

export class PdfService {
  private data: any;
  private templatePath: string;
  private outputPath: string;

  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
    public order?,
    public store?: StoreData,
  ) {
    this.templatePath = __dirname + '/receipt.ejs';
    this.outputPath = __dirname + `/${this.order?.customerId || ''}.pdf`;
  }

  async generateQRCode(
    signedInvoice: string,
    outputFilePath: string,
  ): Promise<string> {
    if (!signedInvoice) {
      throw new CustomError(
        'Signed invoice is required to generate QR code.',
        400,
      );
    }

    try {
      // Convert the signedInvoice to a Buffer
      const dataBuffer = Buffer.from(signedInvoice, 'utf-8');
      const base64Data = dataBuffer.toString('base64');
      console.log('====data buffer is====', dataBuffer);

      // Ensure the output directory exists
      const dir = path.dirname(outputFilePath);
      console.log('====dir is====', dir);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Generate and save QR code as a PNG file
      await QRCode.toFile(
        outputFilePath,
        [{ data: base64Data, mode: 'byte' }],
        {
          errorCorrectionLevel: 'H', // High error correction level
          margin: 1, // Margin around the QR code
          width: 300, // Width of the QR code image
        },
      );

      console.log(`QR code saved successfully at ${outputFilePath}`);
      return outputFilePath;
    } catch (error) {
      console.log('Error generating QR code:', error);
      throw new CustomError('Failed to generate QR code.', 500);
    }
  }

  async renderTemplateToHtml(templateType): Promise<string> {
    try {
      const toWords = new ToWords();
      const imagePath = path.join(__dirname, 'logo.png');
      const signaturePath = path.join(__dirname, 'signature.png');
      const signatureBase64 = fs.readFileSync(signaturePath).toString('base64');
      const imageBase64 = fs.readFileSync(imagePath).toString('base64');

      const {
        customer,
        billingAddress,
        shippingAddress,
        id,
        shopifyOrderName,
        orderProducts,
        transactions,
        storeId,
        shopifyOrderId,
        deliveryCharge = 0,
        subOrderType,
      } = this.order;

      const { billingInfo, didNumber } = this.store || {};
      // let totalAmount = 0;
      let totalGstAmount = 0;
      let totalAmountWithoutTax = 0;
      let totalAmountTaxable = 0;
      let totalTax = 0;
      let finalPayableAmount = 0;
      let receiptTotalAmountWithoutTax = 0;
      let receiptTotalAmountTaxable = 0;
      let receiptTotalTax = 0;
      let totalAmount = 0;
      let receiptFinalPayableAmount = 0;
      let totalDiscount = 0;
      let invoiceItems = null,
        receiptItems = null;

      finalPayableAmount = finalPayableAmount + (deliveryCharge || 0);
      receiptFinalPayableAmount =
        receiptFinalPayableAmount + (deliveryCharge || 0);

      let deliveryChargesTaxableValue = 0;
      let deliveryChargesTax = 0;

      if (deliveryCharge > 0) {
        const result = splitDeliveryCharges(
          deliveryCharge,
          getHighestGST(orderProducts),
        );
        deliveryChargesTaxableValue = result.deliveryChargesTaxableValue;
        deliveryChargesTax = result.deliveryChargesTax;
      }

      //payment done, no gst & cash and carry then only invoice

      if (
        templateType === TemplateType.INVOICE &&
        // (!this.order.gstDetails || !this.order.gstDetails.gstNumber) &&
        this.order.finalDiscountedAmount == transactions?.totalPaidAmount
      ) {
        invoiceItems = orderProducts.filter((item) => {
          return item.deliveryStatus === DeliveryType.CASH_AND_CARRY;
        });

        if (invoiceItems[0]) {
          this.templatePath = __dirname + '/invoice.ejs';
        }
      }

      if (invoiceItems) {
        receiptItems = orderProducts.filter((item) => {
          return !invoiceItems.some(
            (invoiceItem) => invoiceItem.id === item.id,
          );
        });
      } else {
        receiptItems = orderProducts;
      }
      if (templateType === TemplateType.REPLACEMENT_ORDER) {
        this.templatePath = __dirname + '/replacement-order.ejs';
        this.outputPath =
          __dirname + `/${this.order?.customer?.phone || ''}.pdf`;
      }

      this.data = {
        invoice:
          invoiceItems &&
          invoiceItems.length &&
          templateType === TemplateType.INVOICE
            ? {
                cin: 'U24304MH2019PTC333446',
                pan: '**********',
                gst:
                  this.store?.gstDetails?.gstNumber ||
                  'Will be part of Tax Invoice',
                storeId: storeId,
                companyName: 'Comfort Grid Technologies Private Limited',
                companyAddress: `${billingInfo?.address?.line1 || ''} ${billingInfo?.address?.line2 || ''}`,
                companyCity: billingInfo?.address?.city || '',
                companyState: billingInfo?.address?.state || '',
                companyZip: billingInfo?.address?.pinCode || '',
                stateNo: billingInfo?.address?.stateNo || '',
                customerName: customer?.firstName
                  ? `${customer?.firstName || ''} ${customer?.lastName || ''}`
                  : null,
                customerNumber: `${customer?.phone || ''}`,
                customerMail: `${customer?.email || ''}`,
                customerAddress: `${billingAddress?.line1 || ''} ${billingAddress?.line2 || ''}`,
                customerCity: billingAddress?.city || '',
                customerZip: billingAddress?.pinCode || '',
                customerState: billingAddress?.state || '',
                shippingState: shippingAddress?.state || '',
                orderNo: shopifyOrderName || '-',
                invoiceNumber: id || '-',
                shippingAddress: `${shippingAddress?.line1 || ''} ${shippingAddress?.line2 || ''}`,
                shippingCity: shippingAddress?.city || '',
                shippingZip: shippingAddress?.pinCode || '',
                invoiceDate: moment(this.order.createdAt).format('MMM D, YYYY'),
                orderDate: this.order.shopifyCreatedAt
                  ? moment(this.order.shopifyCreatedAt).format('MMM D, YYYY')
                  : '-',
                items: await Promise.all(
                  invoiceItems.map(async (item) => {
                    const gstRate = item.gstPercentage || item.gstRate;
                    totalGstAmount += item.gstAmount;

                    const adjustedCgst = item.gstAmount / 2;
                    const adjustedSgst = item.gstAmount / 2;

                    const amount = item.price * item.quantity - item.gstAmount;

                    // totalAmount += amount;
                    const rate = amount / item.quantity;
                    const itemPrice = Math.round(
                      item.finalItemPriceWithoutGst / item.quantity +
                        item.itemDiscount,
                    );
                    const totalItemValue = Math.round(
                      item.finalItemPriceWithoutGst + item.itemDiscount,
                    );

                    totalAmountWithoutTax += totalItemValue;
                    totalAmountTaxable += Math.round(
                      item.finalItemPriceWithoutGst,
                    );
                    totalTax += Math.round(item.gstAmount);
                    finalPayableAmount += Math.round(item.finalItemPrice);
                    const name =
                      item.customData && item.customData.length
                        ? `${item.title} / ${item.variantTitle} Custom-Size: ${item.customData.length} x ${item.customData.breadth} x ${item.customData.height}`
                        : `${item.title} / ${item.variantTitle}`;

                    return {
                      name,
                      sku: item.sku,
                      hsn: item.hsn,
                      itemPrice,
                      quantity: item.quantity,
                      totalItemValue,
                      ...(templateType === TemplateType.INVOICE && {
                        discount: Math.round(item.itemDiscount),
                      }),
                      taxableValue: Math.round(item.finalItemPriceWithoutGst),
                      rate: Math.round(rate),
                      gstRate,
                      taxAmount: Math.round(item.gstAmount),
                      amount: Math.round(amount),
                      cgst: Math.round(adjustedCgst),
                      sgst: Math.round(adjustedSgst),
                      total: Math.round(item.finalItemPrice),
                    };
                  }),
                ),
                totalAmountWithoutTax: Math.round(totalAmountWithoutTax),
                totalAmountTaxable: Math.round(totalAmountTaxable),
                totalTax: Math.round(totalTax),
                payableAmount: Math.round(Number(finalPayableAmount)),
                deliveryChargesTaxableValue,
                deliveryChargesTax,
                deliveryCharge: Math.round(
                  Number(this.order?.deliveryCharge || 0),
                ),
                totalAmountInWords: toWords.convert(
                  Math.round(Number(finalPayableAmount)),
                ),
                cgst: Math.round(totalGstAmount / 2),
                sgst: Math.round(totalGstAmount / 2),
                reductions: '0',
                ...(templateType === TemplateType.INVOICE && {
                  total: transactions.totalPaidAmount,
                }),
                ...(templateType === TemplateType.QUOTATION && {
                  discounts: Math.round(
                    this.order.totalAmount - this.order.finalDiscountedAmount,
                  ),
                  afterDiscount: this.order.finalDiscountedAmount,
                  expiryDate: moment(this.order.expiresAt).format(
                    'MMM D, YYYY',
                  ),
                }),
                ...(templateType === TemplateType.QUOTATION && {}),
                logo: `data:image/png;base64,${imageBase64}`,
                signature: `data:image/png;base64,${signatureBase64}`,
                storeEmail: this.store.email,
                storeNumber: this.store.phone,
                ...(subOrderType === SubOrderType.LOCK_PRICE && {
                  finalDiscountedAmount: this.order.finalDiscountedAmount,
                  subOrderType: subOrderType,
                  bookingAmountStatus:
                    this.order.finalDiscountedAmount ==
                    transactions?.totalPaidAmount
                      ? 'PAID'
                      : 'PENDING',
                  productFinalAmount: this.order.productFinalAmount,
                  bnplProduct: this.order.bnplProduct,
                }),
              }
            : null,

        receipt:
          receiptItems &&
          receiptItems.length &&
          templateType === TemplateType.INVOICE
            ? {
                modeOfPayment: [
                  ...new Set(
                    this.order.transactions.transactionDetails.map(
                      (transaction) => transaction.mode,
                    ),
                  ),
                ].join(', '),

                cin: 'U24304MH2019PTC333446',
                pan: '**********',
                gst:
                  this.store?.gstDetails?.gstNumber ||
                  'Will be part of Tax Invoice',
                storeId: storeId,
                companyName: 'Comfort Grid Technologies Private Limited',
                companyAddress: `${billingInfo?.address?.line1 || ''} ${billingInfo?.address?.line2 || ''}`,
                companyCity: billingInfo?.address?.city || '',
                companyZip: billingInfo?.address?.pinCode || '',
                customerName: customer?.firstName
                  ? `${customer?.firstName || ''} ${customer?.lastName || ''}`
                  : null,
                customerAddress: `${billingAddress?.line1 || ''} ${billingAddress?.line2 || ''}`,
                customerNumber: `${customer?.phone || ''}`,
                customerMail: `${customer?.email || ''}`,
                customerCity: billingAddress?.city || '',
                customerZip: billingAddress?.pinCode || '',
                customerState: billingAddress?.state || '',
                shippingState: shippingAddress?.state || '',
                shippingAddress: `${shippingAddress?.line1 || ''} ${shippingAddress?.line2 || ''}`,
                shippingCity: shippingAddress?.city || '',
                shippingZip: shippingAddress?.pinCode || '',
                invoiceNumber: shopifyOrderName || id,
                invoiceDate: moment(this.order.createdAt).format('MMM D, YYYY'),
                items: await Promise.all(
                  receiptItems.map(async (item) => {
                    totalGstAmount += item.gstAmount;

                    const adjustedCgst = item.gstAmount / 2;
                    const adjustedSgst = item.gstAmount / 2;
                    const mrp = item.mrp ? Number(item.mrp) : 0;
                    const mrpDiscount = mrp ? mrp - item.price : 0;

                    const amount = item.price * item.quantity - item.gstAmount;
                    totalAmount +=
                      mrp !== 0
                        ? Math.round(mrp * item.quantity)
                        : Math.round(item.price * item.quantity);
                    const rate = amount / item.quantity;
                    const gstRate = item.gstPercentage || item.gstRate;
                    const itemPrice = Math.round(item.price);
                    const totalItemValue = Math.round(
                      item.finalItemPriceWithoutGst + item.itemDiscount,
                    );

                    receiptTotalAmountWithoutTax += totalItemValue;
                    receiptTotalAmountTaxable += Math.round(
                      item.finalItemPriceWithoutGst,
                    );
                    receiptTotalTax += Math.round(item.gstAmount);
                    receiptFinalPayableAmount += Math.round(
                      item.finalItemPrice,
                    );
                    const name =
                      item.customData && item.customData.length
                        ? `${item.title} / ${item.variantTitle} Custom-Size: ${item.customData.length} x ${item.customData.breadth} x ${item.customData.height}`
                        : `${item.title} / ${item.variantTitle}`;

                    return {
                      mrp,
                      mrpDiscount,
                      name,
                      sku: item.sku,
                      hsn: item.hsn,
                      itemPrice,
                      quantity: item.quantity,
                      totalItemValue,
                      ...(templateType === TemplateType.INVOICE && {
                        discount: Math.round(item.itemDiscount),
                      }),
                      discount: Math.round(item.itemDiscount),
                      taxableValue: Math.round(item.finalItemPriceWithoutGst),
                      rate: Math.round(rate),
                      gstRate,

                      taxAmount: Math.round(item.gstAmount),
                      amount: Math.round(amount),
                      cgst: Math.round(adjustedCgst),
                      sgst: Math.round(adjustedSgst),
                      total: Math.round(item.finalItemPrice),
                    };
                  }),
                ),
                totalAmountWithoutTax: Math.round(receiptTotalAmountWithoutTax),
                totalAmountTaxable: Math.round(receiptTotalAmountTaxable),
                totalTax: Math.round(receiptTotalTax),
                payableAmount: Math.round(receiptFinalPayableAmount),
                deliveryChargesTaxableValue,
                deliveryChargesTax,
                deliveryCharge: Math.round(
                  Number(this.order?.deliveryCharge || 0),
                ),
                totalAmount,
                totalAmountInWords: toWords.convert(
                  Math.round(receiptFinalPayableAmount),
                ),
                cgst: Math.round(totalGstAmount / 2),
                sgst: Math.round(totalGstAmount / 2),
                reductions: '0',
                ...(templateType === TemplateType.INVOICE && {
                  total: transactions.totalPaidAmount,
                }),
                ...(templateType === TemplateType.QUOTATION && {
                  discounts: Math.round(
                    this.order.totalAmount - this.order.finalDiscountedAmount,
                  ),
                  afterDiscount: this.order.finalDiscountedAmount,
                  expiryDate: moment(this.order.expiresAt).format(
                    'MMM D, YYYY',
                  ),
                }),
                ...(templateType === TemplateType.QUOTATION && {}),
                totalPaidAmount:
                  invoiceItems && invoiceItems.length
                    ? Math.round(receiptFinalPayableAmount)
                    : transactions.totalPaidAmount,
                remainingAmount: transactions.remainingAmount,
                logo: `data:image/png;base64,${imageBase64}`,
                signature: `data:image/png;base64,${signatureBase64}`,
                storeEmail: this.store.email,
                storeNumber: this.store.phone,
                ...(subOrderType === SubOrderType.LOCK_PRICE && {
                  finalDiscountedAmount: this.order.finalDiscountedAmount,
                  subOrderType: subOrderType,
                  bookingAmountStatus:
                    this.order.finalDiscountedAmount ==
                    transactions?.totalPaidAmount
                      ? 'PAID'
                      : 'PENDING',
                  productFinalAmount: this.order.productFinalAmount,
                  bnplProduct: this.order.bnplProduct,
                }),
              }
            : null,

        quotation:
          templateType === TemplateType.QUOTATION
            ? {
                storeId: storeId,
                cin: 'U24304MH2019PTC333446',
                pan: '**********',
                gst:
                  this.store?.gstDetails?.gstNumber ||
                  'Will be part of Tax Invoice',

                companyName: 'COMFORT GRID TECHNOLOGIES PRIVATE LIMITED',
                companyAddress: `${billingInfo?.address?.line1 || ''} ${billingInfo?.address?.line2 || ''}`,
                companyCity: billingInfo?.address?.city || '',
                companyZip: billingInfo?.address?.pinCode || '',
                customerName: customer?.firstName
                  ? `${customer?.firstName || ''} ${customer?.lastName || ''}`
                  : null,
                customerNumber: customer?.phone || '',
                customerMail: customer?.mail || '',
                customerAddress: `${billingAddress?.line1 || ''} ${billingAddress?.line2 || ''}`,
                customerCity: billingAddress?.city || '',
                customerZip: billingAddress?.pinCode || '',
                customerState: billingAddress?.state || '',
                invoiceNumber: shopifyOrderName || id,

                invoiceDate: moment(this.order?.createdAt).format(
                  'MMM D, YYYY',
                ),
                items: await Promise.all(
                  receiptItems.map(async (item) => {
                    console.log('Inside :>> ', item);
                    const gstRate = item.gstPercentage || item.gstRate;
                    totalGstAmount += item.gstAmount;

                    const amount = item.price * item.quantity;
                    const mrp = item.mrp ? Number(item.mrp) : 0;
                    // totalAmount += amount;
                    const rate = amount / item.quantity;
                    const itemPrice = item.price;
                    const mrpDiscount = mrp ? mrp - item.price : 0;
                    const discount = Math.round(item.itemDiscount) || 0;

                    console.log('itemPrice :>> ', itemPrice);
                    totalAmountWithoutTax += amount;
                    totalAmountTaxable +=
                      mrp !== 0
                        ? Math.round(mrp * item.quantity)
                        : Math.round(amount);
                    totalTax += Math.round(item.gstAmount);
                    finalPayableAmount += Math.round(item.finalItemPrice);
                    const itemTotalDiscount =
                      Math.round(mrpDiscount) * item.quantity +
                      Math.round(discount);
                    totalDiscount += Math.round(itemTotalDiscount);
                    const name =
                      item.customData && item.customData.length
                        ? `${item.title} / ${item.variantTitle} Custom-Size: ${item.customData.length} x ${item.customData.breadth} x ${item.customData.height}`
                        : `${item.title} / ${item.variantTitle}`;

                    return {
                      mrp: mrp || 0,
                      name,
                      sku: item.sku,
                      itemPrice,
                      quantity: item.quantity,
                      totalItemValue: Math.round(amount),
                      mrpDiscount: Math.round(mrpDiscount),
                      discount: Math.round(discount),
                      taxableValue: Math.round(item.finalItemPrice),
                      rate: Math.round(rate),
                      gstRate,
                      taxAmount: Math.round(item.gstAmount),
                      amount: Math.round(amount),
                      total: Math.round(item.finalItemPrice),
                      itemTotalDiscount,
                    };
                  }),
                ),
                totalAmountWithoutTax: Math.round(totalAmountWithoutTax),
                totalAmountTaxable,
                totalTax: Math.round(totalTax),
                totalDiscount,
                payableAmount: Math.round(finalPayableAmount),
                deliveryChargesTaxableValue,
                deliveryChargesTax,
                deliveryCharge: Math.round(
                  Number(this.order?.deliveryCharge || 0),
                ),
                totalAmountInWords: toWords.convert(
                  Math.round(finalPayableAmount),
                ),
                reductions: '0',
                ...(templateType === TemplateType.QUOTATION && {
                  discounts: Math.round(
                    this.order.totalAmount - this.order.finalDiscountedAmount,
                  ),
                  afterDiscount: this.order.finalDiscountedAmount,
                  expiryDate: moment(this.order.expiresAt).format(
                    'MMM D, YYYY',
                  ),
                }),
                ...(templateType === TemplateType.QUOTATION && {}),
                logo: `data:image/png;base64,${imageBase64}`,
                signature: `data:image/png;base64,${signatureBase64}`,
                storeEmail: this.store?.email,
                storeNumber: didNumber || this.store?.phone,
                ...(templateType === TemplateType.QUOTATION &&
                  this.order.type === QuotationType.BNPL && {
                    type: this.order.type,
                    bookingAmount: this.order.bookingAmount,
                    bookingAmountStatus: this.order.bookingAmountStatus,
                    bnplProduct: this.order.bnplProduct,
                  }),
              }
            : null,

        replacementOrder:
          templateType === TemplateType.REPLACEMENT_ORDER
            ? {
                ...this.order,
                cin: 'U24304MH2019PTC333446',
                pan: '**********',
                logo: `data:image/png;base64,${imageBase64}`,
                signature: `data:image/png;base64,${signatureBase64}`,
                companyName: 'Comfort Grid Technologies Private Limited',
                customerName: customer?.firstName
                  ? `${customer?.firstName || ''} ${customer?.lastName || ''}`
                  : null,
                customerNumber: `${customer?.phone || ''}`,
                customerAddress: `${shippingAddress?.line1 || ''} ${shippingAddress?.line2 || ''}`,
                customerCity: shippingAddress?.city || '',
                customerZip: shippingAddress?.pinCode || '',
                customerState: shippingAddress?.state || '',
                invoiceNumber: shopifyOrderId || id,
                invoiceDate: moment(this.order.createdAt).format('MMM D, YYYY'),
                items: await Promise.all(
                  [this.order.replacedProduct]?.map(async (item) => {
                    const name =
                      item.customData && item.customData.length
                        ? `${item.title} /  ${item.customData.length} x ${item.customData.breadth} x ${item.customData.height}`
                        : `${item.title} `;
                    return {
                      ...item,
                      title: name,
                    };
                  }),
                ),
                totalAmount: this.order?.totalAmount || 0,
                shipmentCost: this.order?.isShippingCharged
                  ? this.order?.shippingCost
                  : 0,
                purchasedTitle:
                  this.order?.orderType !== 'RETURN' &&
                  this.order?.replacementType === ReplacementType.FULL
                    ? 'Purchased Products'
                    : 'Part Products',
                customDiscountAmount:
                  (this.order?.customDiscountAmount).toFixed(2) || 0,
                purchasedProducts: await Promise.all(
                  [
                    ...(this.order?.orderProducts || []),
                    ...(this.order?.accessories || []),
                  ]?.map(async (item) => {
                    const name =
                      item.customData && item.customData.length
                        ? `${item.title} / Custom-Size: ${item.customData.length} x ${item.customData.breadth} x ${item.customData.height}`
                        : `${item.title} / ${item.variantTitle}`;
                    return {
                      ...item,
                      title: name,
                    };
                  }),
                ),
                finalDiscountedAmount:
                  this.order?.finalDiscountedAmount.toFixed(2),
                replacedItemTotal:
                  (this.order?.replacedProduct?.price || 0) *
                    (this.order?.replacedProduct?.quantity || 0) -
                    this.order.replacedProduct.itemDiscount ||
                  0 - this.order.replacedProduct.bankDiscount ||
                  0,
                purchasedItemsPrice: [
                  ...(this.order?.orderProducts || []),
                  ...(this.order?.accessories || []),
                ].reduce((total, item) => {
                  const price = item?.price || 0;
                  const quantity = item?.quantity || 0;
                  return total + price * quantity;
                }, 0),
                diffrenceAmount: Math.abs(
                  (this.order?.replacedProduct?.price || 0) *
                    (this.order?.replacedProduct?.quantity || 0) -
                    (this.order?.replacedProduct?.itemDiscount || 0) -
                    (this.order?.replacedProduct?.bankDiscount || 0) -
                    [
                      ...(this.order?.orderProducts || []),
                      ...(this.order?.accessories || []),
                    ].reduce((total, item) => {
                      return total + (item?.price || 0) * (item?.quantity || 0);
                    }, 0),
                ).toFixed(2),
                refundAmount: this.order.refundedPrice,
              }
            : null,
      };

      if (templateType == TemplateType.QUOTATION) {
        this.templatePath = __dirname + '/quotation.ejs';
      }

      console.log(
        'this.templatePath :>> ',
        this.templatePath,
        '`data :>> `',
        this.data,
      );

      return new Promise(async (resolve, reject) => {
        ejs.renderFile(this.templatePath, await this.data, (err, html) => {
          if (err) {
            reject(err);
          } else {
            resolve(html);
          }
        });
      });
    } catch (error) {
      throw new CustomError(error, 500);
    }
  }

  async createNewGSTInvoice(templateType): Promise<string> {
    console.log('=========Inside createNewGSTInvoice=====');
    const toWords = new ToWords();
    const imagePath = path.join(__dirname, 'logo.png');
    const signaturePath = path.join(__dirname, 'signature.png');
    const signatureBase64 = fs.readFileSync(signaturePath).toString('base64');
    const imageBase64 = fs.readFileSync(imagePath).toString('base64');
    const qrpath = path.join(__dirname, `./qr-codes/${this.order.id}.png`);
    const qrBase64 = fs.readFileSync(qrpath).toString('base64');

    const qrCodeUrl = this.order.qrCodeUrl || '';
    console.log('QR Code URL:', qrCodeUrl);

    const {
      customer,
      billingAddress,
      shippingAddress,
      id,
      shopifyOrderName,
      orderProducts,
      transactions,
      storeId,
      shopifyOrderId,
      IRN,
      deliveryCharge = 0,
      subOrderType,
      //  signedInvoice,
    } = this.order;

    const { billingInfo, didNumber } = this.store || {};
    // let totalAmount = 0;
    let totalGstAmount = 0;
    let totalAmountWithoutTax = 0;
    let totalAmountTaxable = 0;
    let totalTax = 0;
    let finalPayableAmount = 0;
    let receiptTotalAmountWithoutTax = 0;
    let receiptTotalAmountTaxable = 0;
    let receiptTotalTax = 0;
    let totalAmount = 0;
    let receiptFinalPayableAmount = 0;
    let totalDiscount = 0;
    let invoiceItems = null,
      receiptItems = null;

    let deliveryChargesTaxableValue = 0;
    let deliveryChargesTax = 0;

    if (deliveryCharge > 0) {
      const result = splitDeliveryCharges(
        deliveryCharge,
        getHighestGST(orderProducts),
      );
      deliveryChargesTaxableValue = result.deliveryChargesTaxableValue;
      deliveryChargesTax = result.deliveryChargesTax;
    }

    finalPayableAmount = finalPayableAmount + (deliveryCharge || 0);
    receiptFinalPayableAmount =
      receiptFinalPayableAmount + (deliveryCharge || 0);

    invoiceItems = orderProducts.filter((item) => {
      return item.deliveryStatus === DeliveryType.CASH_AND_CARRY;
    });

    if (invoiceItems[0]) {
      this.templatePath = __dirname + '/gstInvoice.ejs';
    }

    if (invoiceItems) {
      receiptItems = orderProducts.filter((item) => {
        return !invoiceItems.some((invoiceItem) => invoiceItem.id === item.id);
      });
    } else {
      receiptItems = orderProducts;
    }

    try {
      this.data = {
        invoice:
          invoiceItems &&
          invoiceItems.length &&
          templateType === TemplateType.INVOICE
            ? {
                cin: 'U24304MH2019PTC333446',
                pan: '**********',
                gst:
                  this.store?.gstDetails?.gstNumber ||
                  'Will be part of Tax Invoice',
                storeId: storeId,
                companyName: 'Comfort Grid Technologies Private Limited',
                companyAddress: `${billingInfo?.address?.line1 || ''} ${billingInfo?.address?.line2 || ''}`,
                companyCity: billingInfo?.address?.city || '',
                companyState: billingInfo?.address?.state || '',
                companyZip: billingInfo?.address?.pinCode || '',
                stateNo: billingInfo?.address?.stateNo || '',
                customerName: customer?.firstName
                  ? `${customer?.firstName || ''} ${customer?.lastName || ''}`
                  : null,
                customerNumber: `${customer?.phone || ''}`,
                customerMail: `${customer?.email || ''}`,
                customerAddress: `${billingAddress?.line1 || ''} ${billingAddress?.line2 || ''}`,
                customerCity: billingAddress?.city || '',
                customerZip: billingAddress?.pinCode || '',
                customerState: billingAddress?.state || '',
                shippingState: shippingAddress?.state || '',
                orderNo: shopifyOrderName || '-',
                invoiceNumber: id || '-',
                irn: IRN || '-',
                qrCode: qrCodeUrl || '-',
                shippingAddress: `${shippingAddress?.line1 || ''} ${shippingAddress?.line2 || ''}`,
                shippingCity: shippingAddress?.city || '',
                shippingZip: shippingAddress?.pinCode || '',
                invoiceDate: moment(this.order.createdAt).format('MMM D, YYYY'),
                orderDate: this.order.shopifyCreatedAt
                  ? moment(this.order.shopifyCreatedAt).format('MMM D, YYYY')
                  : '-',
                items: await Promise.all(
                  invoiceItems.map(async (item) => {
                    const gstRate = item.gstPercentage || item.gstRate;
                    totalGstAmount += item.gstAmount;

                    const adjustedCgst = item.gstAmount / 2;
                    const adjustedSgst = item.gstAmount / 2;

                    const amount = item.price * item.quantity - item.gstAmount;

                    // totalAmount += amount;
                    const rate = amount / item.quantity;
                    const itemPrice = Math.round(
                      item.finalItemPriceWithoutGst / item.quantity +
                        item.itemDiscount,
                    );
                    const totalItemValue = Math.round(
                      item.finalItemPriceWithoutGst + item.itemDiscount,
                    );

                    totalAmountWithoutTax += totalItemValue;
                    totalAmountTaxable += Math.round(
                      item.finalItemPriceWithoutGst,
                    );
                    totalTax += Math.round(item.gstAmount);
                    finalPayableAmount += Math.round(item.finalItemPrice);
                    const name =
                      item.customData && item.customData.length
                        ? `${item.title} / ${item.variantTitle} Custom-Size: ${item.customData.length} x ${item.customData.breadth} x ${item.customData.height}`
                        : `${item.title} / ${item.variantTitle}`;

                    return {
                      name,
                      sku: item.sku,
                      hsn: item.hsn,
                      itemPrice,
                      quantity: item.quantity,
                      totalItemValue,
                      ...(templateType === TemplateType.INVOICE && {
                        discount: Math.round(item.itemDiscount),
                      }),
                      taxableValue: Math.round(item.finalItemPriceWithoutGst),
                      rate: Math.round(rate),
                      gstRate,
                      taxAmount: Math.round(item.gstAmount),
                      amount: Math.round(amount),
                      cgst: Math.round(adjustedCgst),
                      sgst: Math.round(adjustedSgst),
                      total: Math.round(item.finalItemPrice),
                    };
                  }),
                ),
                totalAmountWithoutTax: Math.round(totalAmountWithoutTax),
                totalAmountTaxable: Math.round(totalAmountTaxable),
                totalTax: Math.round(totalTax),
                payableAmount: Math.round(Number(finalPayableAmount)),
                deliveryChargesTaxableValue,
                deliveryChargesTax,
                deliveryCharge: Math.round(
                  Number(this.order?.deliveryCharge || 0),
                ),
                totalAmountInWords: toWords.convert(
                  Math.round(Number(finalPayableAmount)),
                ),
                cgst: Math.round(totalGstAmount / 2),
                sgst: Math.round(totalGstAmount / 2),
                reductions: '0',
                ...(templateType === TemplateType.INVOICE && {
                  total: transactions.totalPaidAmount,
                }),
                ...(templateType === TemplateType.QUOTATION && {
                  discounts: Math.round(
                    this.order.totalAmount - this.order.finalDiscountedAmount,
                  ),
                  afterDiscount: this.order.finalDiscountedAmount,
                  expiryDate: moment(this.order.expiresAt).format(
                    'MMM D, YYYY',
                  ),
                }),
                ...(templateType === TemplateType.QUOTATION && {}),
                logo: `data:image/png;base64,${imageBase64}`,
                signature: `data:image/png;base64,${signatureBase64}`,
                qrImage: `data:image/png;base64,${qrBase64}`,
                storeEmail: this.store.email,
                storeNumber: this.store.phone,
                ...(subOrderType === SubOrderType.LOCK_PRICE && {
                  finalDiscountedAmount: this.order.finalDiscountedAmount,
                  subOrderType: subOrderType,
                  bookingAmountStatus:
                    this.order.finalDiscountedAmount ==
                    transactions?.totalPaidAmount
                      ? 'PAID'
                      : 'PENDING',
                  productFinalAmount: this.order.productFinalAmount,
                  bnplProduct: this.order.bnplProduct,
                }),
              }
            : null,

        receipt:
          receiptItems &&
          receiptItems.length &&
          templateType === TemplateType.INVOICE
            ? {
                modeOfPayment: [
                  ...new Set(
                    this.order.transactions.transactionDetails.map(
                      (transaction) => transaction.mode,
                    ),
                  ),
                ].join(', '),

                cin: 'U24304MH2019PTC333446',
                pan: '**********',
                gst:
                  this.store?.gstDetails?.gstNumber ||
                  'Will be part of Tax Invoice',
                storeId: storeId,
                companyName: 'Comfort Grid Technologies Private Limited',
                companyAddress: `${billingInfo?.address?.line1 || ''} ${billingInfo?.address?.line2 || ''}`,
                companyCity: billingInfo?.address?.city || '',
                companyZip: billingInfo?.address?.pinCode || '',
                customerName: customer?.firstName
                  ? `${customer?.firstName || ''} ${customer?.lastName || ''}`
                  : null,
                customerAddress: `${billingAddress?.line1 || ''} ${billingAddress?.line2 || ''}`,
                customerNumber: `${customer?.phone || ''}`,
                customerMail: `${customer?.email || ''}`,
                customerCity: billingAddress?.city || '',
                customerZip: billingAddress?.pinCode || '',
                customerState: billingAddress?.state || '',
                shippingState: shippingAddress?.state || '',
                shippingAddress: `${shippingAddress?.line1 || ''} ${shippingAddress?.line2 || ''}`,
                shippingCity: shippingAddress?.city || '',
                shippingZip: shippingAddress?.pinCode || '',
                invoiceNumber: shopifyOrderName || id,
                invoiceDate: moment(this.order.createdAt).format('MMM D, YYYY'),
                items: await Promise.all(
                  receiptItems.map(async (item) => {
                    totalGstAmount += item.gstAmount;

                    const adjustedCgst = item.gstAmount / 2;
                    const adjustedSgst = item.gstAmount / 2;
                    const mrp = item.mrp ? Number(item.mrp) : 0;
                    const mrpDiscount = mrp ? mrp - item.price : 0;

                    const amount = item.price * item.quantity - item.gstAmount;
                    totalAmount +=
                      mrp !== 0
                        ? Math.round(mrp * item.quantity)
                        : Math.round(item.price * item.quantity);
                    const rate = amount / item.quantity;
                    const gstRate = item.gstPercentage || item.gstRate;
                    const itemPrice = Math.round(item.price);
                    const totalItemValue = Math.round(
                      item.finalItemPriceWithoutGst + item.itemDiscount,
                    );

                    receiptTotalAmountWithoutTax += totalItemValue;
                    receiptTotalAmountTaxable += Math.round(
                      item.finalItemPriceWithoutGst,
                    );
                    receiptTotalTax += Math.round(item.gstAmount);
                    receiptFinalPayableAmount += Math.round(
                      item.finalItemPrice,
                    );
                    const name =
                      item.customData && item.customData.length
                        ? `${item.title} / ${item.variantTitle} Custom-Size: ${item.customData.length} x ${item.customData.breadth} x ${item.customData.height}`
                        : `${item.title} / ${item.variantTitle}`;

                    return {
                      mrp,
                      mrpDiscount,
                      name,
                      sku: item.sku,
                      hsn: item.hsn,
                      itemPrice,
                      quantity: item.quantity,
                      totalItemValue,
                      ...(templateType === TemplateType.INVOICE && {
                        discount: Math.round(item.itemDiscount),
                      }),
                      discount: Math.round(item.itemDiscount),
                      taxableValue: Math.round(item.finalItemPriceWithoutGst),
                      rate: Math.round(rate),
                      gstRate,

                      taxAmount: Math.round(item.gstAmount),
                      amount: Math.round(amount),
                      cgst: Math.round(adjustedCgst),
                      sgst: Math.round(adjustedSgst),
                      total: Math.round(item.finalItemPrice),
                    };
                  }),
                ),
                totalAmountWithoutTax: Math.round(receiptTotalAmountWithoutTax),
                totalAmountTaxable: Math.round(receiptTotalAmountTaxable),
                totalTax: Math.round(receiptTotalTax),
                payableAmount: Math.round(receiptFinalPayableAmount),
                totalAmount,
                totalAmountInWords: toWords.convert(
                  Math.round(receiptFinalPayableAmount),
                ),
                cgst: Math.round(totalGstAmount / 2),
                sgst: Math.round(totalGstAmount / 2),
                reductions: '0',
                ...(templateType === TemplateType.INVOICE && {
                  total: transactions.totalPaidAmount,
                }),
                ...(templateType === TemplateType.QUOTATION && {
                  discounts: Math.round(
                    this.order.totalAmount - this.order.finalDiscountedAmount,
                  ),
                  afterDiscount: this.order.finalDiscountedAmount,
                  expiryDate: moment(this.order.expiresAt).format(
                    'MMM D, YYYY',
                  ),
                }),
                ...(templateType === TemplateType.QUOTATION && {}),
                totalPaidAmount:
                  invoiceItems && invoiceItems.length
                    ? Math.round(receiptFinalPayableAmount)
                    : transactions.totalPaidAmount,
                remainingAmount: transactions.remainingAmount,
                deliveryChargesTaxableValue,
                deliveryChargesTax,
                deliveryCharge: Math.round(
                  Number(this.order?.deliveryCharge || 0),
                ),
                logo: `data:image/png;base64,${imageBase64}`,
                signature: `data:image/png;base64,${signatureBase64}`,
                storeEmail: this.store.email,
                storeNumber: this.store.phone,
                ...(subOrderType === SubOrderType.LOCK_PRICE && {
                  finalDiscountedAmount: this.order.finalDiscountedAmount,
                  subOrderType: subOrderType,
                  bookingAmountStatus:
                    this.order.finalDiscountedAmount ==
                    transactions?.totalPaidAmount
                      ? 'PAID'
                      : 'PENDING',
                  productFinalAmount: this.order.productFinalAmount,
                  bnplProduct: this.order.bnplProduct,
                }),
              }
            : null,

        quotation:
          templateType === TemplateType.QUOTATION
            ? {
                storeId: storeId,
                cin: 'U24304MH2019PTC333446',
                pan: '**********',
                gst:
                  this.store?.gstDetails?.gstNumber ||
                  'Will be part of Tax Invoice',

                companyName: 'COMFORT GRID TECHNOLOGIES PRIVATE LIMITED',
                companyAddress: `${billingInfo?.address?.line1 || ''} ${billingInfo?.address?.line2 || ''}`,
                companyCity: billingInfo?.address?.city || '',
                companyZip: billingInfo?.address?.pinCode || '',
                customerName: customer?.firstName
                  ? `${customer?.firstName || ''} ${customer?.lastName || ''}`
                  : null,
                customerNumber: customer?.phone || '',
                customerMail: customer?.mail || '',
                customerAddress: `${billingAddress?.line1 || ''} ${billingAddress?.line2 || ''}`,
                customerCity: billingAddress?.city || '',
                customerZip: billingAddress?.pinCode || '',
                customerState: billingAddress?.state || '',
                invoiceNumber: shopifyOrderName || id,

                invoiceDate: moment(this.order?.createdAt).format(
                  'MMM D, YYYY',
                ),
                items: await Promise.all(
                  receiptItems.map(async (item) => {
                    console.log('Inside :>> ', item);
                    const gstRate = item.gstPercentage || item.gstRate;
                    totalGstAmount += item.gstAmount;

                    const amount = item.price * item.quantity;
                    const mrp = item.mrp ? Number(item.mrp) : 0;
                    // totalAmount += amount;
                    const rate = amount / item.quantity;
                    const itemPrice = item.price;
                    const mrpDiscount = mrp ? mrp - item.price : 0;
                    const discount = Math.round(item.itemDiscount) || 0;

                    console.log('itemPrice :>> ', itemPrice);
                    totalAmountWithoutTax += amount;
                    totalAmountTaxable +=
                      mrp !== 0
                        ? Math.round(mrp * item.quantity)
                        : Math.round(amount);
                    totalTax += Math.round(item.gstAmount);
                    finalPayableAmount += Math.round(item.finalItemPrice);
                    const itemTotalDiscount =
                      Math.round(mrpDiscount) * item.quantity +
                      Math.round(discount);
                    totalDiscount += Math.round(itemTotalDiscount);
                    const name =
                      item.customData && item.customData.length
                        ? `${item.title} / ${item.variantTitle} Custom-Size: ${item.customData.length} x ${item.customData.breadth} x ${item.customData.height}`
                        : `${item.title} / ${item.variantTitle}`;

                    return {
                      mrp: mrp || 0,
                      name,
                      sku: item.sku,
                      itemPrice,
                      quantity: item.quantity,
                      totalItemValue: Math.round(amount),
                      mrpDiscount: Math.round(mrpDiscount),
                      discount: Math.round(discount),
                      taxableValue: Math.round(item.finalItemPrice),
                      rate: Math.round(rate),
                      gstRate,
                      taxAmount: Math.round(item.gstAmount),
                      amount: Math.round(amount),
                      total: Math.round(item.finalItemPrice),
                      itemTotalDiscount,
                    };
                  }),
                ),
                totalAmountWithoutTax: Math.round(totalAmountWithoutTax),
                totalAmountTaxable,
                totalTax: Math.round(totalTax),
                totalDiscount,
                payableAmount: Math.round(finalPayableAmount),
                deliveryChargesTaxableValue,
                deliveryChargesTax,
                deliveryCharge: Math.round(
                  Number(this.order?.deliveryCharge || 0),
                ),
                totalAmountInWords: toWords.convert(
                  Math.round(finalPayableAmount),
                ),
                reductions: '0',
                ...(templateType === TemplateType.QUOTATION && {
                  discounts: Math.round(
                    this.order.totalAmount - this.order.finalDiscountedAmount,
                  ),
                  afterDiscount: this.order.finalDiscountedAmount,
                  expiryDate: moment(this.order.expiresAt).format(
                    'MMM D, YYYY',
                  ),
                }),
                ...(templateType === TemplateType.QUOTATION && {}),
                logo: `data:image/png;base64,${imageBase64}`,
                signature: `data:image/png;base64,${signatureBase64}`,
                storeEmail: this.store?.email,
                storeNumber: didNumber || this.store?.phone,
                ...(templateType === TemplateType.QUOTATION &&
                  this.order.type === QuotationType.BNPL && {
                    type: this.order.type,
                    bookingAmount: this.order.bookingAmount,
                    bookingAmountStatus: this.order.bookingAmountStatus,
                    bnplProduct: this.order.bnplProduct,
                  }),
              }
            : null,

        replacementOrder:
          templateType === TemplateType.REPLACEMENT_ORDER
            ? {
                ...this.order,
                cin: 'U24304MH2019PTC333446',
                pan: '**********',
                logo: `data:image/png;base64,${imageBase64}`,
                signature: `data:image/png;base64,${signatureBase64}`,
                companyName: 'Comfort Grid Technologies Private Limited',
                customerName: customer?.firstName
                  ? `${customer?.firstName || ''} ${customer?.lastName || ''}`
                  : null,
                customerNumber: `${customer?.phone || ''}`,
                invoiceNumber: shopifyOrderId || id,
                invoiceDate: moment(this.order.createdAt).format('MMM D, YYYY'),
                items: [this.order.replacedProduct],
                totalAmount: this.order?.totalAmount || 0,
                shipmentCost: this.order?.isShippingCharged
                  ? this.order?.shippingCost
                  : 0,
                purchasedTitle:
                  this.order?.orderType !== 'RETURN' &&
                  this.order?.replacementType === ReplacementType.FULL
                    ? 'Purchased Products'
                    : 'Part Products',
                customDiscountAmount:
                  (this.order?.customDiscountAmount).toFixed(2) || 0,
                purchasedProducts: [
                  ...(this.order?.orderProducts || []),
                  ...(this.order?.accessories || []),
                ],
                finalDiscountedAmount:
                  this.order?.finalDiscountedAmount.toFixed(2),
                replacedItemTotal:
                  (this.order?.replacedProduct?.price || 0) *
                    (this.order?.replacedProduct?.quantity || 0) -
                    this.order.replacedProduct.itemDiscount || 0,
                purchasedItemsPrice: [
                  ...(this.order?.orderProducts || []),
                  ...(this.order?.accessories || []),
                ].reduce((total, item) => {
                  const price = item?.price || 0;
                  const quantity = item?.quantity || 0;
                  return total + price * quantity;
                }, 0),
                diffrenceAmount: Math.abs(
                  (this.order?.replacedProduct?.price || 0) *
                    (this.order?.replacedProduct?.quantity || 0) -
                    (this.order?.replacedProduct?.itemDiscount || 0) -
                    [
                      ...(this.order?.orderProducts || []),
                      ...(this.order?.accessories || []),
                    ].reduce((total, item) => {
                      return total + (item?.price || 0) * (item?.quantity || 0);
                    }, 0),
                ).toFixed(2),
                refundAmount: this.order.refundedPrice,
              }
            : null,
      };
    } catch (err) {
      console.log('Error in generating data', err);
    }

    console.log('=========Inside createNewGSTInvoice DATA is=====', this.data);

    console.log(
      '=========Inside createNewGSTInvoice PATH is=====',
      this.templatePath,
    );

    return new Promise(async (resolve, reject) => {
      ejs.renderFile(this.templatePath, await this.data, (err, html) => {
        if (err) {
          reject(err);
        } else {
          resolve(html);
        }
      });
    });
  }

  async convertHtmlToPdf(html: string) {
    try {
      const executablePath = process.env.CHROMIUM_PATH || '/usr/bin/chromium';
      console.log(
        '======convertHtmlToPdf puppteer path is=====',
        executablePath,
      );
      const browser = await puppeteer.launch({
        executablePath: puppeteer.executablePath(),
        args: [
          '--disable-gpu', // Disable GPU acceleration
          '--disable-dev-shm-usage', // Avoid issues with Docker's shared memory
          '--disable-setuid-sandbox', // Disable sandboxing
          '--no-sandbox', // Disable sandboxing
          '--disable-software-rasterizer', // Disable software rasterizer
          '--disable-webgl', // Disable WebGL
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-gpu-sandbox', // Disable GPU sandbox
          '--disable-accelerated-2d-canvas', // Disable accelerated 2D canvas
          '--disable-2d-canvas-clip-aa', // Disable 2D canvas clip anti-aliasing
          '--disable-2d-canvas-image-chromium', // Disable 2D canvas image chromium
          '--disable-accelerated-jpeg-decoding', // Disable accelerated JPEG decoding
          '--disable-accelerated-mjpeg-decode', // Disable accelerated MJPEG decode
          '--disable-accelerated-video-decode', // Disable accelerated video decode
          '--disable-infobars', // Disable infobars
          '--window-size=1280x1696', // Set window size
          '--remote-debugging-port=9222', // Set remote debugging port
          '--no-first-run', // Disable first run
          '--no-default-browser-check', // Disable default browser check
          '--no-pings', // Disable pings
          '--no-zygote', // Disable zygote
          '--disable-site-isolation-trials', // Disable site isolation trials
          '--disable-software-rasterizer', // Disable software rasterizer
          '--disable-gpu-sandbox', // Disable GPU sandbox
          '--disable-dev-shm-usage', // Disable dev shm usage
          '--disable-setuid-sandbox', // Disable setuid sandbox
          '--disable-accelerated-2d-canvas', // Disable accelerated 2D canvas
          '--disable-gpu-process-crash-limit', // Disable GPU process crash limit
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-webgl', // Disable WebGL
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-gpu-sandbox', // Disable GPU sandbox
          '--disable-dev-shm-usage', // Disable dev shm usage
          '--disable-setuid-sandbox', // Disable setuid sandbox
          '--disable-accelerated-2d-canvas', // Disable accelerated 2D canvas
          '--disable-gpu-process-crash-limit', // Disable GPU process crash limit
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-webgl', // Disable WebGL
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-gpu-sandbox', // Disable GPU sandbox
          '--disable-dev-shm-usage', // Disable dev shm usage
          '--disable-setuid-sandbox', // Disable setuid sandbox
          '--disable-accelerated-2d-canvas', // Disable accelerated 2D canvas
          '--disable-gpu-process-crash-limit', // Disable GPU process crash limit
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-webgl', // Disable WebGL
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-gpu-sandbox', // Disable GPU sandbox
          '--disable-dev-shm-usage', // Disable dev shm usage
          '--disable-setuid-sandbox', // Disable setuid sandbox
          '--disable-accelerated-2d-canvas', // Disable accelerated 2D canvas
          '--disable-gpu-process-crash-limit', // Disable GPU process crash limit
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-webgl', // Disable WebGL
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-gpu-sandbox', // Disable GPU sandbox
          '--disable-dev-shm-usage', // Disable dev shm usage
          '--disable-setuid-sandbox', // Disable setuid sandbox
          '--disable-accelerated-2d-canvas', // Disable accelerated 2D canvas
          '--disable-gpu-process-crash-limit', // Disable GPU process crash limit
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-webgl', // Disable WebGL
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-gpu-sandbox', // Disable GPU sandbox
          '--disable-dev-shm-usage', // Disable dev shm usage
          '--disable-setuid-sandbox', // Disable setuid sandbox
          '--disable-accelerated-2d-canvas', // Disable accelerated 2D canvas
          '--disable-gpu-process-crash-limit', // Disable GPU process crash limit
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-webgl', // Disable WebGL
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-gpu-sandbox', // Disable GPU sandbox
          '--disable-dev-shm-usage', // Disable dev shm usage
          '--disable-setuid-sandbox', // Disable setuid sandbox
          '--disable-accelerated-2d-canvas', // Disable accelerated 2D canvas
          '--disable-gpu-process-crash-limit', // Disable GPU process crash limit
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-webgl', // Disable WebGL
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-gpu-sandbox', // Disable GPU sandbox
          '--disable-dev-shm-usage', // Disable dev shm usage
          '--disable-setuid-sandbox', // Disable setuid sandbox
          '--disable-accelerated-2d-canvas', // Disable accelerated 2D canvas
          '--disable-gpu-process-crash-limit', // Disable GPU process crash limit
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-webgl', // Disable WebGL
          '--disable-extensions', // Disable extensions
          '--disable-default-apps', // Disable default apps
          '--disable-sync', // Disable sync
          '--disable-translate', // Disable translate
          '--disable-gpu-sandbox', // Disable GPU sandbox
          '--disable-dev-shm-usage', // Disable dev shm usage
          '--disable-setuid-sandbox',
        ],
        headless: true,
      });

      console.log('browser :>> ');

      const page = await browser.newPage();
      await page.setContent(html, { waitUntil: 'load' });

      const pdfPath = this.outputPath;
      console.log('pdfPath :>> ', pdfPath);

      const fileBuffer = await page.pdf({
        path: pdfPath,
        format: 'a4',
        printBackground: true,
        preferCSSPageSize: true,
      });

      console.log('fileBuffer :>> ', fileBuffer);

      await browser.close();

      posLogger.info(
        'nodemailer',
        'convertHtmlToPdf',
        'PDF generated successfully',
      );
      return fileBuffer;
    } catch (err) {
      posLogger.error('nodemailer', 'convertHtmlToPdf', err);
      // throw new CustomError(err.name, 500);
    }
  }

  async sendEmailWithoutAttachment(to: string, subject: string, text: string) {
    try {
      const transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
          user: '<EMAIL>',
          pass: 'iodk nwfq hmir eese',
        },
      });

      const mailOptions = {
        from: '<EMAIL>',
        to,
        subject,
        html: text,
      };
      return transporter.sendMail(mailOptions);
    } catch (error) {
      console.log('Fail to send mail ::::: ', error);
      throw error;
    }
  }

  async sendEmailWithFileAttachment(
    to: string,
    subject: string,
    text: string,
    fileType: string,
    templateType: string,
    fileExtension: string,
    csvData?,
  ) {
    try {
      const transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
          user: '<EMAIL>',
          pass: 'iodk nwfq hmir eese',
        },
      });

      const attachments =
        templateType === TemplateType.REPORT ||
        templateType === TemplateType.REFUNDS
          ? csvData.map(({ name, content }) => ({
              filename: `${name}.${fileExtension}`,
              content,
              contentType: fileType,
              sizeMB: Buffer.byteLength(content, 'utf-8') / (1024 * 1024),
            }))
          : [
              {
                filename: `${templateType}.${fileExtension}`,
                path: this.outputPath,
                contentType: fileType,
                sizeMB: null,
              },
            ];
      // console.log('attachments', attachments);
      const MAX_ATTACHMENT_SIZE_MB = 25;
      const oversizedAttachments = attachments.filter(
        (attachment) =>
          attachment.sizeMB && attachment.sizeMB > MAX_ATTACHMENT_SIZE_MB,
      );

      if (oversizedAttachments.length > 0) {
        const oversizedNames = oversizedAttachments
          .map((a) => a.filename)
          .join(', ');
        const errorMessage =
          'The export that you requested contains data that is too large and cannot be exported and emailed. Please try selecting a shorter duration for the data export and try again.';

        await transporter.sendMail({
          from: '<EMAIL>',
          to: to,
          subject: subject,
          text: errorMessage,
        });
        posLogger.info(
          'Error',
          'sendEmailWithFileAttachment',
          `Oversized attachments (${oversizedNames}) were not sent.`,
        );
        return;
      }
      /* eslint-disable @typescript-eslint/no-unused-vars */
      const mailOptions = {
        from: '<EMAIL>',
        to: to,
        subject: subject,
        text: text,
        attachments: attachments.map(({ sizeMB, ...attachment }) => attachment), // Exclude sizeMB
      };

      return transporter.sendMail(mailOptions);
    } catch (err) {
      posLogger.error('nodemailer', 'sendEmailWithFileAttachment', err);
      throw new CustomError(err.name, 500);
    }
  }

  async sendWhatsappWithFileAttachment(
    send_to,
    caption,
    media_url,
    templateType,
  ) {
    console.log(
      'send_to, caption, media_url :>> ',
      send_to,
      caption,
      media_url,
      templateType,
    );

    const url =
      templateType == TemplateType.REPLACEMENT_ORDER
        ? `https://media.smsgupshup.com/GatewayAPI/rest?userid=2000197692&password=9LzraftQ&send_to=${send_to}&v=1.1&format=json&msg_type=TEXT&method=SENDMESSAGE&msg=${caption}&isTemplate=true&footer=The+Sleep+Company`
        : `https://media.smsgupshup.com/GatewayAPI/rest?userid=2000233295&password=t6yZNm2q&send_to=${send_to}&v=1.1&format=json&msg_type=DOCUMENT&method=SENDMEDIAMESSAGE&caption=${caption}&media_url=${media_url}&filename=${templateType}.pdf`;
    console.log('url :>> ', url);
    const { response } = await fetch(`${url}`, {
      method: templateType == TemplateType.REPLACEMENT_ORDER ? 'GET' : 'POST',
    }).then(async (response) => {
      return response.json();
    });

    posLogger.info('nodemailer', 'sendWhatsappWithFileAttachment', response);
    if (response.status !== 'success')
      throw new CustomError('Error sending whatsapp message', 500);
  }

  async sendBrochureByWhatsapp(
    customerName,
    customerNo,
    brochureType,
    storeId?,
  ) {
    let reviewsBrochureLink;
    if (brochureType == 'STORE_REVIEWS') {
      const getStoreHandler = new GetStore(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const store = await getStoreHandler.getStore(storeId);
      const { reviewsBrochure } = store;
      reviewsBrochureLink = reviewsBrochure;
      if (!reviewsBrochure) {
        throw new CustomError('Review not found', 404);
      }
    }
    const brochureMap = {
      PILLOW: {
        name: 'Pillow',
        fileName: 'Pillow_Brochure',
        documentLink: `https://${this.configService.get('MEDIA_BUCKET')}.thesleepcompany.in/BROCHURE/PILLOW_BROCHURE.pdf`,
      },
      ADJUSTABLE_DESK: {
        name: 'Adjustable Desk',
        fileName: 'Adjustable_Desk_Brochure',
        documentLink: `https://${this.configService.get('MEDIA_BUCKET')}.thesleepcompany.in/BROCHURE/ADJUSTABLE_DESK_BROCHURE.pdf`,
      },
      CHAIRS: {
        name: 'Chairs',
        fileName: 'Chairs_Brochure',
        documentLink: `https://${this.configService.get('MEDIA_BUCKET')}.thesleepcompany.in/BROCHURE/CHAIRS_BROCHURE.pdf`,
      },
      MATTRESS: {
        name: 'Mattress',
        fileName: 'Mattress_Brochure',
        documentLink: `https://${this.configService.get('MEDIA_BUCKET')}.thesleepcompany.in/BROCHURE/MATTRESS_BROCHURE.pdf`,
      },
      RECLINER_BED: {
        name: 'Recliner Bed',
        fileName: 'Recliner_Bed_Brochure',
        documentLink: `https://${this.configService.get('MEDIA_BUCKET')}.thesleepcompany.in/BROCHURE/RECLINER_BED_BROCHURE.pdf`,
      },
      RECLINER_SOFA: {
        name: 'Recliner Sofa',
        fileName: 'Recliner_Sofa_Brochure',
        documentLink: `https://${this.configService.get('MEDIA_BUCKET')}.thesleepcompany.in/BROCHURE/RECLINER_SOFA_BROCHURE.pdf`,
      },
      ELITE_RECLINER_SOFA: {
        name: 'Elite Recliner Sofa',
        fileName: 'Elite_Recliner_Sofa_Brochure',
        documentLink: `https://${this.configService.get('MEDIA_BUCKET')}.thesleepcompany.in/BROCHURE/ELITE_RECLINER_SOFA_BROCHURE.pdf`,
      },
      SENSAI: {
        name: 'SensAI',
        fileName: 'SensAI_Brochure',
        documentLink: `https://${this.configService.get('MEDIA_BUCKET')}.thesleepcompany.in/BROCHURE/SENSAI_BROCHURE.pdf`,
      },
      STORE_REVIEWS: {
        name: 'Store Reviews',
        fileName: 'Store Reviews Brochure',
        documentLink: `${reviewsBrochureLink}`,
      },
    };
    const brochureDetails = brochureMap[brochureType];

    if (!brochureDetails) {
      throw new CustomError(
        `Brochure type "${brochureType}" is not defined in the brochure map.`,
        404,
      );
    }

    const templateParams = [customerName, brochureDetails.name];
    const documentData = {
      type: 'document',
      document: {
        link: brochureDetails.documentLink,
        filename: brochureDetails.fileName,
      },
    };

    const response = await fetch(
      'https://api.gupshup.io/wa/api/v1/template/msg',
      {
        method: 'POST',
        headers: {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/x-www-form-urlencoded',
          apikey: 'dbenrvn4wy7hbju4htr4sm1tq5iziexx',
        },
        body: new URLSearchParams({
          channel: 'whatsapp',
          source: '919152131284',
          destination: customerNo,
          'src.name': 'kHwzAO4Tjvw1N7A5yfRsFT5Q',
          template: JSON.stringify({
            id: 'eec338c5-fa60-4ced-b9d2-c5177f88d159',
            params: templateParams,
          }),
          message: JSON.stringify(documentData),
        }),
      },
    );

    const result = await response.json();

    if (result.status !== 'submitted') {
      throw new CustomError(`Failed to send WhatsApp message`, 400);
    }

    console.log('WhatsApp message sent successfully:', result);
    return {
      link: brochureDetails.documentLink,
    };
  }

  async getEINVUSERNAME(gstno) {
    const gstinMap = new Map([
      ['36**********1Z9', 'API_CGTPL_TLG'],
      ['33**********1ZF', 'API_CGTPL_POSTN'],
      ['09**********1Z6', 'API_CGTPL_POSUP'],
      ['24**********1ZE', 'API_CGTPL_POSGJ'],
      ['07**********1ZA', 'API_CGTPL_POSDL'],
      ['32**********1ZH', 'API_CGTPL_POSKL'],
      ['18**********1Z7', 'API_CGTPL_POSAS'],
      ['23**********1Z6', 'API_CGTPL_POSMP'],
      ['21**********1ZK', 'API_CGTPL_POSOD'],
      ['37**********1Z7', 'API_CGTPL_POSAP'],
      ['08**********1Z8', 'API_CGTPL_POSRJ'],
      ['03**********1ZR', 'API_CGTPL_POSPB'],
      ['30**********1ZT', 'API_CGTPL_POSGOA'],
      ['05**********1ZE', 'API_CGTPL_POSUTK'],
      ['10**********1ZN', 'API_CGTPL_POSBR'],
      ['27**********1Z8', 'API_CGTPL_EASY1'],
      ['29**********2Z3', 'API_CGTPL_EASYKT1'],
      ['06**********1ZC', 'API_CGTPL_EASYHR1'],
      ['19**********1Z5', 'API_CGTPL_EASYWB1'],
    ]);
    return gstinMap.get(gstno);
  }

  async downloadAndUploadInvoice(IRN, filePath) {
    console.log('====IRN and filepath is====', IRN, filePath);
    const apiUrl = 'http://Einvlive.webtel.in/v1.03/PrintEInvByIRN';
    const requestBody = {
      Irn: IRN,
      GSTIN: this.store?.gstDetails?.gstNumber,
      CDKey: '1699179',
      EInvUserName: await this.getEINVUSERNAME(
        this.store?.gstDetails?.gstNumber,
      ),
      EInvPassword: 'Busy@12345',
      EFUserName: '63CE7EF0-5CB6-402D-AA88-CAA74428F0D7',
      EFPassword: 'BF749CD7-C4BF-434E-B94D-CD184D0BA643',
    };

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const responseData = await response.json();
      console.log('====responseData====', responseData);
      if (
        !responseData ||
        responseData[0]?.Status !== '1' ||
        !responseData[0]?.File
      ) {
        throw new Error('Failed to retrieve PDF URL');
      }

      const pdfUrl = responseData[0].File;
      console.log('PDF URL:', pdfUrl);

      // Download the PDF file
      const pdfResponse = await fetch(pdfUrl);
      const pdfBuffer = await pdfResponse.arrayBuffer();

      const s3Handler = new S3Interactions(
        this.configService,
        this.docClient,
        this.s3Client,
      );

      const s3Response = await s3Handler.uploadToS3(
        pdfBuffer,
        filePath,
        'application/pdf',
      );
      console.log('S3 Response:', s3Response);

      // Define S3 upload path

      return { success: true, s3Url: s3Response };
    } catch (error) {
      console.error('Error downloading/uploading invoice:', error);
      return { success: false, error: error.message };
    }
  }

  async sendMessageOnWhatsApp(payload) {
    try {
      const url = `https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/sendwhatsapp`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': 'UBQJgJrPzW67r32ydSn9H8APs1VcZSkN3LKjoukp',
        },
        body: JSON.stringify({
          template_attributes: {
            ...payload,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to send quotation on whatsapp : ${response.statusText}`,
        );
      }

      return await response.json();
    } catch (error) {
      throw new CustomError(
        `Failed to send quotation on whatsapp: ${error.message}`,
        400,
      );
    }
  }

  async generateAndSendFile(
    templateType: string,
    fileType: string,
    fileExtension: string,
    type?: string,
  ) {
    try {
      const filePath =
        templateType != TemplateType.REPLACEMENT_ORDER
          ? `${templateType}/${this.order.customerId}/${this.order.id}.${fileExtension}`
          : `${templateType}/${this.order.customer.phone}/${this.order.id}.${fileExtension}`;

      let redirectUrl;
      if (this.order?.IRN && templateType === TemplateType.INVOICE) {
        await this.downloadAndUploadInvoice(this.order.IRN, filePath);
      } else {
        console.log('===Calling old template method===', templateType);
        const html = await this.renderTemplateToHtml(templateType);
        const file = await this.convertHtmlToPdf(html);
        if (type == 'DOWNLOAD' || type == 'WHATSAPP') {
          const s3Handler = new S3Interactions(
            this.configService,
            this.docClient,
            this.s3Client,
          );

          await s3Handler.uploadToS3(file, filePath, fileType);
        }
      }

      const finalFilePath = `https://${this.configService.get('MEDIA_BUCKET')}.thesleepcompany.in/${filePath}`;

      if (type == 'DOWNLOAD' || type == 'WHATSAPP') {
        if (type == 'WHATSAPP') {
          if (templateType == TemplateType.REPLACEMENT_ORDER) {
            await this.sendWhatsappWithFileAttachment(
              this.order.customer.phone,
              `Dear+${this.order.customer.firstName || ''}%2C%0A%0APlease+review+and+confirm+the+details+of+your+replacement+for+Order+${this.order.shopifyOrderId}+in+the+link+below.+%0A${finalFilePath}.%0AOnce+confirmed+your+replacement+will+be+booked.+%0A%0AThank+you+for+choosing+us%21%F0%9F%92%99`,
              finalFilePath,
              TemplateType.REPLACEMENT_ORDER,
            );
          } else if (templateType == TemplateType.QUOTATION) {
            const { type, finalDiscountedAmount, bookingAmountStatus } =
              this.order;
            // await this.sendWhatsappWithFileAttachment(
            //   this.order.customer.phone,
            //   `Hello%2C%0A%0AThank+you+for+choosing+The+Sleep+Company.+Here+is+your+quotation%3A%0A%0AQuotation%3A+${finalFilePath}%0A%0ATotal+Amount%3A+%E2%82%B9${finalDiscountedAmount}%0A%0AValid+Until%3A+${moment(expiresAt).format('DD-MM-YYYY')}%0A%0APlease+review+and+let+us+know+if+you+need+any+modifications.%0A%0AWe+look+forward+to+assisting+you%21`,
            //   finalFilePath,
            //   templateType,
            // );
            let data: any = {
              templateName: 'posQuotation',
              quotationlink: finalFilePath,
              phoneNo: this.order.customer.phone,
              quotationAmount: finalDiscountedAmount,
            };
            if (type === QuotationType.BNPL && bookingAmountStatus === 'PAID') {
              data = {
                templateName: 'bnplQuotation',
                quotationlink: finalFilePath,
                phoneNo: this.order.customer.phone,
              };
            }
            await sendMessageOnWhatsApp(data);
          } else {
            await this.sendWhatsappWithFileAttachment(
              this.order.customer.phone,
              `Hi, this is your ${templateType}`,
              finalFilePath,
              templateType,
            );
          }
        }
      } else if (type == 'EMAIL') {
        await this.sendEmailWithFileAttachment(
          this.order.customer.email,
          'Greetings from The Sleep Company',
          `Dear Customer this is your ${templateType}`,
          fileType,
          templateType,
          fileExtension,
        );
      }

      fs.unlink(this.outputPath, (err) => {
        if (err) {
          posLogger.error('nodemailer', 'unlink', { err });
        } else {
          posLogger.info(
            'nodemailer',
            'unlink',
            'Successfully unlink completed',
          );
        }
      });

      return filePath;
    } catch (error) {
      posLogger.error('nodemailer', 'generateAndSendFile', { error });
      // throw new CustomError(error.name, 400);
    }
  }
}
