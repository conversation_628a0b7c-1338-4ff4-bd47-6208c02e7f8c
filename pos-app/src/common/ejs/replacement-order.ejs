<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Replacement</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f7f7f7;
            zoom: 0.80;
            color: #1D2F5A;
        }

        .container {
            width: 90%;
            margin: 20px auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .header img {
            height: 50px;
        }

        .header div {
            text-align: right;
        }

        .replacement-info {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
            border-top: 2px solid black;
            padding-top: 20px;
        }

        .replacement-info > div {
            background: #fff;
            padding: 10px;
            border-radius: 10px;
            border: 1px solid rgb(145, 136, 136);
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
        }

        .seller-info {
            display: grid;
            grid-template-columns: 0.4fr 2fr;
            gap: 10px;
        }

        .seller-info .company-name {
            font-size: small;
            font-weight: 510;
            grid-column: span 2;
        }

        .seller-info div {
            margin-bottom: 5px;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-bottom: 20px;
            border-radius: 10px;
            border: 1px solid rgb(145, 136, 136);
            overflow: hidden;
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
        }

        tbody tr {
            font-weight: 450;
            font-size: small;
        }

        table th,
        table td {
            border: none;
        }

        th,
        td {
            padding: 10px;
            text-align: left;
        }

        th {
            font-size: small;
            background-color: #F1F3F9;
        }

        tbody tr:not(:last-child) {
            border-bottom: 1px solid #ddd;
        }

        .total-container {
            display: flex;
            justify-content: flex-end;
        }

        .total {
            width: 35%;
            background: #fff;
            padding: 10px;
            border-radius: 10px;
            border: 1px solid rgb(145, 136, 136);
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
            margin-top: 20px;
        }

        .total div {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .total div p {
            margin: 0;
        }

        .total hr {
            border: none;
            border-top: 1px solid #ddd;
            margin: 5px 0;
        }

        .item-key {
            font-size: small;
            font-weight: 510;
        }

        .item-val {
            font-weight: normal;
        }

        .replacement-for .item-val {
            font-size: small;
        }

        .seller-info .item-val {
            font-size: small;
        }

        .seller-info .item-key {
            font-size: small;
        }

        .key-value-pair {
            gap: 10px;
            font-size: small;
            display: flex;
        }

        .tnc .item-val {
            font-size: small;
        }

        .item-container {
            font-size: small;
            display: flex;
            flex-wrap: wrap;
        }

        .item-row {
            display: flex;
            margin-right: 20px;
            margin-bottom: 10px;
        }

        .queries {
            background-color: #f1f1f1;
            text-align: center;
            padding: 10px;
            font-size: medium;
            margin-top: 40px;
            border-radius: 8px;
        }

        .blue-text {
            color: blue;
            text-decoration: underline;
        }

        .title {
            font-weight: bold;
        }

        .key-value-pair .item-key {
            font-weight: 550;
        }

        .key-value-pair .item-val {
            font-weight: 400;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <img src="<%= replacementOrder.logo %>" alt="<%= replacementOrder.companyName %>">
            <div class="title" style="font-size: 20px;">
                <% if (replacementOrder.orderType === 'REPLACEMENT') { %>
                    Replacement
                <% } else { %>
                    Return
                <% } %>
            </div>
            
            <div>
                <div class="key-value-pair">
                    <span class="item-key">
                        <% if (replacementOrder.orderType === 'REPLACEMENT') { %>
                            Replacement Date:
                        <% } else if (replacementOrder.orderType === 'RETURN') { %>
                            Return Date:
                        <% } %>
                    </span>
                    <span class="item-val"><%= replacementOrder.invoiceDate %></span>
                </div>
                <div class="key-value-pair" style="margin-top: 2px;">
                    <span class="item-key">
                        <% if (replacementOrder.orderType === 'REPLACEMENT') { %>
                            Replacement No:
                        <% } else if (replacementOrder.orderType === 'RETURN') { %>
                            Return No:
                        <% } %>
                    </span>
                    <span class="item-val"><%= replacementOrder.invoiceNumber %></span>
                </div>
            </div>
        </div>
        <div class="replacement-info">
            <div>
                <h3>Seller</h3>
                <div class="seller-info">
                    <div class="company-name"><%= replacementOrder.companyName %></div>
                    <div class="item-key">Account:</div>
                    <div class="item-val">************ / ICIC0001206 / COMFORT GRID TECHNOLOGIES PRIVATE LIMITED</div>
                    <div class="item-key">Email:</div>
                    <div class="item-val"><EMAIL></div>
                </div>

                <div class="item-container">
                    <div class="item-row" style="margin-top: 20px;">
                        <div style="width: 300px;">
                            <span class="item-key">CIN:</span>
                            <span class="item-val"><%= replacementOrder.cin %></span>
                        </div>
                    </div>
                    <div class="item-row" style="margin-top: 10px;">
                        <!-- <div style="width: 300px;">
                            <span class="item-key">GST:</span>
                            <span class="item-val"><%= replacementOrder.gst %></span>
                        </div> -->
                        <div>
                            <span class="item-key">PAN :</span>
                            <span class="item-val"><%= replacementOrder.pan %></span>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <% if (replacementOrder.orderType === 'REPLACEMENT') { %>
                    <h3 class="replacement-for">Replacement For</h3>
                <% } else if (replacementOrder.orderType === 'RETURN') { %>
                    <h3 class="replacement-for">Return For</h3>
                <% } %>

                <% if (replacementOrder.customerName) { %>
                    <p>
                      <span class="item-key">Name:</span>
                      <span style="font-size: small;" class="item-val"><%= replacementOrder.customerName %></span>
                    </p>
                <% } %>
                <p><span class="item-key">Ph No:</span> <span style="font-size: small;" class="item-val"><%= replacementOrder.customerNumber %></span></p>
                <p><span class="item-key">Address:</span> 
                <span style="font-size: small;" class="item-val">
                    <%= replacementOrder.customerAddress %> <br />
                    <%= replacementOrder.customerCity %> - <%= replacementOrder.customerZip %> <br />
                    <%= replacementOrder.customerState %> <br />
                </span></p>
                
            </div>
        </div>

        <% if (replacementOrder.orderType === 'REPLACEMENT') { %>
            <h4>Replaceable Product</h4>
        <% } else if (replacementOrder.orderType === 'RETURN') { %>
            <h4>Return Product</h4>
        <% } %>

        <table>
            <thead>
                <tr>
                    <th>Sr No</th>
                    <th>Item Name</th>
                    <!-- <th>SKU</th> -->
                    <th>Selling Price</th>
                    <th>Quantity</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <% replacementOrder.items.forEach((item, index) => { %>
                    <tr>
                        <td><%= index + 1 %></td>
                        <td><%= item.title %></td>
                        <!-- <td><%= item.sku %></td> -->
                        <td><%= item.finalItemPrice / item.quantity  %></td>
                        <td><%= item.quantity %></td>
                        <td><%= item.finalItemPrice - item.itemDiscount - item.bankDiscount %></td>
                    </tr>
                <% }); %>
            </tbody>
        </table>

        <h4><%= replacementOrder.purchasedTitle %></h4>
        <table>
            <thead>
                <tr>
                    <th>Sr No</th>
                    <th>Item Name</th>
                    <!-- <th>SKU</th> -->
                    <th>Selling Price</th>
                    <th>Quantity</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <% replacementOrder.purchasedProducts.forEach((item, index) => { %>
                    <tr>
                        <td><%= index + 1 %></td>
                        <td><%= item.title %></td>
                        <!-- <td><%= item.sku %></td> -->
                        <td><%= item.finalItemPrice  / item.quantity %></td>
                        <td><%= item.quantity %></td>
                        <td><%= item.finalItemPrice %></td>
                    </tr>
                <% }); %>
            </tbody>
        </table>
            
        <div class="total-container">
            <% if (replacementOrder.replacementType === 'FULL') { %>
            <div class="total">
                <div>
                    <% if (replacementOrder.orderType === 'REPLACEMENT') { %>
                        <p style="text-align: left;">Replaceable Item Price:</p>
                    <% } else if (replacementOrder.orderType === 'RETURN') { %>
                        <p style="text-align: left;">Return Item Price:</p>
                    <% } %>
                    <p style="text-align: right;">₹<%= replacementOrder.replacedItemTotal %></p>
                </div>
                <hr>
            
                <div>
                    <p style="text-align: left;">New Item Price:</p>
                    <p style="text-align: right;">₹<%= replacementOrder.purchasedItemsPrice %></p>
                </div>
                <div>
                    <p style="text-align: left;">Difference Amount:</p>
                    <p style="text-align: right;">₹<%= replacementOrder.diffrenceAmount %></p>
                </div>
                
                <div>
                    <p style="text-align: left;">Shipping Cost:</p>
                    <p style="text-align: right;">₹<%= replacementOrder.shipmentCost %></p>
                </div>

                <% if (replacementOrder.orderType === 'REPLACEMENT') { %>
                <div>
                    <p style="text-align: left;">Total Discount:</p>
                    <p style="text-align: right;">₹<%= replacementOrder.customDiscountAmount %></p>
                </div>
                <% } %>
                
                <div>
                    <% if (replacementOrder.orderType === 'REPLACEMENT') { %>
                        <p style="text-align: left;">Total Amount Due:</p>
                        <p style="text-align: right;">₹<%= replacementOrder.finalDiscountedAmount %></p>
                    <% } else if (replacementOrder.orderType === 'RETURN') { %>
                        <p style="text-align: left;">Total Amount Due:</p>
                        <p style="text-align: right;">₹<%= replacementOrder.refundAmount %></p>
                    <% } %>
                </div>
            </div>
            <% } %>
            <% if (replacementOrder.replacementType === 'PART') { %>
                <div class="total">
                    <div>
                        <p style="text-align: left;">Total:</p>
                        <p style="text-align: right;">₹<%= replacementOrder.purchasedItemsPrice %></p>
                    </div>
                    <div>
                        <p style="text-align: left;">Shipping Cost:</p>
                        <p style="text-align: right;">₹<%= replacementOrder.shipmentCost %></p>
                    </div>
                
                    <div>
                        <p style="text-align: left;">Total Discount:</p>
                        <p style="text-align: right;">₹<%= replacementOrder.customDiscountAmount %></p>
                    </div>
                
                    <div>
                            <p style="text-align: left;">Total Amount Due:</p>
                            <p style="text-align: right;">₹<%= replacementOrder.finalDiscountedAmount %></p>
                        
                    </div>
                </div>
                <% } %>
        </div>
        
        <hr style="margin-top: 20px;">
        <div class="queries">
            For Any Queries, reach out at TSC <span class="blue-text"><EMAIL></span>
        </div>
    </div>
</body>

</html>
