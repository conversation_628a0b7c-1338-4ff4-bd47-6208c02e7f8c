<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>

  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      max-width: 1024px;
      margin: 0 auto;
      zoom:0.75;
      padding-top:20px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
    }

    .header img {
      width: 190px;
      height:50px;
    }

    .authorized-signatory img{
      width: 190px;
      height:120px;
    }


    .header > .left {
      text-align: left;
    }

    .header > .right {
      text-align: right;
    }

    .main {
      padding: 1rem;
    }

    .invoice {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 1rem;
    }

    .invoice-details {
      display: flex;
      height: fit-content;
      justify-content: end;
    }

    .invoice-details-table {
      border-collapse: collapse;
    }

    .invoice-details-table td {
      padding: 0.5rem;
    }

    .invoice-details-table .value {
      text-align: end;
    }

    .items-table {
      width: 100%;
      border-collapse: collapse;
    }

    .items-table thead {
      background-color: #bedfff;
    }

    .background-blue {
      background-color: #03b6fc;
    }

    .items-table th,
    .items-table td {
      border: 1px solid grey;
      padding: 0.5rem;
      text-align: start;
    }

    .toc {
      display: flex;
      justify-content: space-between;
    }

    .toc-list {
      display: grid;
      list-style-type: decimal;
      margin-left: -1rem;
      gap: 0.5rem;
      font-size: 14px;
    }

    .final-charges {
      padding: 1rem;
      border-collapse: collapse;
      margin-top: 1rem;
    }

    .final-charges td {
      padding: 0.5rem;
      border: 1px solid grey;
    }

    .final-charges .total {
      background-color: #bedfff;
    }

    .final-charges .value {
      text-align: end;
    }

    .footer {
      padding: 1rem;
    }

    .footer > .contact {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f1f1f1;
      padding: 1rem;
    }

    .footer > .authorized-signatory {
      display: flex;
      align-items: end;
      justify-content: end;
      flex-direction: column;
    }

    .address {
      line-height: 0.2rem;
    }

    .bold700 {
      font-weight: 700;
      font-size: 1.1rem;
    }

    .bold500 {
      font-weight: 600;
    }

    .page-break {
      page-break-after: always;
    }
  </style>


 <body>

  <% if (invoice && invoice.companyName) { %>
    <!-- Render invoice details -->
    <div>
      <header class="header">
        <div class="right">
          <img src="<%= invoice.logo %>" alt="logo" />
        </div>
  
        <div class="left">
          <!-- <h3>Invoice</h3>
          <p>Delivery invoice</p> -->
          <div class="invoice-details">
            <table class="invoice-details-table">
              <tr>
                <td class="bold700">Invoice No</td>
                <td class="value"><%= invoice.invoiceNumber %></td>
              </tr>

              <tr>
                <td class="bold700">Date</td>
                <td class="value"><%= invoice.invoiceDate %></td>
              </tr>
            </table>
          </div>
        </div>
      </header>

      <main class="main">
        <div class="invoice">
          <div>
            <p class="bold700">SELLER</p>
            <p class="bold500">Comfort Grid Technologies Private Limited Regd
              <p><b>Office:</b> 
            Regd Office: 7th Floor, Unit 7A, Techweb
                Centre, New Link Road, Near Raigad Military School,
                  Jogeshwari West, Mumbai, Mumbai Suburban,
                    Maharashtra - 400102
                    <br>
                    <b>Ph No.:</b> 9811981911  <br>
                    <b>Email:</b> <EMAIL>  <br>
                    <b>CIN:</b> U24304MH2019PTC333446  <br>
                    <b>PAN:</b> **********  <br>
                <b>Store Id:</b> <%= invoice.storeId %>
          </div>
            <!-- <div class="billed-by">
              <p class="bold700">Billed By</p>
              <p class="bold500"><%= invoice.companyName %></p>
              <p><%= invoice.companyAddress %></p>
              <p class="address"><%= invoice.companyCity %></p>
              <p class="address"><%= invoice.companyZip %></p>
              
            </div> -->

            <div class="billed-to">
              <p class="bold700">Billed To</p>
              <p class="bold500"><%= invoice.customerName %></p>
              <p><%= invoice.customerAddress %></p>
              <p class="address"><%= invoice.customerCity %></p>
              <p class="address"><%= invoice.customerZip %></p>
              <p><b>Place Of Supply:</b> <%= invoice.shippingState %></p>
              <b>Ph No.:</b>+91 <%= invoice.customerNumber %>  <br>
              <b>Email:</b> <%= invoice.customerMail %>  <br>
            </div>

          
        </div>

        <br>
        <table class="items-table">
          <thead>
            <tr>
              <th>Item Name</th>
              <th>SKU</th>
              <th>HSN/SAC</th>
              <th>Item Price</th>
              <th>Quantity</th>
              <th>Total Item Value</th>
              <th>Discount</th>
              <th>Taxable Value</th>
              <th>GST Rate</th>
              <th>Tax Amount</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            <% invoice.items.forEach(function(item) { %>
              <tr>
                <td><%= item.name %></td>
                <td><%= item.sku %></td>
                <td><%= item.hsn %></td>
                <td><%= item.itemPrice %></td>
                <td><%= item.quantity %></td>
                <td><%= item.totalItemValue %></td>
                <td><%= item.discount %></td>
                <td><%= item.taxableValue %></td>
                <td><%= item.gstRate %></td>
                <td><%= item.taxAmount %></td>
                <td><%= item.total %></td>
              </tr>
            <% }); %>
          </tbody>
        </table>

        <br>
        <div class="toc">
          <div class="right">
            <!-- <p class="bold700">Terms and Conditions</p> -->
            <ol class="toc-list">
              <!-- <li>
                Please pay within 15 days from the date of invoice, overdue <br>
                interest @ 14% will be charged for the delay in payments.
              </li>
              <li>Please quote the invoice number when remitting funds</li> -->
            </ol>
            
          </div>

          <table class="final-charges">
            <tr>
              <td class="bold500">Total</td>
              <td class="value"><%= invoice.totalAmountWithoutTax %></td>
            </tr>

            <tr>
              <td class="bold500">Total Item Taxable Value</td>
              <td class="value"><%= invoice.totalAmountTaxable %></td>
            </tr>
            <tr>
              <td class="bold500">Total Item Tax</td>
              <td class="value"><%= invoice.totalTax %></td>
            </tr>

            <tr class="background-blue">
              <td class="bold500">Invoice Value</td>
              <td class="value"><%= invoice.payableAmount %></td>
            </tr>
          </table>
        </div>
      </main>

      <footer class="footer">
        <div>
          <p class="bold700">Amount Chargeable (in words): ₹ <%= invoice.totalAmountInWords %>  </p>

        </div>
        <div class="authorized-signatory">
          <img src="<%= invoice.signature %>" alt="signature" />
          <b>Authorized Signatory</b>
          <b>For Comfort Grid Technologies Private Limited</b><br>
        </div>

        <div class="contact">
          For any enquiries, reach out at &nbsp;
          <a href="tel:<%= invoice.storeNumber %>" ><%= invoice.storeNumber %></a> &nbsp; / &nbsp;
          <a href="mailto:<%= invoice.storeEmail %>" ><%= invoice.storeEmail %></a>
        </div>
      </footer>
    </div>
<% } %>

<% if (invoice && receipt) { %>
  <div class="page-break"></div>
<% } %>

<% if (receipt) { %>
    <!-- Render invoice details -->
    <div>
      <header class="header">
        <div class="right">
          <img src="<%= receipt.logo %>" alt="logo" />
        </div>
  
        <div class="right">
          <h3>Payment Acknowledgement Receipt</h3>
          <div class="invoice-details">
            <table class="invoice-details-table">
              <tr>
                <td class="bold700">Order No</td>
                <td class="value"><%= receipt.invoiceNumber %></td>
              </tr>

              <tr>
                <td class="bold700">Date</td>
                <td class="value"><%= receipt.invoiceDate %></td>
              </tr>
            </table>
          </div>
        </div>
      </header>

      <main class="main">
        <div class="invoice">
          <div>
            <p class="bold700">SELLER</p>
            <p class="bold500">Comfort Grid Technologies Private Limited Regd
              <p><b>Office:</b> 
            Regd Office: 7th Floor, Unit 7A, Techweb
                Centre, New Link Road, Near Raigad Military School,
                  Jogeshwari West, Mumbai, Mumbai Suburban,
                    Maharashtra - 400102
                    <br>
                    <b>Ph No.:</b> 9811981911  <br>
                    <b>Email:</b> <EMAIL>  <br>
                    <b>CIN:</b> U24304MH2019PTC333446  <br>
                    <b>PAN:</b> **********  <br>
                <b>Store Id:</b><%= receipt.storeId %>
          </div>
            <div class="billed-by">
              <p class="bold700">Billed By</p>
              <p class="bold500"><%= receipt.companyName %></p>
              <p><%= receipt.companyAddress %></p>
              <p class="address"><%= receipt.companyCity %></p>
              <p class="address"><%= receipt.companyZip %></p>
              
            </div>

            <div class="billed-to">
              <p class="bold700">Billing Address</p>
              <p class="bold500"><%= receipt.customerName %></p>
              <p><%= receipt.customerAddress %></p>
              <p class="address"><%= receipt.customerCity %></p>
              <p class="address"><%= receipt.customerZip %></p>
              <b>Ph No.:</b>+91 <%= receipt.customerNumber %>  <br>
              <b>Email:</b> <%= receipt.customerMail %>  <br>
            </div>
            <div>
              <p class="bold700">Shipping Address</p>
              <p><%= receipt.shippingAddress %></p>
              <p class="address"><%= receipt.shippingCity %></p>
              <p class="address"><%= receipt.shippingZip %></p>
            </div>

</div>
        </div>

        <br>
        <table class="items-table">
          <thead>
            <tr>
              <th>Item Name</th>
              <th>SKU</th>
              <th>HSN/SAC</th>
              <th>Item Price</th>
              <th>Quantity</th>
              <th>Total Item Value</th>
              <th>Discount</th>
              <th>Taxable Value</th>
              <th>GST Rate</th>
              <th>Tax Amount</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            <% receipt.items.forEach(function(item) { %>
              <tr>
                <td><%= item.name %></td>
                <td><%= item.sku %></td>
                <td><%= item.hsn %></td>
                <td><%= item.itemPrice %></td>
                <td><%= item.quantity %></td>
                <td><%= item.totalItemValue %></td>
                <td><%= item.discount %></td>
                <td><%= item.taxableValue %></td>
                <td><%= item.gstRate %></td>
                <td><%= item.taxAmount %></td>
                <td><%= item.total %></td>
              </tr>
            <% }); %>
          </tbody>
        </table>

        <br>
        <div class="toc">
          <div class="right">
            <!-- <p class="bold700">Terms and Conditions</p> -->
            <ol class="toc-list">
              <!-- <li>
                Please pay within 15 days from the date of invoice, overdue <br>
                interest @ 14% will be charged for the delay in payments.
              </li>
              <li>Please quote the invoice number when remitting funds</li> -->
            </ol>
          </div>

          <table class="final-charges">
            <tr>
              <td class="bold500">Total</td>
              <td class="value"><%= receipt.totalAmountWithoutTax %></td>
            </tr>

            <tr>
              <td class="bold500">Total Item Taxable Value</td>
              <td class="value"><%= receipt.totalAmountTaxable %></td>
            </tr>
            <tr>
              <td class="bold500">Total Item Tax</td>
              <td class="value"><%= receipt.totalTax %></td>
            </tr>

            <!-- <tr>
              <td class="bold500">Discounts</td>
              <td class="value"><%= receipt.reductions %></td>
            </tr> -->

            <tr class="background-blue">
              <td class="bold500">Total Payable Value</td>
              <td class="value"><%= receipt.payableAmount %></td>
            </tr>

            <tr class="background-blue">
              <td class="bold500">Already Paid Amount</td>
              <td class="value"><%= receipt.totalPaidAmount %></td>
            </tr>

            <tr class="background-blue">
              <td class="bold500">Remaining Amount</td>
              <td class="value"><%= receipt.remainingAmount %></td>
            </tr>
          </table>
        </div>
      </main>

      <footer class="footer">
        <div>
          <p class="bold700">Amount Chargeable (in words): ₹ <%= receipt.totalAmountInWords %>  </p>

        </div>

        <div class="authorized-signatory">
          <img src="<%= receipt.signature %>" alt="signature" />
          <b>Authorized Signatory</b>
          <b>For Comfort Grid Technologies Private Limited</b><br>

        </div>

        <div class="contact">
          For any enquiries, reach out at &nbsp;
          <a href="tel:<%= receipt.storeNumber %>" ><%= receipt.storeNumber %></a> &nbsp; / &nbsp;
          <a href="mailto:<%= receipt.storeEmail %>" ><%= receipt.storeEmail %></a>
        </div>
      </footer>
    </div>
<% } %>
 </body>
</html>