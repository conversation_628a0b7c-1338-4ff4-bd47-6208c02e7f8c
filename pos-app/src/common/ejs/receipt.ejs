<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Acknowledgment Receipt</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            /* background-color: #f7f7f7; */
            zoom: 0.8;
            color: #1D2F5A;

        }

        .container {

            /* background: #fff; */
            padding: 20px;
            /* border-radius: 8px; */
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .heade-tncr {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .header img {
            height: 50px;
        }

        .header div {
            text-align: right;
        }

        .title {
            font-size: larger;
        }

        .info-container {
            font-size: small;
            margin-bottom: 20px;
            border-top: 2px solid #000;
            padding-top: 20px;
        }

        .info-box {
            background: #fff;
            padding: 10px;
            border-radius: 10px;
            border: 1px solid grey;
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
        }

        .seller-info {
            display: grid;
            grid-template-columns: 1fr 8fr;
            gap: 10px;

        }

        .seller-info h3 {
            grid-column: span 2;
            /* Spanning across both columns */
        }
        .seller-info .company-name{
            font-size: large;
            font-weight: 520;
            grid-column: span 2;
            
        }

        .key-value {
            display: contents;
        }

        .item-key {
            font-weight: 510;
        }

        .item-val {
            font-weight: normal;
    
        }

        .addresses {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            border: 1px solid grey;
            overflow: hidden;
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
        }

        .address-box {
            background: #fff;
            padding: 10px;

        }

        .address-box h3 {
            margin-bottom: 10px;
        }

        .address-details {
            font-size: small;
            display: grid;
            grid-template-columns: auto 1fr;
            align-items: start;
            gap: 10px;
        }
    

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-bottom: 20px;
            border: 1px solid grey;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
        }

        tbody tr {
            font-weight: 450;
            
            font-size: small;
        }

        table th,
        table td {
            border: none;
        }
        @page {
            margin: 0.7cm 1rem; 

        }
        th,
        td {
            padding: 10px;
            text-align: left;
        }

        th {
            font-size: small;
            background-color: #f1f3f9;
        }

        tbody tr:not(:last-child) {
            border-bottom: 1px solid #ddd;
        }

        .total-container {
            display: flex;
            justify-content: space-between;
            /* Aligns the two inner divs with space between them */
            align-items: flex-start;
            /* Aligns items at the start of the container */
        }

        .total {
            width: 30%;
            background: #fff;
            padding: 10px;
            border-radius: 10px;
            border: 1px solid grey;
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
            margin-top: 20px;
        }

        .total div {
            display: flex;
            justify-content: space-between;
        }

        .total div p {
            margin: 0;
        }

        .total hr {
            border: none;
            border-top: 1px solid #ddd;
            margin: 10px 0;
        }

        .amount-in-words {
            margin-top: 20px;
            font-weight: 500;
        }

        .right-aligned-container {
            text-align: right;
        }

        .right-aligned-container img {
            display: block;
            margin-left: auto;
            width: 190px;
            height: 120px;
        }

        .right-aligned-container .text-one,
        .right-aligned-container .text-two {
            display: block;
            font-size: medium;
    
            font-weight: 550;

        }

        .key-value-pair {
            display: flex;
        }

        hr {
            margin-top: 20px;
        }
        .queries {
    background-color: #f1f1f1; /* Light grey background */
    text-align: center; /* Center aligned text */
    padding: 10px; /* Padding for space around the text */
    font-size: medium; /* Adjust font size as needed */
    margin-top: 40px; /* Space above the queries section */
    border-radius: 8px; /* Optional: rounded corners */
}
.main-container {
    width: 80%;
    margin: 50px auto;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* Optional: Box shadow for depth */
}
.blue-text {
    color: blue; /* Blue text color */
    text-decoration: underline; /* Adds underline */

}

.header-tnc {
    page-break-before: always;

    background-color: #f0f0f0; /* Grey background color */
margin-top: 20px;

    padding: 10px;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
}

.content {
    line-height: 1.6;
}

.content ul {
    list-style-type: none;
    padding-left: 0;
}

.content ul li {
    margin-bottom: 10px;
}
    </style>
</head>

    <body>
        <div class="container">
            <div class="header">
                <img src="<%= receipt.logo %>" alt="The Sleep Company">
                <div class="title" style="font-weight: 510;">Payment Acknowledgment Receipt</div>
                <!-- Invoice Date and Number -->
                <div>
                    <div class="key-value-pair">
                        <span class="item-key">Order Date: &nbsp;</span>
                        <span class="item-val" >
                            <%= receipt.invoiceDate %>
                        </span>
                    </div>
                    <div class="key-value-pair" style="margin-top: 2px;">
                        <span class="item-key">Order No: &nbsp;</span>
                        <span class="item-val">
                            <%= receipt.invoiceNumber %>
                        </span>
                    </div>
                </div>
            </div>

            <div class="info-container">
                <!-- Seller Info -->
                <div class="seller-info info-box">
                    <span class="company-name">Seller</span>
                    <span class="company-name" style="font-weight: 500;">
                        <%= receipt.companyName %>
                    </span>
                    <div class="key-value">
                        <span class="item-key">Office:</span>
                        <span class="item-val">
                            <%= receipt.companyAddress %>
                                <%= receipt.companyCity %>
                                    <%= receipt.companyZip %>
                        </span>
                    </div>
                    <div class="key-value">
                        <span class="item-key">Email:</span>
                        <span class="item-val">
                            <EMAIL>
                        </span>
                    </div>
                    <div class="key-value">
                        <span class="item-key">CIN:</span>
                        <span class="item-val">
                            <%= receipt.cin %>
                        </span>
                    </div>
                    <div class="key-value">
                        <span class="item-key">GST:</span>
                        <span class="item-val">
                            <%= receipt.gst %>
                        </span>
                    </div>
                    <div class="key-value">
                        <span class="item-key">PAN:</span>
                        <span class="item-val">
                            <%= receipt.pan %>
                        </span>
                    </div>
                    <div class="key-value">
                        <span class="item-key">STORE ID:</span>
                        <span class="item-val">
                            <%= receipt.storeId %>
                        </span>
                    </div>
                </div>

                <!-- Billing and Shipping Addresses -->
                <div class="addresses">
                    <div class="address-box">
                        <h3>Billing Address</h3>
                        <div class="address-details">
                        <span class="item-key">Address:</span> <span class="item-val"><%= receipt.customerAddress %>, <%= receipt.customerCity %>, <%= receipt.customerState %> - <%= receipt.customerZip %></span>
                        <span class="item-key">Phone:</span> <span class="item-val"><%= receipt.customerNumber %></span>
                        <span class="item-key">Email:</span> <span class="item-val"><%= receipt.customerMail %></span>
                        </div>
                    </div>
                    <div class="address-box">
                        <h3>Shipping Address</h3>
                        <div class="address-details">
                        <span class="item-key">Name:</span> <span class="item-val"><%= receipt.customerName %></span>
                        <span class="item-key">Address:</span> <span class="item-val"><%= receipt.shippingAddress %>, <%= receipt.shippingCity %>, <%= receipt.shippingState %> - <%= receipt.shippingZip %></span>

                        </div>
                    </div>
                </div>
                
                
                

            </div>

            <!-- Items Table -->
            <table>
                <thead>
                    <tr>
                        <th>Item Name</th>
                        <th>SKU</th>
                        <th>HSN/SAC</th>
                        <% if (receipt.items[0].mrp !==0) { %>
                            <th>MRP</th>
                            <th>Discount</th>
                            <% } %>
                                <th>Selling Price</th>
                                <th>Additional Discount</th>
                                <th>Quantity</th>
                                <th>Status</th>
                                <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <% receipt.items.forEach(function(item) { %>
                        <tr>
                            <td>
                                <%= item.name %>
                            </td>
                            <td>
                                <%= item.sku %>
                            </td>
                            <td>
                                <%= item.hsn %>
                            </td>
                            <% if (receipt.items[0].mrp !==0) { %>
                                <td>
                                    <%= item.mrp %>
                                </td>
                                <td>
                                    <%= item.mrpDiscount %>
                                </td>
                                <% } %>
                                    <td>
                                        <%= item.itemPrice %>
                                    </td>
                                    <td>
                                        <%= item.discount || '-' %>
                                    </td>
                                    <td>
                                        <%= item.quantity %>
                                    </td>
                                    <td></td>
                                    <td>
                                        <%= item.total %>
                                    </td>
                        </tr>
                        <% }); %>
                        <% if (receipt.subOrderType && receipt.subOrderType === "LOCK_PRICE") { %>
                            <tr>
                                <td>Booking Amount</td>
                                <td><%= receipt.bnplProduct.sku %></td>
                                <td><%= receipt.bnplProduct.hsn %></td>
                                <% if (receipt.items[0].mrp !==0) { %>
                                    <td><%= receipt.bnplProduct.price %></td>
                                    <td>0</td>
                                <% } %>
                                <td><%= receipt.bnplProduct.price %></td>
                                <td>0</td>
                                <td><%= receipt.bnplProduct.quantity %></td>
                                <td><%= receipt.bookingAmountStatus %></td>
                                <td><%= receipt.finalDiscountedAmount %></td>
                            </tr>
                        <% } %>
                </tbody>
            </table>

            <!-- Total Amount Details -->
            <div class="total-container">
                <div class="amount-in-words">
                    <p>Amount Chargeable (in words): <%= receipt.totalAmountInWords %>
                    </p>
                    <p>Mode of Payment: <%= receipt.modeOfPayment %>
                    </p>
                </div>

                <div class="total">
                    <div>
                        <p class="item-key">Total:</p>
                        <p class="item-val">₹<%= receipt.totalAmount %>
                        </p>
                    </div>
                    <hr>
                    <div>
                        <p class="item-key">Total Item Taxable Value:</p>
                        <p class="item-val">₹<%= receipt.totalAmountTaxable %>
                        </p>
                    </div>
                    <hr>
                    <div>
                        <p class="item-key">Total Item Tax:</p>
                        <p class="item-val">₹<%= receipt.totalTax %>
                        </p>
                    </div>
                    <% if (receipt.deliveryCharge > 0) { %>
                        <hr>
                        <div>
                            <p class="item-key">Total Delivery Charges</p>
                            <p class="item-val">₹<%= receipt.deliveryCharge %></p>
                        </div>
                        <hr>
                        <div>
                            
                            <p class="item-key">Delivery Charges Taxable Value</p>
                            <p class="item-val">₹<%= receipt.deliveryChargesTaxableValue %></p>
                        </div>
                        <hr>
                      <div>

                          <p class="item-key">Tax on Delivery Charge</p>
                          <p class="item-val">₹<%= receipt.deliveryChargesTax %></p>
                        </div>
                      <% } %>
                    <hr>
                    <div>
                        <p class="item-key">Total Payable Value:</p>
                        <p class="item-val">₹<%= receipt.payableAmount %>
                        </p>
                    </div>
                    <hr>
                    <div>
                        <p class="item-key">Already Paid Amount:</p>
                        <p class="item-val">₹<%= receipt.totalPaidAmount || 0 %>
                        </p>
                    </div>
                    <hr>
                    <div>
                        <p class="item-key">Remaining Amount:</p>
                        <p class="item-val">₹<%= receipt.subOrderType && receipt.subOrderType === "LOCK_PRICE" ? receipt.productFinalAmount : (receipt.remainingAmount || 0) %>
                        </p>
                    </div>
                </div>
            </div>

            <hr>

            <!-- Footer with Authorized Signature -->
            <footer style="margin-top: 10px;">
                <div class="right-aligned-container">
                    <img src="<%= receipt.signature %>" alt="Signature">
                    <div class="text-one">Authorized Signature</div>
                    <div class="text-two" style="margin-top: 10px;">For <%= receipt.companyName %>
                    </div>
                </div>
                <div class="queries">
                    For Any Queries, reach out at <span class="blue-text"><%= receipt.storeNumber %></span> / <span class="blue-text"><%= receipt.storeEmail %></span>
                </div>
                <div class="header-tnc">Terms and Conditions</div>
                <div class="content">
                    <p><strong>TERMS OF ORDER</strong></p>
                    <ol>
                        <li>I/We agree to make payment in full before the delivery of Goods or Documents.</li>
                        <li>I/We agree that this order will not be binding on the store unless and until it is confirmed by the company Head office.</li>
                        <li>I/We agree not to cancel the order.</li>
                        <li>I/We agree that the delivery time mentioned is indicative. I/We agree that we will not claim compensation nor cancel the order if there is any delay through any cause whatsoever.</li>
                        <li>I/We agree that the prices stated herein are tentative and will be charged as ruling at the time of delivery.</li>
                        <li>I/We agree that the company or its employees shall not be held responsible for any damage caused to the property of the customer during delivery.</li>
                        <li>I/We agree to be charged additionally for redelivery of the order, should I/We fail to take delivery whatsoever.</li>
                        <li>Taxes shall be levied as applicable at the time of delivery.</li>
                        <li>Due to unavoidable circumstances, If there is cancellation of the order, I/We agree to bear the charges for the same.</li>
                        <li>I/We agree to the details of the product mentioned overleaf.</li>
                    </ol>
                    <p class="item-val">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Note : Invoice can be generated from the State other than the State where Order was taken as per the availability of Stock</p>
                </div>
            </footer>
        </div>
        

    </body>


</html>
