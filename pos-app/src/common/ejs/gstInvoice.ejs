<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GSTInvoice</title>

    <style>
        .container {
            padding: 2rem 2rem;
        }

        body {
            font-family: Arial, sans-serif;
            zoom: 0.8;
            color: #1D2F5A;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;

        }

        hr {
            border: 1px solid grey;
        }

        header img {
            max-height: 50px;
        }

        .detail-container {
            margin-top: 20px;
            padding: 1rem;
            border: 1px solid grey;
            border-radius: 10px;
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
        }

        .header-text {
            font-weight: 550;
            font-size: larger
        }

        .flex-row {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }

        .grid-container-one {
            font-size: small;
            display: grid;
            grid-template-columns: 1fr 10fr;
            gap: 8px;
        }

        .grid-container-two {
            font-size: small;
            display: grid;
            grid-template-columns: 2fr 2fr 1.6fr;
            gap: 20px;
        }

        .grid-container-three {
            font-size: small;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 10px;

        }

        .grid-item-key {
            font-weight: 510;
        }

        .grid-item-val {
            font-weight: small;

        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th,
        td {
            padding: 8px;
            text-align: left;
        }

        th {
            font-size: small;

            background-color: #F1F3F9;

        }

        thead {
            border-radius: 10px;
        }

        .padding-zero {
            padding: 0 !important;
            font-size: small;

        }

        .rows-text {
            font-size: small;

        }

        .qr-image-class {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
        }

        tbody tr {
            font-weight: 450;

            font-size: small;
        }

        .table-container {
            overflow-x: auto;
            border: 1px solid grey;
            margin-top: 20px;
            border-radius: 10px;
            page-break-inside: avoid;
            box-shadow: -6px 0 6px rgba(0, 0, 0, 0.08),
                6px 0 6px rgba(0, 0, 0, 0.08),
                0 6px 6px rgba(0, 0, 0, 0.08);
        }

        .table-container table tr:last-child {
            box-shadow: 0 6px 6px rgba(0, 0, 0, 0.08);
        }

        .flex-container {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }


        .grid-container-four {
            margin-top: 15px;
            margin-right: 100px;
            display: grid;
            grid-template-columns: 250px 80px;
            width: 330px;
            padding: 10px;
            gap: 10px;

        }

        .grid-item-label {
            font-weight: 520;
            font-size: small;
            text-align: right;
            padding-right: 10px;
        }

        .grid-item-value {
            text-align: left;
            font-weight: normal;
            color: #605e5e;
            font-size: small;
            padding-left: 6px;
        }

        .right-aligned-container {
            text-align: right;
        }


        .right-aligned-container img {
            display: block;
            margin-left: auto;
            width: 190px;
            height: 120px;
        }

        .right-aligned-container .text-one,
        .right-aligned-container .text-two {
            display: block;
            font-size: medium;

            font-weight: 550;
        }

        .queries {
            background-color: #f1f1f1;
            /* Light grey background */
            text-align: center;
            /* Center aligned text */
            padding: 10px;
            /* Padding for space around the text */
            font-size: medium;
            /* Adjust font size as needed */
            margin-top: 40px;
            /* Space above the queries section */
            border-radius: 8px;
            /* Optional: rounded corners */
        }

        .blue-text {
            color: blue;
            /* Blue text color */
            text-decoration: underline;
            /* Adds underline */

        }
    </style>
</head>



<body class="container">

    <header>
        <img src="<%= invoice.logo %>" alt="Company Logo">
        <div>
            <div>Original for recipient</div>
            <div>TAX INVOICE</div>
        </div>
    </header>
    <!-- Render invoice details -->
    <div>
        <div class="detail-container">
            <span class="header-text">QR Code</span>
            <div class="qr-image-class">
                <img src="<%= invoice.qrImage %>" alt="QR Code">
            </div>
        </div>
        <hr style="border: 2px solid black;">
        <div class="detail-container">
            <span class="header-text">Seller</span>
            <p>
                <%= invoice.companyName %>
            </p>
            <p class="grid-item-val" style="font-size: small    ;">
                <%= invoice.companyAddress %>
                    <%= invoice.companyCity %>
                        <%= invoice.companyState %> - <%= invoice.companyZip %>
            </p>
            <div class="grid-container-one">
                <div class="grid-item-key">Email:</div>
                <div class="grid-item-val"><EMAIL></div>
                <div class="grid-item-key">CIN:</div>
                <div class="grid-item-val">
                    <%= invoice.cin %>
                </div>
                <div class="grid-item-key">PAN:</div>
                <div class="grid-item-val">
                    <%= invoice.pan %>
                </div>
            </div>
            </br>
            <span class="header-text">Shipped From</span>
            <p>
                <%= invoice.companyName %>
            </p>
            <p class="grid-item-val" style="font-size: small    ;">
                <%= invoice.companyAddress %>
                    <%= invoice.companyCity %>
                        <%= invoice.companyState %> - <%= invoice.companyZip %>
            </p>
            <div class="grid-container-one">
                <div class="grid-item-key">GST:</div>
                <div class="grid-item-val">
                    <%= invoice.gst %>
                </div>
            </div>
        </div>
        <div class="flex-row">
            <div class="detail-container">
                <span class="header-text">Billed To</span>
                <p class="grid-item-val">
                    <%= invoice.customerName %> <br />
                        <p class="grid-item-val">
                            <%= invoice.customerAddress %> <br />
                                <%= invoice.customerCity %> - <%= invoice.customerZip %> <br />
                                        <%= invoice.customerState %> <br />

                        </p>
                        <p class="grid-item-val"> Place Of Supply: <%= invoice.companyState %>
                        </p>
                        <p class="grid-item-val"> State Code: <%= invoice.stateNo %>
                        </p>
                </p>
            </div>
            <div class="detail-container">
                <span class="header-text">Shipped To</span>
                <p class="grid-item-val">
                <p class="grid-item-val">
                    <%= invoice.shippingAddress %> <br />
                        <%= invoice.shippingCity %> - <%= invoice.shippingZip %> <br />
                                <%= invoice.customerState %> <br />

                </p>
                <p class="grid-item-val"> Place Of Supply: <%= invoice.shippingState %>
                </p>
                <p class="grid-item-val"> State Code: <%= invoice.stateNo %>
                </p>
                </p>
            </div>

            <div class="detail-container">
                <span class="header-text">Invoice Number</span>
                <p class="grid-item-val">
                <p class="grid-item-val">
                <div class="grid-container-three">
                    <div class="grid-item-key">Invoice Number:</div>
                    <div class="grid-item-val">
                        <%= invoice.invoiceNumber %>
                    </div>
                    <div class="grid-item-key">Invoice Date</div>
                    <div class="grid-item-val">
                        <%= invoice.invoiceDate %>
                    </div>
                    <div class="grid-item-key">Order Date</div>
                    <div class="grid-item-val">
                        <%= invoice.orderDate %>
                    </div>
                </div>
                </p>
                </p>
            </div>

            <div class="detail-container">
                <div class="grid-container-three">
                    <div class="grid-item-key">Portal:</div>
                    <div class="grid-item-val">Shopify</div>
                    <div class="grid-item-key">IRN:</div>
                    <div class="grid-item-val">
                        <%= invoice.irn %>
                    </div>
                    <div class="grid-item-key">Order No:</div>
                    <div class="grid-item-val">
                        <%= invoice.orderNo %>
                    </div>
                    <div class="grid-item-key">Order Date</div>
                    <div class="grid-item-val">
                        <%= invoice.orderDate %>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th style="border-top-left-radius: 10px;">Sr No</th>
                        <th>Description Of Goods or Services</th>
                        <th>SKU</th>
                        <th>HSN code</th>
                        <th>Item Price</th>
                        <th>Qty</th>
                        <th>Total Item Value</th>
                        <th>Item Discount</th>
                        <th>Taxable Value</th>
                        <th>CGST</th>
                        <th>Tax Amt</th>
                        <th>SGST</th>
                        <th style="border-top-right-radius: 10px;">Tax Amt</th>
                    </tr>
                </thead>
                <tbody>
                    <% invoice.items.forEach((item, index)=> { %>
                        <tr class="rows-text">
                            <td>
                                <%= index + 1 %>
                            </td>
                            <td>
                                <%= item.name %>
                            </td>
                            <td>
                                <%= item.sku %>
                            </td>
                            <td>
                                <%= item.hsn %>
                            </td>
                            <td>
                                <%= item.itemPrice %>
                            </td>
                            <td>
                                <%= item.quantity %>
                            </td>
                            <td>
                                <%= item.totalItemValue %>
                            </td>
                            <td>
                                <%= item.discount %>
                            </td>
                            <td>
                                <%= item.taxableValue %>
                            </td>
                            <td>
                                <%= parseFloat(item.gstRate.replace('%', '' )) / 2%>%
                            </td>
                            <td>
                                <%= item.taxAmount / 2 %>
                            </td>
                            <td>
                                <%= parseFloat(item.gstRate.replace('%', '' )) / 2 %>%
                            </td>
                            <td>
                                <%= item.taxAmount / 2 %>
                            </td>
                        </tr>
                        <% }) %>
                </tbody>
            </table>
            <div class="flex-container">
                <div class="grid-container-four">
                    <div class="grid-item-label">Total</div>
                    <div class="grid-item-value">₹<%= invoice.totalAmountWithoutTax %>
                    </div>
                    <div class="grid-item-label">Total Item Taxable Value</div>
                    <div class="grid-item-value">₹<%= invoice.totalAmountTaxable %>
                    </div>
                    <div class="grid-item-label">Total Item Tax</div>
                    <div class="grid-item-value">₹<%= invoice.totalTax %>
                    </div>
                    <div class="grid-item-label">Invoice Value</div>
                    <div class="grid-item-value">₹<%= invoice.payableAmount %>
                    </div>
                    <div class="grid-item-label">Final Invoice Amount</div>
                    <div class="grid-item-value">₹<%= invoice.payableAmount %>
                    </div>
                </div>
            </div>
        </div>
        <footer style="margin-top: 60px;">
            <p class="grid-item-val" style="font-size: medium; ">Amount Chargeable (in words): <%=
                    invoice.totalAmountInWords %>
            </p>
            <div class="right-aligned-container">
                <img src="<%= invoice.signature %>" alt="Signature">
                <div class="text-one">Authorized Signature</div>
                <div class="text-two" style="margin-top: 10px;">For <%= invoice.companyName %>
                </div>
            </div>
            <div class="queries">
                For Any Queries, reach out at <span class="blue-text">
                    <%= invoice.storeNumber %>
                </span> / <span class="blue-text">
                    <%= invoice.storeEmail %>
                </span>
            </div>
            <div class="header-tnc">Terms and Conditions</div>
            <div class="content">
                <p><strong>TERMS OF ORDER</strong></p>
                <ol>
                    <li>I/We agree to make payment in full before the delivery of Goods or Documents.</li>
                    <li>I/We agree that this order will not be binding on the store unless and until it is confirmed by
                        the company Head office.</li>
                    <li>I/We agree not to cancel the order.</li>
                    <li>I/We agree that the delivery time mentioned is indicative. I/We agree that we will not claim
                        compensation nor cancel the order if there is any delay through any cause whatsoever.</li>
                    <li>I/We agree that the prices stated herein are tentative and will be charged as ruling at the time
                        of delivery.</li>
                    <li>I/We agree that the company or its employees shall not be held responsible for any damage caused
                        to the property of the customer during delivery.</li>
                    <li>I/We agree to be charged additionally for redelivery of the order, should I/We fail to take
                        delivery whatsoever.</li>
                    <li>Taxes shall be levied as applicable at the time of delivery.</li>
                    <li>Due to unavoidable circumstances, If there is cancellation of the order, I/We agree to bear the
                        charges for the same.</li>
                    <li>I/We agree to the details of the product mentioned overleaf.</li>
                </ol>
                
                <p class="item-val">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Note : Invoice can be
                    generated from the State other than the State where Order was taken as per the availability of Stock
                </p>
            </div>
        </footer>


    </div>
</body>

</html>