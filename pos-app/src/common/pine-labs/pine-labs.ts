import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import fetch from 'node-fetch';
import { posLogger } from '../logger';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { AppSsmClient } from '../ssm-client/ssm-client';

@Injectable()
export class PineLabsService {
  private merchantId;
  private securityToken;

  private requestUrl;

  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {}

  async init() {
    if (!this.merchantId) {
      const {
        Parameter: { Value: merchantId },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/pine-labs/merchant-id`,
          WithDecryption: true,
        }),
      );
      this.merchantId = merchantId;
    }

    if (!this.securityToken) {
      const {
        Parameter: { Value: securityToken },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/pine-labs/security-token`,
          WithDecryption: true,
        }),
      );
      this.securityToken = securityToken;
    }

    if (!this.requestUrl) {
      const {
        Parameter: { Value: requestUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/pine-labs/app-url`,
          WithDecryption: true,
        }),
      );
      this.requestUrl = requestUrl;
    }
  }

  async createPaymentLink(transactionNumber, amount, posId, storeId, phone) {
    await this.init();

    const url = `${this.requestUrl}/UploadBilledTransaction`;
    const headers = {
      'Content-Type': 'application/json',
    };
    const body = {
      TransactionNumber: transactionNumber,
      SequenceNumber: '1',
      AllowedPaymentMode: '0',
      Amount: amount * 100,
      MerchantID: this.merchantId,
      SecurityToken: this.securityToken,
      StoreID: storeId,
      ClientID: posId,
      AutoCancelDurationInMinutes: 5,
      CustomerMobileNumber: phone,
    };

    posLogger.info('PineLabsService', 'createLink', { url, headers, body });

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(body),
      });

      posLogger.info('PineLabsService', 'createLink', {
        status: response.status,
        statusText: response.statusText,
      });

      const res = await response.json();
      posLogger.info('PineLabsService', 'createLink', { res });

      return res;
    } catch (error) {
      posLogger.error('PineLabsService', 'createLink', {
        error: error.message || error,
      });
      throw error;
    }
  }

  async verifyPaymentLink(plutusTransactionReferenceID, posId, storeId) {
    await this.init();

    const url = `${this.requestUrl}/GetCloudBasedTxnStatus`;
    const headers = {
      'Content-Type': 'application/json',
    };
    const body = {
      MerchantID: this.merchantId,
      SecurityToken: this.securityToken,
      StoreID: storeId,
      ClientID: posId,
      PlutusTransactionReferenceID: plutusTransactionReferenceID,
    };

    posLogger.info('PineLabsService', 'verifyLink', { url, headers, body });

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(body),
      });

      const data = await response.json();

      console.log('PineLabsService', JSON.stringify(data));

      return data;
    } catch (error) {
      posLogger.error('PineLabsService', 'verifyLink', {
        error: error.message || error,
      });
      throw error;
    }
  }

  async cancelPaymentLink(
    plutusTransactionReferenceID,
    amount,
    posId,
    storeId,
  ) {
    await this.init();

    const url = `${this.requestUrl}/CancelTransaction`;
    const headers = {
      'Content-Type': 'application/json',
    };
    const body = {
      MerchantID: this.merchantId,
      SecurityToken: this.securityToken,
      StoreID: storeId,
      ClientID: posId,
      PlutusTransactionReferenceID: plutusTransactionReferenceID,
      Amount: amount * 100,
    };

    posLogger.info('PineLabsService', 'cancelLink', { url, headers, body });

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(body),
      });

      const data = await response.json();
      posLogger.info('PineLabsService', 'cancelLink', { data });
      return data;
    } catch (error) {
      posLogger.error('PineLabsService', 'cancelLink', {
        error: error.message || error,
      });
      throw error;
    }
  }
}
