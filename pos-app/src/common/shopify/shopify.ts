import { ConfigService } from '@nestjs/config';
import { Injectable } from '@nestjs/common';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { AppSsmClient } from '../ssm-client/ssm-client';

@Injectable()
export class AppShopify {
  private SHOPIFY_ACCESS_TOKEN;
  private SHOPIFY_ADMIN_BASE_URL;
  private WISHLIST_API_ACCESS_TOKEN;
  private WISHLIST_URL;

  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {}

  async init() {
    if (!this.SHOPIFY_ACCESS_TOKEN) {
      const {
        Parameter: { Value: SHOPIFY_ACCESS_TOKEN },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/shopify/access-token`,
          WithDecryption: true,
        }),
      );
      this.SHOPIFY_ACCESS_TOKEN = SHOPIFY_ACCESS_TOKEN;
    }

    if (!this.SHOPIFY_ADMIN_BASE_URL) {
      const {
        Parameter: { Value: SHOPIFY_ADMIN_BASE_URL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/shopify/admin-base-url`,
          WithDecryption: true,
        }),
      );
      this.SHOPIFY_ADMIN_BASE_URL = SHOPIFY_ADMIN_BASE_URL;
    }

    if (!this.WISHLIST_API_ACCESS_TOKEN) {
      const {
        Parameter: { Value: WISHLIST_API_ACCESS_TOKEN },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/wishlist/access-token`,
          WithDecryption: true,
        }),
      );
      this.WISHLIST_API_ACCESS_TOKEN = WISHLIST_API_ACCESS_TOKEN;
    }

    if (!this.WISHLIST_URL) {
      const {
        Parameter: { Value: WISHLIST_URL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/wishlist/api-url`,
          WithDecryption: true,
        }),
      );
      this.WISHLIST_URL = WISHLIST_URL;
    }
  }

  async getShopifyAccessToken() {
    await this.init();
    return this.SHOPIFY_ACCESS_TOKEN;
  }

  async getShopifyAdminBaseUrl() {
    await this.init();
    return this.SHOPIFY_ADMIN_BASE_URL;
  }

  async getWishListUrl() {
    await this.init();
    return this.WISHLIST_URL;
  }

  async getWishListAccessToken() {
    await this.init();
    return this.WISHLIST_API_ACCESS_TOKEN;
  }
}
