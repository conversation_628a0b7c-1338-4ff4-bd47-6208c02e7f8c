export const posLogger = {
  // message here can be...
  // request payload if it is first log
  // Query params or query results, if it in between function
  // Response payload if it is last log of the function
  info: (moduleName, functionName, message) => {
    console.log('INFO', moduleName, functionName, {
      message: JSON.stringify(message),
    });
    console.log('\n\n'); //Will remove this when we start sending logs to Kibana or any searchable logs storing platform
  },
  error: (moduleName, functionName, error) => {
    console.log('ERROR', moduleName, functionName, {
      error: JSON.stringify(error),
    });
    console.log('\n\n'); //Will remove this when we start sending logs to Kibana or any searchable logs storing platform
  },
};
