import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EmailService {
  constructor(private configService: ConfigService) {}

  async sendEmailWithPdfAttachment(to: string, subject: string, text: string) {
    const transporter = nodemailer.createTransport({
      host: this.configService.get('EMAIL_SERVICE'),
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: this.configService.get('EMAIL_USER'),
        pass: this.configService.get('EMAIL_PASSWORD'),
      },
    });

    const mailOptions = {
      from: this.configService.get('EMAIL_USER'),
      to: to,
      subject: subject,
      text: text,
      attachments: [
        {
          filename: './invoice.pdf',
          path: './template.pdf',
          contentType: 'application/pdf',
        },
      ],
    };

    return await transporter.sendMail(mailOptions);
  }
}
