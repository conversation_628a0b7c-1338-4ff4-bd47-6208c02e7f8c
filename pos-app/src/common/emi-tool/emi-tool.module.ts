import { Module } from '@nestjs/common';
import { EmiToolService } from './emi-tool.service';
import { ConfigService } from '@nestjs/config';
import { EmiToolController } from './emi-tool.controller';
import { AppDocumentClient } from '../document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from '../ssm-client/ssm-client';

@Module({
  providers: [
    EmiToolService,
    ConfigService,
    AppDocumentClient,
    AppConfigParameters,
    AppSsmClient,
  ],
  controllers: [EmiToolController],
  exports: [EmiToolService],
})
export class EmiToolModule {}
