import { Injectable } from '@nestjs/common';
import fetch from 'node-fetch';
import { ConfigService } from '@nestjs/config';
import { posLogger } from '../logger';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { AppDocumentClient } from '../document-client/document-client';
import { PutCommand, QueryCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ProductStatus } from 'src/common/enum/products';
import { QueryProducts } from 'src/products/lib/list-products';
import { Products } from 'src/products/entities/product.entity';

@Injectable()
export class EmiToolService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  // Utility function to process items in batches
  private async processInBatches<T>(
    items: Products, // Accept Products type
    batchSize: number,
    processFn: (item: any) => Promise<T>,
  ): Promise<T[]> {
    const results: T[] = [];
    // Use items.data, which is the array of products
    for (let i = 0; i < items.data.length; i += batchSize) {
      const batch = items.data.slice(i, i + batchSize);
      posLogger.info('EmiToolService', 'processInBatches', {
        batchNumber: Math.floor(i / batchSize) + 1,
        batchSize: batch.length,
        totalItems: items.data.length,
      });

      try {
        const batchResults = await Promise.all(
          batch.map(async (item) => {
            try {
              return await processFn(item);
            } catch (error) {
              posLogger.error('EmiToolService', 'processInBatches', {
                item,
                error: error.message,
              });
              return null; // Continue with other items in the batch
            }
          }),
        );
        results.push(...batchResults.filter((result) => result !== null));
      } catch (error) {
        posLogger.error('EmiToolService', 'processInBatches', {
          batchNumber: Math.floor(i / batchSize) + 1,
          error: error.message,
        });
      }
    }
    return results;
  }

  async fetchEmiOptions(): Promise<any> {
    try {
      const queryProductsHandler = new QueryProducts(
        this.configService,
        this.ssmClient,
        this.configParameters,
      );

      // Define the array of product categories
      const categories = [
        'Sofas',
        'Chairs',
        'Mattresses',
        'Beds & Bed Frames',
        'Desk',
        'Pillows',
        'Cushions',
        'Bundle',
        'Bedding',
        '',
      ];

      const BATCH_SIZE = 5; // Configurable batch size
      const allResults = [];

      // Iterate over each category
      for (const category of categories) {
        posLogger.info('EmiToolService', 'fetchEmiOptions', {
          category,
          message: `Processing EMI options for category: ${category}`,
        });

        // Fetch product details for the current category
        const productDetails: Products =
          await queryProductsHandler.queryProducts(category);

        // Process products in batches for the current category
        const fetchResults = await this.processInBatches(
          productDetails, // Pass the entire Products object
          BATCH_SIZE,
          async (product) => {
            if (!product?.price || !product?.sk) {
              throw new Error('Invalid product data');
            }

            // Fetch PayU EMI options
            const payuResponse = await fetch(
              'https://api.payu.in/calculateEmi/v2',
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'x-credential-username': `OADt8R`,
                },
                body: JSON.stringify({
                  txnAmount: product?.price,
                  skus: [
                    {
                      skuId: product?.sk,
                      skuAmount: product?.price,
                      quantity: 1,
                    },
                  ],
                }),
              },
            );

            if (!payuResponse.ok) {
              throw new Error(
                `PayU API request failed with status: ${payuResponse.status}`,
              );
            }

            const payuData = await payuResponse.json();

            // Fetch Snapmint EMI options
            const snapmintResponse = await fetch(
              'https://api.snapmint.com/v1/merchants/orders/merchant_plans?merchant_id=4897',
              {
                method: 'GET',
                headers: {
                  Authorization: 'Bearer z0SPhHnE',
                },
              },
            );

            console.log('=========snapmintResponse=========', snapmintResponse);

            if (!snapmintResponse.ok) {
              throw new Error(
                `Snapmint API request failed with status: ${snapmintResponse.status}`,
              );
            }

            const snapmintData = await snapmintResponse.json();

            // console.log('=========snapmintData=========', snapmintData);

            // const snapmintData = {};

            // Store both PayU and Snapmint data
            await this.storeEmiData(
              product?.sk,
              product?.price,
              payuData,
              snapmintData,
            );
            await this.getMinimumEmiAmount(product?.sk, product?.pk);

            return { category, product: product?.sk, payuData, snapmintData };
          },
        );

        // Aggregate results for the category
        allResults.push(...fetchResults);
        posLogger.info('EmiToolService', 'fetchEmiOptions', {
          category,
          totalProductsProcessed: fetchResults.length,
          totalProductsAttempted: productDetails.data.length,
        });
      }

      console.log(
        'EMI options fetched successfully for all categories',
        allResults,
      );
      posLogger.info('EmiToolService', 'fetchEmiOptions', {
        totalCategoriesProcessed: categories.length,
        totalProductsProcessed: allResults.length,
      });

      return allResults;
    } catch (error) {
      console.log(`Error fetching EMI options: ${error.message}`);
      posLogger.error('EmiToolService', 'fetchEmiOptions', {
        error: error.message,
      });
      throw error;
    }
  }

  private removeUndefinedValues(obj: any): any {
    if (obj === null || obj === undefined) return null;
    if (typeof obj !== 'object') return obj;

    const cleanedObj = Array.isArray(obj) ? [] : {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const value = obj[key];
        if (value !== undefined) {
          cleanedObj[key] =
            typeof value === 'object' && value !== null
              ? this.removeUndefinedValues(value)
              : value;
        }
      }
    }
    return cleanedObj;
  }

  private bankCodeToName: { [key: string]: string } = {
    '7': 'AXIS',
    '15': 'HDFC',
    '20': 'CITI',
    '21': 'ICICI',
    '54': 'AMEX',
  };

  async storeEmiData(
    productId: string,
    productPrice: string,
    payuData: any,
    snapmintData: any,
  ): Promise<void> {
    try {
      // Validate PayU data
      if (!payuData?.result || payuData.status !== 1) {
        throw new Error('Invalid PayU EMI data response');
      }

      // Validate Snapmint data
      if (snapmintData.status !== 'Success' || !snapmintData.plans) {
        throw new Error('Invalid Snapmint EMI data response');
      }

      console.log(`Storing EMI data for product ${productId}`, {
        payuData,
        snapmintData,
      });

      // Process PayU plans
      const payuPlans = {};
      for (const bankCode in payuData.result) {
        const bankName = this.bankCodeToName[bankCode] || bankCode;
        payuPlans[bankName] = {};
        const bankPlans = payuData.result[bankCode];
        for (const planCode in bankPlans) {
          const plan = bankPlans[planCode];
          payuPlans[bankName][planCode] = {
            transactionAmount: plan?.transactionAmount || '',
            emiAmount: plan?.emiAmount || '',
            emiInterest: plan?.emiBankInterest || '',
            cardType: plan?.cardType || '',
            tenure: plan?.tenure || '',
            totalPayable: plan?.totalPayableAmount || '',
            bankCode: plan?.bankCode || bankCode,
            bankName: bankName,
            processingFee: plan?.processingFee || '',
            emiValue: plan?.emi_value || '',
            emiInterestPaid: plan?.emi_interest_paid || '',
          };
        }
      }

      const snapmintPlans = {};
      const price = parseFloat(productPrice);
      // Sort plans by tenure in ascending order
      const sortedPlans = snapmintData.plans.sort(
        (a: any, b: any) => a.tenure - b.tenure,
      );
      sortedPlans.forEach((plan: any, index: number) => {
        const downpaymentAmount = Number((price * plan.dp_rate).toFixed(2));
        const remainingAmount = Number((price - downpaymentAmount).toFixed(2));
        const emiAmount = Number((remainingAmount / plan.tenure).toFixed(2));

        snapmintPlans[`Plan ${index + 1}`] = {
          product_price: Number(price.toFixed(2)),
          dp_rate: Number(plan?.dp_rate.toFixed(2)) || 0,
          tenure: plan?.tenure || 0,
          companyName: 'SNAPMINT',
          emi_amount_rate: Number(plan?.emi_amount_rate.toFixed(2)) || 0,
          emi_amount: emiAmount,
          downpaymentAmount: downpaymentAmount,
        };
      });

      const timestamp = new Date().toISOString();
      const rawItem = {
        id: productId,
        sk: `EMI#${timestamp}`,
        payuPlans,
        snapmintPlans,
        createdAt: timestamp,
        updatedAt: timestamp,
      };

      console.log(
        'Raw Item before cleaning:',
        JSON.stringify(rawItem, null, 2),
      );
      const cleanedItem = this.removeUndefinedValues(rawItem);
      console.log('Cleaned Item:', JSON.stringify(cleanedItem, null, 2));

      const EMI_TOOL_TABLE = await this.configParameters.getEmiToolTableName();

      const params = {
        TableName: EMI_TOOL_TABLE,
        Item: cleanedItem,
      };

      const command = new PutCommand(params);
      await this.docClient.createItem(command);
      console.log(`Stored EMI data for product ${productId}`);
    } catch (error) {
      console.log(
        `Error storing EMI data for product ${productId}: ${error.message}`,
      );
      throw error;
    }
  }

  async getAllProducts(): Promise<{ pk: string; sk: string; price: string }[]> {
    try {
      const PRODUCT_TABLE = await this.configParameters.getProductTableName();
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      // Elasticsearch query to fetch only active products
      const response = await esHandler.search({
        index: PRODUCT_TABLE,
        body: {
          size: 1000, // Adjust size as needed
          from: 0,
          query: {
            term: {
              status: ProductStatus.ACTIVE,
            },
          },
        },
      });

      // Process Elasticsearch response
      const items = response.body.hits.hits.map((hit) => hit._source);

      // Filter items by price and map to required format
      const products = items
        .filter((item) => {
          const price = parseFloat(item?.price);
          return price > 10; // Maintain price filter
        })
        .map((item) => ({
          pk: 'PRODUCT', // Always PRODUCT since variants are excluded
          sk: item?.sku || item?.id, // Use sku or fallback to id
          price: item?.price,
        }));

      // Log counts
      const productCount = products.length;

      console.log(`Fetched ${productCount} active products`);

      return products;
    } catch (error) {
      console.log(`Error fetching products: ${error.message}`);
      posLogger.error('EmiToolService', 'getAllProducts', {
        error: error.message,
      });
      throw error;
    }
  }

  async updateProductWithMinEmi(
    productId: string,
    minimumEmi: number,
    pk: string = 'PRODUCT', // Default to PRODUCT, but allow VARIANT
  ): Promise<void> {
    try {
      const PRODUCT_TABLE = await this.configParameters.getProductTableName();

      const params = {
        TableName: PRODUCT_TABLE,
        Key: {
          pk: pk, // Use the provided pk (PRODUCT or VARIANT)
          sk: productId,
        },
        UpdateExpression:
          'SET minimumEmi = :minimumEmi, updatedAt = :updatedAt',
        ExpressionAttributeValues: {
          ':minimumEmi': minimumEmi,
          ':updatedAt': new Date().toISOString(),
        },
      };

      const command = new UpdateCommand(params);
      await this.docClient.updateItem(command);
      posLogger.info('EmiToolService', 'updateProductWithMinEmi', {
        productId,
        pk,
        minimumEmi,
        message: `${pk} updated with minimum EMI amount`,
      });
    } catch (error) {
      posLogger.error('EmiToolService', 'updateProductWithMinEmi', {
        productId,
        pk,
        error: error.message,
      });
      throw error;
    }
  }

  async getMinimumEmiAmount(
    productId: string,
    pk: string = 'PRODUCT',
  ): Promise<void> {
    const EMI_TOOL_TABLE = await this.configParameters.getEmiToolTableName();

    const command = new QueryCommand({
      TableName: EMI_TOOL_TABLE,
      KeyConditionExpression: 'id = :id',
      ExpressionAttributeValues: {
        ':id': productId,
      },
    });
    console.log('====command=====', command);
    const response = await this.docClient.queryItems(command);
    console.log('====response=====', response);
    const items = response.Items || [];
    console.log('=====EMI Table items=====', items);
    let minimumEmi = Number.MAX_VALUE;

    items.forEach((item) => {
      const payuPlans = item.payuPlans || {};

      // PayU plans
      for (const bankCode in payuPlans) {
        const bankPlans = payuPlans[bankCode];
        for (const planCode in bankPlans) {
          const plan = bankPlans[planCode];
          const emiAmount = parseFloat(plan.emiAmount);

          if (!isNaN(emiAmount) && emiAmount > 0 && emiAmount < minimumEmi) {
            minimumEmi = emiAmount;
          }
        }
      }

      // Snapmint plans
      const snapmintPlans = item.snapmintPlans || {};
      for (const planKey in snapmintPlans) {
        const plan = snapmintPlans[planKey];
        const emiAmount = parseFloat(plan.emi_amount) || 2000;

        if (!isNaN(emiAmount) && emiAmount > 0 && emiAmount < minimumEmi) {
          minimumEmi = emiAmount;
        }
      }
      posLogger.info('EmiToolService', 'getMinimumEmiAmount', {
        minimumEmi,
        productId,
        pk,
      });
    });

    // Update the product or variant with the minimum EMI
    await this.updateProductWithMinEmi(productId, minimumEmi, pk);
  }

  async getPayuPlans(productId: string): Promise<any> {
    try {
      const EMI_TOOL_TABLE = await this.configParameters.getEmiToolTableName();

      const command = new QueryCommand({
        TableName: EMI_TOOL_TABLE,
        KeyConditionExpression: 'id = :id',
        ExpressionAttributeValues: {
          ':id': productId,
        },
      });

      const response = await this.docClient.queryItems(command);
      const items = response.Items || [];

      if (items.length === 0) {
        throw new Error(`No EMI data found for product ${productId}`);
      }

      const latestItem = items.sort((a, b) => b.sk.localeCompare(a.sk))[0];
      const payuPlans = latestItem.payuPlans || {};

      // Sort plans for each bank by tenure
      const sortedPayuPlans: { [bankName: string]: any } = {};
      for (const bankName in payuPlans) {
        const bankPlans = payuPlans[bankName];
        const plansArray = Object.keys(bankPlans).map((planCode) => ({
          planCode,
          plan: bankPlans[planCode],
          tenureNumber: parseInt(
            bankPlans[planCode].tenure.match(/(\d+)/)?.[1] || '0',
          ),
        }));

        // Sort by tenureNumber
        plansArray.sort((a, b) => a.tenureNumber - b.tenureNumber);

        // Reconstruct the sorted bank plans object
        sortedPayuPlans[bankName] = {};
        plansArray.forEach(({ planCode, plan }) => {
          sortedPayuPlans[bankName][planCode] = plan;
        });
      }

      posLogger.info('EmiToolService', 'getPayuPlans', {
        productId,
        payuPlansCount: Object.keys(sortedPayuPlans).length,
      });

      return {
        status: 'success',
        productId,
        payuPlans: sortedPayuPlans,
      };
    } catch (error) {
      posLogger.error('EmiToolService', 'getPayuPlans', {
        productId,
        error: error.message,
      });
      return {
        status: 'failed',
        error: error.message,
      };
    }
  }

  async getSnapmintPlans(productId: string): Promise<any> {
    try {
      const EMI_TOOL_TABLE = await this.configParameters.getEmiToolTableName();

      const command = new QueryCommand({
        TableName: EMI_TOOL_TABLE,
        KeyConditionExpression: 'id = :id',
        ExpressionAttributeValues: {
          ':id': productId,
        },
      });

      const response = await this.docClient.queryItems(command);
      const items = response.Items || [];

      if (items.length === 0) {
        throw new Error(`No EMI data found for product ${productId}`);
      }

      const latestItem = items.sort((a, b) => b.sk.localeCompare(a.sk))[0];
      const snapmintPlans = latestItem.snapmintPlans || {};

      posLogger.info('EmiToolService', 'getSnapmintPlans', {
        productId,
        snapmintPlansCount: Object.keys(snapmintPlans).length,
      });

      return {
        productId,
        snapmintPlans,
      };
    } catch (error) {
      posLogger.error('EmiToolService', 'getSnapmintPlans', {
        productId,
        error: error.message,
      });
      return {
        status: 'failed',
        error: error.message,
      };
    }
  }

  async getNoCostEmiPlans(productId: string): Promise<any> {
    try {
      const EMI_TOOL_TABLE = await this.configParameters.getEmiToolTableName();

      const command = new QueryCommand({
        TableName: EMI_TOOL_TABLE,
        KeyConditionExpression: 'id = :id',
        ExpressionAttributeValues: {
          ':id': productId,
        },
      });

      const response = await this.docClient.queryItems(command);
      const items = response.Items || [];

      if (items.length === 0) {
        throw new Error(`No EMI data found for product ${productId}`);
      }

      const latestItem = items.sort((a, b) => b.sk.localeCompare(a.sk))[0];
      const payuPlans = latestItem.payuPlans || {};
      const snapmintPlans = latestItem.snapmintPlans || {};

      const noCostPayuPlans: any = {};
      for (const bankCode in payuPlans) {
        const bankPlans = payuPlans[bankCode];
        for (const planCode in bankPlans) {
          const plan = bankPlans[planCode];

          const emiInterest = parseFloat(plan?.emiInterest) || '';

          // console.log('====emiInterest====', plan);

          if (emiInterest === '') {
            if (!noCostPayuPlans[bankCode]) {
              noCostPayuPlans[bankCode] = {};
            }
            noCostPayuPlans[bankCode][planCode] = plan;
          }
        }
      }

      const noCostSnapmintPlans: any = {};
      for (const planKey in snapmintPlans) {
        const plan = snapmintPlans[planKey];
        console.log('=====inside snapmint plans======', plan);
        const emiRate = parseFloat(plan?.emi_amount_rate);
        if (emiRate > 0) continue;
        const productPrice = parseFloat(plan?.product_price);
        const downpayment = parseFloat(plan?.downpaymentAmount);
        const emiAmount = parseFloat(plan?.emi_amount);
        const tenure = parseInt(plan?.tenure);

        const totalPaid = downpayment + emiAmount * tenure;

        if (Math.abs(totalPaid - productPrice) < 0.01) {
          noCostSnapmintPlans[planKey] = plan;
        }
      }

      posLogger.info('EmiToolService', 'getNoCostEmiPlans', {
        productId,
        payuPlansCount: Object.keys(noCostPayuPlans).length,
        snapmintPlansCount: Object.keys(noCostSnapmintPlans).length,
      });

      return {
        status: 'success',
        productId,
        noCostEmiPlans: {
          payuPlans: noCostPayuPlans,
          snapmintPlans: noCostSnapmintPlans,
        },
      };
    } catch (error) {
      posLogger.error('EmiToolService', 'getNoCostEmiPlans', {
        productId,
        error: error.message,
      });
      return {
        status: 'failed',
        error: error.message,
      };
    }
  }
}
