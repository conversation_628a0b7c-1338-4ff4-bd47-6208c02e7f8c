import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { EmiToolService } from './emi-tool.service';

@Controller('emi-tool')
export class EmiToolController {
  constructor(private readonly emiToolService: EmiToolService) {}
  @Get('products')
  async getAllProducts() {
    return await this.emiToolService.getAllProducts();
  }

  @Get('emi')
  async getEmiOptions() {
    return await this.emiToolService.fetchEmiOptions();
  }

  @Post('min-emi')
  async getMinimumEmiAmount(@Body() body: { data: any[] }): Promise<any> {
    console.log('=====data in min-emi api is=====', body);
    return await this.emiToolService.getMinimumEmiAmount('8343872930104');
  }

  @Get('payu-plans/:productId')
  async getPayuPlans(@Param('productId') productId: string) {
    return await this.emiToolService.getPayuPlans(productId);
  }

  @Get('snapmint-plans/:productId')
  async getSnapmintPlans(@Param('productId') productId: string) {
    return await this.emiToolService.getSnapmintPlans(productId);
  }

  @Get('no-cost-emi-plans/:productId')
  async getNoCostEmiPlans(@Param('productId') productId: string) {
    return await this.emiToolService.getNoCostEmiPlans(productId);
  }
}
