import { Injectable } from '@nestjs/common';
import { PincodesService } from 'src/pincodes/pincodes.service';
import { EInvoice } from './einvoice';
import { AppConfigParameters } from 'src/config/config';
import moment from 'moment';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { GetCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { posLogger } from 'src/common/logger';
import { OrderChangeLog } from 'src/orders/lib/order-change-log';

interface OrderProduct {
  finalItemPrice: number;
  title: string;
  finalItemPriceWithoutGst: number;
  itemDiscount: number;
  gstPercentage: string;
  quantity: number;
  hsn: string;
}

@Injectable()
export class EInvoiceService {
  constructor(
    private readonly eInvoice: EInvoice,
    private readonly pincodesService: PincodesService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  private async fetchStateNo(pincodeId: string): Promise<number> {
    const pincodeData = await this.pincodesService.validatePincode(pincodeId);
    return Number(pincodeData.stateNo);
  }

  private validateOrder(order): void {
    if (!order || !order.id) {
      throw new Error('Invalid order data: Order ID is missing');
    }
    if (!order.billingAddress || !order.billingAddress.pinCode) {
      throw new Error('Invalid order data: Billing address is incomplete');
    }
  }

  private async isIgstApplicable(pinCode, storeID) {
    try {
      const buyersStateNo = await this.fetchStateNo(pinCode);
      const STORE_TABLE = await this.configParameters.getStoreTableName();

      const param = new GetCommand({
        TableName: STORE_TABLE,
        Key: { id: storeID },
      });

      const { Item: data } = await this.docClient.getItem(param);
      console.log('==== isIgstApplicable data is====STEP-1', data);
      if (!data) console.log('====data is not present in dynamo====');
      const isIgstApplicable =
        (data?.billingInfo?.address?.stateNo).toString() !==
        buyersStateNo.toString();
      console.log('====isIgstApplicable====', isIgstApplicable);
      return isIgstApplicable;
    } catch (e) {
      posLogger.error('EInvoice', 'isIgstApplicable', {
        error: e,
      });
      throw e;
    }
  }

  private calculateGSTAmount(product: OrderProduct, isIgstApplicable: boolean) {
    const taxableValue = product?.finalItemPriceWithoutGst;
    const gstRate = parseFloat(product?.gstPercentage) / 100;

    if (isIgstApplicable) {
      const igstAmt = parseFloat((taxableValue * gstRate).toFixed(2));
      return { igstAmt, cgstAmt: 0.0, sgstAmt: 0.0 };
    } else {
      const cgstAmt = parseFloat((taxableValue * (gstRate / 2)).toFixed(2));
      const sgstAmt = parseFloat((taxableValue * (gstRate / 2)).toFixed(2));
      return { igstAmt: 0.0, cgstAmt, sgstAmt };
    }
  }

  private async calculateItemList(
    orderProducts: OrderProduct[],
    isIgstApplicable,
  ) {
    return (
      orderProducts?.map((product, index) => {
        const taxableValue = product?.finalItemPriceWithoutGst;
        const { igstAmt, cgstAmt, sgstAmt } = this.calculateGSTAmount(
          product,
          isIgstApplicable,
        );
        const finalItemPrice = product?.finalItemPrice || 1;
        const orderQuantity = product?.quantity || 1;
        return {
          SlNo: (index + 1).toString() || '234',
          PrdDesc: product?.title || '',
          IsServc: 'N',
          HsnCd: product?.hsn?.split('.').join(''),
          Qty: product?.quantity || 0,
          Unit: 'NOS',
          UnitPrice: finalItemPrice / orderQuantity,
          TotAmt:
            product?.finalItemPriceWithoutGst + product?.itemDiscount || 0,
          Discount: product?.itemDiscount || 0.0,
          AssAmt: parseFloat(taxableValue.toFixed(2)),
          GstRt: parseFloat(product?.gstPercentage || '0'),
          IgstAmt: igstAmt,
          CgstAmt: cgstAmt,
          SgstAmt: sgstAmt,
          TotItemVal: parseFloat(
            (taxableValue + igstAmt + cgstAmt + sgstAmt).toFixed(2),
          ),
        };
      }) || []
    );
  }

  private calculateValDtls(
    orderProducts: OrderProduct[],
    isIgstApplicable,
  ): any {
    let AssVal = 0;
    let CgstVal = 0;
    let SgstVal = 0;
    let IgstVal = 0;

    orderProducts.forEach((product) => {
      const taxableValue = product?.finalItemPriceWithoutGst;
      const { igstAmt, cgstAmt, sgstAmt } = this.calculateGSTAmount(
        product,
        isIgstApplicable,
      );

      AssVal += taxableValue;
      CgstVal += cgstAmt;
      SgstVal += sgstAmt;
      IgstVal += igstAmt;
    });

    const TotInvVal = AssVal + CgstVal + SgstVal + IgstVal;

    return {
      AssVal: parseFloat(AssVal.toFixed(2)),
      CgstVal: parseFloat(CgstVal.toFixed(2)),
      SgstVal: parseFloat(SgstVal.toFixed(2)),
      IgstVal: parseFloat(IgstVal.toFixed(2)),
      TotInvVal: parseFloat(TotInvVal.toFixed(2)),
    };
  }

  private async fetchSellerGSTIN(storeId: string) {
    try {
      const STORE_TABLE = await this.configParameters.getStoreTableName();

      const command = new GetCommand({
        TableName: STORE_TABLE,
        Key: { id: storeId },
      });

      const response = await this.docClient.getItem(command);
      if (!response || !response.Item) {
        throw new Error('Store details not found');
      }

      const storeData = response.Item;
      console.log('====storeData====', storeData);

      return storeData?.gstDetails?.gstNumber || '';
    } catch (error) {
      console.error('Error fetching seller GSTIN:', error);
      throw new Error('Failed to fetch seller GSTIN');
    }
  }

  private async fetchSellerDetails(storeId: string) {
    try {
      const STORE_TABLE = await this.configParameters.getStoreTableName();

      const command = new GetCommand({
        TableName: STORE_TABLE,
        Key: { id: storeId },
      });

      const response = await this.docClient.getItem(command);
      if (!response || !response.Item) {
        throw new Error('Store details not found');
      }

      const storeData = response.Item;

      return {
        Gstin: storeData?.gstDetails?.gstNumber || '',
        LglNm: 'COMFORT GRID TECHNOLOGIES PRIVATE LIMITED',
        Addr1: storeData?.address?.line1.substring(0, 90) || '',
        Loc: storeData?.address?.city || '',
        Pin: parseInt(storeData?.address?.pinCode || '0'),
        Stcd:
          storeData?.address?.stateNo ||
          (await this.fetchStateNo(storeData?.address?.pinCode)),
      };
    } catch (error) {
      console.error('Error fetching seller details:', error);
      throw new Error('Failed to fetch seller details');
    }
  }

  private async fetchShippingDetails(storeId: string, companyName: string) {
    try {
      const STORE_TABLE = await this.configParameters.getStoreTableName();
      const command = new GetCommand({
        TableName: STORE_TABLE,
        Key: { id: storeId },
      });
      const response = await this.docClient.getItem(command);
      if (!response || !response.Item) {
        throw new Error('Store details not found for shipping');
      }
      const storeData = response.Item;

      return {
        LglNm: companyName || '',
        Addr1: storeData?.address?.line1.substring(0, 90) || '',
        Loc: storeData?.address?.city || '',
        Pin: parseInt(storeData?.address?.pinCode || '0'),
        Stcd:
          storeData?.address?.stateNo ||
          (await this.fetchStateNo(storeData?.address?.pinCode)),
      };
    } catch (error) {
      console.error('Error fetching shipping details:', error);
    }
  }

  private async getEINVUSERNAME(gstno) {
    const gstinMap = new Map([
      ['36AAICC4845L1Z9', 'API_CGTPL_TLG'],
      ['33AAICC4845L1ZF', 'API_CGTPL_POSTN'],
      ['09AAICC4845L1Z6', 'API_CGTPL_POSUP'],
      ['24AAICC4845L1ZE', 'API_CGTPL_POSGJ'],
      ['07AAICC4845L1ZA', 'API_CGTPL_POSDL'],
      ['32AAICC4845L1ZH', 'API_CGTPL_POSKL'],
      ['18AAICC4845L1Z7', 'API_CGTPL_POSAS'],
      ['23AAICC4845L1ZG', 'API_CGTPL_POSMP'],
      ['21AAICC4845L1ZK', 'API_CGTPL_POSOD'],
      ['37AAICC4845L1Z7', 'API_CGTPL_POSAP'],
      ['08AAICC4845L1Z8', 'API_CGTPL_POSRJ'],
      ['03AAICC4845L1ZI', 'API_CGTPL_POSPB'],
      ['30AAICC4845L1ZL', 'API_CGTPL_POSGOA'],
      ['05AAICC4845L1ZE', 'API_CGTPL_POSUTK'],
      ['10AAICC4845L1ZN', 'API_CGTPL_POSBR'],
      ['27AAICC4845L1Z8', 'API_CGTPL_EASY1'],
      ['29AAICC4845L2Z3', 'API_CGTPL_EASYKT1'],
      ['06AAICC4845L1ZC', 'API_CGTPL_EASYHR1'],
      ['19AAICC4845L1Z5', 'API_CGTPL_EASYWB1'],
    ]);
    return gstinMap.get(gstno);
  }

  async generateEInvoiceForGstOrders(order) {
    this.validateOrder(order);

    console.log('======order in generateEInvoiceForGstOrders=====', order);

    try {
      const configValues = await this.eInvoice.getConfigValues();
      const { CDKey, EFUserName, EFPassword, EInvPassword, ApiUrl } =
        configValues;

      const date = new Date(order?.createdAt || Date.now());
      const formattedDate = `${String(date.getDate()).padStart(2, '0')}/${String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;
      const stateNo = await this.fetchStateNo(order.billingAddress.pinCode);
      const isIgstApplicable = await this.isIgstApplicable(
        order.billingAddress.pinCode,
        order.storeId,
      );
      const GSTINNO = await this.fetchSellerGSTIN(order?.storeId);
      const EInvUser = await this.getEINVUSERNAME(GSTINNO);

      const data = {
        CDKey,
        EFUserName,
        EFPassword,
        EInvUserName: EInvUser,
        EInvPassword,
        GSTIN: GSTINNO,
        TranDtls: {
          SupTyp: 'B2B',
        },
        DocDtls: {
          Typ: 'INV',
          No: order.id || '',
          Dt: formattedDate,
        },
        SellerDtls: await this.fetchSellerDetails(order?.storeId),
        BuyerDtls: {
          Gstin: order?.gstDetails?.gstNumber || '',
          LglNm: order?.gstDetails?.companyName || '',
          Addr1: order?.billingAddress?.line1 || '',
          Loc: order?.billingAddress?.city || '',
          Pin: order?.billingAddress?.pinCode || 0,
          Pos: stateNo || '96',
          Stcd: stateNo || '96',
        },
        ShipDtls: await this.fetchShippingDetails(
          order?.storeId,
          order?.gstDetails?.companyName,
        ),
        ItemList: await this.calculateItemList(
          order?.orderProducts,
          isIgstApplicable,
        ),
        ValDtls: this.calculateValDtls(order?.orderProducts, isIgstApplicable),
      };

      console.log('====json data is======', data);

      const response = await fetch(ApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }

      const responseData = await response.json();

      if (!responseData || !responseData[0]?.Irn) {
        console.log('===Webtel Api failed with response===', responseData);
        throw new Error('Invalid API response: Missing IRN');
      }

      const IRN = responseData[0]?.Irn || '';
      const signedInvoice = responseData[0]?.SignedInvoice || '';
      const ORDER_TABLE = await this.configParameters.getOrderTableName();

      const command = new UpdateCommand({
        TableName: ORDER_TABLE,
        Key: { id: order.id },
        UpdateExpression:
          'SET #IRN = :IRN, #signedInvoice = :signedInvoice, updatedAt = :updatedAt',
        ExpressionAttributeNames: {
          '#IRN': 'IRN',
          '#signedInvoice': 'signedInvoice',
        },
        ExpressionAttributeValues: {
          ':IRN': IRN,
          ':signedInvoice': signedInvoice,
          ':updatedAt': moment().toISOString(),
        },
        ConditionExpression: 'attribute_exists(id)',
        ReturnValues: 'ALL_NEW',
      });

      console.log('====Dynamodb Command is====', command);

      const responseValue = await this.docClient.updateItem(command);

      console.log('====Dynamodb responseValue is====', responseValue);

      if (responseValue) {
        console.log(
          '=====Webtel API call successful with response=====',
          responseValue,
        );

        const { Attributes } = responseValue;
        const orderChangeLog = new OrderChangeLog(
          this.configParameters,
          this.docClient,
        );
        orderChangeLog.log(Attributes);

        return { success: true, data: responseValue };
      } else {
        console.error('Error response from API:', responseValue);
        return { success: false, error: responseValue };
      }
    } catch (error) {
      console.error('Error generating IRN', error);
      return { success: false, error: error.message };
    }
  }

  // async printEInvoiceForGstOrders: any{

  // }
}
