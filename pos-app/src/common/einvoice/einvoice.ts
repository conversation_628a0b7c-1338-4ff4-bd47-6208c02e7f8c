import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { AppSsmClient } from '../ssm-client/ssm-client';

@Injectable()
export class EInvoice {
  private configValues: {
    CDKey?: string;
    EFUserName?: string;
    EFPassword?: string;
    EInvUserName?: string;
    EInvPassword?: string;
    GSTIN?: string;
    ApiUrl?: string;
  } = {};

  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {}

  private async fetchConfigValue(key: string): Promise<string> {
    const {
      Parameter: { Value },
    } = await this.ssmClient.getSSMParamByKey(
      new GetParameterCommand({
        Name: `/${this.configService.get('STACK_NAME')}/einvoice/${key}`,
        WithDecryption: true,
      }),
    );
    return Value;
  }

  async init() {
    console.log(
      '====EInvoice=====stack name',
      this.configService.get('STACK_NAME'),
    );
    const keys = [
      'CDKey',
      'EFUserName',
      'EFPassword',
      'EInvPassword',
      'GSTIN',
      'ApiUrl',
    ];

    for (const key of keys) {
      if (!this.configValues[key]) {
        this.configValues[key] = await this.fetchConfigValue(key);
        console.log('====EInvoice=====config values', this.configValues);
      }
    }
  }

  async getConfigValues() {
    await this.init();
    return this.configValues;
  }
}
