import { Modu<PERSON> } from '@nestjs/common';
import { SsmClientModule } from '../ssm-client/ssm-client.module';
import { PincodesService } from 'src/pincodes/pincodes.service';
import { EInvoiceService } from './einvoice.service';
import { EInvoice } from './einvoice';
import { ConfigParametersModule } from 'src/config/config.module';
import { DocumentClientModule } from '../document-client/document-client.module';

@Module({
  imports: [DocumentClientModule, SsmClientModule, ConfigParametersModule],
  providers: [EInvoice, EInvoiceService, PincodesService],
  exports: [EInvoiceService],
})
export class EInvoiceModule {}
