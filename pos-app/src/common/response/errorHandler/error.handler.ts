import { ErrorInput, ErrorResponse } from './error.handler.dto';

export class <PERSON>rrorHandler {
  async getErrorResponse(error: ErrorInput): Promise<ErrorResponse> {
    return {
      status: error.code,
      message: error.message,
      success: false,
    };
  }
}

export class CustomError extends Error {
  statusCode: number;
  code: string;
  scope: string | undefined;

  constructor(message: string, statusCode: number, scope?: string) {
    super(message);
    this.statusCode = statusCode;
    this.scope = scope || undefined;
    Object.setPrototypeOf(this, CustomError.prototype);
  }
}

export class EnhancedError extends Error {
  statusCode: number;
  source: string;

  constructor(message: string, statusCode: number, source?: string) {
    super(message);
    this.statusCode = statusCode;
    this.source = source || 'INTERNAL_SERVER';
    this.name = 'EnhancedError';
    Object.setPrototypeOf(this, EnhancedError.prototype);
  }
}