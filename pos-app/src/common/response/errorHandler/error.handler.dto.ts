import { HttpStatus } from '@nestjs/common';
import { Field, registerEnumType } from '@nestjs/graphql';

registerEnumType(HttpStatus, { name: 'HttpStatus' });
export class ErrorInput {
  @Field(() => String)
  message: string;

  @Field(() => HttpStatus)
  code: number;
}

export class ErrorResponse {
  @Field(() => String)
  message: string;

  @Field(() => HttpStatus)
  status: number;

  @Field(() => Boolean)
  success: boolean;
}
