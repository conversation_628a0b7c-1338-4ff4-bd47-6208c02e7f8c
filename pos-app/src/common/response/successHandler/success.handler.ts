import { HttpStatus } from '@nestjs/common';

import { SuccessInput, SuccessResponse } from './success.handler.dto';

export class SuccessHandler {
  getSuccessResponse(response: SuccessInput): SuccessResponse {
    switch (response?.code) {
      case 200:
        return {
          message: 'Ok',
          data: response?.data,
          status: HttpStatus.OK,
          success: true,
        };
      case 201:
        return {
          message: 'Data Created successfully',
          data: response?.data,
          status: HttpStatus.CREATED,
          success: true,
        };
      case 204:
        return {
          message: 'There is no content to return',
          status: HttpStatus.NO_CONTENT,
          success: true,
          data: null,
        };
      default:
        return {
          message: 'Default',
          status: HttpStatus.NO_CONTENT,
          success: true,
          data: null,
        };
    }
  }
}
