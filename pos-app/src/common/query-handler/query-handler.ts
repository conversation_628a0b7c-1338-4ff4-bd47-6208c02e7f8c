import { ElasticClient } from 'src/utils/elasticsearch.config';

export class QueryHandlerService {
  private esHandler: ElasticClient;

  constructor(private esClient: ElasticClient) {
    this.esHandler = esClient;
  }

  async query({
    index,
    limit: size = 100,
    page = 1,
    filter,
    sort,
    nextToken: nt,
  }) {
    let searchAfter;
    if (nt) {
      searchAfter = nt
        ? JSON.parse(Buffer.from(nt, 'base64').toString('ascii'))
        : undefined;
    }
    console.log('searchAfter :>> ', filter);

    const searchParams = {
      index,
      size,
      from: (page - 1) * size,
      body: {
        version: false,
        track_total_hits: true,
        search_after: searchAfter,
        query: filter,
        sort: [...sort],
      },
    };
    console.log('query :>> ', JSON.stringify(searchParams));

    // Executing the OpenSearch request
    const { body } = await this.esHandler.search(searchParams);

    const { hits } = body;
    const { hits: results = [], total } = hits;
    const lastResult = results[results.length - 1];
    const nextToken =
      lastResult && lastResult.sort
        ? Buffer.from(JSON.stringify(lastResult.sort), 'ascii').toString(
            'base64',
          )
        : null;

    return {
      page,
      pageSize: size,
      totalPages: Math.ceil(total.value / size),
      total: total.value,
      items: results.map(({ _source }) => _source),
      nextToken: nextToken,
    };
  }

  async queryAll({ index, filter, nextToken: nT, sortItems }) {
    const { items, nextToken } = await this.query({
      index,
      filter,
      sort: sortItems,
      nextToken: nT,
      limit: 9999,
    });
    console.log('itemsnt :>> ', nT);

    if (nextToken) {
      const nextItems = await this.queryAll({
        index,
        filter,
        nextToken,
        sortItems,
      });
      return [...items, ...nextItems];
    }

    return items;
  }
}
