// import { CognitoIdentityProviderClient } from '@aws-sdk/client-cognito-identity-provider';
// import credentials from '../../utils/aws-init';
// import config from 'src/config/config';

// const appConfig = config[process.env.NODE_ENV];
// const { REGION } = appConfig;

// let cognitoClient;
// Make sure to execute locally, use `export NODE_ENV=local` command on terminal before starting server
// For the dev & prod envs, credentials object is not required, it will directly use Fargate Task Execution Role
// if (process.env.NODE_ENV === 'local') {
//   cognitoClient = new CognitoIdentityProviderClient({
//     credentials,
//     region: REGION,
//   });
// } else {
//   cognitoClient = new CognitoIdentityProviderClient({
//     region: REGION,
//   });
// }
// export default cognitoClient;
