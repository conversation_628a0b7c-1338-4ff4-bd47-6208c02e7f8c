// import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
// import credentials from '../../utils/aws-init';
// import config from 'src/config/config';

// const appConfig = config[process.env.NODE_ENV];
// const { REGION } = appConfig;

// let dynamoDBClient;
// // Make sure to execute locally, use `export NODE_ENV=local` command on terminal before starting server
// // For the dev & prod envs, credentials object is not required, it will directly use Fargate Task Execution Role
// if (process.env.NODE_ENV === 'local') {
//   dynamoDBClient = new DynamoDBClient({
//     credentials,
//     region: REGION,
//   });
// } else {
//   dynamoDBClient = new DynamoDBClient({
//     region: REGION,
//   });
// }
// export default dynamoDBClient;
