import { Injectable } from '@nestjs/common';
import fetch from 'node-fetch';
import { ConfigService } from '@nestjs/config';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { generateSHA256Hash } from '../helper/hash-generator';
import { posLogger } from '../logger';
import { TransactionType } from '../enum/payment';

@Injectable()
export class MswipeService {
  private requestUrl: string;
  private verificationUrl: string;
  private requestClientCode: string;
  private verificationClientCode: string;
  private userId: string;
  private password: string;
  private salt: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly ssmClient: AppSsmClient,
  ) {}

  private async init() {
    if (!this.requestUrl) {
      const {
        Parameter: { Value: requestUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/mswipe/request-url`,
          WithDecryption: true,
        }),
      );
      this.requestUrl = requestUrl;
    }

    if (!this.verificationUrl) {
      const {
        Parameter: { Value: verificationUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/mswipe/verification-url`,
          WithDecryption: true,
        }),
      );
      this.verificationUrl = verificationUrl;
    }

    if (!this.requestClientCode) {
      const {
        Parameter: { Value: requestClientCode },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/mswipe/request-client-code`,
          WithDecryption: true,
        }),
      );
      this.requestClientCode = requestClientCode;
    }

    if (!this.verificationClientCode) {
      const {
        Parameter: { Value: verificationClientCode },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/mswipe/verification-client-code`,
          WithDecryption: true,
        }),
      );
      this.verificationClientCode = verificationClientCode;
    }

    if (!this.userId) {
      const {
        Parameter: { Value: userId },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/mswipe/user-id`,
          WithDecryption: true,
        }),
      );
      this.userId = userId;
    }

    if (!this.password) {
      const {
        Parameter: { Value: password },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/mswipe/password`,
          WithDecryption: true,
        }),
      );
      this.password = password;
    }
    if (!this.salt) {
      const {
        Parameter: { Value: salt },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/mswipe/salt`,
          WithDecryption: true,
        }),
      );
      this.salt = salt;
    }
  }

  async createPayment(
    amount: number,
    tid: string,
    invoiceNo: string,
    txntype: TransactionType,
    orderId: string,
    transactionId: string,
  ): Promise<any> {
    await this.init();
    const key = amount * 100 + txntype + this.requestClientCode + this.salt;
    const mac = generateSHA256Hash(key);
    posLogger.info('MswipeService', 'createPayment', {
      amount,
      tid,
      invoiceNo,
      txntype,
    });

    const body = {
      amount: (amount * 100).toString(),
      clientcode: this.requestClientCode,
      mac,
      notes: txntype,
      exnotes1: orderId,
      exnotes2: transactionId,
      txntype,
      storeid: '',
      tid,
      invoiceNo,
    };

    posLogger.info('MswipeService', 'createPayment', {
      url: `${this.requestUrl}/generatetoken`,
      body,
    });

    try {
      const response = await fetch(`${this.requestUrl}/generatetoken`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });

      const data = await response.json();
      posLogger.info('MswipeService', 'createPayment', {
        status: response.status,
        statusText: response.statusText,
        data,
      });

      return data;
    } catch (error) {
      posLogger.error('MswipeService', 'createPayment', {
        error: error.message || error,
      });
      throw error;
    }
  }

  async cancelPayment(token: string, invoiceNo: string): Promise<any> {
    await this.init();
    const body = {
      token,
      clientcode: this.requestClientCode,
      invoiceno: invoiceNo,
    };

    posLogger.info('MswipeService', 'cancelPayment', {
      url: `${this.requestUrl}/canceltoken`,
      headers: { 'Content-Type': 'application/json' },
      body,
    });

    try {
      const response = await fetch(`${this.requestUrl}/canceltoken`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      posLogger.info('MswipeService', 'cancelPayment', {
        status: response.status,
        statusText: response.statusText,
        data,
      });

      return data;
    } catch (error) {
      posLogger.error('MswipeService', 'cancelPayment', {
        error: error.message || error,
      });
      throw error;
    }
  }

  async verifyPayment(invoiceNo: string): Promise<any> {
    await this.init();
    const body = {
      client_code: this.verificationClientCode,
      mer_invoiceno: invoiceNo,
    };

    try {
      const response = await fetch(this.verificationUrl, {
        method: 'POST',
        headers: {
          UserID: this.userId,
          Password: this.password,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();
      posLogger.info('MswipeService', 'verifyPayment', {
        status: response.status,
        statusText: response.statusText,
        data,
      });

      console.log('MswipeService', JSON.stringify(data));

      return data;
    } catch (error) {
      posLogger.error('MswipeService', 'verifyPayment', {
        error: error.message || error,
      });
      throw error;
    }
  }
}
