import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { posLogger } from '../logger';
export class RazorPayPosModule {
  private appKey;
  private appURL;
  private appUsername;

  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {}

  async init() {
    if (!this.appKey) {
      const {
        Parameter: { Value: appKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/razorpay-pos/app-key`,
          WithDecryption: true,
        }),
      );
      this.appKey = appKey;
    }

    if (!this.appURL) {
      const {
        Parameter: { Value: appURL },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/razorpay-pos/app-url`,
          WithDecryption: true,
        }),
      );
      this.appURL = appURL;
    }

    if (!this.appUsername) {
      const {
        Parameter: { Value: appUsername },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/razorpay-pos/app-username`,
          WithDecryption: true,
        }),
      );
      this.appUsername = appUsername;
    }
  }

  async createPaymentRequest(amount, orderId, transactionID, deviceId, mode) {
    posLogger.info('RazorPayPos', 'createPaymentRequest', {
      input: { amount, orderId, transactionID, deviceId, mode },
    });
    try {
      await this.init();

      posLogger.info('RazorPayPos', 'createPaymentRequest', {
        msg: 'Parameters from SSM for RazorPayPOS',
        appKey: this.appKey,
        appUsername: this.appUsername,
        appURL: this.appURL,
      });

      const response = await fetch(`${this.appURL}/pay`, {
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'POST',
        body: JSON.stringify({
          appKey: this.appKey,
          username: this.appUsername,
          amount: `${amount}`,
          externalRefNumber2: orderId,
          externalRefNumber: transactionID,
          pushTo: {
            // deviceId: "1492218286|ezetap_android"
            deviceId: `${deviceId}|ezetap_android`,
          },
          // mode: "CARD,CASH,UPI"
          mode,
        }),
      }).then(async (response) => {
        return response.json();
      });
      console.log('createPaymentRequest RazorPayPos :>> ', response);
      return response;
    } catch (e) {
      posLogger.error('RazorPayPos', 'createPaymentRequest', { error: { e } });
      throw e;
    }
  }

  async fetchTransactionStatus(p2pRequestId) {
    posLogger.info('RazorPayPos', 'fetchTransactionStatus', {
      input: { p2pRequestId },
    });
    try {
      await this.init();
      const response = await fetch(`${this.appURL}/status`, {
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'POST',
        body: JSON.stringify({
          appKey: this.appKey,
          username: this.appUsername,
          origP2pRequestId: p2pRequestId,
        }),
      }).then(async (response) => {
        const data = await response.json();
        console.log(
          'fetchTransactionStatus RazorPayPos ',
          JSON.stringify(data),
        );
        return data;
      });

      return response;
    } catch (e) {
      posLogger.error('RazorPayPos', 'fetchTransactionStatus', {
        error: { e },
      });
      throw e;
    }
  }

  async cancelTransactionRequest(p2pRequestId) {
    posLogger.info('RazorPayPos', 'cancelTransactionRequest', {
      input: { p2pRequestId },
    });
    try {
      await this.init();
      const response = await fetch(`${this.appURL}/cancel`, {
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'POST',
        body: JSON.stringify({
          appKey: this.appKey,
          username: this.appUsername,
          origP2pRequestId: p2pRequestId,
        }),
      }).then(async (response) => {
        return response.json();
      });
      return response;
    } catch (e) {
      posLogger.error('RazorPayPos', 'fetchTransactionStatus', {
        error: { e },
      });
      throw e;
    }
  }
}
