import {
  AdminCreateUserCommand,
  AdminDeleteUserCommand,
  AdminDisableUserCommand,
  AdminEnableUserCommand,
  AdminUpdateUserAttributesCommand,
  CognitoIdentityProviderClient,
} from '@aws-sdk/client-cognito-identity-provider';
import { Injectable } from '@nestjs/common';
import { AppAwsSdkCredentials } from '../aws-sdk-credentials';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from '../ssm-client/ssm-client';

@Injectable()
export class AppCognitoClient {
  private client;

  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {
    // In case of running locally, use shared profile
    if (process.env.NODE_ENV === 'local') {
      const credentials = new AppAwsSdkCredentials(this.configService);
      this.client = new CognitoIdentityProviderClient({
        region: this.configService.get('REGION'),
        credentials: credentials.getCredentials(),
      });

      // In case of fargate, only use Region
    } else {
      this.client = new CognitoIdentityProviderClient({
        region: this.configService.get('REGION'),
      });
    }
  }

  async adminCreateUser(createCommand: AdminCreateUserCommand) {
    return this.client.send(createCommand);
  }

  async adminDeleteUser(deleteCommand: AdminDeleteUserCommand) {
    return this.client.send(deleteCommand);
  }

  async adminUpdateUserAttributes(
    updateCommand: AdminUpdateUserAttributesCommand,
  ) {
    return this.client.send(updateCommand);
  }

  async adminDisableUser(disableCommand: AdminDisableUserCommand) {
    return this.client.send(disableCommand);
  }

  async adminEnableUser(enableCommand: AdminEnableUserCommand) {
    return this.client.send(enableCommand);
  }
}
