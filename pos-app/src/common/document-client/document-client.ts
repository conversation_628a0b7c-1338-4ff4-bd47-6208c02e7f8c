import { DynamoDBClient, QueryCommand } from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  PutCommand,
  ScanCommand,
  UpdateCommand,
  GetCommand,
  DeleteCommand,
  Batch<PERSON>riteCommand,
  BatchGetCommand,
  TransactWriteCommand,
} from '@aws-sdk/lib-dynamodb';
import { Injectable } from '@nestjs/common';
import { AppAwsSdkCredentials } from '../aws-sdk-credentials';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppDocumentClient {
  private client;
  private docClient;

  constructor(private configService: ConfigService) {
    // In case of running locally, use shared profile
    if (process.env.NODE_ENV === 'local') {
      const credentials = new AppAwsSdkCredentials(this.configService);
      this.client = new DynamoDBClient({
        region: this.configService.get('REGION'),
        credentials: credentials.getCredentials(),
      });

      // In case of fargate, only use Region
    } else {
      this.client = new DynamoDBClient({
        region: this.configService.get('REGION'),
      });
    }

    // Finally reusable docClient
    this.docClient = DynamoDBDocumentClient.from(this.client);
  }

  async createItem(putCommand: PutCommand) {
    return this.docClient.send(putCommand);
  }

  async getItem(getItem: GetCommand) {
    return this.docClient.send(getItem);
  }

  async scanItems(scanCommand: ScanCommand) {
    return this.docClient.send(scanCommand);
  }

  async queryItems(queryCommand: QueryCommand) {
    return this.docClient.send(queryCommand);
  }

  async updateItem(updateCommand: UpdateCommand) {
    return this.docClient.send(updateCommand);
  }

  async deleteItem(deleteCommand: DeleteCommand) {
    return this.docClient.send(deleteCommand);
  }
  async createItems(batchCommand: BatchWriteCommand) {
    return this.docClient.send(batchCommand);
  }

  async batchGetItems(batchGetCommand: BatchGetCommand) {
    return this.docClient.send(batchGetCommand);
  }

  async transactWriteItems(transactWriteItems: TransactWriteCommand) {
    return this.docClient.send(transactWriteItems);
  }
}
