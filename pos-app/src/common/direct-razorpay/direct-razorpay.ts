import Razorpay from 'razorpay';
import { posLogger } from '../logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from '../document-client/document-client';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { Injectable } from '@nestjs/common';
import { PaymentStatus } from '../enum/payment';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { CustomError } from '../response/errorHandler/error.handler';
import moment from 'moment';

@Injectable()
export class DirectRazorPay {
  private RazorPayClient;
  private successCallbackUrl;

  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
  ) {}

  async init() {
    if (!this.RazorPayClient) {
      const {
        Parameter: { Value: RazorPayAPIKey },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/direct-razorpay/api-key`,
          WithDecryption: true,
        }),
      );

      const {
        Parameter: { Value: RazorPayAPISecret },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/direct-razorpay/api-secret`,
          WithDecryption: true,
        }),
      );

      this.RazorPayClient = new Razorpay({
        key_id: RazorPayAPIKey,
        key_secret: RazorPayAPISecret,
      });
    }

    if (!this.successCallbackUrl) {
      const {
        Parameter: { Value: SuccessCallbackUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/direct-razorpay/callback-url`,
          WithDecryption: true,
        }),
      );

      this.successCallbackUrl = SuccessCallbackUrl;
    }
  }

  async createRazorPayPaymentLink(
    orderId,
    transactionId,
    transactionAmount,
    phone,
    isFirstTimeVisit,
  ) {
    await this.init();
    posLogger.info('Direct-razorPay', 'createRazorPayPaymentLink', {
      input: orderId,
    });
    try {
      const encodedQuery = btoa(
        JSON.stringify({ id: orderId, isFirstTimeVisit }),
      );

      const paymentLinkRequest = {
        amount: transactionAmount * 100,
        currency: 'INR',
        accept_partial: false,
        expire_by: moment().add(15, 'h').unix(),
        customer: {
          contact: phone,
        },
        notify: {
          sms: true,
        },
        notes: {
          orderId,
          transactionId,
        },
        callback_url: `${this.successCallbackUrl}?q=${encodedQuery}`,
        callback_method: 'get',
      };
      posLogger.info('Direct-razorPay', 'createRazorPayPaymentLink', {
        msg: 'Razor pay create payment link request payload',
        payload: paymentLinkRequest,
      });

      const linkResult =
        await this.RazorPayClient.paymentLink.create(paymentLinkRequest);

      posLogger.info('Direct-razorPay', 'createRazorPayPaymentLink', {
        msg: 'Razor pay create payment response',
        response: linkResult,
      });

      if (!linkResult) throw new Error('Razorpay request is failed.');

      return {
        referenceId: linkResult.id,
        amount: linkResult.amount,
        ...linkResult,
        status: PaymentStatus.CREATED,
      };
    } catch (e) {
      posLogger.error('Direct-razorPay', 'createRazorPayPaymentLink', {
        error: e,
      });
      throw e;
    }
  }

  async fetchRazorPayLinkByLinkId(linkID) {
    await this.init();
    posLogger.info('Direct-razorPay', 'fetchRazorPayLinkByLinkId', {
      input: linkID,
    });
    try {
      const response = await this.RazorPayClient.paymentLink.fetch(linkID);

      const {
        id: referenceId,
        status,
        amount,
        payments,
        amount_paid,
      } = response;

      console.log(
        'fetchRazorPayLinkByLinkId RazorPayLink',
        JSON.stringify(response),
      );

      if (!referenceId || !status || !amount)
        throw new Error('Razorpay request is failed.');

      return {
        referenceId,
        status: status.toUpperCase(),
        amount,
        payments,
        amount_paid,
      };
    } catch (e) {
      posLogger.error('Direct-razorPay', 'fetchRazorPayLinkByLinkId', {
        error: e,
      });
      throw e;
    }
  }

  async cancelRazorPayLinkByLinkId(linkID) {
    posLogger.info('Direct-razorPay', 'cancelRazorPayLinkByLinkId', {
      input: linkID,
    });
    try {
      await this.init();
      const {
        id: referenceId,
        status,
        amount,
      } = await this.RazorPayClient.paymentLink.cancel(linkID);

      if (!referenceId || !status || !amount)
        throw new CustomError('Razorpay request is failed.', 400);

      return { referenceId, status, amount };
    } catch (e) {
      posLogger.error('Direct-razorPay', 'cancelRazorPayLinkByLinkId', {
        error: e,
      });
      throw e;
    }
  }

  async reNotifyRazorPayLink(linkID) {
    posLogger.info('Direct-razorPay', 'reNotifyRazorPay', {
      input: linkID,
    });
    try {
      const { success } = await this.RazorPayClient.paymentLink.notifyBy(
        linkID,
        'sms',
      );

      if (!success) throw new Error('Razorpay request is failed.');

      return { success };
    } catch (e) {
      posLogger.error('Direct-razorPay', 'cancelRazorPayLinkByLinkId', {
        error: e,
      });
      throw e;
    }
  }
  async getActualPaidRazorPayAmount(payment_id: string): Promise<any> {
    await this.init();
    try {
      const username = this.RazorPayClient.key_id;
      const password = this.RazorPayClient.key_secret;

      if (!username || !password) {
        throw new Error(
          'Razorpay credentials are missing in environment variables.',
        );
      }

      const auth = Buffer.from(`${username}:${password}`).toString('base64');

      const url = `https://api.razorpay.com/v1/payments/${payment_id}/?expand[]=offers`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Authorization: `Basic ${auth}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      console.log('verify razopyay res', data);
      return data;
    } catch (e) {
      posLogger.error('Payment', 'verifyRazorpayPaidAmount', e);
      throw e;
    }
  }
}
