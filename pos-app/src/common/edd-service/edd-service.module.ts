import { Module } from '@nestjs/common';
import { EddService } from './edd-service';
import { ConfigService } from '@nestjs/config';
import { EddController } from './edd-service.controller';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';

@Module({
  providers: [
    EddService,
    ConfigService,
    AppDocumentClient,
    AppSsmClient,
    AppConfigParameters,
  ],
  controllers: [EddController],
  exports: [EddService],
})
export class EddServiceModule {}
