import { Body, Controller, Post } from '@nestjs/common';
import { EddService } from './edd-service';

@Controller('edd')
export class EddController {
  constructor(private readonly eddService: EddService) {}

  @Post('get-edd')
  async predictDelivery(
    @Body() body: { data: any[]; drop_pincode: string; storeId: string },
  ) {
    console.log('=====predictDelivery======', body);

    // Call the service with the entire payload once
    const response = await this.eddService.getEstimatedDeliveryDate(body);

    console.log(response.apiType, '::::::: Type API');
    console.log(JSON.stringify(response.data), '::::::: data');
    console.log(response.cartMinDate, '::::::: cartMinDate');
    console.log(response.cartMaxDate, '::::::: cartMaxDate');
    console.log(response.cartDispatchDate, '::::::: dispatchDate');
    console.log(response.requestId, '::::::: requestId');

    return {
      data: response.data,
      cartDispatchDate: response?.cartDispatchDate,
      cartMinDate: response?.cartMinDate,
      cartMaxDate: response?.cartMaxDate,
      cartIsXpressDelivery: response?.cartIsXpressDelivery,
      cartXpressDeliveryHours: response?.cartXpressDeliveryHours || '',
      header: response.requestId,
      posFallbackEdd: response?.posFallbackEdd || undefined,
      apiType: response.apiType || 'old',
    };
  }
}
