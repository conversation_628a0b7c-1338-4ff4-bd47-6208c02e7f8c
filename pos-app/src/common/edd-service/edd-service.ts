import { Injectable } from '@nestjs/common';
import fetch from 'node-fetch';
import { ConfigService } from '@nestjs/config';
import { posLogger } from '../logger';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';

@Injectable()
export class EddService {
  private eddUrl: string =
    'https://apis.thesleepcompany.in/edd/edd/get-pre-order-edd';

  constructor(
    private readonly configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getEDDFailureResponse(body: any): Promise<any> {
    const now = new Date();

    const minDate = new Date(now);

    const newMinDate = new Date(minDate.setDate(minDate.getDate() + 5));
    const newMaxDate = new Date(minDate.setDate(minDate.getDate() + 3));

    function formatDate(date) {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${year}-${month}-${day}`;
    }

    const formattedMinDate = formatDate(newMinDate);
    const formattedMaxDate = formatDate(newMaxDate);

    return {
      data: body.data.map((item: any) => {
        return {
          response: {
            ...item,
            dispatchDate: formattedMinDate,
            newMinDate: formattedMinDate,
            newMaxDate: formattedMaxDate,
          },
        };
      }),
      cartDispatchDate: formattedMinDate,
      cartMinDate: formattedMinDate,
      cartMaxDate: formattedMaxDate,
      cartIsXpressDelivery: false,
      cartXpressDeliveryHours: null,
      posFallbackEdd: true,
    };
  }

  async getEstimatedDeliveryDate(body: any): Promise<any> {
    const headers = {
      'Content-Type': 'application/json',
    };

    posLogger.info('EddService', 'getEstimatedDeliveryDate', {
      url: this.eddUrl,
      body,
    });

    let finalBody = body;
    let tempUrl = this.eddUrl;

    const getGlobalConfigurationHandler = new GetGlobalConfiguration(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    const { value: NEW_EDD_LIVE_STORES } =
      await getGlobalConfigurationHandler.getGlobalConfiguration(
        'NEW_EDD_LIVE_STORES',
      );

    const newEddLiveStores = NEW_EDD_LIVE_STORES?.split(',').map((id: string) =>
      id.trim(),
    );

    console.log(newEddLiveStores, 'newEddLiveStores');

    let isAllNewEddProduct = false;

    if (
      (newEddLiveStores.length && newEddLiveStores.includes(body.storeId)) ||
      newEddLiveStores[0] === 'all'
    ) {
      const { value: NEW_EDD_PRODUCT_IDS } =
        await getGlobalConfigurationHandler.getGlobalConfiguration(
          'NEW_EDD_PRODUCT_IDS',
        );

      const newEddProductIds = NEW_EDD_PRODUCT_IDS?.split(',').map(
        (id: string) => id.trim(),
      );

      console.log(newEddProductIds, 'newEddProductIds');

      isAllNewEddProduct = body.data.every((item: any) =>
        newEddProductIds.includes(item.product_id),
      );

      if (!isAllNewEddProduct) {
        tempUrl = 'https://apis.thesleepcompany.in/edd/edd/get-edd';
        finalBody = {
          data: finalBody.data.map((item: any) => ({
            ...item,
            product_id: item.product_id,
            variant_ids: [item.variant_id],
            drop_pincode: finalBody.drop_pincode,
          })),
        };
      }
    } else {
      tempUrl = 'https://apis.thesleepcompany.in/edd/edd/get-edd';
      finalBody = {
        data: finalBody.data.map((item: any) => ({
          ...item,
          product_id: item.product_id,
          variant_ids: [item.variant_id],
          drop_pincode: finalBody.drop_pincode,
        })),
      };
    }

    console.log(isAllNewEddProduct, 'isAllNewEddProduct');
    console.log(tempUrl, 'tempUrl');
    console.log(JSON.stringify(finalBody), 'finalBody');

    try {
      const response = await fetch(tempUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(finalBody),
      });

      console.log('=======api response is=====', response);

      const data = await response.json();
      console.log('=======api response data is=====', data);
      posLogger.info('EddService', 'getEstimatedDeliveryDate', {
        status: response.status,
        statusText: response.statusText,
        data,
      });
      if (response.ok) {
        if (isAllNewEddProduct) {
          return {
            ...data,
            apiType: 'new',
            requestId: response.headers.get('X-Request-ID') || undefined,
          };
        } else {
          return {
            apiType: 'old',
            cartMinDate: data.cartMinDate,
            cartMaxDate: data.cartMaxDate,
            cartDispatchDate: undefined,
            cartIsXpressDelivery: data.cartIsXpressDelivery,
            cartXpressDeliveryHours: data.cartXpressDeliveryHours,
            data: data.data.map((item: any) => {
              return {
                ...item,
                response: {
                  ...item.response,
                  ...item.response.data[0],
                },
              };
            }),
          };
        }
      } else {
        return await this.getEDDFailureResponse(body);
      }
    } catch (error) {
      posLogger.error('EddService', 'getEstimatedDeliveryDate', {
        error: error.message || error,
      });
      return await this.getEDDFailureResponse(body);
    }
  }
}
