import moment from 'moment';
import { posLogger } from '../logger';
import { CustomError } from '../response/errorHandler/error.handler';

export const filterFormatter = async (
  sortingFilter,
  searchingFilter,
  sortingFilterType,
  filter?,
) => {
  //Extract Search fields and their corresponding values
  const termSearchArray = await termSearchFieldFormatter(
    filter?.termSearchFields,
    searchingFilter,
  );
  //Extract Search fields and their corresponding values
  const textSearchArray = await textSearchFieldFormatter(
    filter?.textSearchFields,
    searchingFilter,
  );

  //Extract the SortField and it's corresponding value
  const sortObject = await sortFormatter(
    filter?.sortBy,
    sortingFilter,
    sortingFilterType,
  );

  return { searchArray: [...termSearchArray, ...textSearchArray], sortObject };
};

export const termSearchFieldFormatter = async (
  searchFields,
  searchingFilter,
) => {
  if (searchFields) {
    const searchArray: Array<Record<string, any>> = [];

    await Promise.all(
      Object.entries(searchFields || {}).map(
        ([fieldName, fieldValue]: [string, string]) => {
          if (searchingFilter?.has(fieldName)) {
            if (searchingFilter.get(fieldName) === 'date') {
              const gteDate = moment(fieldValue).startOf('day').toISOString();
              const lteDate = moment(fieldValue).endOf('day').toISOString();

              const obj = {
                range: {
                  [fieldName]: {
                    gte: gteDate,
                    lte: lteDate,
                  },
                },
              };
              searchArray.push(obj);
            } else if (
              fieldName === 'storeId' ||
              fieldName === 'requestedStoreId'
            ) {
              const obj = {
                term: {
                  [`${fieldName}.keyword`]: fieldValue,
                },
              };
              searchArray.push(obj);
            } else {
              const obj = {
                term: {
                  [`${fieldName}`]: fieldValue,
                },
              };
              searchArray.push(obj);
            }
          } else {
            return;
          }
        },
      ),
    );

    posLogger.info('filter', 'termSearchFieldFormatter', searchArray);
    return searchArray;
  } else {
    return [];
  }
};

export const textSearchFieldFormatter = async (
  searchFields,
  searchingFilter,
) => {
  if (searchFields) {
    const searchArray: Array<Record<string, any>> = [];

    await Promise.all(
      Object.entries(searchFields || {}).map(
        ([fieldName, fieldValue]: [string, string]) => {
          if (fieldValue === undefined || fieldValue.trim() === '') {
            return;
          }

          if (searchingFilter?.has(fieldName)) {
            if (searchingFilter.get(fieldName) === 'arrayString') {
              const obj = {
                match: {
                  [`${fieldName}.keyword`]: fieldValue,
                },
              };
              searchArray.push(obj);
            } else if (fieldName === 'customerphone') {
              const obj = {
                match_phrase_prefix: {
                  'customer.phone': fieldValue,
                },
              };
              searchArray.push(obj);
            } else if (fieldName === 'customeremail') {
              const obj = {
                match_phrase_prefix: {
                  'customer.email': fieldValue,
                },
              };
              searchArray.push(obj);
            } else {
              const obj = {
                match_phrase_prefix: {
                  [fieldName]: fieldValue,
                },
              };
              searchArray.push(obj);
            }
          } else {
            throw new CustomError(`${fieldName} doesn't exist`, 400);
          }
        },
      ),
    );

    posLogger.info('filter', 'textSearchFieldFormatter', searchArray);
    return searchArray;
  } else {
    return [];
  }
};

export const sortFormatter = async (sort, sortingFilter, sortingFilterType) => {
  if (sort) {
    const [[sortField, sortOrder]]: Array<[string, string]> = Object.entries(
      sort || {},
    );

    const sortOrderLowerCase = sortOrder ? sortOrder.toLowerCase() : undefined;

    //throw error if sortOrder doesn't exist
    if (
      !!sortOrder?.length &&
      sortOrderLowerCase !== 'desc' &&
      sortOrderLowerCase !== 'asc'
    ) {
      throw new CustomError(`Please enter valid value for ${sortField}`, 400);
    }

    if (sortingFilter.has(sortField)) {
      type SortObjectType = {
        [key: string]: {
          order: string; // Order is a static value with two possible options
          unmapped_type: string;
        };
      };

      const sortObject: SortObjectType = {};

      if (sortField && sortOrder) {
        const fieldType = sortingFilter.get(sortField);
        const filterType = sortingFilterType[sortField];

        if (fieldType === 'string') {
          if (sortField !== 'id') {
            sortObject[`${sortField}`] = {
              order: sortOrder,
              unmapped_type: `${filterType}`,
            };
          } else if (sortField == 'id') {
            sortObject[`${sortField}.keyword`] = {
              order: sortOrder,
              unmapped_type: `${filterType}`,
            };
          }
        }
      }

      return sortObject;
    } else {
      //throw error if sortField doesn't exist
      throw new CustomError(`${sortField} doesn't exist`, 400);
    }
  } else {
    return {};
  }
};
