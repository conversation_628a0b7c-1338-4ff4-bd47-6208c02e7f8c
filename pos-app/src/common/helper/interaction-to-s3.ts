import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from '../document-client/document-client';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { AppS3Client } from '../s3-client/s3-client';

export class S3Interactions {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,

    private s3Client: AppS3Client,
  ) {}

  async uploadToS3(file, filePath, contentType, bucket = undefined) {
    const command = new PutObjectCommand({
      Bucket: bucket || `${this.configService.get('STACK_NAME')}-public-bucket`,
      Key: filePath,
      Body: file,
      ContentType: contentType,
    });

    try {
      return await this.s3Client.putS3Object(command);
    } catch (err) {
      console.error('Error uploading PDF to S3:', err);
    }
  }
}
