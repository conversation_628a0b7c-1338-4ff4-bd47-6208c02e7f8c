export const optInCustomer = async (phone: string) => {
  try {
    fetch(
      `https://media.smsgupshup.com/GatewayAPI/rest?method=OPT_IN&format=json&userid=2000233295&password=t6yZNm2q&phone_number=${phone.slice(1, phone.length)}&v=1.1&auth_scheme=plain&channel=WHATSAPP`,
      {
        method: 'POST',
      },
    )
      .then((res) => res.json())
      .then(() => {})
      .catch((error) => {
        console.error(
          'Error opting in customer:',
          error.response?.data || error.message,
        );
      });
  } catch (error) {
    console.log(error);
  }
};
