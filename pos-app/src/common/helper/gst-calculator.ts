export const getGstRateCalculator = (product) => {
  const {
    productType: orderProductType,
    price,
    finalItemPrice,
    productTitle: orderProductTitle,
    product_type,
    title,
  } = product;
  const productType = orderProductType || product_type;
  const productTitle = orderProductTitle || title;
  let gstRate = '1.18',
    gstPercentage = '18%';
  let finalItemPriceWithoutGst = finalItemPrice / 1.18;

  if (productType === 'Bedding') {
    if (price > 1000 || productTitle === 'All Weather Comforter (Exchange)') {
      gstRate = '1.12';
      gstPercentage = '12%';
      finalItemPriceWithoutGst = finalItemPrice / 1.12;
    } else {
      gstRate = '1.05';
      gstPercentage = '5%';
      finalItemPriceWithoutGst = finalItemPrice / 1.05;
    }
  }
  // const gstAmount = (finalItemPrice * quantity * parseFloat(gstRate)) / 100;
  const gstAmount = finalItemPrice - finalItemPriceWithoutGst;
  return {
    gstRate,
    gstPercentage,
    gstAmount: Number(Number(gstAmount).toFixed(2)),
    finalItemPriceWithoutGst: Number(
      Number(finalItemPriceWithoutGst).toFixed(2),
    ),
  };
};
