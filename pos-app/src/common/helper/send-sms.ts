export const sendSms = async (payload: any) => {
  try {
    console.log(payload, '::: payload');
    const response = await fetch(
      'https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/send_sms',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template_attributes: payload,
        }),
      },
    );

    if (!response.ok) {
      throw new Error(`Error: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending SMS:', error);
    throw error;
  }
};
