import moment from 'moment';
import { CouponStatus } from '../enum/coupons';

export const getPriceRuleStatus = (
  start,
  end,
  isActive = true,
  isEditingQuotation = false,
) => {
  if (!isActive && !isEditingQuotation) {
    return CouponStatus.DEACTIVATED;
  }

  const date = moment().toDate();
  const startDate = moment(start).toDate();
  const endDate = end ? moment(end).toDate() : null;

  if (endDate && endDate < date && !isEditingQuotation) {
    return CouponStatus.EXPIRED;
  }
  if (startDate > date) {
    return CouponStatus.SCHEDULED;
  }
  return CouponStatus.ACTIVE;
};
