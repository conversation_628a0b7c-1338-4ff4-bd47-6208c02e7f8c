import { posLogger } from '../logger';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { AppDocumentClient } from '../document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { GetCampaignCoupon } from 'src/campaign-coupons/lib/get-campaign-coupon';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';

export class CouponUsageHandler {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async handleCouponUsage(
    orderData: {
      promotionalCode?: string;
      campaignCode?: string;
      storeId?: string;
    },
    operation: 'inc' | 'dec',
  ) {
    posLogger.info('CouponUsageHandler', 'handleCouponUsage', {
      orderData,
      operation,
    });

    const { promotionalCode, campaignCode, storeId } = orderData;
    const increment = operation === 'inc' ? 1 : -1;

    try {
      if (promotionalCode) {
        const COUPONS_TABLE = await this.configParameters.getCouponTableName();

        const command = new UpdateCommand({
          TableName: COUPONS_TABLE,
          Key: { pk: 'COUPON', sk: promotionalCode },
          UpdateExpression:
            'SET usage_count = usage_count + :usage_count, updatedAt = :updatedAt',
          ExpressionAttributeValues: {
            ':updatedAt': moment().toISOString(),
            ':usage_count': increment,
          },
          ConditionExpression: 'attribute_exists(code)',
        });

        await this.docClient.updateItem(command);
      }

      if (campaignCode) {
        const getCampaignHandler = new GetCampaignCoupon(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );

        const coupon = await getCampaignHandler.getCampaignCoupon(campaignCode);
        const { usageLimitForEachStore, storeWiseUsageCount = [] } = coupon;

        let command;
        if (usageLimitForEachStore) {
          const newUsageCount = [...storeWiseUsageCount];

          const existingStoreIndex = newUsageCount.findIndex(
            (usageCountPerStore) => usageCountPerStore.storeId === storeId,
          );

          if (existingStoreIndex !== -1) {
            newUsageCount[existingStoreIndex].usageCount += increment;
          } else if (operation === 'inc') {
            newUsageCount.push({ storeId, usageCount: 1 });
          }

          command = new UpdateCommand({
            TableName: await this.configParameters.getCampaignCouponTableName(),
            Key: { code: campaignCode },
            UpdateExpression:
              'SET storeWiseUsageCount=:storeWiseUsageCount , updatedAt = :updatedAt',
            ExpressionAttributeValues: {
              ':updatedAt': moment().toISOString(),
              ':storeWiseUsageCount': newUsageCount,
            },
            ConditionExpression: 'attribute_exists(code)',
          });
        } else {
          command = new UpdateCommand({
            TableName: await this.configParameters.getCampaignCouponTableName(),
            Key: { code: campaignCode },
            UpdateExpression:
              'SET usageCount = usageCount + :usageCount, updatedAt = :updatedAt',
            ExpressionAttributeValues: {
              ':updatedAt': moment().toISOString(),
              ':usageCount': increment,
            },
            ConditionExpression: 'attribute_exists(code)',
          });
        }

        await this.docClient.updateItem(command);
      }
    } catch (error) {
      posLogger.error('CouponUsageHandler', 'handleCouponUsage', { error });
      throw error;
    }
  }
}
