export const getBuyXGetYProducts = (
  applicableProducts: any,
  applicableItemsY: any,
  cartItems: any,
  buyXQuantity: any,
): any => {
  let remainingCart = [...cartItems];
  const xProducts = [];
  let yProducts = [];

  const applicableXItems = remainingCart.filter((cartItem: any) =>
    applicableProducts.some(
      (product: any) =>
        product.productId === cartItem.productId &&
        (!product.variantId || product.variantId === cartItem.variantId),
    ),
  );

  let accumulatedXQuantity = 0;
  for (const item of applicableXItems) {
    if (accumulatedXQuantity < buyXQuantity) {
      const neededQuantity = Math.min(
        buyXQuantity - accumulatedXQuantity,
        item.quantity,
      );
      xProducts.push({ ...item, quantity: neededQuantity });
      accumulatedXQuantity += neededQuantity;

      const index = remainingCart.findIndex(
        (cartItem: any) => cartItem === item,
      );
      if (index !== -1) {
        remainingCart[index] = {
          ...remainingCart[index],
          quantity: remainingCart[index].quantity - neededQuantity,
        };
      }
    }
  }

  remainingCart = remainingCart.filter((item: any) => item.quantity > 0);

  if (accumulatedXQuantity >= buyXQuantity) {
    const applicableYItems = remainingCart.filter((cartItem: any) =>
      applicableItemsY.some(
        (product: any) =>
          product.productId === cartItem.productId &&
          (!product.variantId || product.variantId === cartItem.variantId),
      ),
    );
    yProducts = applicableYItems.map((item: any) => ({ ...item }));
  }

  return { xProducts, yProducts };
};
