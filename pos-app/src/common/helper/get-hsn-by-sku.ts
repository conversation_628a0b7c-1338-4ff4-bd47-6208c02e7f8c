import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from '../document-client/document-client';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { AppConfigParameters } from 'src/config/config';

export class GetHsnBySku {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getHsnBySku(sku: string) {
    const HSN_TABLE = await this.configParameters.getHSNCodeTableName();

    const param = new GetCommand({
      TableName: HSN_TABLE,
      Key: {
        id: `${sku}`,
      },
    });

    const { Item = {} } = await this.docClient.getItem(param);

    return Item?.hsnCode || '';
  }
}
