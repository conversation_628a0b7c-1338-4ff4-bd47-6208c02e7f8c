import csv from 'csvto<PERSON><PERSON>';
export const csvToArray = async (filePath, getHandler) => {
  try {
    const csvData = await (
      await getHandler.getObject(filePath)
    )?.Body?.transformToString();

    return new Promise((resolve, reject) => {
      const results = [];

      csv({})
        .fromString(csvData)
        .subscribe(
          (row) => {
            results.push(row);
          },
          (error) => reject(error),
          () => resolve(results),
        );
    });
  } catch (error) {
    throw new Error(`Failed to process CSV: ${error.message}`);
  }
};
