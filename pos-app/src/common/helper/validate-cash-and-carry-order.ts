import { QueryInventoryTrackingByIds } from 'src/inventory-tracking/lib/list-inventory-tracking-by-ids';
import { isSellableInventoryExist } from 'src/orders/helpers/is-sellable-inventory-exist';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { posLogger } from '../logger';

export class ValidateCashAndCarryOrder {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validate(
    storeId: string,
    orderProducts: { sku: string; quantity: number }[],
  ) {
    try {
      const inventoryTrackingHandler = new QueryInventoryTrackingByIds(
        this.configService,
        this.ssmClient,
        this.docClient,
        this.configParameters,
      );

      const { data: inventoryTrackingData } =
        await inventoryTrackingHandler.queryInventoryTrackingByIds(
          storeId,
          orderProducts.map((product) => product.sku),
        );

      const orderedItems = orderProducts.map((orderProduct) => ({
        id: orderProduct.sku,
        quantity: orderProduct.quantity,
      }));

      const isCashAndCarryPossible = isSellableInventoryExist(
        orderedItems,
        inventoryTrackingData,
      );

      if (!isCashAndCarryPossible) {
        throw new CustomError(
          'Inventory not available for cash and carry',
          400,
        );
      }
      posLogger.info('Inventory', 'CashAndCarry validated', {
        input: { storeId, orderProducts },
      });
    } catch (e) {
      throw new CustomError(
        e.message || 'Inventory validation for cash and carry failed',
        400,
      );
    }
  }
}
