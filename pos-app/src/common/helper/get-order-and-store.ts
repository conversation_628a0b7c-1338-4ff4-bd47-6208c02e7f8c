import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from '../document-client/document-client';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { GetOrder } from 'src/orders/lib/get-order';
import { OrderData } from 'src/orders/entities/order.entity';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { StoreData } from 'src/stores/entities/store.entity';
import { PdfService } from '../ejs/nodemailer.service';
import { GetQuotation } from 'src/quotations/lib/get-quotation';
import { QuotationData } from 'src/quotations/entities/quotation.entity';
import { CustomError } from '../response/errorHandler/error.handler';
import { TemplateType } from '../enum/template-type';
import { AppS3Client } from '../s3-client/s3-client';
import { AppConfigParameters } from 'src/config/config';

export class GetOrderAndStoreAndSendPdf {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private s3Client: AppS3Client,
    private configParameters: AppConfigParameters,
  ) {}

  async getOrderAndStoreAndSendPdf(orderId: string, type: string) {
    const getOrderHandler = new GetOrder(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const order: OrderData = await getOrderHandler.getOrder(orderId);

    const { storeId, customer } = order;

    if (!customer?.email && type == 'EMAIL') {
      throw new CustomError('Cannot find customer email', 404);
    }

    if (!customer?.phone && type == 'WHATSAPP') {
      throw new CustomError('Cannot find customer phone number', 404);
    }

    const getStorehandler = new GetStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const store: StoreData = await getStorehandler.getStore(storeId);

    const pdfService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
      order,
      store,
    );
    const message = await pdfService.generateAndSendFile(
      TemplateType.INVOICE,
      'application/pdf',
      'pdf',
      type,
    );
    return message;
  }

  async getQuotationAndStoreAndSendPdf(quotationId: string, type: string) {
    const getQuotationHandler = new GetQuotation(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const quotation: QuotationData =
      await getQuotationHandler.getQuotation(quotationId);

    const { storeId, customer } = quotation;

    if (!customer?.email && type == 'EMAIL') {
      throw new CustomError('Cannot find customer email', 404);
    }

    if (!customer?.phone && type == 'WHATSAPP') {
      throw new CustomError('Cannot find customer phone number', 404);
    }

    const getStorehandler = new GetStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const store: StoreData = await getStorehandler.getStore(storeId);

    const finalQuotation = {
      ...quotation,
      orderProducts: quotation.quotationProducts,
    };

    const pdfService = new PdfService(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.s3Client,
      this.configParameters,
      finalQuotation,
      store,
    );
    const message = await pdfService.generateAndSendFile(
      TemplateType.QUOTATION,
      'application/pdf',
      'pdf',
      type,
    );

    return message;
  }
}
