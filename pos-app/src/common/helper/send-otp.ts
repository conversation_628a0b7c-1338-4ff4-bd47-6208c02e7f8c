import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';

export class OtpService {
  private static otpURL: string | null = null;

  constructor(
    private readonly ssmClient: AppSsmClient,
    private readonly configService: ConfigService,
  ) {}

  async init(): Promise<void> {
    if (!OtpService.otpURL) {
      const {
        Parameter: { Value },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/otp/url`,
          WithDecryption: true,
        }),
      );
      OtpService.otpURL = Value;
    }
  }

  async sendSms(templateAttributes: {
    templateName: string;
    OTP: string;
    phoneNo: string;
  }): Promise<any> {
    try {
      await this.init();
      if (!OtpService.otpURL) {
        throw new Error('OTP URL is not initialized. Call init() first.');
      }
      console.log(
        '${OtpService.otpURL}/send_sms',
        `${OtpService.otpURL}/send_sms`,
      );
      console.log('templateAttributes', templateAttributes);

      const response = await fetch(`${OtpService.otpURL}/send_sms`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template_attributes: templateAttributes,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error sending SMS:', error);
      throw error;
    }
  }
}
