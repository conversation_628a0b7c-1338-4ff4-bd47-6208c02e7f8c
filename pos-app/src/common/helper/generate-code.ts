import { posLogger } from '../logger';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { GetLocalConfiguration } from 'src/local-configurations/lib/get-local-configuration';
import { AppDocumentClient } from '../document-client/document-client';
import { UpdateLocalConfiguration } from 'src/local-configurations/lib/update-local-configuration';
import moment from 'moment';
import { AppConfigParameters } from 'src/config/config';

export class GenerateCode {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  getFinancialYear() {
    const currentMonth = moment().month() + 1;
    const currentYear = moment().year();

    let financialYearStart: number, financialYearEnd: number;
    if (currentMonth < 4) {
      financialYearStart = currentYear - 1;
      financialYearEnd = currentYear;
    } else {
      financialYearStart = currentYear;
      financialYearEnd = currentYear + 1;
    }

    // Format the financial year as "YY-YY"
    const financialYear =
      financialYearStart.toString().substr(-2) +
      financialYearEnd.toString().substr(-2);

    return financialYear;
  }

  async generateCode(storeId: string, key: string, prefix: string) {
    posLogger.info('GenerateCode', 'generateCode', {
      input: { storeId, key, prefix },
    });
    try {
      const getConfigHandler = new GetLocalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const localConfig = await getConfigHandler.getLocalConfiguration(
        storeId,
        key,
      );

      const value = Number(localConfig?.value || 0) + 1;

      const updateConfigHandler = new UpdateLocalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      await updateConfigHandler.updateLocalConfiguration({
        storeId,
        key,
        value: value,
      });

      // const randomNumber = Math.floor(Math.random() * (value * 10));
      const financialYear = this.getFinancialYear();

      const code = `${prefix}${financialYear}-${String(value).padStart(4, '0')}`;
      // + String(randomNumber).padStart(4, '0')
      return code;
    } catch (e) {
      posLogger.error('generateCode', 'generateCode', { error: e });
      throw e;
    }
  }
}
