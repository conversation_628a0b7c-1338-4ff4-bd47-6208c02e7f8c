import CryptoJS from 'crypto-js';
import { OrderData } from '../../orders/entities/order.entity';

export const generateCheckSumHash = (
  merchandId: string,
  token: string,
  orderPayload: OrderData,
  transactionId: string,
  transactionAmount: string,
  fullName,
) => {
  if (!orderPayload || !orderPayload.customer) {
    throw new Error('Invalid order data');
  }

  const text = `${merchandId}|${transactionId}|${transactionAmount}|${fullName}|${orderPayload.customer.email}|${token}`;

  const res = CryptoJS.SHA512(text).toString(CryptoJS.enc.Hex);
  return res;
};
