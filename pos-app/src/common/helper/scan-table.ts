import { AppDocumentClient } from '../document-client/document-client';
import { ScanCommand } from '@aws-sdk/lib-dynamodb';

export class ScanDB {
  constructor(private docClient: AppDocumentClient) {}

  async scanTable(TableName: string, nextToken?: Record<string, any>) {
    try {
      const command = new ScanCommand({
        TableName,
        ExclusiveStartKey: nextToken,
      });

      const { LastEvaluatedKey, Items = [] } =
        await this.docClient.scanItems(command);

      if (LastEvaluatedKey) {
        const entities = await this.scanTable(TableName, LastEvaluatedKey);
        return [...Items, ...entities];
      }

      return Items;
    } catch (error) {
      throw error;
    }
  }
}
