import { CustomError } from '../response/errorHandler/error.handler';

export const sendMessageOnWhatsApp = async (payload) => {
  try {
    const url = `https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/sendwhatsapp`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'UBQJgJrPzW67r32ydSn9H8APs1VcZSkN3LKjoukp',
      },
      body: JSON.stringify({
        template_attributes: {
          ...payload,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    throw new CustomError(
      `Failed to send message on whatsapp: ${error.message}`,
      500,
    );
  }
};
