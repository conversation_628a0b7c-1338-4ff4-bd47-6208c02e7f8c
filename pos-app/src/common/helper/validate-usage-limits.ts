import { CustomError } from '../response/errorHandler/error.handler';

export const validateCouponUsageLimit = (
  usageLimit,
  usageCount,
  isPromo = false,
) => {
  if (usageLimit && usageCount && usageCount >= usageLimit) {
    throw new CustomError(
      `${isPromo ? 'Promotional' : 'Campaign'} Coupon usage limit exceeded.`,
      400,
    );
  } else {
    return { isValidated: true };
  }
};
