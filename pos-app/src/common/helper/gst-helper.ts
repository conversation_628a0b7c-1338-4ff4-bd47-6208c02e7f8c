export function getHighestGST(data) {
  if (!data.length) return null;

  const gstValues = data.map((item) => {
    const gst = item.gstPercentage;
    if (typeof gst === 'string') {
      return parseFloat(gst.replace('%', '')) || 0;
    }
    return gst || 0;
  });

  const maxGst = Math.max(...gstValues);

  return maxGst;
}

export function splitDeliveryCharges(totalValue, gstPercentage) {
  const taxableValue = totalValue / (1 + gstPercentage / 100);
  const tax = totalValue - taxableValue;

  return {
    deliveryChargesTaxableValue: Math.round(taxableValue),
    deliveryChargesTax: Math.round(tax),
  };
}
