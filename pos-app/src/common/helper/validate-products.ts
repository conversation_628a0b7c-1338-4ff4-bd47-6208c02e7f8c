import { GetProduct } from 'src/products/lib/get-product';
import { GetVariant } from 'src/products/lib/get-variant';
import { ProductStatus } from '../enum/products';
import { AppDocumentClient } from '../document-client/document-client';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { CustomError } from '../response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from '../shopify/shopify';
import { posLogger } from '../logger';
import axios from 'axios';

export class ValidateProducts {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async checkShopifyInventory(variant: any) {
    const shopifyClient = new AppShopify(this.configService, this.ssmClient);

    const [SHOPIFY_ADMIN_BASE_URL, SHOPIFY_ACCESS_TOKEN] = await Promise.all([
      shopifyClient.getShopifyAdminBaseUrl(),
      shopifyClient.getShopifyAccessToken(), // Ensure this method fetches your private app token
    ]);

    let graphqlEndpoint: string;
    if (SHOPIFY_ADMIN_BASE_URL.includes('/admin/api/')) {
      const shopBaseUrl = SHOPIFY_ADMIN_BASE_URL.split('/admin/api/')[0];
      graphqlEndpoint = `${shopBaseUrl}/admin/api/2023-10/graphql.json`;
    } else {
      graphqlEndpoint = `${SHOPIFY_ADMIN_BASE_URL}/graphql.json`;
    }

    posLogger.info(
      'ValidateProducts',
      'checkShopifyInventory',
      `Constructed GraphQL endpoint: ${graphqlEndpoint}`,
    );

    const query = `
      query getVariant($id: ID!) {
        productVariant(id: $id) {
          id
          title
          sku
          inventoryQuantity
          price
        }
      }
    `;

    const shopifyVariantId = `gid://shopify/ProductVariant/${variant.id}`;

    try {
      const response = await axios.post(
        graphqlEndpoint,
        {
          query,
          variables: { id: shopifyVariantId },
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
          },
        },
      );

      if (response.data.errors) {
        posLogger.error(
          'ValidateProducts',
          'checkShopifyInventory',
          `GraphQL errors: ${JSON.stringify(response.data.errors)}`,
        );
        throw new Error('Failed to fetch Shopify variant data');
      }

      const variantData = response.data.data.productVariant;

      if (!variantData || variantData.inventoryQuantity <= 0) {
        throw new Error(
          `Variant ${variant?.productTitle} - ${variant?.variantTitle} is out of stock`,
        );
      }

      return true;
    } catch (error) {
      posLogger.error(
        'ValidateProducts',
        'checkShopifyInventory',
        `Error fetching variant data: ${error.message}`,
      );
      throw error;
    }
  }

  async validateProducts(
    products,
    checkStatus = true,
    checkInventory = true,
    maintainPrice = false,
    checkShopifyInventory = false,
  ) {
    try {
      return await Promise.all(
        products.map(async ({ productId, variantId, ...restProductData }) => {
          const findProductHandler = new GetProduct(
            this.configService,
            this.docClient,
            this.ssmClient,
            this.configParameters,
          );

          const product = await findProductHandler.getProductById(productId);
          if (product.status === ProductStatus.DRAFT) {
            throw new CustomError(`${product.title} is discontinued`, 400);
          }

          if (variantId) {
            const findVariantHandler = new GetVariant(
              this.configService,
              this.docClient,
              this.ssmClient,
              this.configParameters,
            );
            const { variant: variantData } =
              await findVariantHandler.getVariantById(variantId, productId);

            const { data: variant } = variantData;

            if (!variant)
              throw new CustomError(
                `${product.title}- Variant ${variantId} does not exist`,
                404,
              );

            if (variant.status === ProductStatus.DRAFT && checkStatus)
              throw new CustomError(
                `${product.title}- Variant ${variantId} is discontinued`,
                400,
              );

            if (!variant.sku) {
              throw new CustomError(
                `${product.title}- Variant ${variantId} does not have sku`,
                404,
              );
            }
            console.log('checkInventory', checkInventory);
            console.log('checkShopifyInventory', checkShopifyInventory);
            if (
              checkShopifyInventory &&
              variant?.inventory_management &&
              variant?.inventory_management == 'shopify'
            ) {
              await this.checkShopifyInventory(variant);
            }
            if (
              checkInventory &&
              variant?.inventory_management &&
              variant?.inventory_management == 'shopify' &&
              variant?.inventory_quantity == 0
            ) {
              throw new CustomError(
                `${product.title}- Variant ${variant?.variantTitle} is out of stock!`,
                404,
              );
            }
            return {
              ...restProductData,
              product,
              variant,
              productId,
              variantId,
              price: maintainPrice
                ? restProductData.price
                : Number(variant.price || 0),
            };
          }

          return {
            ...restProductData,
            product,
            productId,
            variantId,
            price: maintainPrice
              ? restProductData.price
              : Number(product.price || 0),
          };
        }),
      );
    } catch (e) {
      throw e;
    }
  }
}
