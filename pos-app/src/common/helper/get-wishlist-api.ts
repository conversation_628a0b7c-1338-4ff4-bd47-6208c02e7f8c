import { posLogger } from '../logger';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { CustomError } from '../response/errorHandler/error.handler';
import { AppShopify } from '../shopify/shopify';

export class ShopifyWishList {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private shopifyClient: AppShopify,
  ) {}

  async fetchWishlistFromShopify(customerId) {
    posLogger.info('wishlist', 'fetchWishlistFromShopify', {
      input: customerId,
    });
    try {
      const [WISHLIST_API_ACCESS_TOKEN, WISHLIST_URL] = await Promise.all([
        await this.shopifyClient.getWishListAccessToken(),
        await this.shopifyClient.getWishListUrl(),
      ]);

      const response = await fetch(`${WISHLIST_URL}/${customerId}`, {
        method: 'GET',
        headers: {
          'x-api-key': WISHLIST_API_ACCESS_TOKEN,
        },
      });

      if (!response.ok) {
        throw new CustomError('Network response was not ok', 403);
      }

      const data = await response.json();
      const { wishlist } = data;
      return wishlist;
    } catch (e) {
      posLogger.error('wishlist', 'fetchWishlistFromShopify', { error: e });
      throw e;
    }
  }
}
