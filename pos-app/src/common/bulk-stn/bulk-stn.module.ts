import { Module } from '@nestjs/common';
import { BulkSTNService } from './bulk-stn.service';
import { ConfigService } from '@nestjs/config';
import { BulkSTNController } from './bulk-stn.controller';
import { AppDocumentClient } from '../document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppS3Client } from '../s3-client/s3-client';
import { AppSsmClient } from '../ssm-client/ssm-client';

@Module({
  providers: [
    BulkSTNService,
    AppConfigParameters,
    AppDocumentClient,
    AppS3Client,
    AppSsmClient,
  ],
  controllers: [BulkSTNController],
  exports: [BulkSTNService],
})
export class BulkSTNModule {}
