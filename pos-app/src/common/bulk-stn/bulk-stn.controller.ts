import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { BulkSTNService } from './bulk-stn.service';
import { Response } from 'express';
import { HttpStatus } from '@nestjs/common';
import { posLogger } from '../logger';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { Express } from 'express';

@Controller('bulk-stn')
export class BulkSTNController {
  constructor(private readonly stnService: BulkSTNService) {}

  @Get('download')
  async downloadCSV(@Res() res: Response) {
    try {
      console.log('=====inside bulk STN download function=======');
      const data = [
        {
          childSKUId: '',
          storeID: '',
          senderWHLocation: '',
          quantity: 0,
          remarks: '',
        },
      ];

      const csvContent = await this.stnService.generateCSV(data);

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader(
        'Content-Disposition',
        'attachment; filename="template.csv"',
      );

      return res.status(HttpStatus.OK).send(csvContent);
    } catch (error) {
      console.log(`Error in downloadCSV: ${error.message}`);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to generate CSV file',
        error: error.message,
      });
    }
  }

  @Post('upload')
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, cb) => {
        const ext = extname(file.originalname).toLowerCase();
        if (ext !== '.csv') {
          return cb(null, false);
        }
        cb(null, true);
      },
      limits: { fileSize: 1000000 },
    }),
  )
  async uploadCSV(
    @UploadedFile() file: Express.Multer.File,
    @Res() res: Response,
  ) {
    try {
      if (!file) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          status: 'failed',
          message: 'Please upload a file in CSV format',
        });
      }

      console.log('=====uploadCSV is====', file);

      const csvData = await this.stnService.parseCSVFromBuffer(file.buffer);
      console.log('=====csvData is====', csvData);
      const validationResult = await this.stnService.validateCSVData(csvData);

      if (!validationResult.isValid) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          status: 'failed',
          message: 'Invalid Format Uploaded',
          errors: validationResult.errors,
        });
      }

      const { s3Key, bulkSTNCode } =
        await this.stnService.uploadToS3AndCreateDraftSTN(file.buffer, csvData);

      return res.status(HttpStatus.OK).json({
        status: 'success',
        message: 'Bulk STN uploaded successfully',
        s3Key: s3Key,
        bulkSTNCode,
      });
    } catch (error) {
      console.log(`Error in uploadCSV: ${error.message}`);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        status: 'error',
        message: 'Failed to process CSV file',
        error: error.message,
      });
    }
  }

  @Post('submit')
  async submitBulkSTN(
    @Body() body: { otp: string; bulkSTNCode: string; phoneNumber: string },
    @Res() res: Response,
  ) {
    try {
      const { otp, bulkSTNCode, phoneNumber } = body;

      if (!otp || !bulkSTNCode || !phoneNumber) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          status: 'failed',
          message: 'OTP, bulkSTNCode, and phoneNumber are required',
        });
      }

      await this.stnService.submitBulkSTN(bulkSTNCode, phoneNumber, otp);

      return res.status(HttpStatus.OK).json({
        status: 'success',
        message: `Bulk STN ${bulkSTNCode} submitted successfully`,
      });
    } catch (error) {
      posLogger.error('BulkSTNController', 'submitBulkSTN', {
        error: error.message,
        bulkSTNCode: body.bulkSTNCode,
      });
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        status: 'error',
        message: 'Failed to submit Bulk STN',
        error: error.message,
      });
    }
  }

  @Get('all-stn/:bulkSTNCode')
  async getSTNsByBulkSTNCode(
    @Param('bulkSTNCode') bulkSTNCode: string,
    @Res() res: Response,
  ) {
    try {
      const stns = await this.stnService.getSTNsByBulkSTNCode(bulkSTNCode);
      return res.status(HttpStatus.OK).json({
        status: 'success',
        message: `Found ${stns.length} STNs for bulkSTNCode: ${bulkSTNCode}`,
        data: stns,
      });
    } catch (error) {
      posLogger.error('BulkSTNController', 'getSTNsByBulkSTNCode', {
        error: error.message,
        bulkSTNCode,
      });
      return res.status(error.status || HttpStatus.INTERNAL_SERVER_ERROR).json({
        status: 'error',
        message: 'Failed to retrieve STNs',
        error: error.message,
      });
    }
  }

  @Post('send-otp')
  async sendOtp(
    @Body() body: { phoneNumber: string; bulkSTNCode: string },
    @Res() res: Response,
  ) {
    try {
      const { phoneNumber, bulkSTNCode } = body;

      if (!phoneNumber || !bulkSTNCode) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          status: 'failed',
          message: 'Phone number and bulkSTNCode are required',
        });
      }

      const otp = await this.stnService.sendOTP(phoneNumber, bulkSTNCode);

      return res.status(HttpStatus.OK).json({
        status: 'success',
        message: 'OTP sent successfully',
        otp,
      });
    } catch (error) {
      console.log(`Error in sendOtp: ${error.message}`);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        status: 'error',
        message: 'Failed to send OTP',
        error: error.message,
      });
    }
  }
}
