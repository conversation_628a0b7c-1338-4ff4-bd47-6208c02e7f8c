import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { posLogger } from '../logger';
import { json2csv, csv2json } from 'json-2-csv';
import * as fs from 'fs';
import * as path from 'path';
import { PutCommand, GetCommand, DeleteCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { AppDocumentClient } from '../document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppS3Client } from '../s3-client/s3-client';
import { CreateSTN } from 'src/stn/lib/create-stn';
import { STNData } from 'src/stn/entity/stn.entity';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { S3Interactions } from '../helper/interaction-to-s3';
import { S3UploaderService } from 'src/s3-uploader/s3-uploader.service';
import { generateOtp } from '../helper/generate-otp';
import { GetSTN } from 'src/stn/lib/get-stn-by-id';
import {
  STNRequestMode,
  STNTransferMode,
  STNStatus,
} from 'src/common/enum/stn';
import { GenerateCode } from '../helper/generate-code';
import { CreateSTNInput } from 'src/stn/dto/create-stn.input';

@Injectable()
export class BulkSTNService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private s3Client: AppS3Client,
    private ssmClient: AppSsmClient,
  ) {}

  private s3Handler = new S3Interactions(
    this.configService,
    this.docClient,
    this.s3Client,
  );

  async uploadToS3AndCreateDraftSTN(
    fileBuffer: Buffer,
    csvData: any[],
  ): Promise<{ s3Key: string; bulkSTNCode: string }> {
    // Return both s3Key and bulkSTNCode
    try {
      const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
      const filePath = `BULKSTN/bulk-stn-${uniqueSuffix}.csv`;

      // Generate a unique bulkSTNCode
      const codeHandler = new GenerateCode(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const bulkSTNCode = await codeHandler.generateCode(
        'BULK_STN', // Unique identifier for bulk STN code generation
        'BULK_STN_COUNT',
        'BSTN-', // Prefix for bulk STN code
      );

      const s3Response = await this.s3Handler.uploadToS3(
        fileBuffer,
        filePath,
        'text/csv',
      );
      console.log('S3 Response:', s3Response);

      await this.createDraftSTN(csvData, filePath, bulkSTNCode);

      return { s3Key: filePath, bulkSTNCode }; // Return both s3Key and bulkSTNCode
    } catch (error) {
      posLogger.error('BulkSTN', 'uploadToS3AndCreateDraftSTN', {
        error: error.message,
        stack: error.stack,
      });
      throw new Error(
        `Failed to upload to S3 or create draft STN: ${error.message}`,
      );
    }
  }

  async parseCSVFromBuffer(buffer: Buffer): Promise<any[]> {
    try {
      const csvData = buffer.toString('utf8');
      const jsonData = await csv2json(csvData);
      console.log('=====jsonData is====', jsonData);
      return jsonData;
    } catch (error) {
      console.log(`Error parsing CSV from buffer: ${error.message}`);
      throw new Error('Failed to parse CSV from buffer');
    }
  }

  async generateCSV(data: any[]): Promise<string> {
    try {
      const csv = await json2csv(data, {});
      return csv;
    } catch (error) {
      console.log(`Error generating CSV: ${error.message}`);
      throw new Error('Failed to generate CSV');
    }
  }

  async parseCSV(filePath: string): Promise<any[]> {
    try {
      const csvData = fs.readFileSync(filePath, 'utf8');
      const jsonData = await csv2json(csvData);
      console.log('=====jsonData is====', jsonData);
      return jsonData;
    } catch (error) {
      console.log(`Error parsing CSV: ${error.message}`);
      throw new Error('Failed to parse CSV');
    }
  }

  async validateCSVData(
    data: any[],
  ): Promise<{ isValid: boolean; errors?: string[] }> {
    const errors = [];

    for (const row of data) {
      if (!this.isValidSKU(row.childSKUId)) {
        errors.push(
          `Invalid SKU ID, SKU ID should start with TSC: ${row.childSKUId}`,
        );
      }
      if (!this.isValidStoreID(row.storeID)) {
        errors.push(
          `Invalid Store ID, Store ID should start with TSC: ${row.storeID}`,
        );
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  isValidSKU(skuId: string): boolean {
    const isValid = skuId.length > 0 && skuId.includes('TSC');
    return isValid;
  }

  isValidStoreID(storeID: string): boolean {
    const isValid =
      storeID.length > 0 &&
      (storeID.includes('TSC') || storeID?.includes('Test-store'));
    return isValid;
  }

  async getSTNsByBulkSTNCode(bulkSTNCode: string): Promise<STNData[]> {
    const getSTNHandler = new GetSTN(this.docClient, this.configParameters);
    return await getSTNHandler.getSTNByBulkSTNCode(bulkSTNCode);
  }

  async createDraftSTN(
    data: any[],
    s3Key: string,
    bulkSTNCode: string,
  ): Promise<void> {
    try {
      const codeHandler = new GenerateCode(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const STN_TABLE_NAME = await this.configParameters.getSTNTableName();

      interface CSVRow {
        storeID: string;
        childSKUId: string;
        quantity: string | number;
        senderWHLocation: string;
        'remarks\r'?: string;
        [key: string]: any;
      }

      const groupedByStore = data.reduce(
        (acc, item: CSVRow) => {
          const storeId = item.storeID;
          if (!acc[storeId]) {
            acc[storeId] = [];
          }
          acc[storeId].push(item);
          return acc;
        },
        {} as { [key: string]: CSVRow[] },
      );

      const stnPayloads: STNData[] = await Promise.all(
        Object.entries(groupedByStore).map(
          async ([storeId, items]: [string, CSVRow[]]) => {
            const id = await codeHandler.generateCode(
              storeId,
              'STN_COUNT',
              `STN${storeId}-`,
            );

            const products = items.map((item) => ({
              sku: item.childSKUId,
              quantity: Number(item.quantity),
              title: item.childSKUId,
              price: null,
            }));

            const firstItem = items[0];
            const remarks = firstItem['remarks\r']?.trim() || '';

            return {
              id: id,
              storeId: storeId,
              requestedStoreId: null,
              requestMode: STNRequestMode.FORWARD,
              transferMode: STNTransferMode.WAREHOUSE,
              status: STNStatus.DRAFT,
              products: products,
              sourceWarehouseMappingId: firstItem.senderWHLocation,
              createdAt: moment().toISOString(),
              updatedAt: moment().toISOString(),
              attachmentS3Key: s3Key,
              remarks1: remarks,
              remarks2: null,
              ecomInvoiceId: null,
              ecomOrderId: null,
              ecomSubOrderId: null,
              progressStatus: null,
              bulkSTNCode: bulkSTNCode,
            };
          },
        ),
      );

      const creationPromises: Promise<void>[] = stnPayloads.map(
        async (payload) => {
          try {
            const command = new PutCommand({
              TableName: STN_TABLE_NAME,
              Item: payload,
              ConditionExpression: 'attribute_not_exists(id)',
            });

            await this.docClient.createItem(command);

            posLogger.info('BulkSTN', 'createDraftSTN', {
              id: payload.id,
              storeId: payload.storeId,
              status: payload.status,
              productCount: payload.products.length,
              s3Key: payload.attachmentS3Key,
            });
          } catch (error) {
            posLogger.error('BulkSTN', 'createDraftSTN', {
              storeId: payload.storeId,
              products: payload.products.map((p) => p.sku),
              error: error.message,
            });
            throw error;
          }
        },
      );

      await Promise.all(creationPromises);

      posLogger.info('BulkSTN', 'createDraftSTN', {
        message: `Successfully created ${stnPayloads.length} draft STNs with grouped products`,
        s3Key: s3Key,
      });
    } catch (error) {
      posLogger.error('BulkSTN', 'createDraftSTN', {
        error: error.message,
        stack: error.stack,
      });
      throw new Error(`Failed to create draft STNs: ${error.message}`);
    }
  }

  async sendOTP(phoneNumber: string, bulkSTNCode: string): Promise<string> {
    const getSTNHandler = new GetSTN(this.docClient, this.configParameters);
    const url =
      'https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod/send_sms';
    const otp = generateOtp();

    const Item = await this.getSTNsByBulkSTNCode(bulkSTNCode);
    const attachmentS3Key = Item[0]?.attachmentS3Key;
    console.log('=====attachmentS3Key======', attachmentS3Key);
    const tracelink = `https://pos-stage-public-bucket.s3.ap-south-1.amazonaws.com/${attachmentS3Key}`;

    const payload = {
      template_attributes: {
        templateName: 'BulkStnCreationPos',
        bulkSTNCode,
        OTP: otp,
        url: tracelink,
        phoneNo: phoneNumber,
      },
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to send OTP. API Response: ${errorText}`);
      }

      const result = await response.json();
      posLogger.info('BulkSTN', 'sendOTP', {
        message: 'OTP sent successfully',
        phoneNumber,
        otp,
        result,
      });

      await this.storeOtp(phoneNumber, otp, bulkSTNCode);
      return otp;
    } catch (error) {
      posLogger.error('BulkSTN', 'sendOTP', {
        error: error.message,
        phoneNumber,
      });
      throw new Error(`Failed to send OTP via SMS: ${error.message}`);
    }
  }

  async storeOtp(
    phoneNumber: string,
    otp: string,
    bulkSTNCode: string,
  ): Promise<void> {
    try {
      const otpItem = {
        id: bulkSTNCode,
        phoneNumber,
        otp,
        createdAt: moment().toISOString(),
        ttl: moment().add(5, 'minutes').unix(),
      };

      const BULK_STN_TABLE_NAME = 'pos-stage-bulk-stn-table';
      console.log('BULK_STN_TABLE_NAME', BULK_STN_TABLE_NAME, otpItem);

      const putCommand = new PutCommand({
        TableName: BULK_STN_TABLE_NAME,
        Item: otpItem,
      });

      await this.docClient.createItem(putCommand);

      posLogger.info('BulkSTN', 'storeOtp', {
        message: 'OTP stored successfully',
        phoneNumber,
        bulkSTNCode,
      });
    } catch (error) {
      posLogger.error('BulkSTN', 'storeOtp', {
        error: error.message,
        phoneNumber,
        bulkSTNCode,
      });
      throw new Error(`Failed to store OTP: ${error.message}`);
    }
  }

  async createBulkSTN(data: any[]): Promise<void> {
    // Implement bulk STN creation logic here
  }

  async verifyOTP(
    otp: string,
    bulkSTNCode: string,
    phoneNumber: string,
  ): Promise<boolean> {
    posLogger.info('BulkSTN', 'verifyOTP', { phoneNumber, bulkSTNCode, otp });

    try {
      const params = {
        TableName: 'pos-stage-bulk-stn-table',
        Key: {
          id: bulkSTNCode,
        },
      };

      const result = await this.docClient.getItem(new GetCommand(params));
      console.log('======verify otp =======', result);
      if (!result.Item || result.Item.otp !== otp) {
        posLogger.info('BulkSTN', 'verifyOTP', {
          message: 'Invalid OTP or OTP not found',
          phoneNumber,
          bulkSTNCode,
        });
        throw new Error('Invalid OTP or OTP not found');
      }

      const createdAt = moment(result.Item.createdAt);
      if (moment().diff(createdAt, 'minutes') > 5) {
        posLogger.info('BulkSTN', 'verifyOTP', {
          message: 'OTP expired',
          phoneNumber,
          bulkSTNCode,
        });
        throw new Error('OTP has expired');
      }

      posLogger.info('BulkSTN', 'verifyOTP', {
        message: 'OTP verified successfully',
        phoneNumber,
        bulkSTNCode,
      });
      return true;
    } catch (error) {
      posLogger.error('BulkSTN', 'verifyOTP', {
        error: error.message,
        phoneNumber,
        bulkSTNCode,
      });
      throw error;
    }
  }

  async submitBulkSTN(
    bulkSTNCode: string,
    phoneNumber: string,
    otp: string,
  ): Promise<void> {
    posLogger.info('BulkSTN', 'submitBulkSTN', {
      bulkSTNCode,
      phoneNumber,
      otp,
    });

    try {
      // Verify OTP
      const isOTPValid = await this.verifyOTP(otp, bulkSTNCode, phoneNumber);
      if (!isOTPValid) {
        throw new Error('Invalid OTP provided. Please verify and try again.');
      }

      // Fetch all STNs for the bulkSTNCode
      const getSTNHandler = new GetSTN(this.docClient, this.configParameters);
      const stns = await getSTNHandler.getSTNByBulkSTNCode(bulkSTNCode);
      if (!stns || stns.length === 0) {
        throw new Error(`No STNs found for bulkSTNCode: ${bulkSTNCode}`);
      }

      // Filter for DRAFT STNs
      const draftSTNs = stns.filter((stn) => stn.status === STNStatus.DRAFT);
      if (draftSTNs.length === 0) {
        throw new Error(`No draft STNs found for bulkSTNCode: ${bulkSTNCode}`);
      }

      const createSTNHandler = new CreateSTN(
        this.docClient,
        this.configService,
        this.ssmClient,
        this.configParameters,
      );

      for (const draftSTN of draftSTNs) {
        try {
          await createSTNHandler.updateSTNToCreated(draftSTN);
          posLogger.info('BulkSTN', 'submitBulkSTN', {
            message: 'Updated STN to CREATED',
            id: draftSTN.id,
            storeId: draftSTN.storeId,
            status: STNStatus.CREATED,
            bulkSTNCode,
          });
        } catch (error) {
          posLogger.error('BulkSTN', 'submitBulkSTN', {
            storeId: draftSTN.storeId,
            stnId: draftSTN.id,
            error: error.message,
            bulkSTNCode,
          });
          throw new Error(
            `Failed to update STN ${draftSTN.id}: ${error.message}`,
          );
        }
      }

      posLogger.info('BulkSTN', 'submitBulkSTN', {
        message: `Successfully updated ${draftSTNs.length} STNs to CREATED for bulkSTNCode: ${bulkSTNCode}`,
      });
    } catch (error) {
      posLogger.error('BulkSTN', 'submitBulkSTN', {
        error: error.message,
        bulkSTNCode,
        phoneNumber,
      });
      throw new Error(`Failed to submit Bulk STN: ${error.message}`);
    }
  }
}
