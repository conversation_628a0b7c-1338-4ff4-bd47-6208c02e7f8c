import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  SQSClient,
  SendMessageCommand,
  DeleteMessageCommand,
} from '@aws-sdk/client-sqs';
import { AppAwsSdkCredentials } from '../aws-sdk-credentials';

@Injectable()
export class AppSqsClient {
  private sqsClient: SQSClient;

  constructor(private configService: ConfigService) {
    // const credentials = new AppAwsSdkCredentials(this.configService);

    if (process.env.NODE_ENV === 'local') {
      const credentials = new AppAwsSdkCredentials(this.configService);
      this.sqsClient = new SQSClient({
        region: this.configService.get<string>('REGION'),
        credentials: credentials.getCredentials(),
      });

      // In case of fargate, only use Region
    } else {
      this.sqsClient = new SQSClient({
        region: this.configService.get<string>('REGION'),
      });
    }
  }

  async sendMessage(params: any) {
    // const params = {
    //   QueueUrl: queueUrl,
    //   MessageBody: JSON.stringify(messageBody),
    //   DelaySeconds: 0, // Optional: delay before message is visible
    // };

    try {
      const data = await this.sqsClient.send(new SendMessageCommand(params));
      console.log('Message sent successfully:', data.MessageId);
      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }
  async deleteMessage(params: any) {
    try {
      const data = await this.sqsClient.send(new DeleteMessageCommand(params));
      console.log('Message deleted successfully:', data);
      return data;
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }
}
