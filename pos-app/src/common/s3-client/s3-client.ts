import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';
import { ConfigService } from '@nestjs/config';
import { AppAwsSdkCredentials } from '../aws-sdk-credentials';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AppS3Client extends S3Client {
  private s3Client: S3Client;

  constructor(private configService: ConfigService) {
    super();
    // In case of running locally, use shared profile
    if (process.env.NODE_ENV === 'local') {
      const credentials = new AppAwsSdkCredentials(this.configService);
      this.s3Client = new S3Client({
        region: this.configService.get('REGION'),
        credentials: credentials.getCredentials(),
      });

      // In case of fargate, only use Region
    } else {
      this.s3Client = new S3Client({
        region: this.configService.get('REGION'),
      });
    }
  }

  async putS3Object(command: PutObjectCommand) {
    return this.s3Client.send(command);
  }

  async getObject(command: GetObjectCommand) {
    return await this.s3Client.send(command);
  }
}
