export enum ReplacementType {
  FULL = 'FULL',
  PART = 'PART',
}

export enum ReplacementOptions {
  REFUND = 'REFUND',
  ACCESSORIES = 'ACCESSORIES',
}

export enum PickupPriority {
  DELIVERY_BEFORE_PICKUP = 'DELIVERY_BEFORE_PICKUP',
  PIC<PERSON>UP_BEFORE_DELIVERY = 'PICKUP_BEFORE_DELIVERY',
  PICKUP_WITH_DELIVER = 'PICKUP_WITH_DELIVER',
  NO_PICKUP = 'NO_PICKUP',
}

export enum ReplaceableDiscountType {
  WAIVE_OFF = 'WAIVE_OFF',
  PART_DISCOUNT = 'PART_DISCOUNT', //Extra price taken from customer even for replaceable window applicable
  NO_DISCOUNT = 'NO_DISCOUNT',
  FORCED_DISCOUNT = 'FORCED_DISCOUNT', //Extra discount taken by customer even for replaceable window not applicable
}
export const PickupPriorities = [
  {
    label: 'RP-Cx Pickup & Order booked together',
    value: PickupPriority.DELIVERY_BEFORE_PICKUP,
  },
  {
    label: 'Pickup First',
    value: PickupPriority.PICKUP_BEFORE_DELIVERY,
  },
  // {
  //   label: 'Pickup With Deliver',
  //   value: PickupPriority.PICKUP_WITH_DELIVER,
  // },
  {
    label: 'No Pickup',
    value: PickupPriority.NO_PICKUP,
  },
];
