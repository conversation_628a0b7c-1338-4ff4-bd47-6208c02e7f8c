export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PARTIALLY_CONFIRMED = 'PARTIALLY_CONFIRMED',
  SENT_TO_SHOPIFY = 'SENT_TO_SHOPIFY',
  ON_HOLD = 'ON_HOLD',
  CANCELLED = 'CANCELLED',
  DELETED_FROM_SHOPIFY = 'DELETED_FROM_SHOPIFY',
  SENT_TO_EE = 'SENT_TO_EE',
  ORDER_CREATED = 'ORDER_CREATED',
  AWB_CANCELLED = 'RETURN_CANCELLED',
  AWB_REINITIALIZED = 'RETURN_REINITIALIZED',
}

export enum OrderFilterBy {
  SHOPIFY = 'SHOPIFY',
  INVOICE = 'INVOICE',
  TRANSACTION = 'TRANSACTION',
}

export enum SubOrderType {
  LOCK_PRICE = 'LOCK_PRICE',
  FINAL_ORDER = 'FINAL_ORDER',
}

export enum OrderType {
  POS = 'POS',
  REPLACEMENT = 'REPLACEMENT',
  RETURN = 'RETURN',
  BNPL = 'BNPL',
}
