export enum PaymentStatus {
  COMPLETED = 'COMPLETED',
  CONFIRMED = 'CONFIRMED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
  PAID = 'PAID',
  CREATED = 'CREATED',
  PENDING = 'PENDING',
}

export enum PaymentMode {
  RAZORPAY_POS = 'RAZORPAY_POS',
  RAZORPAY = 'RAZORPAY',
  CASH = 'CASH',
  CHEQUE = 'CHEQUE',
  BANK_TRANSFER = 'BANK_TRANSFER',
  M_SWIPE = 'M_SWIPE',
  PAYU = 'PAYU',
  SNAPMINT = 'SNAPMINT',
  PINELABS = 'PINELABS',
}
export enum TransactionType {
  CARD = '00',
  DIGITAL = '03',
}
