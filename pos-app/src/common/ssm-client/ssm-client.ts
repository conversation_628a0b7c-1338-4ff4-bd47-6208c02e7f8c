import { ConfigService } from '@nestjs/config';
import { GetParameterCommand, SSMClient } from '@aws-sdk/client-ssm';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AppSsmClient {
  private ssmClient;

  constructor(private configService: ConfigService) {
    // In case of running locally, use shared profile
    // if (process.env.NODE_ENV === 'local') {
    //   const credentials = new AppAwsSdkCredentials(this.configService);
    //   this.ssmClient = new SSMClient({
    //     region: this.configService.get('REGION'),
    //     credentials: credentials.getCredentials(),
    //   });

    //   // In case of fargate, only use Region
    // } else {

    this.ssmClient = new SSMClient({
      region: this.configService.get('REGION'),
    });
    // }
  }



  async getSSMParamByKey(command: GetParameterCommand) {
    return this.ssmClient.send(command);
  }

  /**
   * Creates a map with SSM parameter key and its value
   * For example: {"/pos-prod/cognito/userpool_id": "ap-south-1_Y3OJo6T4R"}
   */
  async getSSMParamMap(parameterName: string): Promise<Record<string, string>> {
    const command = new GetParameterCommand({
      Name: parameterName,
      WithDecryption: true,
    });

    const response = await this.ssmClient.send(command);
    const parameterValue = response.Parameter?.Value;

    if (!parameterValue) {
      throw new Error(`Parameter ${parameterName} not found or has no value`);
    }

    return {
      [parameterName]: parameterValue
    };
  }

  /**
   * Gets the cognito user pool ID map for the specified stack
   * Returns: {"/pos-prod/cognito/userpool_id": "actual-user-pool-id"}
   */
  async getCognitoUserPoolIdMap(stackName: string = 'pos-prod'): Promise<Record<string, string>> {
    const parameterName = `/${stackName}/cognito/userpool_id`;
    return this.getSSMParamMap(parameterName);
  }

  /**
   * Returns a hardcoded map of all SSM parameters to avoid rate limiting
   * Based on the SAM deployment command parameters
   */
  getHardcodedSSMParametersMap(stackName: string = 'pos-prod'): Record<string, string> {
    return {
      // Cognito Parameters
      [`/${stackName}/cognito/userpool_id`]: 'ap-south-1_Y3OJo6T4R', // This will be set by CloudFormation
      [`/${stackName}/cognito/client_id`]: 'web-client-id-from-cloudformation', // This will be set by CloudFormation

      // OTP
      [`/${stackName}/otp/url`]: 'https://64net879hb.execute-api.ap-south-1.amazonaws.com/prod',

      // OpenSearch
      [`/${stackName}/opensearch/username`]: 'admin',
      [`/${stackName}/opensearch/password`]: 'Admin@1234',
      [`/${stackName}/opensearch/endpoint`]: 'opensearch-endpoint-from-cloudformation', // This will be set by CloudFormation

      // Shopify
      [`/${stackName}/shopify/access-token`]: '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',
      [`/${stackName}/shopify/admin-base-url`]: 'https://thesleepcompanystore.myshopify.com/admin/api/2024-01',

      // Wishlist
      [`/${stackName}/wishlist/access-token`]: 'KPwkS7MFb71KXrezuxUYJaf4EnFGvgkK9kgWaf7q',
      [`/${stackName}/wishlist/api-url`]: 'https://api.thesleepcompany.in/wishlist',

      // RazorPay POS
      [`/${stackName}/razorpay-pos/app-key`]: 'cfbf0178-9a06-428d-b7ce-5db521914152',
      [`/${stackName}/razorpay-pos/app-username`]: '**********',
      [`/${stackName}/razorpay-pos/app-url`]: 'https://www.ezetap.com/api/3.0/p2padapter',

      // Direct RazorPay
      [`/${stackName}/direct-razorpay/api-key`]: '***********************',
      [`/${stackName}/direct-razorpay/api-secret`]: 'GDv4bZufMykSaOKTk3U2yd4D',
      [`/${stackName}/direct-razorpay/callback-url`]: 'https://success.thesleepcompany.in',

      // PayU POS
      [`/${stackName}/payu-pos/token-endpoint`]: 'https://accounts.payu.in/oauth/token',
      [`/${stackName}/payu-pos/updation-link`]: 'https://oneapi.payu.in/payment-links',
      [`/${stackName}/payu-pos/verify-payment`]: 'https://info.payu.in/merchant/postservice?form=2',
      [`/${stackName}/payu-pos/merchant-id`]: '160565',
      [`/${stackName}/payu-pos/client-id`]: '9c82c52a0278489e8220fae36ea3435591d1418ecc9d1252b0b1051afe1d7057',
      [`/${stackName}/payu-pos/client-secret`]: '978871752e39fa9518650de4224a2d9ad164ee9e520191c8f8411f38ce0e68c3',
      [`/${stackName}/payu-pos/key`]: 'z6VP03',
      [`/${stackName}/payu-pos/salt`]: 'EWRcq4f4',

      // Backend & Media
      [`/${stackName}/backend/base-url`]: 'https://pos-api.thesleepcompany.in',
      [`/${stackName}/api-gateway/baseurl`]: 'https://pos-event.thesleepcompany.in',

      // Mswipe
      [`/${stackName}/mswipe/request-url`]: 'https://ap.Mswipeota.com/JF',
      [`/${stackName}/mswipe/verification-url`]: 'https://www.mswipetech.com/verificationapi/api/VerificationApi/MswipeCardSaleVerificationApi',
      [`/${stackName}/mswipe/request-client-code`]: '9401166288',
      [`/${stackName}/mswipe/verification-client-code`]: 'COMFORTGRIDTECHNOLOGIESPVTLTD',
      [`/${stackName}/mswipe/user-id`]: '9401166288@SOL',
      [`/${stackName}/mswipe/password`]: '9401166288~Ds@240701',
      [`/${stackName}/mswipe/salt`]: 'p055UX9MDSNO9Db7KeJ8APHE8qmrGz1H',

      // FreshDesk
      [`/${stackName}/freshdesk/api-url`]: 'https://thesleepcompanycare.freshdesk.com',
      [`/${stackName}/freshdesk/api-key`]: '********************',
      [`/${stackName}/freshdesk/password`]: 'X',
      [`/${stackName}/freshdesk/group-id`]: '84000291010',

      // EasyEcom
      [`/${stackName}/easyecom/email`]: '<EMAIL>',
      [`/${stackName}/easyecom/password`]: 'Sleep@2024',
      [`/${stackName}/easyecom/base-url`]: 'https://api.easyecom.io',
      [`/${stackName}/easyecom/locationKey`]: 'ne10776308481',

      // Slack
      [`/${stackName}/slack/webhook`]: '*********************************************************************************',

      // Replacement
      [`/${stackName}/replacement/url`]: 'https://gybqro5fdj.execute-api.ap-south-1.amazonaws.com/prod',
      [`/${stackName}/replacement/apiKey`]: 'PpuHE5OWtR3xYmZTZWRBwaa3ogMvAumX8kWc4Alm',

      // All Store Access Token
      [`/${stackName}/all-store/access-token`]: '57631294-erwl-7437-wtxz-0a5c7500e0df',

      // Payment Details
      [`/${stackName}/payment-details/url`]: 'https://apis.thesleepcompany.in',

      // Request Module
      [`/${stackName}/request-module/api-url`]: 'https://apis.thesleepcompany.in/order-management/request-return-replacement',
      [`/${stackName}/request-module/api-key`]: 'REQUESTVN9b3C3c7kTVLp94LNhKmsMH5',

      // Snapmint
      [`/${stackName}/snapmint/app-url`]: 'https://api.snapmint.com',
      [`/${stackName}/snapmint/request-app-url`]: 'https://super-apis.snapmint.com',
      [`/${stackName}/snapmint/failure-url`]: 'https://thesleepcompany.in/',
      [`/${stackName}/snapmint/success-edge-url`]: 'snapmint-success-edge-url-from-cloudformation', // This will be set by CloudFormation

      // Leadsquared
      [`/${stackName}/leadsquared/access-key`]: 'u%24r3f8f6b578c93f2d348094733191527e8',
      [`/${stackName}/leadsquared/secret-key`]: '524000eef0227ce5479ef4383d98e07ed283923d',

      // Pine Labs
      [`/${stackName}/pine-labs/app-url`]: 'https://www.plutuscloudservice.in:8201/API/CloudBasedIntegration/V1',
      [`/${stackName}/pine-labs/security-token`]: '02d388ae-bad1-41a7-b4f7-5432f30f8712',
      [`/${stackName}/pine-labs/merchant-id`]: '650141',
      [`/${stackName}/pine-labs/store-id`]: '1103760'
    };
  }
}

