import { ConfigService } from '@nestjs/config';
import { GetParameterCommand, SSMClient } from '@aws-sdk/client-ssm';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AppSsmClient {
  private ssmClient;

  constructor(private configService: ConfigService) {
    // In case of running locally, use shared profile
    // if (process.env.NODE_ENV === 'local') {
    //   const credentials = new AppAwsSdkCredentials(this.configService);
    //   this.ssmClient = new SSMClient({
    //     region: this.configService.get('REGION'),
    //     credentials: credentials.getCredentials(),
    //   });

    //   // In case of fargate, only use Region
    // } else {

    this.ssmClient = new SSMClient({
      region: this.configService.get('REGION'),
    });
    // }
  }



  async getSSMParamByKey(command: GetParameterCommand) {
    return this.ssmClient.send(command);
  }

  /**
   * Creates a map with SSM parameter key and its value
   * For example: {"/pos-prod/cognito/userpool_id": "ap-south-1_Y3OJo6T4R"}
   */
  async getSSMParamMap(parameterName: string): Promise<Record<string, string>> {
    const command = new GetParameterCommand({
      Name: parameterName,
      WithDecryption: true,
    });

    const response = await this.ssmClient.send(command);
    const parameterValue = response.Parameter?.Value;

    if (!parameterValue) {
      throw new Error(`Parameter ${parameterName} not found or has no value`);
    }

    return {
      [parameterName]: parameterValue
    };
  }

  /**
   * Gets the cognito user pool ID map for the specified stack
   * Returns: {"/pos-prod/cognito/userpool_id": "actual-user-pool-id"}
   */
  async getCognitoUserPoolIdMap(stackName: string = 'pos-prod'): Promise<Record<string, string>> {
    const parameterName = `/${stackName}/cognito/userpool_id`;
    return this.getSSMParamMap(parameterName);
  }
}
