import { Injectable } from '@nestjs/common';
import { AppSsmClient } from './ssm-client';

@Injectable()
export class SSMUsageExample {
  constructor(private ssmClient: AppSsmClient) {}

  // Example 1: Get the complete hardcoded map
  getAllParameters() {
    const allParams = this.ssmClient.getHardcodedSSMParametersMap('pos-prod');
    console.log('All SSM Parameters:', allParams);
    return allParams;
  }

  // Example 2: Get a specific parameter
  getCognitoUserPoolId() {
    const userPoolId = this.ssmClient.getHardcodedCognitoUserPoolId('pos-prod');
    console.log('Cognito User Pool ID:', userPoolId);
    return userPoolId;
  }

  // Example 3: Get any specific parameter by name
  getSpecificParameter(parameterName: string) {
    const value = this.ssmClient.getHardcodedSSMParameter(parameterName, 'pos-prod');
    console.log(`Parameter ${parameterName}:`, value);
    return value;
  }

  // Example 4: Get multiple parameters at once
  getPaymentGatewayConfig() {
    const stackName = 'pos-prod';
    const razorpayKey = this.ssmClient.getHardcodedSSMParameter(`/${stackName}/direct-razorpay/api-key`, stackName);
    const razorpaySecret = this.ssmClient.getHardcodedSSMParameter(`/${stackName}/direct-razorpay/api-secret`, stackName);
    const payuKey = this.ssmClient.getHardcodedSSMParameter(`/${stackName}/payu-pos/key`, stackName);
    
    return {
      razorpay: {
        key: razorpayKey,
        secret: razorpaySecret
      },
      payu: {
        key: payuKey
      }
    };
  }

  // Example 5: Get the map with the specific key you requested
  getCognitoUserPoolIdMap() {
    const stackName = 'pos-prod';
    const parameterName = `/${stackName}/cognito/userpool_id`;
    const value = this.ssmClient.getHardcodedSSMParameter(parameterName, stackName);
    
    // This returns the map you requested: {"/pos-prod/cognito/userpool_id": "actual-value"}
    return {
      [parameterName]: value
    };
  }
}
