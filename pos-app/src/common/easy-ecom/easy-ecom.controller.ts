import { Controller, Get, Query } from '@nestjs/common';
import { EasyEcomService } from './easy-ecom';

@Controller('ecom')
export class EasyEcomController {
  constructor(private readonly ecomService: EasyEcomService) {}

  @Get('inventory')
  async getInventoryBySku(
    @Query('sku') sku: string,
    @Query('storeId') storeId: string,
  ) {
    if (!sku || !storeId) {
      throw new Error('Both sku and storeId query parameters are mandatory');
    }

    const response = await this.ecomService.getInventorySerialsBySku(
      sku,
      storeId,
    );

    return {
      data: response,
    };
  }
}
