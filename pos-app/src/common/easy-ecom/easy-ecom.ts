import { Injectable } from '@nestjs/common';
import { GetParameterCommand } from '@aws-sdk/client-ssm';
import fetch from 'node-fetch';
import { posLogger } from '../logger';
import { CustomError } from '../response/errorHandler/error.handler';
import { CreateSTNInput } from 'src/stn/dto/create-stn.input';
import { GetStore } from 'src/stores/lib/get-store-by-id';
import { GetInventoryCredential } from 'src/inventory-credentials/lib/get-inventory-credential';
import { GetSkuPrice } from 'src/sku-price-master/lib/get-sku-price';
import { STNRequestMode, STNTransferMode } from '../enum/stn';
import { v4 as uuid } from 'uuid';
import moment from 'moment';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { OrderType } from '../enum/order';

@Injectable()
export class EasyEcomService {
  private email: string;
  private password: string;
  private baseUrl: string;

  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async init() {
    if (!this.email) {
      const {
        Parameter: { Value: email },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/easyecom/email`,
          WithDecryption: true,
        }),
      );
      this.email = email;
    }

    if (!this.password) {
      const {
        Parameter: { Value: password },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/easyecom/password`,
          WithDecryption: true,
        }),
      );
      this.password = password;
    }

    if (!this.baseUrl) {
      const {
        Parameter: { Value: baseUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/easyecom/base-url`,
          WithDecryption: true,
        }),
      );
      this.baseUrl = baseUrl;
    }
  }

  async getEasyecomAccessToken(locationKey: string) {
    await this.init();

    const body = {
      email: this.email,
      password: this.password,
      location_key: locationKey,
    };
    try {
      const response = await fetch(`${this.baseUrl}/access/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      posLogger.info('EasyEcomService', 'getEasyecomAccessToken', {
        statusText: response.statusText,
      });

      const { data } = await response.json();
      const {
        token: { jwt_token },
      } = data;

      return jwt_token;
    } catch (error) {
      posLogger.error('EasyEcomService', 'getEasyecomAccessToken', {
        error: error.message || error,
      });
      throw new CustomError('Error while getting access token ', 400);
    }
  }

  async createOrder(locationKey, easyecomPayload) {
    const token = await this.getEasyecomAccessToken(locationKey);
    posLogger.info('EasyEcomService', 'createOrder', { easyecomPayload });

    try {
      const response = await fetch(`${this.baseUrl}/webhook/v2/createOrder`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ ...easyecomPayload }),
      });
      const data = await response.json();

      if (data?.code !== 200) {
        throw new CustomError(
          data?.message ||
            data?.data[0].Message ||
            'Error while creating order',
          400,
        );
      }

      return data;
    } catch (error) {
      throw error;
    }
  }

  async createProducts(products, isSameState, type = 'POS', order) {
    const skuPriceHandler = new GetSkuPrice(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    let discountRatio = 1; // Default 1 (no discount applied)
    let adjustedShippingCost = 0;

    // Apply discount distribution only if order type is NOT POS
    if (type !== OrderType.POS) {
      adjustedShippingCost = order?.shippingCost;
      // Calculate total Shopify product price
      const totalShopifyProductPrice = products.reduce(
        (sum, product) => sum + Number(product?.price) * product?.quantity,
        0,
      );

      // Calculate total order value
      const totalOrderValue =
        totalShopifyProductPrice +
        (order.isShippingCharged ? order.shippingCost : 0);

      // Calculate discount ratio
      discountRatio =
        (totalOrderValue - (order.customDiscountAmount || 0)) / totalOrderValue;

      // Calculate adjusted shipping cost if applicable
      if (order.isShippingCharged) {
        adjustedShippingCost = order.shippingCost * discountRatio;
      }
    }

    return await Promise.all(
      products.map(async (product) => {
        const {
          sku,
          quantity,
          price: shopifyPrice,
          customData = null,
        } = product;

        let breadth = null,
          length = null,
          height = null;
        if (customData) {
          ({ breadth = null, length = null, height = null } = customData);
        }

        let checkSkuStatus = true;

        if (type !== OrderType.POS) {
          checkSkuStatus = false;
        }

        const { title, price: basePrice } = await skuPriceHandler.getSkuPrice(
          sku,
          checkSkuStatus,
        );

        if (type === OrderType.POS && !basePrice) {
          throw new CustomError(
            `Pricing not found for SKU-${sku} in Price master`,
            400,
          );
        }

        const id = uuid();
        const price = isSameState ? basePrice : basePrice * 1.5;

        const customFields =
          type !== OrderType.POS && customData
            ? [
                { value: length.toString(), id: 17351 },
                { value: breadth.toString(), id: 17347 },
                { value: height.toString(), id: 17350 },
                {
                  id: 17352,
                  value: `productId:${product?.productId}, variantId:${product?.variantId}, variantTitle:${product?.variantTitle}`,
                },
              ]
            : !customData && type !== OrderType.POS
              ? [
                  {
                    id: 17352,
                    value: `productId:${product?.productId}, variantId:${product?.variantId}, variantTitle:${product?.variantTitle}`,
                  },
                ]
              : null;

        // **Calculate Discounted Price for Each Product (Only if NOT POS)**
        const discountedPrice = shopifyPrice * discountRatio;

        console.log('discountedPrice>>', discountedPrice);
        return {
          ecomProduct: {
            Price:
              type === OrderType.POS
                ? Number(price).toFixed(2)
                : Number(discountedPrice).toFixed(2),
            productName: title,
            Quantity: quantity,
            Sku: `${sku}_EASY`,
            OrderItemId: id,
            ...(customFields && { custom_fields: customFields }),
          },
          posProduct: {
            ...product,
            price:
              type === OrderType.POS
                ? Number(price).toFixed(2)
                : Number(discountedPrice).toFixed(2),
          },
        };
      }),
    ).then((results) => {
      const ecomProducts = results.map((result) => result.ecomProduct);
      const posProducts = results.map((result) => result.posProduct);

      return [ecomProducts, posProducts, adjustedShippingCost];
    });
  }

  async createSTNOrder(
    createSTNInput: CreateSTNInput,
    id: string,
    sourceWarehouseMappingId: string,
  ) {
    const {
      storeId,
      requestMode,
      products,
      remarks1,
      transferMode,
      requestedStoreId = null,
    } = createSTNInput;
    posLogger.info('EasyEcomService', 'createOrder', {
      input: createSTNInput,
    });

    const getStoreHandler = new GetStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    const getInventoryCredentialHandler = new GetInventoryCredential(
      this.docClient,
      this.configParameters,
    );

    const storeData = await getStoreHandler.getStore(storeId);
    let sourceId: string, destinationId: string;

    if (transferMode === STNTransferMode.WAREHOUSE) {
      sourceId =
        requestMode === STNRequestMode.FORWARD
          ? sourceWarehouseMappingId
          : storeId;
      destinationId =
        requestMode === STNRequestMode.FORWARD
          ? storeId
          : sourceWarehouseMappingId;
    } else {
      if (!requestedStoreId) {
        throw new CustomError(
          `Requested store id is required for ${transferMode} `,
          400,
        );
      }
      sourceId = requestedStoreId;
      destinationId = storeId;
    }
    let ecomState: string;

    const { locationKey, state: sourceState } =
      await getInventoryCredentialHandler.getInventoryCredential(sourceId);

    const { secretKey, state: destinationState } =
      await getInventoryCredentialHandler.getInventoryCredential(destinationId);

    if (requestMode === STNRequestMode.FORWARD) {
      ecomState = destinationState;
    } else {
      ecomState = sourceState;
    }

    const {
      address: shippingAddress,
      billingInfo: { address: billingAddress },
    } = storeData;

    const [ecomProducts, posProducts] = await this.createProducts(
      products,
      destinationState === sourceState,
      OrderType.POS,
      null,
    );

    const easyecomPayload = {
      orderType: 'stocktransferorder',
      orderNumber: id,
      orderDate: moment().toISOString(),
      remarks1,
      items: ecomProducts,
      customer: [
        {
          customerId: secretKey,
          billing: {
            addressLine1: billingAddress.line1,
            addressLine2: billingAddress?.line2,
            postalCode: billingAddress.pinCode,
            city: billingAddress.city,
            state: ecomState,
            country: billingAddress.country,
          },
          shipping: {
            addressLine1: shippingAddress.line1,
            addressLine2: shippingAddress?.line2,
            postalCode: shippingAddress.pinCode,
            city: shippingAddress.city,
            state: ecomState,
            country: shippingAddress.country,
            latitude: shippingAddress.latitude,
            longitude: shippingAddress.longitude,
          },
        },
      ],
    };

    try {
      const result = await this.createOrder(locationKey, easyecomPayload);
      const { code } = result;

      posLogger.info('EasyEcomService', 'createOrder', {
        result,
      });
      if (code !== 200) {
        throw new CustomError(result.message, 400);
      }
      return {
        ...result,
        posProducts,
        locationKey,
        sourceWarehouseMappingId,
      };
    } catch (error) {
      posLogger.error('EasyEcomService', 'createOrder', {
        error: error.message || error,
      });
      throw new CustomError(`${error} `, 400);
    }
  }

  async getOrderDetails(orderId: string, location_key: string) {
    const token = await this.getEasyecomAccessToken(location_key);
    const url = `${this.baseUrl}/orders/V2/getOrderDetails?reference_code=${orderId}`;

    try {
      posLogger.info(
        'EasyEcomService',
        'getOrderDetails',
        `Fetching details for order ID: ${orderId}`,
      );

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();

      posLogger.info('EasyEcomService', 'getOrderDetails', {
        data,
      });

      if (data.code !== 200 || !Array.isArray(data.data)) {
        throw new CustomError('Error fetching order details', 400);
      }

      return data.data.length ? data.data[0] : null; // Return the first order in the array or null if not found
    } catch (error) {
      posLogger.error('EasyEcomService', 'getOrderDetails', {
        error: error.message || error,
      });
      throw error;
    }
  }

  async getInventorySerialsBySku(sku: string, storeId: string) {
    await this.init();

    // Initialize GetStore handler to fetch store data
    const getStoreHandler = new GetStore(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );

    // Fetch store data
    const storeData = await getStoreHandler.getStore(storeId);
    if (!storeData) {
      throw new CustomError(`Store with ID ${storeId} not found`, 404);
    }

    const locationKey =
      storeData?.easyEcomWarehouseLocationId || 'ne32540912881';
    if (!locationKey) {
      throw new CustomError(
        `No EasyEcom location key found for store ${storeId}`,
        400,
      );
    }

    const token = await this.getEasyecomAccessToken(locationKey);
    const url = `${this.baseUrl}/inventory/getInventorySerialsBySku?sku=${sku}&status=1`;

    try {
      posLogger.info('EasyEcomService', 'getInventorySerialsBySku', {
        sku,
        locationKey,
        storeId,
      });

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      posLogger.info('EasyEcomService', 'getInventorySerialsBySku', {
        statusCode: data.code,
        data,
      });

      if (data.code !== 200) {
        throw new CustomError(
          data.message || 'Error fetching inventory serials',
          400,
        );
      }

      return data.data; // Return the inventory serials data
    } catch (error) {
      posLogger.error('EasyEcomService', 'getInventorySerialsBySku', {
        error: error.message || error,
      });
      throw new CustomError(
        `Failed to fetch inventory serials for SKU ${sku}: ${error.message || 'Unknown error'}`,
        400,
      );
    }
  }

  async fetchPurchaseOrderById(
    createdAfter: string,
    createdBefore: string,
    _poRefNum: string,
    locationKey: string,
  ) {
    const token = await this.getEasyecomAccessToken(locationKey);
    posLogger.info('EasyEcomService', 'getPurchaseOrder', {
      createdAfter,
      createdBefore,
      _poRefNum,
      locationKey,
    });
    const poRefNum = `Auto PO ${_poRefNum}`;
    const fetchPOs = async (
      url: string,
      accumulatedPOs: any[] = [],
    ): Promise<any[]> => {
      try {
        const response = await fetch(url, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const { data, nextUrl } = await response.json();

        if (!data || !Array.isArray(data)) {
          throw new CustomError('Invalid response from EasyEcom API', 400);
        }

        const matchingPOs = data.filter((po) => po.po_ref_num === poRefNum);
        accumulatedPOs.push(...matchingPOs);

        if (nextUrl) {
          return fetchPOs(`${this.baseUrl + nextUrl}`, accumulatedPOs);
        }

        if (accumulatedPOs.length === 0) {
          throw new CustomError(
            `No purchase orders found with reference number ${poRefNum}`,
            404,
          );
        }

        posLogger.info('EasyEcomService', 'fetchPurchaseOrderById', {
          accumulatedPOs,
        });

        return accumulatedPOs;
      } catch (error) {
        posLogger.error('EasyEcomService', 'fetchPurchaseOrderById', {
          error: error.message || error,
        });
        throw error;
      }
    };

    const initialUrl = `${this.baseUrl}/wms/V2/getPurchaseOrderDetails?created_after=${createdAfter}&created_before=${createdBefore}`;
    return fetchPOs(initialUrl);
  }

  async createGRNAgainstPO(
    purchaseOrderId: string,
    vendorId: number,
    items: { sku: string; quantity: number; cost: number; title: string }[],
    locationKey: string,
  ) {
    await this.init();

    const token = await this.getEasyecomAccessToken(locationKey);
    /* eslint-disable @typescript-eslint/no-unused-vars */
    const grnPayload = {
      vendor_id: vendorId,
      purchase_order_id: purchaseOrderId,
      items: items.map(({ title, ...item }) => {
        return { ...item, sku: `${item.sku}_EASY`, shelf: 'Dummy' };
      }),
    };
    posLogger.info('EasyEcomService', 'createGRNAgainstPO', {
      grnPayload,
    });
    try {
      const response = await fetch(`${this.baseUrl}/wms/QueueGrnApi`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(grnPayload),
      });

      const result = await response.json();

      if (result.code !== 200) {
        throw new CustomError(
          result.message[0]?.Error || 'Error creating GRN',
          400,
        );
      }

      posLogger.info('EasyEcomService', 'createGRNAgainstPO', {
        result,
      });
      return result;
    } catch (error) {
      posLogger.error('EasyEcomService', 'createGRNAgainstPO', {
        error: error.message[0].Error || error,
      });
      throw error;
    }
  }

  async generateB2BInvoiceAPI(invoiceId: string, locationKey: string) {
    await this.init();

    const token = await this.getEasyecomAccessToken(locationKey);

    posLogger.info('EasyEcomService', 'generateB2BInvoiceAPI', {
      invoiceId,
    });
    try {
      const response = await fetch(
        `${this.baseUrl}/orders/generateB2BInvoiceAPI`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            invoiceId,
          }),
        },
      );

      const result = await response.json();

      if (result.code !== 200) {
        throw new CustomError(
          'Store Inventory is not synced/matching with WMS, please contact HO to resolve this issue.',
          400,
        );
      }

      posLogger.info('EasyEcomService', 'generateB2BInvoiceAPI', {
        result,
      });
      return result;
    } catch (error) {
      posLogger.error('EasyEcomService', 'generateB2BInvoiceAPIiiiii', {
        error,
      });
      throw error;
    }
  }

  async cancelOrder(stnId: string, locationKey: string) {
    await this.init();

    const token = await this.getEasyecomAccessToken(locationKey);

    posLogger.info('EasyEcomService', 'cancelOrder', {
      stnId,
    });
    try {
      const response = await fetch(`${this.baseUrl}/orders/cancelOrder`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          reference_code: stnId,
        }),
      });

      const result = await response.json();

      if (result.code !== 200) {
        throw new CustomError(
          result.message || 'Error generating invoice',
          400,
        );
      }

      posLogger.info('EasyEcomService', 'generateB2BInvoiceAPI', {
        result,
      });
      return result;
    } catch (error) {
      posLogger.error('EasyEcomService', 'generateB2BInvoiceAPI', {
        error: error.message[0].Error || error,
      });
      throw error;
    }
  }

  async holdOrder(invoiceId: string, locationKey: string) {
    posLogger.info('EasyEcomService', 'holdOrder', {
      invoiceId,
    });

    await this.init();
    const token = await this.getEasyecomAccessToken(locationKey);

    try {
      const response = await fetch(`${this.baseUrl}/orders/holdOrders`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          invoice_id: invoiceId,
          hold_reason: 'Other',
        }),
      });

      const result = await response.json();

      posLogger.info('EasyEcomService', 'holdOrder', {
        code: result.code,
      });

      if (result.code !== 200) {
        posLogger.error('EasyEcomService', 'holdOrder', {
          result,
        });
      }
      return result;
    } catch (error) {
      posLogger.error('EasyEcomService', 'holdOrder', {
        error: error.message[0].Error || error,
      });
      throw error;
    }
  }

  async initiateReturn(locationKey, easyecomPayload) {
    console.log('locationKey', locationKey);
    const token = await this.getEasyecomAccessToken(locationKey);
    posLogger.info('EasyEcomService', 'initiateReturn', {
      easyecomPayload,
      token,
      locationKey,
    });

    try {
      const response = await fetch(`${this.baseUrl}/create-initiate-return`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ ...easyecomPayload }),
      });

      return await response.json();
    } catch (error) {
      throw error;
    }
  }

  async cancelInitiatedReturn(locationKey, easyecomPayload) {
    console.log('locationKey', locationKey);
    const token = await this.getEasyecomAccessToken(locationKey);
    posLogger.info('EasyEcomService', 'cancelInitiatedReturn', {
      easyecomPayload,
      token,
      locationKey,
    });

    try {
      const response = await fetch(`${this.baseUrl}/delete-initiated-return`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ ...easyecomPayload }),
      });

      return await response.json();
    } catch (error) {
      console.log('error in rawb', error);
      throw error;
    }
  }

  /**
   * Gets order details directly from EasyEcom API by reference code
   * @param referenceCode Order reference code
   * @returns Order details including warehouse ID
   */
  // async getOrderDetailsByReferenceCode(referenceCode: string) {
  //   await this.init();

  //   try {
  //     posLogger.info('EasyEcomService', 'getOrderDetailsByReferenceCode', {
  //       referenceCode,
  //     });

  //     //TODO: SSM PARAMETER
  //     const bearerToken =
  //       'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvbG9hZGJhbGFuY2VyLW0uZWFzeWVjb20uaW9cL2FjY2Vzc1wvdG9rZW4iLCJpYXQiOjE3NDE3NzE4MjQsImV4cCI6MTc0OTY1NTgyNCwibmJmIjoxNzQxNzcxODI0LCJqdGkiOiJza0FxeDUza0E2SDZHd0lIIiwic3ViIjoyMDE2NDUsInBydiI6ImE4NGRlZjY0YWQwMTE1ZDVlY2NjMWY4ODQ1YmNkMGU3ZmU2YzRiNjAiLCJ1c2VyX2lkIjoyMDE2NDUsImNvbXBhbnlfaWQiOjEwMzgwOSwicm9sZV90eXBlX2lkIjoyLCJwaWlfYWNjZXNzIjoxLCJwaWlfcmVwb3J0X2FjY2VzcyI6MSwicm9sZXMiOm51bGwsImNfaWQiOjEwMzgwOSwidV9pZCI6MjAxNjQ1LCJsb2NhdGlvbl9yZXF1ZXN0ZWRfZm9yIjoxMDM4MDl9.zVlPImSMCkRsRsYQTbwFYv-tfgS10ddKdU7HH4fvqyc'; // Static token for now

  //     // Set cookies from curl example
  //     const cookies = "XSRF-TOKEN=eyJpdiI6IllCVVVEUUhWUHBLMm5XZUVIZHBGZkE9PSIsInZhbHVlIjoiSi94UFpUQmhIQnRzYlIwdFFobmt2cldNUldJNlhyY2xTZXdHVVdyQ0RUMzJqQStSZkw4MGZpSVBSdDdjOFlhM3Y2TU54bjNjZU45cTJINXJKRXN4NjZBdEtRWkFDcU9BN3RpbmxVRmN0dkVjdTdJZllyY2EydFhMOXVZd2NiZ1YiLCJtYWMiOiJhMjc3NmIyMmUwOGU5ODk4NTRhOTQyZDJkZjhiOWMwZTJiNmNkOGQwODJiNDA4MDM2MzcyNDBhYjRkODMxYjYyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IktNOERRQ1JNVzFkaUNBejN3RFZhQnc9PSIsInZhbHVlIjoib015RU5lejdqejhGSlpLVU5EMC9Fc0Z1Sm92MlVtTU1KOHZXQWlSRlNSS0ZOUFh4UjBwSHFQdDJFY3dQNTZwUklCakFqa0JVREZzdUNkVExvTjMxc3lYMnQyMS9vRldyRlFpYTdzbnpZNnVOWVZaRC9vUnJxRjRiNmRyTkdYcEMiLCJtYWMiOiIyNWRjOTY0NDNkMzZiMDllMjY2MzQ4YWZjYWJmOGM1ODE5MDY3NTNmY2RkOWUwYTdhM2VjNDU4NTdjYmU0MjZlIiwidGFnIjoiIn0%3D; PHPSESSID=1jrplg3c0el9v696ftq4eraie9";

  //     const response = await fetch(
  //       `${this.baseUrl}/orders/V2/getOrderDetails?reference_code=${referenceCode}`,
  //       {
  //         method: 'GET',
  //         headers: {
  //           Authorization: `Bearer ${bearerToken}`,
  //           'Content-Type': 'application/json',
  //           Cookie: cookies,
  //         },
  //       },
  //     );
  //     console.log("response in here",response)
  //     const data = await response.json();
  //     console.log("data",data)
  //     posLogger.info('EasyEcomService', 'getOrderDetailsByReferenceCode', {
  //       statusCode: data.code,
  //       dataLength: data.data?.length || 0,
  //     });

  //     if (data.code !== 200 || !data.data || !data.data.length) {
  //       throw new CustomError(
  //         `Failed to get order details from EasyEcom API for reference code ${referenceCode}`,
  //         400,
  //       );
  //     }

  //     return data.data[0]; // Return the first order in the array
  //   } catch (error) {
  //     posLogger.error('EasyEcomService', 'getOrderDetailsByReferenceCode', {
  //       error: error.message || error,
  //     });

  //     throw new CustomError(
  //       `Failed to get order details: ${error.message || 'Unknown error'}`,
  //       error.statusCode || 500,
  //     );
  //   }
  // }
}
