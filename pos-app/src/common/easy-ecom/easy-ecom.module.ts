import { Modu<PERSON> } from '@nestjs/common';
import { EasyEcomService } from './easy-ecom';
import { DocumentClientModule } from '../document-client/document-client.module';
import { SsmClientModule } from '../ssm-client/ssm-client.module';
import { ConfigParametersModule } from 'src/config/config.module';
import { EasyEcomController } from './easy-ecom.controller';

@Module({
  imports: [DocumentClientModule, SsmClientModule, ConfigParametersModule],
  providers: [EasyEcomService],
  exports: [EasyEcomService],
  controllers: [EasyEcomController],
})
export class EasyEcomModule {}
