import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as AWS from 'aws-sdk';

@Injectable()
export class AppAwsSdkCredentials {
  private credentials;

  constructor(private configService: ConfigService) {
    if (process.env.NODE_ENV === 'local') {
      this.credentials = new AWS.SharedIniFileCredentials({
        profile: this.configService.get('AWS_PROFILE'),
      });
    }
  }

  public getCredentials() {
    return this.credentials;
  }
}
