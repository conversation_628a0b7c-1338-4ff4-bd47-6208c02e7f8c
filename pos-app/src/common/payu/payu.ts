import { GetParameterCommand } from '@aws-sdk/client-ssm';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { posLogger } from '../logger';
import { Injectable } from '@nestjs/common';
import fetch from 'node-fetch';
import { generateSHA512Hash } from '../helper/hash-generator';
import moment from 'moment';

@Injectable()
export class PayUModule {
  private clientId;
  private clientSecret;
  private accessToken;
  private key;
  private salt;
  private tokenEndpoint;
  private updationLink;
  private verifyPaymentEndpoint;
  private merchantId;
  private successCallbackUrl;

  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
  ) {}

  async init() {
    if (!this.clientId) {
      const {
        Parameter: { Value: clientId },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/payu-pos/client-id`,
          WithDecryption: true,
        }),
      );
      this.clientId = clientId;
    }

    if (!this.clientSecret) {
      const {
        Parameter: { Value: clientSecret },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/payu-pos/client-secret`,
          WithDecryption: true,
        }),
      );
      this.clientSecret = clientSecret;
    }

    if (!this.key) {
      const {
        Parameter: { Value: key },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/payu-pos/key`,
          WithDecryption: true,
        }),
      );
      this.key = key;
    }

    if (!this.salt) {
      const {
        Parameter: { Value: salt },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/payu-pos/salt`,
          WithDecryption: true,
        }),
      );
      this.salt = salt;
    }
    if (!this.merchantId) {
      const {
        Parameter: { Value: merchantId },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/payu-pos/merchant-id`,
          WithDecryption: true,
        }),
      );
      this.merchantId = merchantId;
    }

    if (!this.tokenEndpoint) {
      const {
        Parameter: { Value: tokenEndpoint },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/payu-pos/token-endpoint`,
          WithDecryption: true,
        }),
      );
      this.tokenEndpoint = tokenEndpoint;
    }

    if (!this.updationLink) {
      const {
        Parameter: { Value: updationLink },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/payu-pos/updation-link`,
          WithDecryption: true,
        }),
      );
      this.updationLink = updationLink;
    }

    if (!this.verifyPaymentEndpoint) {
      const {
        Parameter: { Value: verifyPaymentEndpoint },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/payu-pos/verify-payment`,
          WithDecryption: true,
        }),
      );
      this.verifyPaymentEndpoint = verifyPaymentEndpoint;
    }
    if (!this.successCallbackUrl) {
      const {
        Parameter: { Value: SuccessCallbackUrl },
      } = await this.ssmClient.getSSMParamByKey(
        new GetParameterCommand({
          Name: `/${this.configService.get('STACK_NAME')}/snapmint/success-edge-url`,
          WithDecryption: true,
        }),
      );

      this.successCallbackUrl = SuccessCallbackUrl;
    }
  }

  async fetchOAuthToken(scope: string) {
    await this.init();

    try {
      const response = await fetch(this.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: this.clientId,
          client_secret: this.clientSecret,
          scope: scope,
          grant_type: 'client_credentials',
        }).toString(),
      });

      posLogger.info('PayUModule', 'fetchOAuthToken', {
        statusText: response.statusText,
      });

      const { access_token } = await response.json();

      this.accessToken = access_token;
    } catch (error) {
      posLogger.error('PayUModule', 'fetchOAuthToken', {
        error: error.message || error,
      });
      throw error;
    }
  }

  async createPayuPayment(
    orderId,
    transactionId,
    transactionAmount,
    phone,
    email,
    name,
    isFirstTimeVisit,
  ) {
    await this.fetchOAuthToken('create_payment_links');
    const now = moment().utcOffset('+05:30');
    const expiryDate = now.add(15 * 60 * 60, 'm').format('yyyy-MM-DD HH:mm:ss');

    const headers = {
      merchantId: this.merchantId,
      'Content-Type': 'application/json',
      Authorization: `Bearer ${this.accessToken}`,
    };
    const encodedQuery = btoa(
      JSON.stringify({ id: orderId, isFirstTimeVisit }),
    );
    console.log(
      '${this.successCallbackUrl}?q=${encodedQuery}',
      `${this.successCallbackUrl}?q=${encodedQuery}`,
    );

    const body = {
      subAmount: transactionAmount,
      transactionId,
      isPartialPaymentAllowed: false,
      maxPaymentsAllowed: 1,
      udf: {
        udf1: orderId,
      },
      expiryDate,
      description: 'paymentLink for generate from POS',
      viaSms: true,
      viaEmail: true,
      customer: {
        name,
        email,
        phone,
      },
      source: 'API',
      successURL: `${this.successCallbackUrl}?q=${encodedQuery}`,
    };

    console.log('Payment', this.updationLink, headers, JSON.stringify(body));
    console.log(
      '=====createPayuPayment request body is=====',
      JSON.stringify(body),
    );

    posLogger.info(
      'PayUModule',
      `=====createPayuPayment request body is ${phone}====`,
      {
        body: JSON.stringify(body),
      },
    );

    try {
      const response = await fetch(this.updationLink, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(body),
      });

      posLogger.info('PayUModule', 'createPayuPayment', {
        statusText: response.statusText,
      });

      const data = await response.json();
      console.log('====createPayuPayment response is====', data);
      posLogger.info(
        'PayUModule',
        `====createPayuPayment response is ${phone}====`,
        {
          response: data,
        },
      );

      return data;
    } catch (error) {
      posLogger.error('PayUModule', 'createPayuPayment', {
        error: error.message || error,
      });
      throw error;
    }
  }

  async verifyPayuPayment(var1: string) {
    await this.fetchOAuthToken('create_payment_links');

    const hashString = `${this.key}|verify_payment|${var1}|${this.salt}`;
    const hash = generateSHA512Hash(hashString);

    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Bearer ${this.accessToken}`,
    };

    try {
      const response = await fetch(this.verifyPaymentEndpoint, {
        method: 'POST',
        headers: headers,
        body: new URLSearchParams({
          key: this.key,
          command: 'verify_payment',
          var1,
          hash,
        }).toString(),
      });

      const data = await response.json();
      posLogger.info('PayUModule', '=====verifyPayuPayment response======', {
        response: data,
      });
      console.log('Payu Verify', JSON.stringify(data));

      const {
        status,
        amt = '',
        mihpayid,
        mode = null,
      } = data.transaction_details[var1];

      posLogger.info('PayUModule', 'createPayuPayment', {
        statusText: status,
      });

      return {
        status:
          status.toUpperCase() === 'NOT FOUND' ? null : status.toUpperCase(),
        amountPaid: amt,
        mihpayid,
        mode,
      };
    } catch (error) {
      posLogger.error('PayUModule', 'verifyPayuPayment', error);
      throw error;
    }
  }

  async cancelPaymentLink(paymentLinkId: string) {
    await this.fetchOAuthToken('update_payment_links');

    const headers = {
      merchantId: this.merchantId,
      'Content-Type': 'application/json',
      Authorization: `Bearer ${this.accessToken}`,
    };

    try {
      const response = await fetch(`${this.updationLink}/${paymentLinkId}`, {
        method: 'PUT',
        headers: headers,
        body: JSON.stringify({
          active: false,
        }),
      });

      posLogger.info('PayUModule', 'cancelPaymentLink', {
        status: response.status,
        statusText: response.statusText,
      });

      const data = await response.json();

      return data;
    } catch (error) {
      posLogger.error('PayUModule', 'cancelPaymentLink', {
        error: error.message || error,
      });
      throw error;
    }
  }
}
