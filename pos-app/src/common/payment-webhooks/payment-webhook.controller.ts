import { Body, Controller, Post } from '@nestjs/common';
import { PaymentWebhookService } from './payment-webhook.service';

@Controller('payment-webhook')
export class PaymentWebhookController {
  constructor(private readonly paymentWebhookService: PaymentWebhookService) {}

  @Post('razorpay')
  async handleRazorpayWebhook(@Body() body: any) {
    return await this.paymentWebhookService.handleRazorpayWebhook(body);
  }

  @Post('razorpay-pos')
  async handleRazorpayPosWebhook(@Body() body: any) {
    return await this.paymentWebhookService.handleRazorpayPosWebhook(body);
  }

  @Post('snapmint')
  async handleSnapmintWebhook(@Body() body: any) {
    return await this.paymentWebhookService.handleSnapmintWebhook(body);
  }

  @Post('payu')
  async handlePayuWebhook(@Body() body: any) {
    return await this.paymentWebhookService.handlePayuWebhook(body);
  }

  @Post('pinelabs')
  async handlePineLabsWebhook(@Body() body: any) {
    return await this.paymentWebhookService.handlePineLabsWebhook(body);
  }
}
