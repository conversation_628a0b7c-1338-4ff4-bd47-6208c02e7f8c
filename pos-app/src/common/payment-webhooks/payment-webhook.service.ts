import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { AppDocumentClient } from '../document-client/document-client';
import { PaymentHelpers } from 'src/payments/lib/payment-helpers';
import { PaymentMode, PaymentStatus } from '../enum/payment';
import { CustomError } from '../response/errorHandler/error.handler';
import { PaymentData } from 'src/payments/entities/payment.entity';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { AutoConfirmOrder } from 'src/orders/lib/auto-confirm-order';
import { AppShopify } from '../shopify/shopify';
import { AppS3Client } from '../s3-client/s3-client';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';

@Injectable()
export class PaymentWebhookService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
    private s3Client: AppS3Client,
  ) {
    this.configService = configService;
    this.docClient = docClient;
    this.ssmClient = ssmClient;
    this.configParameters = configParameters;
    this.shopifyClient = shopifyClient;
    this.s3Client = s3Client;
  }

  async handleRazorpayWebhook(body: any) {
    try {
      console.log('Razorpay webhook body >', JSON.stringify(body));
      const { event, payload } = body;
      const { orderId, transactionId } =
        payload.payment_link.entity.notes ||
        payload.order.entity.notes ||
        payload.payment.entity.notes;

      if (!orderId || !transactionId)
        throw new Error(
          'Invalid request, orderId & transactionId are required!',
        );

      if (
        ![
          'payment_link.paid',
          'payment_link.expired',
          'payment_link.cancelled',
        ].includes(event)
      ) {
        return;
      }

      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const Item: PaymentData =
        await paymentHelper.getPaymentByOrderAndTransactionID(
          orderId,
          transactionId,
        );

      const { mode, status: oldStatus, externalRefId, storeId } = Item || {};

      console.log('=======storeId is======', storeId);

      const getGlobalConfigurationHandler = new GetGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const { value: AUTO_CONFIRMATION_LIVE_STORES } =
        await getGlobalConfigurationHandler.getGlobalConfiguration(
          'AUTO_CONFIRMATION_LIVE_STORES',
        );

      const liveStoresArray =
        typeof AUTO_CONFIRMATION_LIVE_STORES === 'string' &&
        AUTO_CONFIRMATION_LIVE_STORES.length
          ? AUTO_CONFIRMATION_LIVE_STORES.split(',')
          : [];

      console.log('======liveStoresArray=======', liveStoresArray);

      if (
        liveStoresArray &&
        liveStoresArray.length &&
        !liveStoresArray?.includes(storeId)
      )
        return;

      if (mode !== PaymentMode.RAZORPAY)
        throw new CustomError(
          'Invalid request, transaction is not made from RazorPay Link!',
          400,
        );

      if (oldStatus == PaymentStatus.COMPLETED) return;

      if (!externalRefId)
        throw new CustomError(
          'External reference ID not available for this transaction.',
          400,
        );

      let newStatus: PaymentStatus;
      let paymentID = null;
      let paymentMethod = null;
      let extraData = undefined;

      if (event === 'payment_link.paid') {
        const { payment } = payload;
        const { id, method } = payment.entity;
        paymentID = id;
        paymentMethod = method;
        newStatus = PaymentStatus.COMPLETED;

        const entity = payment?.entity;
        if (!entity || !method) return; // Exit early if essential data is missing

        switch (method) {
          case 'upi': {
            const upiId = entity.upi?.vpa || entity.vpa || '';
            const rrn = entity.acquirer_data?.rrn || '';
            extraData = { upiId, rrn };
            break;
          }

          case 'card': {
            const card = entity.card || {};
            const {
              name = '',
              last4 = '',
              network = '',
              type = '',
              issuer = '',
              emi = false,
              international = false,
            } = card;

            extraData = {
              cardDetails: {
                cardHolderName: name,
                last4Digits: last4,
                network,
                cardType: type,
                issuer,
                emiEligible: emi,
                international,
              },
            };
            break;
          }

          case 'netbanking': {
            extraData = {
              bankName: entity?.bank || '',
            };
            break;
          }
          default:
            break;
        }
      } else if (event === 'payment_link.expired') {
        newStatus = PaymentStatus.EXPIRED;
      } else if (event === 'payment_link.cancelled') {
        newStatus = PaymentStatus.CANCELLED;
      }

      const updateExpression = [
        'SET #status = :status, #updatedAt = :updatedAt, paymentID = :paymentID, amountPaid = :amountPaid , paymentMethod = :paymentMethod',
      ];

      const expressionAttributeValues: Record<string, any> = {
        ':status': newStatus,
        ':paymentID': paymentID || null,
        ':updatedAt': moment().toISOString(),
        ':amountPaid': Number(payload.payment_link.entity.amount) / 100,
        ':paymentMethod': paymentMethod || null,
      };

      if (extraData) {
        Object.entries(extraData).map(([key, value]) => {
          updateExpression.push(`${key} = :${key}`);
          expressionAttributeValues[`:${key}`] = value;
        });
      }

      if (newStatus !== oldStatus) {
        updateExpression.push(
          'transactionCompletedDate = :transactionCompletedDate',
        );
        expressionAttributeValues[':transactionCompletedDate'] =
          moment().toISOString(); // Current date in ISO format
      }

      await this.docClient.updateItem(
        new UpdateCommand({
          TableName: PAYMENT_TABLE,
          Key: {
            orderId,
            transactionId,
          },
          UpdateExpression: updateExpression.join(', '),
          ExpressionAttributeNames: {
            '#status': 'status',
            '#updatedAt': 'updatedAt',
          },
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: 'ALL_NEW',
          ConditionExpression:
            'attribute_exists(orderId) AND attribute_exists(transactionId)',
        }),
      );

      const handler = new AutoConfirmOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.shopifyClient,
        this.s3Client,
      );
      handler.verifyPaymentAndConfirmOrder(orderId);

      return true;
    } catch (error) {
      console.log('error handleRazorPayWebHook :::: >', error);
      return true;
    }
  }

  async handlePayuWebhook(body: any) {
    try {
      console.log('PayU webhook body >', JSON.stringify(body));
      const { status, mihpayid, mode, txnid, amount, udf1 } = body;

      if (!status || !mihpayid) {
        throw new CustomError(
          'Invalid request, status and mihpayid are required!',
          400,
        );
      }

      const PAYMENT_TABLE = await this.configParameters.getPaymentTableName();

      const paymentHelper = new PaymentHelpers(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const Item: PaymentData =
        await paymentHelper.getPaymentByOrderAndTransactionID(udf1, txnid);

      const {
        mode: paymentMode,
        status: oldStatus,
        orderId,
        externalRefId,
        storeId,
      } = Item || {};

      const getGlobalConfigurationHandler = new GetGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );

      const { value: AUTO_CONFIRMATION_LIVE_STORES } =
        await getGlobalConfigurationHandler.getGlobalConfiguration(
          'AUTO_CONFIRMATION_LIVE_STORES',
        );

      const liveStoresArray =
        typeof AUTO_CONFIRMATION_LIVE_STORES === 'string' &&
        AUTO_CONFIRMATION_LIVE_STORES.length
          ? AUTO_CONFIRMATION_LIVE_STORES.split(',')
          : [];

      console.log(
        '========AUTO_CONFIRMATION_LIVE_STORES========',
        AUTO_CONFIRMATION_LIVE_STORES,
        !liveStoresArray?.includes(storeId),
      );

      console.log('======liveStoresArray=======', liveStoresArray);

      if (
        liveStoresArray &&
        liveStoresArray.length &&
        !liveStoresArray?.includes(storeId)
      )
        return;

      console.log('=====payu webhook status =====', status);

      if (paymentMode !== PaymentMode.PAYU) {
        throw new CustomError(
          'Invalid request, transaction is not made from PayU!',
          400,
        );
      }

      if (oldStatus === PaymentStatus.COMPLETED) {
        console.log('Transaction already completed, skipping update.');
        return true;
      }

      if (!externalRefId) {
        throw new CustomError(
          'External reference ID not available for this transaction.',
          400,
        );
      }

      let newStatus: PaymentStatus = oldStatus;
      const paymentID = mihpayid || null;
      const paymentMethod = mode;
      const amountPaid = amount ? Number(amount) : 0;
      let extraData = undefined;

      // Map PayU webhook status to PaymentStatus
      switch (status) {
        case 'success':
          newStatus = PaymentStatus.COMPLETED;
          break;
        case 'failure':
          newStatus = PaymentStatus.FAILED;
          break;
        case 'pending':
          newStatus = PaymentStatus.PENDING;
          break;
        default:
      }

      if (newStatus === PaymentStatus.COMPLETED) {
        switch (mode) {
          case 'UPI':
            extraData = {
              rrn: body?.bank_ref_num || body?.bank_ref_no || '',
              upiId: body.field1,
            };
            break;
          case 'DC':
          case 'CC':
            extraData = {
              rrn: body?.bank_ref_num || body?.bank_ref_no || '',
              cardDetails: {
                last4Digits: String(body?.card_no).replace(/X/g, ''),
                cardType: mode === 'DC' ? 'Debit' : 'Credit',
                issuer: body?.bankcode || '',
              },
            };
            break;

          default:
            break;
        }
      }

      const updateExpression = [
        'SET #status = :status, #updatedAt = :updatedAt, paymentID = :paymentID, amountPaid = :amountPaid, paymentMethod = :paymentMethod',
      ];

      const expressionAttributeValues: Record<string, any> = {
        ':status': newStatus,
        ':paymentID': paymentID,
        ':updatedAt': moment().toISOString(),
        ':amountPaid': amountPaid,
        ':paymentMethod': paymentMethod,
      };

      const expressionAttributeNames: Record<string, string> = {
        '#status': 'status',
        '#updatedAt': 'updatedAt',
      };

      if (extraData) {
        Object.entries(extraData).map(([key, value]) => {
          updateExpression.push(`${key} = :${key}`);
          expressionAttributeValues[`:${key}`] = value;
        });
      }

      if (newStatus !== oldStatus) {
        updateExpression.push(
          'transactionCompletedDate = :transactionCompletedDate',
        );
        expressionAttributeValues[':transactionCompletedDate'] =
          moment().toISOString();
      }

      await this.docClient.updateItem(
        new UpdateCommand({
          TableName: PAYMENT_TABLE,
          Key: {
            orderId: orderId,
            transactionId: txnid,
          },
          UpdateExpression: updateExpression.join(', '),
          ExpressionAttributeNames: expressionAttributeNames,
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: 'ALL_NEW',
          ConditionExpression:
            'attribute_exists(orderId) AND attribute_exists(transactionId)',
        }),
      );

      const handler = new AutoConfirmOrder(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
        this.shopifyClient,
        this.s3Client,
      );
      handler.verifyPaymentAndConfirmOrder(orderId);

      return true;
    } catch (error) {
      console.log('error handlePayuWebhook :::: >', error);
      return true;
    }
  }

  async handleRazorpayPosWebhook(body: any) {
    try {
      console.log('body :::: >', body);
    } catch (error) {
      console.log('error handleRazorpayPosWebhook :::: >', error);
    }
  }

  async handleSnapmintWebhook(body: any) {
    try {
      console.log('body :::: >', body);
    } catch (error) {
      console.log('error handleSnapmintWebhook :::: >', error);
    }
  }

  async handlePineLabsWebhook(body: any) {
    try {
      console.log('body :::: >', body);
    } catch (error) {
      console.log('error handlePineLabsWebhook :::: >', error);
    }
  }
}
