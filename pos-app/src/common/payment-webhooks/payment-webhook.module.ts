import { Module } from '@nestjs/common';
import { PaymentWebhookService } from './payment-webhook.service';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from '../document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { AppSsmClient } from '../ssm-client/ssm-client';
import { PaymentWebhookController } from './payment-webhook.controller';
import { AppShopify } from '../shopify/shopify';
import { AppS3Client } from '../s3-client/s3-client';

@Module({
  providers: [
    PaymentWebhookService,
    ConfigService,
    AppDocumentClient,
    AppConfigParameters,
    AppSsmClient,
    AppShopify,
    AppS3Client,
  ],
  controllers: [PaymentWebhookController],
  exports: [PaymentWebhookService],
})
export class PaymentWebhookModule {}
