import { ObjectType, Field } from '@nestjs/graphql';

@ObjectType()
export class PrerequisiteQuantityRange {
  @Field(() => String, { nullable: true })
  greater_than_or_equal_to: string;
}

@ObjectType()
export class PrerequisiteSubtotalRange {
  @Field(() => String, { nullable: true })
  greater_than_or_equal_to: string;
}

@ObjectType()
export class PrerequisiteToEntitlementPurchase {
  @Field(() => Number, { nullable: true })
  prerequisite_amount: number;
}

@ObjectType()
export class PrerequisiteToEntitlementQuantityRatio {
  @Field(() => Number, { nullable: true })
  entitled_quantity: number;

  @Field(() => Number, { nullable: true })
  prerequisite_quantity: number;
}

@ObjectType()
export class PriceRule {
  @Field(() => String, { nullable: true })
  admin_graphql_api_id: string;

  @Field(() => String, { nullable: true })
  allocation_limit: string;

  @Field(() => String, { nullable: true })
  allocation_method: string;

  @Field(() => String, { nullable: true })
  created_at: string;

  @Field(() => [Number], { nullable: true })
  customer_segment_prerequisite_ids: number[];

  @Field(() => String, { nullable: true })
  customer_selection: string;

  @Field(() => String, { nullable: true })
  ends_at: string;

  @Field(() => [Number], { nullable: true })
  entitled_collection_ids: number[];

  @Field(() => [Number], { nullable: true })
  entitled_country_ids: number[];

  @Field(() => [Number], { nullable: true })
  entitled_product_ids: number[];

  @Field(() => [Number], { nullable: true })
  entitled_variant_ids: number[];

  @Field(() => Number, { nullable: true })
  id: number;

  @Field(() => Boolean, { nullable: true })
  once_per_customer: boolean;

  @Field(() => [Number], { nullable: true })
  prerequisite_collection_ids: number[];

  @Field(() => [Number], { nullable: true })
  prerequisite_customer_ids: number[];

  @Field(() => [Number], { nullable: true })
  prerequisite_product_ids: number[];

  @Field(() => PrerequisiteQuantityRange, { nullable: true })
  prerequisite_quantity_range: PrerequisiteQuantityRange;

  @Field(() => String, { nullable: true })
  prerequisite_shipping_price_range: string;

  @Field(() => PrerequisiteSubtotalRange, { nullable: true })
  prerequisite_subtotal_range: PrerequisiteSubtotalRange;

  @Field(() => PrerequisiteToEntitlementPurchase, { nullable: true })
  prerequisite_to_entitlement_purchase: PrerequisiteToEntitlementPurchase;

  @Field(() => PrerequisiteToEntitlementQuantityRatio, { nullable: true })
  prerequisite_to_entitlement_quantity_ratio: PrerequisiteToEntitlementQuantityRatio;

  @Field(() => [Number], { nullable: true })
  prerequisite_variant_ids: number[];

  @Field(() => String, { nullable: true })
  starts_at: string;

  @Field(() => String, { nullable: true })
  target_selection: string;

  @Field(() => String, { nullable: true })
  target_type: string;

  @Field(() => String, { nullable: true })
  title: string;

  @Field(() => String, { nullable: true })
  updated_at: string;

  @Field(() => String, { nullable: true })
  usage_limit: string;

  @Field(() => String, { nullable: true })
  value: string;

  @Field(() => String, { nullable: true })
  value_type: string;
}

@ObjectType()
export class CustomerSpecificCoupon {
  @Field(() => String, { nullable: false })
  code: string;
  @Field(() => String, { nullable: false })
  phone: string;
}

@ObjectType()
export class CouponData {
  @Field(() => String, { nullable: true })
  admin_graphql_api_id: string;

  @Field(() => String, { nullable: true })
  code: string;

  @Field(() => String, { nullable: true })
  createdAt?: string;

  @Field(() => String, { nullable: true })
  created_at: string;

  @Field(() => Number, { nullable: true })
  id: number;

  @Field(() => Number, { nullable: true })
  price_rule_id: number;

  @Field(() => String, { nullable: true })
  updatedAt: string;

  @Field(() => String, { nullable: true })
  updated_at: string;

  @Field(() => Number, { nullable: true })
  usage_count: number;

  @Field(() => Boolean, { nullable: true })
  isValidated: boolean;

  // Price rule related fields
  @Field(() => PriceRule, { nullable: true })
  priceRule: PriceRule;
}

@ObjectType()
export class Coupon {
  @Field(() => CouponData, { nullable: true })
  data: CouponData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
