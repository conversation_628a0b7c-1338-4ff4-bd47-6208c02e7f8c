import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class PromotionalCodeDiscountData {
  @Field(() => Boolean, { nullable: false })
  isValidated: boolean;

  @Field(() => Number, { nullable: false })
  discount: number;

  @Field(() => String, { nullable: true })
  message?: string;
}

@ObjectType()
export class PromotionalCodeDiscount {
  @Field(() => PromotionalCodeDiscountData, { nullable: true })
  data: PromotionalCodeDiscountData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
