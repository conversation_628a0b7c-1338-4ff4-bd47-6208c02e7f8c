import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CouponData } from '../entities/coupon.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';

export class GetCoupon {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getCoupon(code: string): Promise<CouponData> {
    posLogger.info('coupon', 'getCoupon shopify', { input: code });
    try {
      const COUPONS_TABLE = await this.configParameters.getCouponTableName();

      const couponCommand = new GetCommand({
        TableName: COUPONS_TABLE,
        Key: { pk: 'COUPON', sk: code },
      });
      const coupon = (await this.docClient.getItem(couponCommand)).Item;

      if (!coupon) {
        throw new CustomError(`Coupon ${code} not found`, 404);
      }

      const priceRuleCommand = new GetCommand({
        TableName: COUPONS_TABLE,
        Key: { pk: 'PRICE_RULE', sk: `${coupon.price_rule_id}` },
      });
      const priceRule = (await this.docClient.getItem(priceRuleCommand)).Item;
      const data = { ...coupon, priceRule };

      return data;
    } catch (e) {
      posLogger.error('coupon', 'getCoupon shopify', { error: e });
      throw e;
    }
  }
}
