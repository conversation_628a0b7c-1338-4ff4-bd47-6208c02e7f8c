import { CouponType } from 'src/common/enum/coupons';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { ConfigService } from '@nestjs/config';
import { CustomCodeValidationInput } from '../types/coupons';
import { GetGlobalConfiguration } from 'src/global-configurations/lib/get-global-configuration';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

export class ValidateCustomCode {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validateCustomCode(
    customCodeValidationInput: CustomCodeValidationInput,
  ): Promise<{ isValidated: boolean }> {
    posLogger.info('coupon', 'validateCustomCode', {
      input: { customCodeValidationInput },
    });

    try {
      const getGlobalConfigurationHandler = new GetGlobalConfiguration(
        this.configService,
        this.docClient,
        this.ssmClient,
        this.configParameters,
      );
      const { value: customDiscountConfig } =
        await getGlobalConfigurationHandler.getGlobalConfiguration(
          customCodeValidationInput.config,
        );

      const {
        code,
        cartTotal,
        totalDiscounts,
        campaignDiscountAmount = 0,
        promotionalDiscountAmount = 0,
        config,
      } = customCodeValidationInput;
      const { value_type, value } = code;
      let discountRules = null;

      const netCartTotal =
        config === 'CUSTOM_DISCOUNT'
          ? cartTotal - campaignDiscountAmount - promotionalDiscountAmount
          : cartTotal;

      try {
        discountRules = JSON.parse(customDiscountConfig);
      } catch (error) {
        throw new CustomError(error.message, 400);
      }
      const applicableDiscountRules = discountRules?.filter(
        (rule: any) =>
          cartTotal >= rule.minAmount &&
          (rule.maxAmount === null || cartTotal <= rule.maxAmount),
      );
      const fixedDiscountRule = applicableDiscountRules?.find(
        (rule: any) => rule.discountType === 'FIXED',
      );
      const percentageDiscountRule = applicableDiscountRules?.find(
        (rule: any) => rule.discountType === 'PERCENTAGE',
      );
      const maxDiscountValue = fixedDiscountRule
        ? Math.round((Number(fixedDiscountRule?.maxDiscountValue) * 100) / 100)
        : Math.round(
            (netCartTotal * Number(percentageDiscountRule?.maxDiscountValue)) /
              100,
          );

      // Apply netCartTotal conditionally instead of totalDiscounts when config === "CUSTOM_DISCOUNT"
      const remainingDiscount = Math.max(
        maxDiscountValue - (config === 'CUSTOM_DISCOUNT' ? 0 : totalDiscounts),
        0,
      );

      const lowerDiscount = Math.round(
        Math.min(remainingDiscount, maxDiscountValue),
      );

      const discountAmount = Math.round((Number(value) * netCartTotal) / 100);

      if (
        !applicableDiscountRules ||
        (value_type === CouponType.FIXED && Number(value) > cartTotal) ||
        (value_type === CouponType.PERCENTAGE &&
          discountAmount > lowerDiscount) ||
        Number(remainingDiscount) == 0 ||
        (value_type === CouponType.FIXED && Number(value) > lowerDiscount)
      ) {
        return { isValidated: false };
      }

      return {
        isValidated: true,
      };
    } catch (e) {
      posLogger.error('coupon', 'validateCustomCode', { error: e });
      throw e;
    }
  }
}
