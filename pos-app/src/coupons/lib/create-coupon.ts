import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import {
  createDiscountCodePayload,
  createPriceRulePayload,
  getDate,
} from 'src/cron/helper/lib';
import { CreateCouponInput } from '../dto/create-coupon.input';
import { CouponData } from '../entities/coupon.entity';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';

export class CreateCoupon {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}

  async createCoupon(
    createCouponInput: CreateCouponInput,
  ): Promise<CouponData> {
    posLogger.info('coupon', 'createCoupon shopify', {
      input: { createCouponInput },
    });
    try {
      const [COUPONS_TABLE, SHOPIFY_ACCESS_TOKEN, SHOPIFY_ADMIN_BASE_URL] =
        await Promise.all([
          await this.configParameters.getCouponTableName(),
          await this.shopifyClient.getShopifyAccessToken(),
          await this.shopifyClient.getShopifyAdminBaseUrl(),
        ]);

      const date = getDate(0);
      const createPriceRuleShopify = await fetch(
        `${SHOPIFY_ADMIN_BASE_URL}/price_rules.json`,
        {
          method: 'POST',
          headers: {
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            price_rule: {
              title: `${createCouponInput.storeId}store${Date.now()}`,
              target_type: 'line_item',
              target_selection: 'all',
              value_type: createCouponInput.value_type,
              value: `-${createCouponInput.value}`,
              allocation_method: 'across',
              customer_selection: 'prerequisite',
              prerequisite_customer_ids: [`${createCouponInput.customerId}`],
              once_per_customer: true,
              usage_limit: 1,
              starts_at: date,
            },
          }),
        },
      );
      const { price_rule: priceRule, errors: priceRuleErrors } =
        await createPriceRuleShopify.json();

      if (priceRuleErrors) throw new Error(JSON.stringify(priceRuleErrors));

      const priceRuleCommand = new PutCommand({
        TableName: COUPONS_TABLE,
        Item: createPriceRulePayload(priceRule),
      });

      await this.docClient.createItem(priceRuleCommand);

      const createDiscountCodeShopify = await fetch(
        `${SHOPIFY_ADMIN_BASE_URL}/price_rules/${priceRule.id}/discount_codes.json`,
        {
          method: 'POST',
          headers: {
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ discount_code: { code: priceRule.title } }),
        },
      );
      const { discount_code: discountCode, errors: discountErrors } =
        await createDiscountCodeShopify.json();

      if (discountErrors) throw new Error(JSON.stringify(discountErrors));

      const discountCodeCommand = new PutCommand({
        TableName: COUPONS_TABLE,
        Item: createDiscountCodePayload(discountCode),
      });
      await this.docClient.createItem(discountCodeCommand);

      return {
        ...discountCode,
        priceRule,
      };
    } catch (e) {
      posLogger.error('coupon', 'createCoupon shopify', { error: e });
      throw e;
    }
  }
}
