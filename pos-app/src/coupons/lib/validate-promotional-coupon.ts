import { CouponStatus } from 'src/common/enum/coupons';
import { getCouponStatus } from 'src/common/helper/get-coupon-status';
import { getPriceRuleStatus } from 'src/common/helper/get-price-rule-status';
import { posLogger } from 'src/common/logger';
import { CouponDetails } from '../types/coupons';
import { AppShopify } from 'src/common/shopify/shopify';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';
import { GetCollection } from 'src/collections/lib/get-collection';
import { getTotalAmount } from 'src/common/helper/get-total-amount';
import { GetCustomer } from 'src/customers/lib/get-customer';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CustomError } from 'src/common/response/errorHandler/error.handler';

export class ValidatePromotionalCode {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private shopifyClient: AppShopify,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validatePromotionalCode(
    couponDetails: CouponDetails,
    isEditingQuotation: boolean,
  ): Promise<{
    isValidated: boolean;
    applicableItems;
    message?: string;
  }> {
    posLogger.info('coupons', 'validatePromotionalCode shopify', {
      input: couponDetails.coupon,
    });
    try {
      const { coupon, cartItems, phone } = couponDetails;
      const { priceRule, usage_count: usageCount } = coupon;
      const {
        usage_limit: usageLimit = null,
        starts_at: startsAt,
        ends_at: endsAt,
        target_selection: targetSelection,
        entitled_product_ids: entitledProductIds,
        entitled_variant_ids: entitledVariantIds,
        entitled_collection_ids: entitledCollectionIds,
        prerequisite_subtotal_range,
        prerequisite_quantity_range,
        prerequisite_customer_ids = null,
        title,
      } = priceRule;

      const invalidCoupons = [
        'PC1500',
        'SHAMIK15',
        'LAZY15',
        'AP1500',
        'TECH15',
        'AKR1500',
        'CS15',
        'TSTG15',
        'ABHI15',
        'TECHSTAR15',
      ];
      if (invalidCoupons.includes(coupon.code)) {
        return {
          isValidated: false,
          applicableItems: null,
          message: `Coupon ${coupon.code} is not applicable`,
        };
      }

      const priceRuleStatus = isEditingQuotation
        ? CouponStatus.ACTIVE
        : getPriceRuleStatus(startsAt, endsAt);

      const couponStatus = getCouponStatus(
        usageCount,
        usageLimit,
        priceRuleStatus,
      );
      if (couponStatus !== CouponStatus.ACTIVE) {
        return { isValidated: false, applicableItems: null };
      }

      let applicableItems = cartItems;

      if (prerequisite_customer_ids?.length) {
        const getHandler = new GetCustomer(
          this.configService,
          this.ssmClient,
          this.shopifyClient,
          this.configParameters,
          this.docClient,
        );
        if (!phone) {
          console.log('number de bhai');
          return {
            isValidated: false,
            applicableItems: null,
            message: `Please Login to avail this coupon`,
          };
        }
        const customer = await getHandler.getCustomer(phone, 'API', true);

        console.log('customer is', prerequisite_customer_ids, customer);
        if (Array.isArray(customer)) {
          const isCustomerExits = prerequisite_customer_ids.some((value: any) =>
            customer.some(
              (customerId: any) => String(customerId) === String(value),
            ),
          );
          console.log('isCustomerExits', isCustomerExits);
          if (!isCustomerExits) {
            return { isValidated: false, applicableItems: null };
          }
        }
      }
      if (targetSelection === 'entitled') {
        if (entitledCollectionIds?.length) {
          const entitledCollectionProductIds = [];

          const getCollectionHandler = new GetCollection(
            this.docClient,
            this.configParameters,
          );

          await Promise.all(
            entitledCollectionIds.map(async (item) => {
              entitledCollectionProductIds.push(
                ...((await getCollectionHandler.getCollection(item.toString()))
                  ?.productIds || []),
              );
            }),
          );

          applicableItems = cartItems.filter((item: any) =>
            entitledCollectionProductIds.includes(item.productId),
          );
        } else {
          applicableItems = cartItems.filter((item) =>
            (entitledProductIds || []).includes(Number(item.productId)),
          );

          applicableItems.push(
            ...cartItems.filter(
              (item) =>
                item.variantId &&
                (entitledVariantIds || []).includes(Number(item.variantId)),
            ),
          );
        }
      }
      const cartQuantity = cartItems.reduce(
        (acc: number, item: any) => acc + Number(item?.quantity),
        0,
      );
      console.log('======cartQuantity======', cartQuantity);

      if (prerequisite_quantity_range) {
        if (
          cartQuantity <
          Number(prerequisite_quantity_range?.greater_than_or_equal_to)
        ) {
          return { isValidated: false, applicableItems: null };
        }
      }
      if (prerequisite_subtotal_range) {
        if (
          getTotalAmount(applicableItems) <
          Number(prerequisite_subtotal_range.greater_than_or_equal_to)
        ) {
          return { isValidated: false, applicableItems: null };
        }
      }

      if (!applicableItems?.length && !title.includes('SPIN-THE-WHEEL')) {
        return {
          isValidated: false,
          applicableItems: null,
        };
      }

      return {
        isValidated: true,
        applicableItems,
      };
    } catch (e) {
      posLogger.error('coupon', 'validatePromotionalCode shopify', {
        error: e,
      });
      throw e;
    }
  }
}
