import { posLogger } from 'src/common/logger';
import { GetCampaignCoupon } from 'src/campaign-coupons/lib/get-campaign-coupon';
import { validateCouponUsageLimit } from 'src/common/helper/validate-usage-limits';
import { GetCoupon } from './get-coupon';
import { CampaignCouponData } from 'src/campaign-coupons/entities/campaign-coupon.entity';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';
import { CouponData } from '../entities/coupon.entity';

export class ValidateUsageLimit {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async validateUsageLimit(
    code: string,
    storeId: string,
    type: 'CAMPAIGN' | 'PROMOTIONAL',
  ): Promise<{ isValidated: boolean }> {
    try {
      posLogger.info('coupon', 'validateUsageLimit', { input: { code, type } });

      let usageLimit = null;
      let usageCount = null;

      if (type === 'CAMPAIGN') {
        const getCampaignCouponHandler = new GetCampaignCoupon(
          this.configService,
          this.docClient,
          this.ssmClient,
          this.configParameters,
        );
        const {
          usageLimit: campaignUsageLimit,
          usageCount: campaignUsageCount,
          usageLimitForEachStore = false,
          storeWiseUsageCount = [],
          storeUsageLimit = [],
        }: CampaignCouponData = await getCampaignCouponHandler.getCampaignCoupon(
          code,
        );

        if (!usageLimitForEachStore) {
          usageLimit = campaignUsageLimit;
          usageCount = campaignUsageCount;
        } else {
          usageLimit =
            storeUsageLimit?.find((limit) => limit.storeId == storeId)
              ?.usageLimit || null;
          usageCount =
            storeWiseUsageCount?.find((usage) => usage.storeId === storeId)
              ?.usageCount || 0;
        }
      } else if (type === 'PROMOTIONAL') {
        const getPromotionalCouponHandler = new GetCoupon(
          this.configService,
          this.ssmClient,
          this.docClient,
          this.configParameters,
        );
        const {
          priceRule: { usage_limit: promoUsageLimit },
          usage_count: promoUsageCount,
        }: CouponData = await getPromotionalCouponHandler.getCoupon(code);

        usageLimit = promoUsageLimit;
        usageCount = promoUsageCount;
      }

      return validateCouponUsageLimit(usageLimit, usageCount, true);
    } catch (error) {
      posLogger.error('coupon', 'validateUsageLimit', { error });
      throw error;
    }
  }
}
