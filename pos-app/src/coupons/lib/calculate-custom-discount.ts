import { ConfigService } from '@nestjs/config';
import { CouponType } from 'src/common/enum/coupons';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import {
  CustomCodeValidationInput,
  CouponCalculationResponse,
} from 'src/coupons/types/coupons';
import { ValidateCustomCode } from './validate-custom-coupon';
import { posLogger } from 'src/common/logger';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppConfigParameters } from 'src/config/config';

export class CalculateCustomCodeDiscount {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
  ) {}

  async calculateCustomDiscount(
    customCodeValidationInput: CustomCodeValidationInput,
  ): Promise<CouponCalculationResponse> {
    posLogger.info('coupon', 'calculateCustomDiscount', {
      input: { customCodeValidationInput },
    });

    const {
      code,
      cartTotal,
      config,
      campaignDiscountAmount = 0,
      promotionalDiscountAmount = 0,
    } = customCodeValidationInput;
    const { value_type, value } = code;
    const res: CouponCalculationResponse = {
      isValidated: false,
      discount: 0,
    };

    const validator = new ValidateCustomCode(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    const { isValidated } = await validator.validateCustomCode(
      customCodeValidationInput,
    );

    if (!isValidated) {
      return res;
    }

    if (value_type === CouponType.FIXED) {
      res.discount = Number(value);
    } else if (value_type === CouponType.PERCENTAGE) {
      const discountBase =
        config === 'CUSTOM_DISCOUNT'
          ? cartTotal - campaignDiscountAmount - promotionalDiscountAmount
          : cartTotal;

      res.discount = Number(discountBase) * (Number(value) / 100);
    }

    return {
      ...res,
      isValidated: true,
      discount:
        config == 'REPLACEMENT_CUSTOM_DISCOUNT' &&
        value == 100 &&
        value_type === CouponType.PERCENTAGE
          ? res.discount
          : Math.round(res.discount),
    };
  }
}
