import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { PromotionalCodeDiscountInput } from '../dto/validate-promotional-coupon.input';
import { PromotionalCodeDiscountData } from '../entities/validate-custom-coupon.entity';
import { CouponCalculationResponse } from '../types/coupons';
import { GetCoupon } from './get-coupon';
import { CouponData, CustomerSpecificCoupon } from '../entities/coupon.entity';
import { ValidatePromotionalCode } from './validate-promotional-coupon';
import { CouponType } from 'src/common/enum/coupons';
import { ValidateProducts } from 'src/common/helper/validate-products';
import { getTotalAmount } from 'src/common/helper/get-total-amount';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';
import { GetCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { GetCollection } from 'src/collections/lib/get-collection';
import { GetCustomer } from 'src/customers/lib/get-customer';
import { extractPrefix } from 'src/spin-wheel/helper/extractPrefix';

export class CalculatePromotionalDiscount {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private docClient: AppDocumentClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}

  async handleCustomerSpecificCoupon(
    phone: string,
    priceRuleId: number,
    existingCustomerIds = [],
  ) {
    try {
      const [SHOPIFY_ACCESS_TOKEN, SHOPIFY_ADMIN_BASE_URL] = await Promise.all([
        await this.shopifyClient.getShopifyAccessToken(),
        await this.shopifyClient.getShopifyAdminBaseUrl(),
      ]);
      const getHandler = new GetCustomer(
        this.configService,
        this.ssmClient,
        this.shopifyClient,
        this.configParameters,
        this.docClient,
      );
      const customer = await getHandler.getCustomer(phone, 'API', true);

      const allCustomerIds = Array.isArray(customer)
        ? [...customer, ...existingCustomerIds]
        : [Number(customer?.id), ...existingCustomerIds];
      const customerIds = [...new Set(allCustomerIds)];
      console.log('customerIds', customerIds);

      const shopifyUpdateResponse = await fetch(
        `${SHOPIFY_ADMIN_BASE_URL}/price_rules/${priceRuleId}.json`,
        {
          method: 'PUT', // Use PUT for updating an existing price rule
          headers: {
            'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            price_rule: {
              customer_selection: 'prerequisite',
              prerequisite_customer_ids: customerIds,
            },
          }),
        },
      );
      console.log('shopifyUpdateResponse', shopifyUpdateResponse);

      if (!shopifyUpdateResponse.ok) {
        const errorDetails = await shopifyUpdateResponse.json();
        throw new CustomError(
          `Failed to update price rule in Shopify. Details: ${JSON.stringify(
            errorDetails,
          )}`,
          shopifyUpdateResponse.status,
        );
      }

      const COUPON_TABLE = await this.configParameters.getCouponTableName();

      console.log(`Updating DynamoDB record for price rule: ${priceRuleId}`);

      const updateExpressionString =
        'SET #customerSelection = :customerSelection, #prerequisiteCustomerIds = :customerIds';

      const expressionAttributeNames = {
        '#customerSelection': 'customer_selection',
        '#prerequisiteCustomerIds': 'prerequisite_customer_ids',
      };

      const expressionAttributeValues = {
        ':customerSelection': 'prerequisite',
        ':customerIds': customerIds,
      };

      const command = new UpdateCommand({
        TableName: COUPON_TABLE,
        Key: {
          pk: 'PRICE_RULE',
          sk: String(priceRuleId),
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ConditionExpression: 'attribute_exists(pk) AND attribute_exists(sk)',
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes } = await this.docClient.updateItem(command);

      if (Attributes) {
        console.log(
          `Successfully updated DynamoDB record for price rule: ${priceRuleId}`,
        );
      }

      return Attributes;
    } catch (error) {
      posLogger.error(
        'handleCustomerSpecificCoupon',
        `Failed to handle customer-specific coupon for priceRuleId: ${priceRuleId}`,
        error,
      );

      if (!(error instanceof CustomError)) {
        throw new CustomError(
          `Unexpected error while handling customer-specific coupon. Details: ${error.message}`,
          500,
        );
      }

      throw error;
    }
  }

  async getFinalDiscountPrice(items) {
    if (!Array.isArray(items) || items.length === 0) {
      return 0;
    }

    if (items.length === 1) {
      return items[0].price;
    }

    const leastPriceObj = items.reduce((minObj, currentObj) =>
      currentObj.price < minObj.price ? currentObj : minObj,
    );

    return leastPriceObj.price;
  }

  async calculatePromotionalDiscount(
    promotionalCodeDiscountInput: PromotionalCodeDiscountInput,
    source = 'API',
    isEditingQuotation = false,
  ): Promise<PromotionalCodeDiscountData> {
    posLogger.info('coupons', 'calculatePromotionalDiscount shopify', {
      input: promotionalCodeDiscountInput,
    });

    try {
      const {
        code,
        cartTotal: cartTotalInput,
        cartItems,
        phone,
      } = promotionalCodeDiscountInput;
      const res: CouponCalculationResponse = {
        isValidated: false,
        discount: 0,
        message: '',
      };

      const getHandler = new GetCoupon(
        this.configService,
        this.ssmClient,
        this.docClient,
        this.configParameters,
      );
      let coupon: CouponData = await getHandler.getCoupon(code);

      const CUSTOMER_SPECIFIC_COUPON_TABLE =
        await this.configParameters.getCustomerSpecificCouponTableName();

      const couponCommand = new GetCommand({
        TableName: CUSTOMER_SPECIFIC_COUPON_TABLE,
        Key: { code },
      });
      const customerCoupon: CustomerSpecificCoupon | null = (
        await this.docClient.getItem(couponCommand)
      ).Item;

      if (customerCoupon) {
        if (!phone) {
          throw new CustomError(
            'Login to your account to use this coupon',
            400,
          );
        }
        const { phone: customerSpecPhone } = customerCoupon;

        if (phone && phone.replace('+91', '') !== customerSpecPhone) {
          throw new CustomError('Invalid coupon for the given customer', 400);
        }
        const {
          priceRule: { prerequisite_customer_ids: existingCustomerIds },
        } = coupon;

        console.log('coupon.price_rule_id', coupon.price_rule_id);
        await this.handleCustomerSpecificCoupon(
          phone,
          coupon.price_rule_id,
          existingCustomerIds,
        );
        coupon = await getHandler.getCoupon(code);
      }

      let cartTotal = cartTotalInput,
        cartProductItems = cartItems;

      if (source === 'API') {
        const productsValidator = new ValidateProducts(
          this.configService,
          this.ssmClient,
          this.docClient,
          this.configParameters,
        );
        cartProductItems = await productsValidator.validateProducts(cartItems);

        if (!cartProductItems?.length) {
          throw new CustomError('Products cannot be empty', 400);
        }

        cartTotal = getTotalAmount(cartProductItems);
      }

      const validator = new ValidatePromotionalCode(
        this.configService,
        this.ssmClient,
        this.shopifyClient,
        this.docClient,
        this.configParameters,
      );
      const { isValidated, applicableItems, message } =
        await validator.validatePromotionalCode(
          {
            coupon,
            cartItems: cartProductItems,
            cartTotal,
            phone: phone,
          },
          isEditingQuotation,
        );
      if (message) {
        res.message = message;
      }
      if (!isValidated) {
        return res;
      }
      const shopifyCoupon = coupon;

      const { priceRule } = coupon;
      const {
        value,
        value_type,
        target_selection,
        allocation_method,
        title,
        prerequisite_subtotal_range,
      } = priceRule;

      let totalPriceForApplicableItems = cartTotal;
      const discountValue = Math.abs(Number(value));

      if (target_selection === 'entitled') {
        totalPriceForApplicableItems = applicableItems.reduce(
          (total, { price, quantity }) => {
            return total + quantity * price;
          },
          0,
        );
      }

      if (value_type === CouponType.PERCENTAGE) {
        if (discountValue > 100) {
          return res;
        }
        res.discount = Math.min(
          totalPriceForApplicableItems * (discountValue / 100),
          cartTotal,
        );
      } else if (value_type === CouponType.FIXED) {
        if (res.discount > cartTotal) {
          return res;
        }

        let totalApplicableQuantity = 1;
        if (target_selection === 'entitled' && allocation_method === 'each') {
          totalApplicableQuantity = applicableItems.reduce(
            (total, { quantity }) => {
              return total + quantity;
            },
            0,
          );
        }

        res.discount = Math.min(
          totalApplicableQuantity * discountValue,
          cartTotal,
        );
      }

      if (
        title.includes('SPIN-THE-WHEEL') &&
        !prerequisite_subtotal_range?.greater_than_or_equal_to
      ) {
        const SPIN_THE_WHEEL_TABLE =
          await this.configParameters.getSpinTheWheelCouponConfigTableName();
        const prefix = extractPrefix(code);

        const couponCommand = new GetCommand({
          TableName: SPIN_THE_WHEEL_TABLE,
          Key: { prefix: prefix },
        });
        const coupon = (await this.docClient.getItem(couponCommand)).Item;

        if (!coupon) {
          throw new CustomError(`Coupon ${code} not found`, 404);
        }

        const {
          entitled_collection_ids = [],
          entitled_variant_ids = [],
          entitled_product_ids = [],
        } = coupon;

        let isExists;
        const modifiedCouponData = {
          ...shopifyCoupon,
          priceRule: {
            ...shopifyCoupon.priceRule,
            entitled_collection_ids,
            entitled_variant_ids,
            entitled_product_ids,
          },
        };
        const { applicableItems } = await validator.validatePromotionalCode(
          {
            coupon: modifiedCouponData,
            cartItems: cartProductItems,
            cartTotal,
            phone: phone,
          },
          isEditingQuotation,
        );

        const collectionIds = Array.isArray(entitled_collection_ids)
          ? entitled_collection_ids
          : [];
        const variantIds = Array.isArray(entitled_variant_ids)
          ? entitled_variant_ids
          : [];
        const productIds = Array.isArray(entitled_product_ids)
          ? entitled_product_ids
          : [];

        if (
          collectionIds.length > 0 ||
          variantIds.length > 0 ||
          productIds.length > 0
        ) {
          const getCollectionHandler = new GetCollection(
            this.docClient,
            this.configParameters,
          );

          const entitledCollectionProductIds = (
            await Promise.all(
              collectionIds.map(
                async (collectionId) =>
                  (
                    await getCollectionHandler.getCollection(
                      collectionId.toString(),
                    )
                  )?.productIds || [],
              ),
            )
          ).flat();

          const entitledIds = new Set([
            ...entitledCollectionProductIds,
            ...productIds.map(Number),
            ...variantIds.map(Number),
          ]);

          isExists = cartItems.filter(
            (item) =>
              entitledIds.has(Number(item.productId)) ||
              entitledIds.has(Number(item.variantId)),
          );
        }

        console.log('isExists', isExists);
        if (!isExists?.length) {
          return {
            isValidated: false,
            discount: 0,
            message: 'Not applicable for current cart items',
          };
        }
        if (allocation_method == 'across') {
          if (value_type == 'percentage') {
            let cartTotalForPercentage = cartTotal;
            if (target_selection === 'entitled') {
              cartTotalForPercentage = applicableItems.reduce(
                (total, { price, quantity }) => {
                  return total + quantity * price;
                },
                0,
              );
            }
            res.discount = Math.min(
              cartTotalForPercentage * (discountValue / 100),
              cartTotal,
            );
          }
          if (value_type == 'fixed_amount') {
            console.log('fixe dis', Math.min(Number(coupon?.value), cartTotal));
            res.discount = Math.min(Number(coupon?.value), cartTotal);
          }
        }
        if (allocation_method == 'each') {
          const finalDiscount =
            await this.getFinalDiscountPrice(applicableItems);

          const totalQuantity = applicableItems.reduce(
            (total, { quantity }) => {
              return total + quantity;
            },
            0,
          );

          console.log('totalQuantity', totalQuantity, finalDiscount);
          res.discount = Number(finalDiscount - 1);
        }
      }
      return {
        ...res,
        isValidated: true,
        discount: Math.round(res.discount),
      };
    } catch (e) {
      posLogger.error('coupon', 'calculatePromotionalDiscount shopify', {
        error: e,
      });
      throw e;
    }
  }
}
