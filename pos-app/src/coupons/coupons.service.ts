import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CreateCouponInput } from './dto/create-coupon.input';
import { CreateCoupon } from './lib/create-coupon';
import { GetCoupon } from './lib/get-coupon';
import { PromotionalCodeDiscountInput } from './dto/validate-promotional-coupon.input';
import { CalculatePromotionalDiscount } from './lib/calculate-promotional-discount';
import { PromotionalCodeDiscountData } from './entities/validate-custom-coupon.entity';
import { CronService } from 'src/cron/cron.service';
import { posLogger } from 'src/common/logger';
import { AppConfigParameters } from 'src/config/config';
import { AppShopify } from 'src/common/shopify/shopify';

@Injectable()
export class CouponsService {
  constructor(
    private readonly cronService: CronService,
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
    private shopifyClient: AppShopify,
  ) {}
  async create(createCouponInput: CreateCouponInput) {
    const createHandler = new CreateCoupon(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
      this.shopifyClient,
    );
    return createHandler.createCoupon(createCouponInput);
  }

  async findOne(code: string) {
    const getHandler = new GetCoupon(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
    );
    return getHandler.getCoupon(code);
  }

  async calculatePromotionalDiscount(
    promotionalCodeValidationInput: PromotionalCodeDiscountInput,
    isEditingQuotation,
  ): Promise<PromotionalCodeDiscountData> {
    const validatePromotionalCodeHandler = new CalculatePromotionalDiscount(
      this.configService,
      this.ssmClient,
      this.docClient,
      this.configParameters,
      this.shopifyClient,
    );
    return await validatePromotionalCodeHandler.calculatePromotionalDiscount(
      promotionalCodeValidationInput,
      'API',
      isEditingQuotation,
    );
  }

  async fetchCoupons() {
    posLogger.info('coupons', 'fetchCoupons shopify', 'fetching coupons');

    this.cronService.fetchShopifyCoupons().catch((error) => {
      posLogger.error('coupons', 'fetchCoupons', error);
    });
    return { message: 'Fetching coupons task has been initiated' };
  }
}
