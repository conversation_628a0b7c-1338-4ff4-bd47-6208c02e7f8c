import {
  CartProductInput,
  CustomCodeInput,
} from 'src/carts/dto/create-cart.input';
import { CouponData } from '../entities/coupon.entity';

export interface CustomCodeValidationInput {
  code: CustomCodeInput;
  cartTotal: number;
  config: string;
  totalDiscounts?: number;
  promotionalDiscountAmount: number;
  campaignDiscountAmount: number;
}

export interface CouponDetails {
  coupon: CouponData;
  cartItems: any;
  cartTotal: number;
  phone?: string;
}

export interface CouponCalculationResponse {
  discount: number;
  isValidated: boolean;
  applicableProductsInCart?: CartProductInput[];
  message?: string;
}

export interface validatedProductResponse {
  isValidated: boolean;
  cartItems: any[];
  freeProducts: any[];
  finalDiscountedAmount: number;
}

export interface getCoupon {
  code: string;
  couponType: string;
}
export interface getCouponData {
  couponData: CouponData;
  type: string;
}

export enum CouponTypes {
  CAMPAIGN = 'CAMPAIGN',
  PROMOTIONAL = 'PROMOTIONAL',
  CUSTOM = 'CUSTOM',
}

export enum DiscountTypes {
  CART = 'CART',
  PRODUCT = 'PRODUCT',
}
