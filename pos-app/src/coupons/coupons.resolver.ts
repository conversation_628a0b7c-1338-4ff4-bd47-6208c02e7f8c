import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { CouponsService } from './coupons.service';
import { CreateCouponInput } from './dto/create-coupon.input';
import { Coupon } from './entities/coupon.entity';
import { PromotionalCodeDiscountInput } from './dto/validate-promotional-coupon.input';
import { PromotionalCodeDiscount } from './entities/validate-custom-coupon.entity';
import { Response } from 'src/products/entities/product.entity';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { AdminGuard, CRMGuard } from 'src/auth/roles.guard';

@Resolver(() => Coupon)
@UseGuards(CustomAuthGuard, CRMGuard)
export class CouponsResolver {
  constructor(
    private readonly couponsService: CouponsService,
    private readonly successHandler: SuccessHandler,
    private readonly errorHandler: ErrorHandler,
  ) {}

  @Mutation(() => Coupon)
  async createCoupon(
    @Args('createCouponInput') createCouponInput: CreateCouponInput,
  ) {
    try {
      const data = await this.couponsService.create(createCouponInput);

      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create shopify coupon',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Coupon, { name: 'getCoupon' })
  async findOne(@Args('code', { type: () => String }) code: string) {
    try {
      const data = await this.couponsService.findOne(code);

      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get coupon',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => PromotionalCodeDiscount, {
    name: 'calculatePromotionalDiscount',
  })
  async calculatePromotionalDiscount(
    @Args('promotionalCodeCalculationInput', {
      type: () => PromotionalCodeDiscountInput,
    })
    promotionalCodeCalculationInput: PromotionalCodeDiscountInput,

    @Args('isEditingQuotation', { type: () => Boolean, nullable: true })
    isEditingQuotation: boolean = false,
  ) {
    try {
      const data = await this.couponsService.calculatePromotionalDiscount(
        promotionalCodeCalculationInput,
        isEditingQuotation,
      );

      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to calculate promotional coupon',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @UseGuards(AdminGuard)
  @Query(() => Response, { name: 'fetchCoupons' })
  async fetchCoupons() {
    return await this.couponsService.fetchCoupons();
  }
}
