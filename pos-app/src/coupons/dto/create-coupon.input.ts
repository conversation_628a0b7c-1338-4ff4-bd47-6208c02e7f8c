import { InputType, Field, registerEnumType } from '@nestjs/graphql';
import { CouponType } from 'src/common/enum/coupons';

// Register the enum with GraphQL
registerEnumType(CouponType, { name: 'CouponType' });

@InputType()
export class CreateCouponInput {
  @Field(() => String)
  value_type: CouponType;

  @Field(() => String)
  value: string;

  @Field(() => String)
  customerId: string;

  @Field(() => String)
  storeId: string;
}
