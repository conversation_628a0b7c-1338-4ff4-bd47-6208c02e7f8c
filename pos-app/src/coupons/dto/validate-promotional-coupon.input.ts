import { Field, InputType } from '@nestjs/graphql';
import { CartProductInput } from 'src/carts/dto/create-cart.input';

@InputType()
export class PromotionalCodeDiscountInput {
  @Field(() => String, { description: 'Coupon Code' })
  code: string;

  @Field(() => Number, { description: 'Cart total' })
  cartTotal: number;

  @Field(() => [CartProductInput], { description: 'Cart items' })
  cartItems: CartProductInput[];

  @Field(() => String, { description: 'Customer phone number', nullable: true })
  phone?: string;
}
