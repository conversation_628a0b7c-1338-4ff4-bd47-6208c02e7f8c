import { ObjectType, Field, Int } from '@nestjs/graphql';
import { CouponType } from 'src/common/enum/coupons';

@ObjectType()
export class CustomRangeData {
  @Field(() => String, { nullable: false })
  min: string;

  @Field(() => String, { nullable: false })
  max: string;

  @Field(() => Boolean, { nullable: true })
  includesMin: boolean;
}

@ObjectType()
export class CustomData {
  @Field(() => String, { nullable: false })
  length: string;

  @Field(() => String, { nullable: false })
  breadth: string;

  @Field(() => String, { nullable: false })
  height: string;

  @Field(() => CustomRangeData, { nullable: true })
  lengthRange: CustomRangeData;

  @Field(() => CustomRangeData, { nullable: true })
  breadthRange: CustomRangeData;
}

@ObjectType()
export class CartProduct {
  @Field(() => String, { nullable: false })
  productId: string;

  @Field(() => String, { nullable: true })
  variantId?: string;

  @Field(() => Int, { nullable: false })
  quantity: number;

  @Field(() => CustomData, { nullable: true })
  customData?: CustomData;

  @Field(() => Number, { nullable: true })
  price?: number;

  @Field(() => String, { nullable: true })
  priceType?: string;
}

@ObjectType()
export class CustomCode {
  @Field(() => CouponType, { nullable: false })
  value_type: CouponType;

  @Field(() => String, { nullable: false })
  approver: string;

  @Field(() => Number, { nullable: false })
  value: number;
}

@ObjectType()
export class CartData {
  @Field(() => String, { nullable: false })
  id: string;

  @Field(() => String, { nullable: false })
  customerId: string;

  @Field(() => [CartProduct], { nullable: true })
  cartProducts: CartProduct[];

  @Field(() => String, { nullable: false })
  storeId: string;

  @Field(() => String, { nullable: false })
  employeeId: string;

  @Field(() => String, { nullable: true })
  campaignCode?: string;

  @Field(() => CustomCode, { nullable: true })
  customCode?: CustomCode;

  @Field(() => String, { nullable: true })
  promotionalCode?: string;

  @Field(() => String, { nullable: true })
  notes?: string;

  @Field(() => String, { nullable: true })
  source?: string;

  @Field(() => String, { nullable: false })
  createdAt: string;

  @Field(() => String, { nullable: false })
  updatedAt: string;

  @Field(() => String, { nullable: true })
  pinCode?: string;
}

@ObjectType()
export class Carts {
  @Field(() => [CartData], { nullable: true })
  data: CartData[];

  @Field(() => Number, { description: 'Count of Total data', nullable: true })
  count: number;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => Number, { nullable: true })
  status?: number;

  @Field(() => Boolean, { nullable: true })
  success?: boolean;
}

@ObjectType()
export class Cart {
  @Field(() => CartData, { nullable: true })
  data: CartData;

  @Field(() => String, { nullable: true })
  message: string;

  @Field(() => Number, { nullable: true })
  status: number;

  @Field(() => Boolean, { nullable: true })
  success: boolean;
}
