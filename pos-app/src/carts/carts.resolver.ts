import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { CartsService } from './carts.service';
import { <PERSON>t, Carts } from './entities/cart.entity';
import { CreateCartInput } from './dto/create-cart.input';
import { UpdateCartInput } from './dto/update-cart.input';
import { ListCartInput } from './dto/list-carts.input';
import { SuccessHandler } from 'src/common/response/successHandler/success.handler';
import { ErrorHandler } from 'src/common/response/errorHandler/error.handler';
import { HttpStatus, UseGuards } from '@nestjs/common';
import { CustomAuthGuard } from 'src/auth/auth.guard';
import { CRMGuard } from 'src/auth/roles.guard';

@Resolver(() => Cart)
@UseGuards(CustomAuthGuard, CRMGuard)
export class CartsResolver {
  constructor(
    private readonly cartsService: CartsService,
    private readonly successHandler: SuccessH<PERSON><PERSON>,
    private readonly errorHandler: <PERSON>rror<PERSON><PERSON><PERSON>,
  ) {}

  @Mutation(() => Cart)
  async createCart(@Args('createCartInput') createCartInput: CreateCartInput) {
    try {
      const data = await this.cartsService.create(createCartInput);
      return this.successHandler.getSuccessResponse({
        data,
        code: 201,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to create cart',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Cart, { name: 'getCart' })
  async findOne(
    @Args('customerId', { type: () => String }) customerId: string,
  ) {
    try {
      const data = await this.cartsService.findOne(customerId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to get cart',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Query(() => Carts, { name: 'listCarts' })
  async findAll(
    @Args('listCartInput', { nullable: true })
    listCartInput?: ListCartInput,
  ) {
    try {
      const { data, count } = await this.cartsService.findAll(listCartInput);
      const res = this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
      return { ...res, count };
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to list carts',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Cart, { name: 'updateCart' })
  async updateCart(@Args('updateCartInput') updateCartInput: UpdateCartInput) {
    try {
      const data = await this.cartsService.update(
        updateCartInput.customerId,
        updateCartInput,
      );
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to update cart',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }

  @Mutation(() => Cart, { name: 'deleteCart' })
  async removeCart(
    @Args('customerId', { type: () => String }) customerId: string,
  ) {
    try {
      const data = await this.cartsService.remove(customerId);
      return this.successHandler.getSuccessResponse({
        data,
        code: 200,
      });
    } catch (error) {
      const errorResponse = this.errorHandler.getErrorResponse({
        message: error.message || 'Failed to delete cart',
        code: error.statusCode || HttpStatus.INTERNAL_SERVER_ERROR,
      });
      return errorResponse;
    }
  }
}
