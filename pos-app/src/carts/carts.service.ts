import { Injectable } from '@nestjs/common';
import { CreateCartInput } from './dto/create-cart.input';
import { CreateCart } from './lib/create-cart';
import { RemoveCart } from './lib/delete-cart';
import { UpdateCartInput } from './dto/update-cart.input';
import { UpdateCart } from './lib/update-cart';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { GetCart } from './lib/get-cart';
import { ListCartInput } from './dto/list-carts.input';
import { QueryCarts } from './lib/list-carts';
import { AppConfigParameters } from 'src/config/config';

@Injectable()
export class CartsService {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async create(createCartInput: CreateCartInput) {
    const createHandler = new CreateCart(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return createHandler.createCart(createCartInput);
  }

  async findOne(customerId: string) {
    const getHandler = new GetCart(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return getHandler.getCartByCustomerID(customerId);
  }

  async findAll(listCartInput: ListCartInput) {
    const queryCartsHandler = new QueryCarts(
      this.configService,
      this.ssmClient,
      this.configParameters,
    );
    return queryCartsHandler.queryCarts(listCartInput);
  }

  async update(customerId: string, updateCartInput: UpdateCartInput) {
    const updateHandler = new UpdateCart(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return updateHandler.updateCart(customerId, updateCartInput);
  }

  async remove(customerId: string) {
    const removeHandler = new RemoveCart(
      this.configService,
      this.docClient,
      this.ssmClient,
      this.configParameters,
    );
    return removeHandler.removeCart(customerId);
  }
}
