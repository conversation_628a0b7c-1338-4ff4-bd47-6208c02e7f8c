// modules
import { v4 as uuid } from 'uuid';
import { UpdateCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CartData } from '../entities/cart.entity';
import { AppConfigParameters } from 'src/config/config';

export class UpdateCart {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async updateCart(
    customerId: string = '',
    { customerId: {}, ...updateCartInput },
  ): Promise<CartData> {
    posLogger.info('cart', 'updateCart', {
      input: { customerId, updateCartInput },
    });
    try {
      const CART_TABLE = await this.configParameters.getCartTableName();

      let updateExpressionString: string =
        'Set createdAt = :createdAt, updatedAt = :updatedAt, #id = :id, ';
      const expressionAttributeNames: Record<string, string> = {
        '#id': 'id',
      };
      const expressionAttributesValues: Record<string, any> = {
        ':id': uuid(),
        ':createdAt': moment().toISOString(),
        ':updatedAt': moment().toISOString(),
      };

      Object.keys(updateCartInput).map((key) => {
        updateExpressionString += `#${key} = :${key}, `;
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributesValues[`:${key}`] = updateCartInput[key];
      });

      updateExpressionString = updateExpressionString.substring(
        0,
        updateExpressionString.length - 2,
      );

      // Fire the update command
      const command = new UpdateCommand({
        TableName: CART_TABLE,
        Key: {
          customerId: `${customerId}`,
        },
        UpdateExpression: updateExpressionString,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributesValues,
        ReturnValues: 'ALL_NEW',
      });

      const { Attributes }: { Attributes: CartData } =
        await this.docClient.updateItem(command);
      return Attributes;
    } catch (e) {
      posLogger.error('cart', 'updateCart', { error: e });
      throw e;
    }
  }
}
