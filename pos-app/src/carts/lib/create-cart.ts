// modules
import { v4 as uuid } from 'uuid';
import { PutCommand } from '@aws-sdk/lib-dynamodb';
import moment from 'moment';
import { CreateCartInput } from '../dto/create-cart.input';
import { CartData } from '../entities/cart.entity';
import { posLogger } from 'src/common/logger';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { AppConfigParameters } from 'src/config/config';

export class CreateCart {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async createCart({
    customerId,
    ...event
  }: CreateCartInput): Promise<CartData> {
    posLogger.info('cart', 'createCart', { input: { customerId, ...event } });
    try {
      const CART_TABLE = await this.configParameters.getCartTableName();

      const Item: CartData = {
        ...event,
        id: uuid(),
        customerId: `${customerId}`,
        createdAt: moment().toISOString(),
        updatedAt: moment().toISOString(),
      };

      const cartCommand = new PutCommand({
        TableName: CART_TABLE,
        Item,
      });

      await this.docClient.createItem(cartCommand);
      return Item;
    } catch (e) {
      posLogger.error('cart', 'createCart', { error: e });
      throw e;
    }
  }
}
