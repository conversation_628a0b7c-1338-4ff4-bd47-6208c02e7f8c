// modules
import { DeleteCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CartData } from '../entities/cart.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
export class RemoveCart {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async removeCart(customerId: string): Promise<CartData> {
    posLogger.info('cart', 'removeCart', { input: { customerId } });
    try {
      const CART_TABLE = await this.configParameters.getCartTableName();

      const param = new DeleteCommand({
        TableName: CART_TABLE,
        Key: {
          customerId: `${customerId}`,
        },
        ReturnValues: 'ALL_OLD',
      });

      const { Attributes }: { Attributes: CartData } =
        await this.docClient.deleteItem(param);

      if (!Attributes) {
        throw new CustomError(
          'Invalid Customer ID! No Cart found for given Customer ID.',
          404,
        );
      }

      return Attributes;
    } catch (e) {
      posLogger.error('cart', 'removeCart', { error: e });
      throw e;
    }
  }
}
