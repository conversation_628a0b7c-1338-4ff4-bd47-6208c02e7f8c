// modules

import { filterFormatter } from 'src/common/helper/filter-helper';
import {
  searchingFilter,
  sortingFilter,
  sortingFilterType,
} from '../filters/filter';
import { ElasticClient } from 'src/utils/elasticsearch.config';
import { ConfigService } from '@nestjs/config';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { posLogger } from 'src/common/logger';
import { ListCartInput } from '../dto/list-carts.input';
import { CartData, Carts } from '../entities/cart.entity';
import { AppConfigParameters } from 'src/config/config';
export class QueryCarts {
  constructor(
    private configService: ConfigService,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async queryCarts(filter: ListCartInput): Promise<Carts> {
    posLogger.info('cart', 'queryCarts', { input: filter });
    try {
      const CART_TABLE = await this.configParameters.getCartTableName();

      const { searchArray, sortObject } = await filterFormatter(
        sortingFilter,
        searchingFilter,
        sortingFilterType,
        filter,
      );

      let size = Number(filter?.size) || 0;
      const from = Number(filter?.from) || 0;
      const paginate = !!size;
      const esHandler = new ElasticClient(this.configService, this.ssmClient);

      if (!paginate) {
        const { body: bodyRes } = await esHandler.count({
          index: CART_TABLE,
        });

        size = bodyRes?.count;
      }

      if (searchArray.length) {
        const { body: bodyRes } = await esHandler.count({
          index: CART_TABLE,
          body: {
            query: {
              bool: {
                must: [...searchArray],
              },
            },
          },
        });
        const response = await esHandler.search({
          index: CART_TABLE,
          body: {
            size,
            from,
            query: {
              bool: {
                must: [...searchArray],
              },
            },
            sort: [sortObject],
          },
        });
        const data: CartData[] = response.body.hits.hits.map(
          (hit) => hit._source,
        );

        return { data, count: bodyRes?.count };
      }

      const response = await esHandler.search({
        index: CART_TABLE,
        body: {
          size,
          from,
          query: {
            match_all: {},
          },
          sort: [sortObject],
        },
      });
      const { body: bodyRes } = await esHandler.count({
        index: CART_TABLE,
      });
      const data: CartData[] = response.body.hits.hits.map(
        (hit) => hit._source,
      );

      return { data, count: bodyRes?.count };
    } catch (e) {
      posLogger.error('cart', 'queryCarts', { error: e });
      throw e;
    }
  }
}
