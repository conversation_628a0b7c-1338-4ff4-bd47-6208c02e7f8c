// modules
import { GetCommand } from '@aws-sdk/lib-dynamodb';
import { ConfigService } from '@nestjs/config';
import { AppDocumentClient } from 'src/common/document-client/document-client';
import { posLogger } from 'src/common/logger';
import { AppSsmClient } from 'src/common/ssm-client/ssm-client';
import { CartData } from '../entities/cart.entity';
import { CustomError } from 'src/common/response/errorHandler/error.handler';
import { AppConfigParameters } from 'src/config/config';
export class GetCart {
  constructor(
    private configService: ConfigService,
    private docClient: AppDocumentClient,
    private ssmClient: AppSsmClient,
    private configParameters: AppConfigParameters,
  ) {}

  async getCartByCustomerID(customerId: string): Promise<CartData> {
    posLogger.info('cart', 'getCartByCustomerID', { input: { customerId } });
    try {
      const CART_TABLE = await this.configParameters.getCartTableName();

      const param = new GetCommand({
        TableName: CART_TABLE,
        Key: {
          customerId: `${customerId}`,
        },
      });

      const { Item }: { Item: CartData } = await this.docClient.getItem(param);
      if (Item) {
        posLogger.info('cart', 'getCartByCustomerID', { res: { Item } });
        return Item;
      }

      throw new CustomError(
        'Invalid Customer ID! No Cart found for given Customer ID.',
        404,
      );
    } catch (e) {
      posLogger.error('cart', 'getCartByCustomerID', { error: e });
      throw e;
    }
  }
}
