import { CartProductInput, CustomCodeInput } from './create-cart.input';
import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class UpdateCartInput {
  @Field(() => String, { nullable: false })
  customerId: string;

  @Field(() => [CartProductInput], { nullable: false })
  cartProducts: CartProductInput[];

  @Field(() => String, { nullable: false })
  storeId: string;

  @Field(() => String, { nullable: false })
  employeeId: string;

  @Field(() => String, { nullable: true })
  campaignCode?: string;

  @Field(() => CustomCodeInput, { nullable: true })
  customCode?: CustomCodeInput;

  @Field(() => String, { nullable: true })
  promotionalCode?: string;

  @Field(() => String, { nullable: true })
  notes?: string;

  @Field(() => String, { nullable: true })
  pinCode?: string;
}
