import { InputType, Field, Int } from '@nestjs/graphql';
import { CouponType } from 'src/common/enum/coupons';

@InputType()
export class CustomRangeDataInput {
  @Field(() => String, { nullable: false })
  min: string;

  @Field(() => String, { nullable: false })
  max: string;

  @Field(() => Boolean, { nullable: true })
  includesMin: boolean;
}

@InputType()
export class CustomDataInput {
  @Field(() => String, { nullable: true })
  length: string;

  @Field(() => String, { nullable: true })
  breadth: string;

  @Field(() => String, { nullable: true })
  height: string;

  @Field(() => CustomRangeDataInput, { nullable: true })
  lengthRange: CustomRangeDataInput;

  @Field(() => CustomRangeDataInput, { nullable: true })
  breadthRange: CustomRangeDataInput;
}

@InputType()
export class CartProductInput {
  @Field(() => String, { nullable: false })
  productId: string;

  @Field(() => String, { nullable: true })
  variantId?: string;

  @Field(() => Int, { nullable: false })
  quantity: number;

  @Field(() => CustomDataInput, { nullable: true })
  customData?: CustomDataInput;

  @Field(() => Number, { nullable: true })
  price?: number;

  @Field(() => String, { nullable: true })
  priceType?: string;

  @Field(() => Number, { nullable: true })
  originalPrice?: number;

  @Field(() => Boolean, { nullable: true })
  hasColorOptions?: boolean;
}

@InputType()
export class CustomCodeInput {
  @Field(() => CouponType, { nullable: true })
  value_type: CouponType;

  @Field(() => String, { nullable: true })
  approver: string;

  @Field(() => Number, { nullable: true })
  value: number;
}

@InputType()
export class CreateCartInput {
  @Field(() => String, { nullable: false })
  customerId: string;

  @Field(() => [CartProductInput], { nullable: false })
  cartProducts: CartProductInput[];

  @Field(() => String, { nullable: false })
  storeId: string;

  @Field(() => String, { nullable: false })
  employeeId: string;

  @Field(() => String, { nullable: true })
  campaignCode?: string;

  @Field(() => CustomCodeInput, { nullable: true })
  customCode?: CustomCodeInput;

  @Field(() => String, { nullable: true })
  promotionalCode?: string;

  @Field(() => String, { nullable: true })
  notes?: string;

  @Field(() => String, { nullable: true })
  pinCode?: string;
}
