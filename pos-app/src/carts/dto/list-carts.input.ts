import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class CartTermSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  customerId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  updatedAt?: string;
}

@InputType()
export class CartTextSearchFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  storeId?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  id?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  employeeId?: string;
}

@InputType()
export class CartSortingFieldsInput {
  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  createdAt?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Value should be in asc or desc',
  })
  updatedAt?: string;
}

@InputType()
export class ListCartInput {
  @Field(() => String, {
    nullable: true,
    description: 'Count from which user wants to retrieve the records',
  })
  from?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Count till which user wants to retrieve the records',
  })
  size?: string;

  @Field(() => CartTermSearchFieldsInput, {
    nullable: true,
    description: 'Search Boolean fields to filter the records',
  })
  termSearchFields?: CartTermSearchFieldsInput;

  @Field(() => CartTextSearchFieldsInput, {
    nullable: true,
    description: 'Search Text fields to filter the records',
  })
  textSearchFields?: CartTextSearchFieldsInput;

  @Field(() => CartSortingFieldsInput, {
    nullable: true,
    description: 'Sort fields to sort the records',
    defaultValue: { createdAt: 'desc' },
  })
  sortBy?: CartSortingFieldsInput;
}
